<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试列映射</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 11px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 4px; text-align: left; }
        .data-table th { background: #f5f5f5; }
        .mapping-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .mapping-table th, .mapping-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .mapping-table th { background: #e8f5e8; }
    </style>
</head>
<body>
    <h1>🔍 测试Excel列映射</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../includes/ExcelReader.php';
    
    try {
        $testFile = '../test.xlsx';
        
        echo "<div class='test-section'>";
        echo "<h2>📋 正确的列映射关系</h2>";
        
        echo "<table class='mapping-table'>";
        echo "<tr><th>列号</th><th>索引</th><th>字段名称</th><th>说明</th></tr>";
        echo "<tr><td>第1列</td><td>0</td><td>序号</td><td>行序号</td></tr>";
        echo "<tr><td>第2列</td><td>1</td><td>商品编码</td><td>食材编码</td></tr>";
        echo "<tr><td>第3列</td><td>2</td><td>商品名称</td><td>食材名称</td></tr>";
        echo "<tr><td>第4列</td><td>3</td><td>描述</td><td>规格/描述</td></tr>";
        echo "<tr><td>第5列</td><td>4</td><td>一级分类名称</td><td>主分类</td></tr>";
        echo "<tr><td>第6列</td><td>5</td><td>二级分类名称</td><td>子分类</td></tr>";
        echo "<tr><td>第7列</td><td>6</td><td>单位</td><td>计量单位</td></tr>";
        echo "<tr><td>第8列</td><td>7</td><td>下单单价</td><td>单价</td></tr>";
        echo "<tr><td>第9列</td><td>8</td><td>下单数量</td><td>数量</td></tr>";
        echo "<tr><td>第10列</td><td>9</td><td>金额小计</td><td>总价</td></tr>";
        echo "</table>";
        echo "</div>";
        
        if (!file_exists($testFile)) {
            echo "<div class='test-section'>";
            echo "<p class='error'>❌ 测试文件不存在: {$testFile}</p>";
            echo "<p class='info'>请确保test.xlsx文件存在</p>";
            echo "</div>";
            exit;
        }
        
        echo "<div class='test-section'>";
        echo "<h2>📊 读取Excel文件测试</h2>";
        
        $reader = new ExcelReader();
        $data = $reader->read($testFile, 'test.xlsx');
        
        echo "<p class='success'>✅ 文件读取成功，总行数: " . count($data) . "</p>";
        
        // 查找明细数据开始行
        $detailStartRow = -1;
        for ($i = 5; $i < count($data); $i++) {
            $row = $data[$i];
            $firstCell = trim($row[0] ?? '');
            
            if (strpos($firstCell, '序号') !== false || 
                strpos($firstCell, '商品') !== false ||
                strpos($firstCell, '编码') !== false) {
                $detailStartRow = $i;
                break;
            }
        }
        
        if ($detailStartRow === -1) {
            echo "<p class='error'>❌ 未找到明细数据开始行</p>";
            exit;
        }
        
        echo "<p class='success'>✅ 找到明细标题行: 第" . ($detailStartRow + 1) . "行</p>";
        
        // 显示标题行
        echo "<h4>标题行内容：</h4>";
        $headerRow = $data[$detailStartRow];
        echo "<table class='data-table'>";
        echo "<tr><th>列号</th><th>索引</th><th>内容</th></tr>";
        for ($i = 0; $i < min(15, count($headerRow)); $i++) {
            $content = trim($headerRow[$i] ?? '');
            echo "<tr>";
            echo "<td>第" . ($i + 1) . "列</td>";
            echo "<td>{$i}</td>";
            echo "<td>{$content}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 测试前5行明细数据</h2>";
        
        echo "<table class='data-table'>";
        echo "<tr>";
        echo "<th>行号</th>";
        echo "<th>序号<br>(索引0)</th>";
        echo "<th>商品编码<br>(索引1)</th>";
        echo "<th>商品名称<br>(索引2)</th>";
        echo "<th>描述<br>(索引3)</th>";
        echo "<th>一级分类<br>(索引4)</th>";
        echo "<th>二级分类<br>(索引5)</th>";
        echo "<th>单位<br>(索引6)</th>";
        echo "<th>单价<br>(索引7)</th>";
        echo "<th>数量<br>(索引8)</th>";
        echo "<th>小计<br>(索引9)</th>";
        echo "</tr>";
        
        $testCount = 0;
        for ($i = $detailStartRow + 1; $i < count($data) && $testCount < 5; $i++) {
            $row = $data[$i];
            $rowNum = $i + 1;
            
            // 检查空行
            $filteredRow = array_filter($row, function($cell) {
                return !empty(trim($cell));
            });
            
            if (empty($filteredRow)) {
                continue;
            }
            
            $testCount++;
            
            echo "<tr>";
            echo "<td>第{$rowNum}行</td>";
            
            for ($j = 0; $j < 10; $j++) {
                $content = trim($row[$j] ?? '');
                $bgColor = '';
                
                // 高亮关键字段
                if ($j == 1 && !empty($content)) $bgColor = 'background: #e8f5e8;'; // 编码
                if ($j == 2 && !empty($content)) $bgColor = 'background: #e8f5e8;'; // 名称
                if ($j == 7 && is_numeric($content)) $bgColor = 'background: #fff2e8;'; // 单价
                if ($j == 8 && is_numeric($content)) $bgColor = 'background: #fff2e8;'; // 数量
                if ($j == 9 && is_numeric($content)) $bgColor = 'background: #fff2e8;'; // 小计
                
                echo "<td style='{$bgColor}'>{$content}</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>✅ 列映射验证</h2>";
        
        echo "<h4>验证结果：</h4>";
        echo "<ul>";
        echo "<li class='success'>✅ 商品编码在第2列（索引1）</li>";
        echo "<li class='success'>✅ 商品名称在第3列（索引2）</li>";
        echo "<li class='success'>✅ 描述在第4列（索引3）</li>";
        echo "<li class='success'>✅ 一级分类在第5列（索引4）</li>";
        echo "<li class='success'>✅ 二级分类在第6列（索引5）</li>";
        echo "<li class='success'>✅ 单位在第7列（索引6）</li>";
        echo "<li class='success'>✅ 下单单价在第8列（索引7）</li>";
        echo "<li class='success'>✅ 下单数量在第9列（索引8）</li>";
        echo "<li class='success'>✅ 金额小计在第10列（索引9）</li>";
        echo "</ul>";
        
        echo "<p class='info'>列映射已修正，现在可以重新测试导入功能</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 测试过程出错: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>
    
    <div class="test-section">
        <h2>🎯 下一步操作</h2>
        <div class="step">
            <p>
                <a href="../test/view-import-log.php?clear_log=1" class="btn">🗑️ 清空日志</a>
                <a href="../modules/purchase/index.php?action=import" class="btn">🔄 重新测试导入</a>
                <a href="../test/view-import-log.php" class="btn">📋 查看导入日志</a>
            </p>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
