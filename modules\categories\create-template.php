<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-plus-circle"></i>
                添加分类
            </h1>
            <div class="header-actions">
                <?php if (isset($default_parent_id) && $default_parent_id > 0): ?>
                    <a href="index.php?action=manage_subcategories&parent_id=<?= $default_parent_id ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        返回子分类管理
                    </a>
                <?php endif; ?>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>
        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <!-- 添加分类表单 -->
        <div class="form-container">
            <div class="form-header">
                <div class="form-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="form-title">
                    <h2>新建分类</h2>
                    <p>请填写分类的基本信息</p>
                </div>
            </div>

            <form method="POST" class="category-form">
                <div class="form-grid">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">父分类</label>
                                <select name="parent_id" class="form-control" onchange="updateCategoryLevel(); updatePreview()">
                                    <option value="">选择父分类（留空为一级分类）</option>
                                    <?php if (!empty($parent_categories)): ?>
                                        <?php foreach ($parent_categories as $parent): ?>
                                            <?php
                                            $selected = '';
                                            if (isset($_POST['parent_id'])) {
                                                $selected = $_POST['parent_id'] == $parent['id'] ? 'selected' : '';
                                            } elseif (isset($default_parent_id) && $default_parent_id == $parent['id']) {
                                                $selected = 'selected';
                                            }
                                            ?>
                                            <option value="<?= $parent['id'] ?>" <?= $selected ?>>
                                                <?= htmlspecialchars($parent['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                                <small class="form-text">选择父分类将创建二级分类，留空将创建一级分类</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">分类级别</label>
                                <div class="level-display">
                                    <span id="levelBadge" class="level-badge level-1">一级分类</span>
                                </div>
                                <small class="form-text">根据是否选择父分类自动确定</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">分类名称</label>
                                <input type="text" name="name" class="form-control" required
                                       placeholder="请输入分类名称" value="<?= htmlspecialchars($_POST['name'] ?? '') ?>"
                                       oninput="updatePreview(); checkNameAvailability(this.value)">
                                <div id="nameCheckResult" class="name-check-result"></div>
                                <?php if (isset($_POST['name']) && !empty($_POST['name'])): ?>
                                    <?php
                                    // 服务器端名称检查
                                    try {
                                        require_once '../../includes/Database.php';
                                        $db = Database::getInstance();
                                        $existing = $db->fetchOne("SELECT id FROM ingredient_categories WHERE name = ?", [trim($_POST['name'])]);
                                        if ($existing) {
                                            echo '<div class="name-check-result unavailable">❌ 分类名称已存在，请使用其他名称</div>';
                                        } else {
                                            echo '<div class="name-check-result available">✅ 分类名称可用</div>';
                                        }
                                    } catch (Exception $e) {
                                        echo '<div class="name-check-result error">⚠️ 检查失败: ' . htmlspecialchars($e->getMessage()) . '</div>';
                                    }
                                    ?>
                                <?php endif; ?>
                            </div>
                            <div class="form-group">
                                <label class="form-label">排序权重</label>
                                <input type="number" name="sort_order" class="form-control" min="0"
                                       placeholder="请输入排序权重" value="<?= htmlspecialchars($_POST['sort_order'] ?? '0') ?>"
                                       oninput="updatePreview()">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">分类描述</label>
                                <textarea name="description" class="form-control" rows="4"
                                          placeholder="请输入分类描述（可选）" oninput="updatePreview()"><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                                <small class="form-text">详细描述该分类包含的食材类型</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="form-actions">
                    <button type="button" onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="submit" class="btn btn-primary btn-submit">
                        <i class="fas fa-save"></i> 保存分类
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* 表单容器样式 - 与食材页面保持一致 */
.form-container {
    max-width: 2600px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px 30px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.form-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    flex-shrink: 0;
}

.form-title h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
}

.form-title p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

.form-grid {
    padding: 30px;
}

.form-section {
    margin-bottom: 40px;
}

.form-section:last-child {
    margin-bottom: 0;
}

.form-section h3 {
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: #667eea;
    font-size: 16px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.form-label.required::after {
    content: '*';
    color: #e53e3e;
    font-size: 16px;
    font-weight: 700;
}

.form-text {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
}

.form-actions {
    background: #f8fafc;
    padding: 20px 30px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid #e2e8f0;
}

.btn-outline-secondary {
    background: transparent;
    color: #6b7280;
    border: 1px solid #d1d5db;
    padding: 12px 20px;
    border-radius: 6px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-outline-secondary:hover {
    background: #6b7280;
    color: white;
    border-color: #6b7280;
    text-decoration: none;
}

.btn-submit {
    background: #4299e1;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-submit:hover {
    background: #3182ce;
}

/* 分类级别标识 */
.level-display {
    display: flex;
    align-items: center;
}

.level-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
}

.level-badge.level-1 {
    background: #e6fffa;
    color: #065f46;
    border: 1px solid #10b981;
}

.level-badge.level-2 {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #f59e0b;
}

/* 名称检查结果样式 */
.name-check-result {
    margin-top: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-height: 20px;
}

.name-check-result.checking {
    background: #e2e8f0;
    color: #64748b;
    border: 1px solid #cbd5e0;
}

.name-check-result.available {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.name-check-result.unavailable {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.name-check-result.error {
    background: #fef3c7;
    color: #d97706;
    border: 1px solid #fed7aa;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .form-header {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .form-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }
    
    .form-title h2 {
        font-size: 20px;
    }
    
    .form-grid {
        padding: 20px;
    }
    
    .form-actions {
        padding: 15px 20px;
        flex-direction: column;
    }
}
</style>

<script>
// 更新分类级别显示
function updateCategoryLevel() {
    const parentSelect = document.querySelector('select[name="parent_id"]');
    const levelBadge = document.getElementById('levelBadge');

    if (parentSelect.value) {
        levelBadge.textContent = '二级分类';
        levelBadge.className = 'level-badge level-2';
    } else {
        levelBadge.textContent = '一级分类';
        levelBadge.className = 'level-badge level-1';
    }
}

// 实时预览更新
function updatePreview() {
    // 简化版预览逻辑，只在控制台输出
    console.log('预览更新');
}

// 检查名称可用性
let nameCheckTimeout;
function checkNameAvailability(name) {
    const resultDiv = document.getElementById('nameCheckResult');

    // 清除之前的定时器
    if (nameCheckTimeout) {
        clearTimeout(nameCheckTimeout);
    }

    // 如果名称为空，清除结果
    if (!name.trim()) {
        if (resultDiv.textContent.includes('正在检查') || resultDiv.textContent.includes('检查失败')) {
            resultDiv.textContent = '';
            resultDiv.className = 'name-check-result';
        }
        return;
    }

    // 显示检查中状态
    resultDiv.textContent = '正在检查名称可用性...';
    resultDiv.className = 'name-check-result checking';

    // 延迟检查，避免频繁请求
    nameCheckTimeout = setTimeout(() => {
        fetch('check-name-simple.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'name=' + encodeURIComponent(name.trim())
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.available) {
                    resultDiv.textContent = '✅ 分类名称可用';
                    resultDiv.className = 'name-check-result available';
                } else if (data.error) {
                    resultDiv.textContent = '❌ 检查失败: ' + (data.message || '未知错误');
                    resultDiv.className = 'name-check-result error';
                } else {
                    resultDiv.textContent = '❌ 分类名称已存在，请使用其他名称';
                    resultDiv.className = 'name-check-result unavailable';
                }
            } catch (parseError) {
                resultDiv.textContent = '';
                resultDiv.className = 'name-check-result';
            }
        })
        .catch(error => {
            resultDiv.textContent = '';
            resultDiv.className = 'name-check-result';
        });
    }, 500);
}

// 表单验证
document.querySelector('.category-form').addEventListener('submit', function(e) {
    const name = document.querySelector('input[name="name"]').value.trim();

    if (!name) {
        e.preventDefault();
        alert('请填写所有必填字段');
        return false;
    }

    // 验证名称长度
    if (name.length > 50) {
        e.preventDefault();
        alert('分类名称不能超过50个字符');
        return false;
    }

    // 验证描述长度
    const description = document.querySelector('textarea[name="description"]').value.trim();
    if (description.length > 500) {
        e.preventDefault();
        alert('分类描述不能超过500个字符');
        return false;
    }

    // 显示提交动画
    const submitBtn = document.querySelector('.btn-submit');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
    submitBtn.disabled = true;

    // 如果验证失败，恢复按钮状态
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    if (typeof updateCategoryLevel === 'function') {
        updateCategoryLevel(); // 初始化分类级别显示
    }
});
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
