<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - 入库单详情</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <?php include 'sidebar.php'; ?>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <div class="content-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1><i class="fas fa-file-alt"></i> 入库单详情</h1>
                        <div>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                            <a href="index.php?action=print&id=<?= $order['id'] ?>" 
                               class="btn btn-primary" target="_blank">
                                <i class="fas fa-print"></i> 打印入库单
                            </a>
                        </div>
                    </div>
                </div>

                <div class="content-body">
                    <!-- 入库单基本信息 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i> 入库单信息
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="info-label">入库单号：</label>
                                        <span class="info-value order-number"><?= htmlspecialchars($order['order_number']) ?></span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">供应商：</label>
                                        <span class="info-value"><?= htmlspecialchars($order['supplier_name']) ?></span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">联系人：</label>
                                        <span class="info-value"><?= htmlspecialchars($order['contact_person'] ?? '-') ?></span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">联系电话：</label>
                                        <span class="info-value"><?= htmlspecialchars($order['phone'] ?? '-') ?></span>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="info-label">入库日期：</label>
                                        <span class="info-value"><?= date('Y年m月d日', strtotime($order['inbound_date'])) ?></span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">操作员：</label>
                                        <span class="info-value"><?= htmlspecialchars($order['operator_name'] ?? '系统') ?></span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">状态：</label>
                                        <?php
                                        $status_class = [
                                            'pending' => 'warning',
                                            'completed' => 'success',
                                            'cancelled' => 'danger'
                                        ];
                                        $status_text = [
                                            'pending' => '待处理',
                                            'completed' => '已完成',
                                            'cancelled' => '已取消'
                                        ];
                                        ?>
                                        <span class="badge bg-<?= $status_class[$order['status']] ?? 'secondary' ?> fs-6">
                                            <?= $status_text[$order['status']] ?? $order['status'] ?>
                                        </span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">创建时间：</label>
                                        <span class="info-value"><?= date('Y-m-d H:i:s', strtotime($order['created_at'])) ?></span>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($order['notes']): ?>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="info-group">
                                        <label class="info-label">备注：</label>
                                        <span class="info-value"><?= nl2br(htmlspecialchars($order['notes'])) ?></span>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- 入库单明细 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list"></i> 入库明细
                                <span class="badge bg-primary ms-2"><?= count($items) ?> 项</span>
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>序号</th>
                                            <th>食材名称</th>
                                            <th>分类</th>
                                            <th>批次号</th>
                                            <th>计划数量</th>
                                            <th>实际数量</th>
                                            <th>单价</th>
                                            <th>总价</th>
                                            <th>生产日期</th>
                                            <th>过期日期</th>
                                            <th>验收状态</th>
                                            <th>备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (!empty($items)): ?>
                                            <?php 
                                            $total_planned = 0;
                                            $total_actual = 0;
                                            $total_amount = 0;
                                            ?>
                                            <?php foreach ($items as $index => $item): ?>
                                                <?php
                                                $total_planned += $item['planned_quantity'];
                                                $total_actual += $item['actual_quantity'];
                                                $total_amount += $item['total_price'];
                                                ?>
                                                <tr>
                                                    <td><?= $index + 1 ?></td>
                                                    <td>
                                                        <div class="ingredient-info">
                                                            <div class="ingredient-name"><?= htmlspecialchars($item['ingredient_name']) ?></div>
                                                            <small class="text-muted"><?= htmlspecialchars($item['unit']) ?></small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-light text-dark"><?= htmlspecialchars($item['category_name'] ?? '-') ?></span>
                                                    </td>
                                                    <td>
                                                        <code class="batch-number"><?= htmlspecialchars($item['batch_number']) ?></code>
                                                    </td>
                                                    <td>
                                                        <span class="quantity"><?= number_format($item['planned_quantity'], 2) ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="quantity actual"><?= number_format($item['actual_quantity'], 2) ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="price">¥<?= number_format($item['unit_price'], 2) ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="total-price">¥<?= number_format($item['total_price'], 2) ?></span>
                                                    </td>
                                                    <td>
                                                        <?= $item['production_date'] ? date('Y-m-d', strtotime($item['production_date'])) : '-' ?>
                                                    </td>
                                                    <td>
                                                        <span class="expired-date"><?= date('Y-m-d', strtotime($item['expired_at'])) ?></span>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $acceptance_class = [
                                                            'pending' => 'warning',
                                                            'accepted' => 'success',
                                                            'rejected' => 'danger'
                                                        ];
                                                        $acceptance_text = [
                                                            'pending' => '待验收',
                                                            'accepted' => '已验收',
                                                            'rejected' => '已拒收'
                                                        ];
                                                        ?>
                                                        <span class="badge bg-<?= $acceptance_class[$item['acceptance_status']] ?? 'secondary' ?>">
                                                            <?= $acceptance_text[$item['acceptance_status']] ?? $item['acceptance_status'] ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <small class="text-muted"><?= htmlspecialchars($item['notes'] ?: '-') ?></small>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                            
                                            <!-- 合计行 -->
                                            <tr class="table-info">
                                                <td colspan="4"><strong>合计</strong></td>
                                                <td><strong><?= number_format($total_planned, 2) ?></strong></td>
                                                <td><strong><?= number_format($total_actual, 2) ?></strong></td>
                                                <td colspan="1"></td>
                                                <td><strong>¥<?= number_format($total_amount, 2) ?></strong></td>
                                                <td colspan="4"></td>
                                            </tr>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="12" class="text-center text-muted py-4">
                                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                                    <div>暂无入库明细</div>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
