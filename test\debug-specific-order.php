<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试特定采购单</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        .data-table th { background: #f5f5f5; }
        .query-box { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔍 调试特定采购单问题</h1>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>📊 已确认采购单详情</h2>";
        
        // 1. 查找状态为2（已确认）的采购单
        $confirmedOrders = $db->fetchAll("
            SELECT po.*, s.name as supplier_name
            FROM purchase_orders po
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            WHERE po.status = 2
            ORDER BY po.created_at DESC
        ");
        
        echo "<p class='info'>状态为2（已确认）的采购单数量: <strong>" . count($confirmedOrders) . "</strong></p>";
        
        if (!empty($confirmedOrders)) {
            echo "<h4>已确认的采购单列表：</h4>";
            echo "<table class='data-table'>";
            echo "<tr><th>ID</th><th>订单号</th><th>供应商</th><th>状态</th><th>订单日期</th><th>总金额</th><th>创建时间</th></tr>";
            foreach ($confirmedOrders as $order) {
                echo "<tr>";
                echo "<td>{$order['id']}</td>";
                echo "<td>{$order['order_number']}</td>";
                echo "<td>{$order['supplier_name']}</td>";
                echo "<td>{$order['status']}</td>";
                echo "<td>{$order['order_date']}</td>";
                echo "<td>" . number_format($order['order_amount'] ?? 0, 2) . "</td>";
                echo "<td>{$order['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // 2. 检查每个采购单的商品项目
            foreach ($confirmedOrders as $order) {
                echo "<h4>采购单 {$order['order_number']} 的商品项目：</h4>";
                
                $items = $db->fetchAll("
                    SELECT poi.*, i.name as ingredient_name, i.unit
                    FROM purchase_order_items poi
                    LEFT JOIN ingredients i ON poi.ingredient_id = i.id
                    WHERE poi.order_id = ?
                ", [$order['id']]);
                
                if (!empty($items)) {
                    echo "<table class='data-table'>";
                    echo "<tr><th>商品ID</th><th>食材ID</th><th>食材名称</th><th>单位</th><th>数量</th><th>单价</th><th>备注</th></tr>";
                    foreach ($items as $item) {
                        $rowClass = empty($item['ingredient_name']) ? 'error' : 'success';
                        echo "<tr class='{$rowClass}'>";
                        echo "<td>{$item['id']}</td>";
                        echo "<td>{$item['ingredient_id']}</td>";
                        echo "<td>" . ($item['ingredient_name'] ?: '❌ 食材不存在') . "</td>";
                        echo "<td>{$item['unit']}</td>";
                        echo "<td>{$item['quantity']}</td>";
                        echo "<td>" . number_format($item['unit_price'], 2) . "</td>";
                        echo "<td>{$item['notes']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<p class='error'>❌ 该采购单没有商品项目！</p>";
                }
            }
        } else {
            echo "<p class='error'>❌ 没有找到状态为2的采购单</p>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 入库模块查询测试</h2>";
        
        echo "<h4>入库模块使用的完整查询：</h4>";
        echo "<div class='query-box'>";
        echo "SELECT <br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.id,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.order_number,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.supplier_id,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;s.name as supplier_name,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.order_date,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.order_amount,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.ingredient_id,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;i.name as ingredient_name,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;i.unit,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.quantity,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.unit_price,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;COALESCE(poi.purpose, poi.notes, '') as purpose<br>";
        echo "FROM purchase_orders po<br>";
        echo "INNER JOIN purchase_order_items poi ON po.id = poi.order_id<br>";
        echo "LEFT JOIN suppliers s ON po.supplier_id = s.id<br>";
        echo "LEFT JOIN ingredients i ON poi.ingredient_id = i.id<br>";
        echo "WHERE po.status IN (1, 2)<br>";
        echo "AND poi.ingredient_id IS NOT NULL<br>";
        echo "AND i.id IS NOT NULL<br>";
        echo "ORDER BY po.order_date DESC, po.order_number ASC";
        echo "</div>";
        
        // 执行入库模块的查询
        $inboundQuery = $db->fetchAll("
            SELECT 
                po.id,
                po.order_number,
                po.supplier_id,
                s.name as supplier_name,
                po.order_date,
                po.order_amount,
                poi.ingredient_id,
                i.name as ingredient_name,
                i.unit,
                poi.quantity,
                poi.unit_price,
                COALESCE(poi.purpose, poi.notes, '') as purpose
            FROM purchase_orders po
            INNER JOIN purchase_order_items poi ON po.id = poi.order_id
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN ingredients i ON poi.ingredient_id = i.id
            WHERE po.status IN (1, 2) 
            AND poi.ingredient_id IS NOT NULL
            AND i.id IS NOT NULL
            ORDER BY po.order_date DESC, po.order_number ASC
        ");
        
        echo "<p class='info'>入库模块查询结果: <strong>" . count($inboundQuery) . "</strong> 条记录</p>";
        
        if (!empty($inboundQuery)) {
            echo "<p class='success'>✅ 查询成功！</p>";
            echo "<table class='data-table'>";
            echo "<tr><th>订单号</th><th>供应商</th><th>食材</th><th>单位</th><th>数量</th><th>单价</th></tr>";
            foreach ($inboundQuery as $item) {
                echo "<tr>";
                echo "<td>{$item['order_number']}</td>";
                echo "<td>{$item['supplier_name']}</td>";
                echo "<td>{$item['ingredient_name']}</td>";
                echo "<td>{$item['unit']}</td>";
                echo "<td>{$item['quantity']}</td>";
                echo "<td>" . number_format($item['unit_price'], 2) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ 入库模块查询结果为空！</p>";
            
            echo "<h4>逐步排查问题：</h4>";
            
            // 步骤1：检查基础采购单
            $step1 = $db->fetchAll("SELECT id, order_number, status FROM purchase_orders WHERE status IN (1, 2)");
            echo "<p><strong>步骤1</strong> - 状态为1或2的采购单: " . count($step1) . " 个</p>";
            
            if (!empty($step1)) {
                // 步骤2：检查采购单商品
                $orderIds = array_column($step1, 'id');
                $step2 = $db->fetchAll("SELECT order_id, ingredient_id FROM purchase_order_items WHERE order_id IN (" . implode(',', $orderIds) . ")");
                echo "<p><strong>步骤2</strong> - 这些采购单的商品项目: " . count($step2) . " 个</p>";
                
                if (!empty($step2)) {
                    // 步骤3：检查食材数据
                    $ingredientIds = array_unique(array_column($step2, 'ingredient_id'));
                    $step3 = $db->fetchAll("SELECT id, name FROM ingredients WHERE id IN (" . implode(',', $ingredientIds) . ")");
                    echo "<p><strong>步骤3</strong> - 对应的食材数据: " . count($step3) . " 个</p>";
                    
                    if (count($step3) < count($ingredientIds)) {
                        $missingIds = array_diff($ingredientIds, array_column($step3, 'id'));
                        echo "<p class='error'>❌ 缺失的食材ID: " . implode(', ', $missingIds) . "</p>";
                    }
                } else {
                    echo "<p class='error'>❌ 采购单没有商品项目</p>";
                }
            }
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🛠️ 解决方案</h2>";
        
        if (empty($inboundQuery)) {
            echo "<h4>根据排查结果，可能的解决方案：</h4>";
            echo "<ol>";
            echo "<li><strong>如果采购单没有商品项目：</strong>";
            echo "<ul>";
            echo "<li><a href='../modules/purchase/index.php' class='btn'>编辑采购单</a> 并添加商品项目</li>";
            echo "</ul>";
            echo "</li>";
            echo "<li><strong>如果食材数据缺失：</strong>";
            echo "<ul>";
            echo "<li><a href='../modules/ingredients/index.php' class='btn'>检查食材管理</a> 确保食材数据存在</li>";
            echo "</ul>";
            echo "</li>";
            echo "<li><strong>如果供应商数据缺失：</strong>";
            echo "<ul>";
            echo "<li><a href='../modules/suppliers/index.php' class='btn'>检查供应商管理</a> 确保供应商数据存在</li>";
            echo "</ul>";
            echo "</li>";
            echo "</ol>";
        } else {
            echo "<p class='success'>✅ 查询正常，数据应该在入库页面显示</p>";
            echo "<p><a href='../modules/inbound/index.php?action=create' class='btn'>测试入库页面</a></p>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-section'>";
        echo "<h2 class='error'>❌ 数据库连接失败</h2>";
        echo "<p class='error'>错误信息: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
    
    <p><a href="../modules/inbound/index.php?action=create">返回入库页面测试</a></p>
</body>
</html>
