<?php
/**
 * 移动端采购单详情页面
 */

require_once '../includes/Database.php';

// 获取采购单ID或订单号
$id = intval($_GET['id'] ?? 0);
$orderNumber = trim($_GET['order_number'] ?? '');

if (!$id && !$orderNumber) {
    header('Location: purchase.php');
    exit;
}

try {
    $db = Database::getInstance();
    
    // 获取采购单基本信息
    if ($orderNumber) {
        // 通过订单号查询
        $order = $db->query("
            SELECT po.*, s.name as supplier_name, s.phone as supplier_phone, s.address as supplier_address,
                   u.name as creator_name
            FROM purchase_orders po
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN users u ON po.created_by = u.id
            WHERE po.order_number = ?
        ", [$orderNumber])->fetch();
    } else {
        // 通过ID查询
        $order = $db->query("
            SELECT po.*, s.name as supplier_name, s.phone as supplier_phone, s.address as supplier_address,
                   u.name as creator_name
            FROM purchase_orders po
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN users u ON po.created_by = u.id
            WHERE po.id = ?
        ", [$id])->fetch();
    }
    
    if (!$order) {
        header('Location: purchase.php');
        exit;
    }
    
    // 获取采购单商品明细
    $items = $db->query("
        SELECT poi.*, i.name as ingredient_name, i.unit,
               ic.name as category_name
        FROM purchase_order_items poi
        LEFT JOIN ingredients i ON poi.ingredient_id = i.id
        LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
        WHERE poi.order_id = ?
        ORDER BY poi.id
    ", [$order['id']])->fetchAll();
    
    // 状态配置
    $statusConfig = [
        1 => ['text' => '待确认', 'class' => 'warning', 'color' => '#f59e0b'],
        2 => ['text' => '已确认', 'class' => 'success', 'color' => '#10b981'],
        3 => ['text' => '已发货', 'class' => 'info', 'color' => '#3b82f6'],
        4 => ['text' => '已完成', 'class' => 'primary', 'color' => '#8b5cf6'],
        5 => ['text' => '已取消', 'class' => 'danger', 'color' => '#ef4444']
    ];
    
    $status = $statusConfig[$order['status']] ?? $statusConfig[1];
    
} catch (Exception $e) {
    error_log("获取采购单详情失败: " . $e->getMessage());
    header('Location: purchase.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购单详情 - 学校食堂食材出入库管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1a202c;
            line-height: 1.6;
        }

        /* 顶部导航栏 */
        .header {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .back-btn {
            color: #4299e1;
            font-size: 20px;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f0f9ff;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #e0f2fe;
            transform: scale(1.1);
        }

        .header-title {
            flex: 1;
        }

        .header-title h1 {
            font-size: 18px;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 2px;
        }

        .header-title p {
            font-size: 14px;
            color: #64748b;
        }

        /* 状态徽章 */
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            color: white;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 16px;
            padding-bottom: 100px;
        }

        /* 主要信息卡片 */
        .main-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .key-info {
            flex: 1;
        }

        .info-row {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            font-size: 14px;
            color: #64748b;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .info-row i {
            color: #4299e1;
            width: 16px;
            text-align: center;
        }

        .amount-display {
            font-size: 24px;
            font-weight: 900;
            color: #059669;
            background: #f0fdf4;
            padding: 12px 20px;
            border-radius: 12px;
            border: 2px solid #bbf7d0;
        }

        /* 详细信息区域 */
        .details-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .details-title {
            font-size: 18px;
            font-weight: 700;
            color: #1a202c;
            margin: 0 0 16px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .details-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #4299e1;
            border-radius: 2px;
        }

        /* 详细信息组 */
        .detail-group {
            margin-bottom: 20px;
        }

        .detail-group:last-child {
            margin-bottom: 0;
        }

        .group-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin: 0 0 12px 0;
            padding-bottom: 8px;
            border-bottom: 1px solid #e5e7eb;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 12px 0;
            border-bottom: 1px solid #f8fafc;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            color: #64748b;
            font-weight: 500;
            font-size: 14px;
            min-width: 80px;
        }

        .detail-value {
            color: #1e293b;
            font-weight: 600;
            font-size: 14px;
            text-align: right;
            flex: 1;
        }

        /* 商品列表 */
        .items-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .item-detail {
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            border-left: 4px solid #4299e1;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .item-name {
            font-weight: 600;
            color: #1a202c;
            font-size: 15px;
            flex: 1;
            margin-right: 12px;
        }

        .item-amount {
            font-weight: 700;
            color: #059669;
            font-size: 16px;
        }

        .item-info {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            font-size: 13px;
            color: #64748b;
            margin-bottom: 8px;
        }

        .item-info span {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .item-info i {
            color: #4299e1;
        }

        .item-notes {
            font-size: 13px;
            color: #6b7280;
            font-style: italic;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .item-notes i {
            color: #9ca3af;
        }

        /* 备注内容 */
        .notes-content {
            color: #374151;
            line-height: 1.6;
            font-size: 14px;
            background: #f8fafc;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
        }






        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        /* 加载动画 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px;
        }

        .loading i {
            font-size: 24px;
            color: #4299e1;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }



        /* 点击反馈 */
        .back-btn:active {
            transform: scale(0.95);
        }

        /* 电话链接样式 */
        .phone-link {
            color: #4299e1;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .phone-link:hover {
            background: #f0f9ff;
        }

        /* 操作按钮区域 */
        .action-section {
            margin-top: 24px;
            padding: 0 16px 24px;
        }

        .inbound-btn {
            width: 100%;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 16px 24px;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .inbound-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
        }

        .inbound-btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .inbound-btn i {
            font-size: 18px;
        }

    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <div class="header-content">
            <a href="purchase.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div class="header-title">
                <h1>采购单详情</h1>
                <p><?= htmlspecialchars($order['order_number']) ?></p>
            </div>
            <span class="status-badge" style="background: <?= $status['color'] ?>">
                <i class="fas fa-circle"></i>
                <?= $status['text'] ?>
            </span>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 主要信息卡片 -->
        <div class="main-card">
            <!-- 订单号已在顶部显示，这里显示关键信息 -->
            <div class="key-info">
                <div class="info-row">
                    <i class="fas fa-truck"></i>
                    <span><?= htmlspecialchars($order['supplier_name'] ?? '未指定供应商') ?></span>
                </div>
                <div class="info-row">
                    <i class="fas fa-boxes"></i>
                    <span><?= count($items) ?> 个商品</span>
                </div>
                <div class="info-row">
                    <i class="fas fa-clock"></i>
                    <span><?= date('m-d H:i', strtotime($order['created_at'])) ?></span>
                </div>
            </div>
            <div class="amount-display">
                ¥<?= number_format($order['total_amount'], 2) ?>
            </div>
        </div>

        <!-- 详细信息区域 -->
        <div class="details-section">
            <h3 class="details-title">订单详情</h3>

            <div class="detail-group">
                <div class="detail-item">
                    <span class="detail-label">订单号</span>
                    <span class="detail-value"><?= htmlspecialchars($order['order_number']) ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">订货日期</span>
                    <span class="detail-value"><?= date('Y-m-d', strtotime($order['order_date'])) ?></span>
                </div>
                <?php if (isset($order['expected_date']) && $order['expected_date']): ?>
                <div class="detail-item">
                    <span class="detail-label">预期交货</span>
                    <span class="detail-value"><?= date('Y-m-d', strtotime($order['expected_date'])) ?></span>
                </div>
                <?php endif; ?>
                <div class="detail-item">
                    <span class="detail-label">创建时间</span>
                    <span class="detail-value"><?= date('Y-m-d H:i', strtotime($order['created_at'])) ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">创建人</span>
                    <span class="detail-value"><?= htmlspecialchars($order['creator_name'] ?? '系统管理员') ?></span>
                </div>
            </div>

            <?php if ($order['supplier_name'] || $order['supplier_phone'] || $order['supplier_address']): ?>
            <div class="detail-group">
                <h4 class="group-title">供应商信息</h4>
                <?php if ($order['supplier_name']): ?>
                <div class="detail-item">
                    <span class="detail-label">供应商</span>
                    <span class="detail-value"><?= htmlspecialchars($order['supplier_name']) ?></span>
                </div>
                <?php endif; ?>
                <?php if ($order['supplier_phone']): ?>
                <div class="detail-item">
                    <span class="detail-label">联系电话</span>
                    <span class="detail-value">
                        <a href="tel:<?= htmlspecialchars($order['supplier_phone']) ?>" class="phone-link">
                            <i class="fas fa-phone"></i>
                            <?= htmlspecialchars($order['supplier_phone']) ?>
                        </a>
                    </span>
                </div>
                <?php endif; ?>
                <?php if ($order['supplier_address']): ?>
                <div class="detail-item">
                    <span class="detail-label">地址</span>
                    <span class="detail-value"><?= htmlspecialchars($order['supplier_address']) ?></span>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- 商品明细区域 -->
        <div class="details-section">
            <h3 class="details-title">商品明细 (<?= count($items) ?> 项)</h3>

            <div class="items-list">
                <?php foreach ($items as $item): ?>
                <div class="item-detail">
                    <div class="item-header">
                        <div class="item-name"><?= htmlspecialchars($item['ingredient_name'] ?? '未知商品') ?></div>
                        <div class="item-amount">¥<?= number_format($item['quantity'] * $item['unit_price'], 2) ?></div>
                    </div>
                    <div class="item-info">
                        <span><i class="fas fa-cubes"></i> <?= $item['quantity'] ?> <?= htmlspecialchars($item['unit'] ?? '个') ?></span>
                        <span><i class="fas fa-tag"></i> ¥<?= number_format($item['unit_price'], 2) ?></span>
                        <?php if (isset($item['category_name']) && $item['category_name']): ?>
                        <span><i class="fas fa-folder"></i> <?= htmlspecialchars($item['category_name']) ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if ($item['notes']): ?>
                    <div class="item-notes">
                        <i class="fas fa-comment"></i> <?= htmlspecialchars($item['notes']) ?>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <?php if ($order['notes']): ?>
        <!-- 备注信息区域 -->
        <div class="details-section">
            <h3 class="details-title">备注信息</h3>
            <div class="notes-content">
                <?= nl2br(htmlspecialchars($order['notes'])) ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($order['status'] == 2): ?>
        <!-- 验收入库按钮 -->
        <div class="action-section">
            <button class="inbound-btn" onclick="goToInbound()">
                <i class="fas fa-warehouse"></i>
                立即验收入库
            </button>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 主卡片和详细区域进入动画
            const mainCard = document.querySelector('.main-card');
            const detailsSections = document.querySelectorAll('.details-section');

            // 主卡片动画
            if (mainCard) {
                setTimeout(() => {
                    mainCard.style.opacity = '0';
                    mainCard.style.transform = 'translateY(20px)';
                    mainCard.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        mainCard.style.opacity = '1';
                        mainCard.style.transform = 'translateY(0)';
                    }, 100);
                }, 100);
            }

            // 详细区域动画
            detailsSections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.opacity = '0';
                    section.style.transform = 'translateY(20px)';
                    section.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, 100);
                }, (index + 1) * 200);
            });

            // 添加触摸反馈
            const backBtn = document.querySelector('.back-btn');
            if (backBtn) {
                backBtn.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });

                backBtn.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            }

            // 商品详情点击效果
            const itemDetails = document.querySelectorAll('.item-detail');
            itemDetails.forEach(item => {
                item.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                });

                item.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 验收入库按钮点击效果
            const inboundBtn = document.querySelector('.inbound-btn');
            if (inboundBtn) {
                inboundBtn.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                });

                inboundBtn.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            }
        });

        // 跳转到入库页面
        function goToInbound() {
            const orderNumber = '<?= htmlspecialchars($order['order_number']) ?>';
            const orderId = '<?= $order['id'] ?>';

            // 跳转到入库页面，传递采购单信息
            window.location.href = 'inbound.php?from_purchase=1&order_id=' + orderId + '&order_number=' + encodeURIComponent(orderNumber);
        }
    </script>
</body>
</html>
