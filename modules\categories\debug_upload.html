<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-area { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .btn { padding: 10px 20px; margin: 5px; cursor: pointer; border: none; border-radius: 4px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 10px 0; }
        .file-info { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .log { background: #f1f1f1; padding: 10px; margin: 10px 0; font-family: monospace; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>文件上传功能调试</h1>
    
    <div class="test-area">
        <h3>测试1: 基础文件选择</h3>
        <input type="file" id="file1" accept=".csv,.xlsx,.xls">
        <p>状态: <span id="status1">等待选择</span></p>
    </div>
    
    <div class="test-area">
        <h3>测试2: 隐藏文件输入 + 按钮触发</h3>
        <input type="file" id="file2" accept=".csv,.xlsx,.xls" style="display: none;">
        <button class="btn btn-primary" onclick="triggerFileSelect()">选择文件</button>
        <p>状态: <span id="status2">等待选择</span></p>
    </div>
    
    <div class="test-area">
        <h3>测试3: Label关联</h3>
        <input type="file" id="file3" accept=".csv,.xlsx,.xls" style="display: none;">
        <label for="file3" class="btn btn-success" style="cursor: pointer; display: inline-block;">
            选择文件 (Label)
        </label>
        <p>状态: <span id="status3">等待选择</span></p>
    </div>
    
    <div class="test-area">
        <h3>测试4: 拖拽上传</h3>
        <div class="upload-area" id="dropArea">
            <p>拖拽文件到此处</p>
            <input type="file" id="file4" accept=".csv,.xlsx,.xls" style="display: none;">
        </div>
        <p>状态: <span id="status4">等待选择</span></p>
    </div>
    
    <div class="file-info" id="fileInfo" style="display: none;">
        <h4>选择的文件信息:</h4>
        <p>文件名: <span id="fileName"></span></p>
        <p>文件大小: <span id="fileSize"></span></p>
        <p>文件类型: <span id="fileType"></span></p>
    </div>
    
    <div class="log" id="log">
        <strong>调试日志:</strong><br>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showFileInfo(file) {
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileType').textContent = file.type || '未知';
            document.getElementById('fileInfo').style.display = 'block';
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function triggerFileSelect() {
            log('🔘 按钮被点击，尝试触发文件选择');
            try {
                document.getElementById('file2').click();
                log('✅ 文件选择触发成功');
            } catch (error) {
                log('❌ 文件选择触发失败: ' + error.message);
            }
        }
        
        // 为所有文件输入添加事件监听器
        ['file1', 'file2', 'file3', 'file4'].forEach(function(id, index) {
            const input = document.getElementById(id);
            const status = document.getElementById('status' + (index + 1));
            
            input.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    log(`✅ ${id} 选择了文件: ${file.name}`);
                    status.textContent = `已选择: ${file.name}`;
                    status.style.color = 'green';
                    showFileInfo(file);
                } else {
                    log(`⚠️ ${id} 没有选择文件`);
                    status.textContent = '没有选择文件';
                    status.style.color = 'orange';
                }
            });
        });
        
        // 拖拽功能
        const dropArea = document.getElementById('dropArea');
        
        dropArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            dropArea.style.backgroundColor = '#e3f2fd';
            dropArea.style.borderColor = '#2196f3';
        });
        
        dropArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            dropArea.style.backgroundColor = '';
            dropArea.style.borderColor = '#ccc';
        });
        
        dropArea.addEventListener('drop', function(e) {
            e.preventDefault();
            dropArea.style.backgroundColor = '';
            dropArea.style.borderColor = '#ccc';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file4 = document.getElementById('file4');
                file4.files = files;
                const event = new Event('change', { bubbles: true });
                file4.dispatchEvent(event);
                log('✅ 拖拽文件成功: ' + files[0].name);
            }
        });
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 页面加载完成');
            log('🌐 浏览器: ' + navigator.userAgent);
            log('📱 平台: ' + navigator.platform);
            
            // 测试文件API支持
            if (window.File && window.FileReader && window.FileList && window.Blob) {
                log('✅ 浏览器支持文件API');
            } else {
                log('❌ 浏览器不支持文件API');
            }
            
            // 测试拖拽API支持
            if ('draggable' in document.createElement('div')) {
                log('✅ 浏览器支持拖拽API');
            } else {
                log('❌ 浏览器不支持拖拽API');
            }
        });
    </script>
</body>
</html>
