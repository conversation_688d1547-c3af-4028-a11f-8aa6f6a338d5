<?php
/**
 * 检查表结构
 */

require_once 'includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>检查表结构</h2>";
    
    // 1. 检查入库单表结构
    echo "<h3>1. inbound_orders 表结构</h3>";
    $columns = $db->query("SHOW COLUMNS FROM inbound_orders")->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. 检查入库单明细表结构
    echo "<h3>2. inbound_order_items 表结构</h3>";
    $columns = $db->query("SHOW COLUMNS FROM inbound_order_items")->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 3. 测试实际查询
    echo "<h3>3. 测试实际查询</h3>";
    
    $query = "
        SELECT io.*, s.name as supplier_name, u.name as operator_name,
               COUNT(ioi.id) as item_count,
               SUM(ioi.actual_quantity) as total_quantity,
               io.total_amount
        FROM inbound_orders io
        LEFT JOIN suppliers s ON io.supplier_id = s.id
        LEFT JOIN users u ON io.operator_id = u.id
        LEFT JOIN inbound_order_items ioi ON io.id = ioi.order_id
        WHERE 1=1
        GROUP BY io.id
        ORDER BY io.inbound_date DESC, io.created_at DESC
        LIMIT 3
    ";
    
    echo "<p><strong>查询SQL:</strong></p>";
    echo "<pre>" . htmlspecialchars($query) . "</pre>";
    
    $records = $db->fetchAll($query);
    
    if (!empty($records)) {
        echo "<p style='color: green;'>✅ 查询成功，返回 " . count($records) . " 条记录</p>";
        
        echo "<h4>第一条记录的所有字段：</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>字段名</th><th>值</th></tr>";
        foreach ($records[0] as $key => $value) {
            echo "<tr>";
            echo "<td><strong>$key</strong></td>";
            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 检查关键字段
        echo "<h4>关键字段检查：</h4>";
        $first_record = $records[0];
        $key_fields = ['order_number', 'item_count', 'status', 'supplier_name', 'operator_name'];
        
        foreach ($key_fields as $field) {
            if (isset($first_record[$field])) {
                echo "<p style='color: green;'>✅ $field: " . htmlspecialchars($first_record[$field]) . "</p>";
            } else {
                echo "<p style='color: red;'>❌ $field: 字段不存在</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ 查询返回空结果</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 检查失败：" . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3, h4 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    background: white;
    margin: 15px 0;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
}

th {
    background: #007bff;
    color: white;
    padding: 10px;
}

td {
    padding: 8px 10px;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
}

p {
    margin: 10px 0;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}
</style>
