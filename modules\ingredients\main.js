/**
 * 食材管理模块 JavaScript
 */

// DOM 加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    initializeSearch();
    initializeAnimations();
    initializeStockWarnings();
}

/**
 * 初始化搜索功能
 */
function initializeSearch() {
    const searchForm = document.querySelector('.search-box form');
    const searchInput = document.querySelector('input[name="search"]');
    
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            showLoading();
        });
    }
    
    // 实时搜索（防抖）
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2 || this.value.length === 0) {
                    filterIngredients(this.value);
                }
            }, 300);
        });
    }
}

/**
 * 过滤食材
 */
function filterIngredients(searchTerm) {
    const tableRows = document.querySelectorAll('.table tbody tr');
    const searchLower = searchTerm.toLowerCase();
    
    tableRows.forEach(row => {
        const ingredientName = row.querySelector('td:first-child strong').textContent.toLowerCase();
        const categoryName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        
        if (ingredientName.includes(searchLower) || categoryName.includes(searchLower)) {
            row.style.display = '';
            row.classList.add('fade-in');
        } else {
            row.style.display = 'none';
            row.classList.remove('fade-in');
        }
    });
    
    // 更新显示的记录数
    updateRecordCount();
}

/**
 * 更新记录数显示
 */
function updateRecordCount() {
    const visibleRows = document.querySelectorAll('.table tbody tr[style=""], .table tbody tr:not([style])');
    const countBadge = document.querySelector('.badge-secondary');
    if (countBadge) {
        countBadge.textContent = `${visibleRows.length} 种食材`;
    }
}

/**
 * 初始化库存警告
 */
function initializeStockWarnings() {
    const stockCells = document.querySelectorAll('.table tbody tr');
    
    stockCells.forEach(row => {
        const currentStockCell = row.querySelector('td:nth-child(4) span');
        const minStockCell = row.querySelector('td:nth-child(5)');
        
        if (currentStockCell && minStockCell) {
            const currentStock = parseFloat(currentStockCell.textContent);
            const minStock = parseFloat(minStockCell.textContent);
            
            if (currentStock <= minStock) {
                row.classList.add('stock-warning');
                // 添加闪烁效果
                currentStockCell.classList.add('blink-warning');
            } else if (currentStock <= minStock * 1.5) {
                row.classList.add('stock-low');
            }
        }
    });
}

/**
 * 初始化动画
 */
function initializeAnimations() {
    // 表格行动画
    const tableRows = document.querySelectorAll('.table tbody tr');
    tableRows.forEach((row, index) => {
        row.style.animationDelay = (index * 0.05) + 's';
        row.classList.add('slide-in-up');
    });
    
    // 统计数字动画
    animateNumbers();
}

/**
 * 数字计数动画
 */
function animateNumbers() {
    const badges = document.querySelectorAll('.badge-secondary');
    
    badges.forEach(badge => {
        const text = badge.textContent;
        const number = parseInt(text.match(/\d+/));
        if (number) {
            animateNumber(badge, 0, number, 800, text);
        }
    });
}

/**
 * 单个数字动画
 */
function animateNumber(element, start, end, duration, template) {
    const startTime = Date.now();
    
    function update() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.round(start + (end - start) * easeOutQuart(progress));
        element.textContent = template.replace(/\d+/, current);
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

/**
 * 缓动函数
 */
function easeOutQuart(t) {
    return 1 - (--t) * t * t * t;
}

/**
 * 显示加载状态
 */
function showLoading() {
    const form = document.querySelector('.search-box form');
    if (form) {
        form.classList.add('loading');
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const form = document.querySelector('.search-box form');
    if (form) {
        form.classList.remove('loading');
    }
}

/**
 * 导出数据
 */
function exportData() {
    // 收集当前显示的食材数据
    const ingredients = [];
    const tableRows = document.querySelectorAll('.table tbody tr');
    
    tableRows.forEach(row => {
        if (row.style.display !== 'none') {
            const cells = row.querySelectorAll('td');
            if (cells.length > 0) {
                ingredients.push({
                    name: cells[0].querySelector('strong').textContent,
                    category: cells[1].textContent,
                    unit: cells[2].textContent,
                    currentStock: cells[3].textContent,
                    minStock: cells[4].textContent,
                    unitPrice: cells[5].textContent,
                    status: cells[6].textContent
                });
            }
        }
    });
    
    // 生成CSV内容
    const csvContent = generateCSV(ingredients);
    
    // 下载文件
    downloadCSV(csvContent, '食材列表.csv');
}

/**
 * 生成CSV内容
 */
function generateCSV(data) {
    const headers = ['食材名称', '分类', '单位', '当前库存', '最低库存', '单价', '库存状态'];
    const csvRows = [headers.join(',')];
    
    data.forEach(row => {
        const values = [
            row.name,
            row.category,
            row.unit,
            row.currentStock,
            row.minStock,
            row.unitPrice,
            row.status
        ];
        csvRows.push(values.join(','));
    });
    
    return csvRows.join('\n');
}

/**
 * 下载CSV文件
 */
function downloadCSV(content, filename) {
    const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

/**
 * 刷新页面数据
 */
function refreshData() {
    showLoading();
    
    // 添加刷新动画
    const refreshBtn = document.querySelector('.btn-outline-info i');
    if (refreshBtn) {
        refreshBtn.classList.add('fa-spin');
    }
    
    // 模拟数据刷新
    setTimeout(() => {
        location.reload();
    }, 500);
}

/**
 * 删除食材确认
 */
function confirmDelete(ingredientId, ingredientName) {
    const message = `确定要删除食材"${ingredientName}"吗？\n\n删除后将无法恢复，请谨慎操作。`;
    
    if (confirm(message)) {
        // 显示加载状态
        const deleteBtn = document.querySelector(`a[href*="action=delete&id=${ingredientId}"]`);
        if (deleteBtn) {
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            deleteBtn.style.pointerEvents = 'none';
        }
        
        // 执行删除
        window.location.href = `?action=delete&id=${ingredientId}`;
    }
}

/**
 * 库存预警检查
 */
function checkStockWarnings() {
    const warningRows = document.querySelectorAll('.stock-warning');
    const lowStockRows = document.querySelectorAll('.stock-low');
    
    if (warningRows.length > 0) {
        const message = `发现 ${warningRows.length} 种食材库存不足，请及时补充！`;
        showNotification(message, 'warning');
    } else if (lowStockRows.length > 0) {
        const message = `发现 ${lowStockRows.length} 种食材库存偏低，建议关注！`;
        showNotification(message, 'info');
    }
}

/**
 * 显示通知
 */
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// 页面加载完成后检查库存预警
window.addEventListener('load', function() {
    setTimeout(checkStockWarnings, 1000);
});

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
    hideLoading();
});

// 页面卸载前的清理
window.addEventListener('beforeunload', function() {
    // 清理定时器和事件监听器
    const loadingElements = document.querySelectorAll('.loading');
    loadingElements.forEach(element => {
        element.classList.remove('loading');
    });
});

/**
 * 导入功能
 */

// 显示导入模态框
function showImportModal() {
    const modal = document.getElementById('importModal');
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';

        // 重置模态框状态
        resetImportModal();

        console.log('模态框已显示');
    }
}

// 关闭导入模态框
function closeImportModal() {
    const modal = document.getElementById('importModal');
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = 'auto';

        console.log('模态框已关闭');
    }
}

// 重置导入模态框
function resetImportModal() {
    // 隐藏进度和结果
    document.getElementById('importProgress').style.display = 'none';
    document.getElementById('importResult').style.display = 'none';

    // 重置表单
    document.getElementById('importForm').reset();

    // 重置进度条
    const progressFill = document.querySelector('.progress-fill');
    if (progressFill) {
        progressFill.style.width = '0%';
    }

    // 显示开始导入按钮
    const startBtn = document.getElementById('startImportBtn');
    if (startBtn) {
        startBtn.style.display = 'inline-flex';
        startBtn.disabled = false;
    }
}

// 下载模板
function downloadTemplate() {
    // 创建模板数据
    const templateData = [
        ['食材名称', '食材分类', '计量单位', '参考单价', '最低库存预警'],
        ['白菜', '蔬菜类', '斤', '2.50', '20'],
        ['土豆', '蔬菜类', '斤', '3.00', '30'],
        ['猪肉', '肉类', '斤', '28.00', '10'],
        ['大米', '粮油类', '斤', '4.20', '50']
    ];

    // 生成CSV内容
    const csvContent = templateData.map(row => row.join(',')).join('\n');

    // 下载文件
    downloadCSV(csvContent, '食材导入模板.csv');

    showNotification('模板下载成功！请按照模板格式填写数据。', 'success');
}

// 开始导入
function startImport() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];

    if (!file) {
        showNotification('请选择要导入的文件', 'warning');
        return;
    }

    // 验证文件类型
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv'
    ];

    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls|csv)$/i)) {
        showNotification('请选择Excel文件(.xlsx, .xls)或CSV文件', 'error');
        return;
    }

    // 显示进度
    showImportProgress();

    // 模拟文件处理
    simulateImport(file);
}

// 显示导入进度
function showImportProgress() {
    // 隐藏步骤，显示进度
    document.querySelector('.import-steps').style.display = 'none';
    document.getElementById('importProgress').style.display = 'block';

    // 隐藏开始导入按钮
    document.getElementById('startImportBtn').style.display = 'none';

    // 开始进度动画
    const progressFill = document.querySelector('.progress-fill');
    let progress = 0;

    const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;

        progressFill.style.width = progress + '%';

        if (progress >= 90) {
            clearInterval(progressInterval);
        }
    }, 200);

    // 保存进度间隔ID以便后续清理
    window.importProgressInterval = progressInterval;
}

// 模拟导入过程
function simulateImport(file) {
    // 模拟处理时间
    setTimeout(() => {
        // 完成进度条
        const progressFill = document.querySelector('.progress-fill');
        progressFill.style.width = '100%';

        // 清理进度间隔
        if (window.importProgressInterval) {
            clearInterval(window.importProgressInterval);
        }

        setTimeout(() => {
            showImportResult();
        }, 500);
    }, 2000 + Math.random() * 2000);
}

// 显示导入结果
function showImportResult() {
    // 隐藏进度，显示结果
    document.getElementById('importProgress').style.display = 'none';
    document.getElementById('importResult').style.display = 'block';

    // 模拟导入结果
    const skipDuplicates = document.getElementById('skipDuplicates').checked;
    const updateExisting = document.getElementById('updateExisting').checked;

    // 生成随机结果
    const totalRecords = Math.floor(Math.random() * 50) + 10;
    const successCount = Math.floor(totalRecords * 0.8);
    const skipCount = skipDuplicates ? Math.floor(totalRecords * 0.15) : 0;
    const errorCount = totalRecords - successCount - skipCount;

    // 更新统计数字
    document.getElementById('successCount').textContent = successCount;
    document.getElementById('skipCount').textContent = skipCount;
    document.getElementById('errorCount').textContent = errorCount;

    // 如果有错误，显示错误详情
    if (errorCount > 0) {
        showErrorDetails(errorCount);
    }

    // 添加完成按钮
    const modalFooter = document.querySelector('.modal-footer');
    modalFooter.innerHTML = `
        <button type="button" class="btn btn-outline-secondary" onclick="closeImportModal()">关闭</button>
        <button type="button" class="btn btn-primary" onclick="refreshAfterImport()">
            <i class="fas fa-sync-alt"></i> 刷新页面
        </button>
    `;

    // 显示成功通知
    if (successCount > 0) {
        showNotification(`成功导入 ${successCount} 条食材记录！`, 'success');
    }
}

// 显示错误详情
function showErrorDetails(errorCount) {
    const errorDetails = document.getElementById('errorDetails');
    const errorList = document.getElementById('errorList');

    // 生成模拟错误
    const errors = [
        '第3行：食材名称不能为空',
        '第7行：计量单位格式不正确',
        '第12行：参考单价必须为数字',
        '第15行：最低库存预警必须为正数'
    ];

    errorList.innerHTML = '';
    for (let i = 0; i < Math.min(errorCount, errors.length); i++) {
        const li = document.createElement('li');
        li.textContent = errors[i];
        errorList.appendChild(li);
    }

    errorDetails.style.display = 'block';
}

// 导入完成后刷新页面
function refreshAfterImport() {
    closeImportModal();
    refreshData();
}

// 文件拖拽功能
document.addEventListener('DOMContentLoaded', function() {
    const fileUpload = document.querySelector('.file-upload');
    const fileInput = document.getElementById('importFile');
    const fileLabel = document.querySelector('.file-upload-label');

    if (fileUpload && fileInput && fileLabel) {
        // 文件选择事件
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                const fileName = this.files[0].name;
                fileLabel.innerHTML = `
                    <i class="fas fa-file-excel"></i>
                    <span>已选择文件：${fileName}</span>
                    <small>点击重新选择文件</small>
                `;
                fileLabel.style.borderColor = '#48bb78';
                fileLabel.style.background = '#f0fff4';
            }
        });

        // 拖拽事件
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            fileUpload.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            fileUpload.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            fileUpload.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            fileLabel.style.borderColor = '#4299e1';
            fileLabel.style.background = '#ebf8ff';
        }

        function unhighlight() {
            fileLabel.style.borderColor = '#cbd5e0';
            fileLabel.style.background = '#f7fafc';
        }

        fileUpload.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                fileInput.files = files;
                fileInput.dispatchEvent(new Event('change'));
            }
        }
    }
});

// 点击模态框外部关闭
window.addEventListener('click', function(event) {
    const modal = document.getElementById('importModal');
    if (event.target === modal) {
        closeImportModal();
    }
});
