<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-plus-circle"></i>
                添加供应商
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>
        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <!-- 添加供应商表单 -->
        <div class="form-container">
            <div class="form-header">
                <div class="form-icon">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="form-title">
                    <h2>新建供应商</h2>
                    <p>请填写供应商的基本信息</p>
                </div>
            </div>

            <form method="POST" class="supplier-form">
                <div class="form-grid">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">供应商名称</label>
                                <input type="text" name="name" class="form-control" required 
                                       placeholder="请输入供应商名称" value="<?= htmlspecialchars($_POST['name'] ?? '') ?>">
                            </div>
                            <div class="form-group">
                                <label class="form-label required">联系人</label>
                                <input type="text" name="contact_person" class="form-control" required 
                                       placeholder="请输入联系人姓名" value="<?= htmlspecialchars($_POST['contact_person'] ?? '') ?>">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">联系电话</label>
                                <input type="tel" name="phone" class="form-control" required 
                                       placeholder="请输入联系电话" value="<?= htmlspecialchars($_POST['phone'] ?? '') ?>">
                            </div>
                            <!-- 邮箱字段暂时移除，等数据库字段添加后再启用 -->
                            <!--
                            <div class="form-group">
                                <label class="form-label">邮箱地址</label>
                                <input type="email" name="email" class="form-control"
                                       placeholder="请输入邮箱地址" value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                            </div>
                            -->
                        </div>
                    </div>

                    <!-- 地址信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-map-marker-alt"></i> 地址信息</h3>
                        
                        <div class="form-group">
                            <label class="form-label">详细地址</label>
                            <textarea name="address" class="form-control" rows="3" 
                                      placeholder="请输入详细地址"><?= htmlspecialchars($_POST['address'] ?? '') ?></textarea>
                        </div>
                    </div>

                    <!-- 备注信息暂时移除，等数据库字段添加后再启用 -->
                    <!--
                    <div class="form-section">
                        <h3><i class="fas fa-sticky-note"></i> 备注信息</h3>

                        <div class="form-group">
                            <label class="form-label">备注</label>
                            <textarea name="notes" class="form-control" rows="4"
                                      placeholder="请输入备注信息（可选）"><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                        </div>
                    </div>
                    -->
                </div>

                <!-- 提交按钮 -->
                <div class="form-actions">
                    <button type="button" onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="submit" class="btn btn-primary btn-submit">
                        <i class="fas fa-save"></i> 保存供应商
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* 表单容器样式 - 与其他页面保持一致 */
.form-container {
    max-width: 2600px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px 30px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.form-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    flex-shrink: 0;
}

.form-title h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
}

.form-title p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

.form-grid {
    padding: 30px;
}

.form-section {
    margin-bottom: 40px;
}

.form-section:last-child {
    margin-bottom: 0;
}

.form-section h3 {
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: #667eea;
    font-size: 16px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.form-label.required::after {
    content: '*';
    color: #e53e3e;
    font-size: 16px;
    font-weight: 700;
}

.form-text {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
}

.form-actions {
    background: #f8fafc;
    padding: 20px 30px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid #e2e8f0;
}

.btn-outline-secondary {
    background: transparent;
    color: #6b7280;
    border: 1px solid #d1d5db;
    padding: 12px 20px;
    border-radius: 6px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-outline-secondary:hover {
    background: #6b7280;
    color: white;
    border-color: #6b7280;
    text-decoration: none;
}

.btn-submit {
    background: #4299e1;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-submit:hover {
    background: #3182ce;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .form-header {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .form-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }
    
    .form-title h2 {
        font-size: 20px;
    }
    
    .form-grid {
        padding: 20px;
    }
    
    .form-actions {
        padding: 15px 20px;
        flex-direction: column;
    }
}
</style>

<script>
// 表单验证
document.querySelector('.supplier-form').addEventListener('submit', function(e) {
    const name = document.querySelector('input[name="name"]').value.trim();
    const contactPerson = document.querySelector('input[name="contact_person"]').value.trim();
    const phone = document.querySelector('input[name="phone"]').value.trim();
    
    if (!name || !contactPerson || !phone) {
        e.preventDefault();
        alert('请填写所有必填字段');
        return false;
    }
    
    // 验证电话号码格式
    const phoneRegex = /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/;
    if (!phoneRegex.test(phone)) {
        e.preventDefault();
        alert('请输入正确的电话号码格式');
        return false;
    }
    
    // 验证邮箱格式（如果填写了）
    const emailInput = document.querySelector('input[name="email"]');
    if (emailInput) {
        const email = emailInput.value.trim();
        if (email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('请输入正确的邮箱格式');
                return false;
            }
        }
    }
});
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
