<?php
/**
 * 分类导入页面模板 - 简洁版
 */

// 引入通用头部
require_once '../../includes/header.php';
?>

<link rel="stylesheet" href="style.css">

<div class="main-content">
    <?php require_once 'sidebar.php'; ?>

    <!-- 页面标题栏 -->
    <div class="topbar">
        <div class="topbar-left">
            <h1><i class="fas fa-upload"></i> 导入食材分类</h1>
        </div>
        <div class="topbar-right">
        </div>
    </div>

    <div class="content">
        <!-- 错误信息 -->
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <?= htmlspecialchars($error_message) ?>
            </div>
        <?php endif; ?>

        <!-- 上传区域 -->
        <div class="upload-section" style="background: #fff; border-radius: 12px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); padding: 2rem; margin-bottom: 2rem;">
            <div class="section-header" style="margin-bottom: 1.5rem; padding-bottom: 1rem; border-bottom: 1px solid #e9ecef;">
                <h3 style="margin: 0; color: #2c3e50; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-cloud-upload-alt"></i> 选择导入文件
                </h3>
            </div>

            <div class="upload-area" onclick="document.getElementById('fileInput').click()" style="border: 3px dashed #dee2e6; border-radius: 12px; padding: 3rem 2rem; text-align: center; background: #f8f9fa; transition: all 0.3s ease; cursor: pointer; margin-bottom: 1.5rem;">
                <div class="upload-icon" style="font-size: 48px; color: #6c757d; margin-bottom: 20px;">
                    <i class="fas fa-file-excel"></i>
                </div>
                <div class="upload-text">
                    <h4 style="color: #495057; margin-bottom: 10px;">点击此处选择文件</h4>
                    <p style="color: #6c757d; margin-bottom: 20px;">支持 CSV、Excel (.xlsx) 格式，文件大小不超过 2MB</p>
                </div>
                
                <form method="POST" enctype="multipart/form-data" id="importForm" style="display: inline-block;">
                    <input type="file" name="import_file" accept=".csv,.xlsx,.xls" required style="display: none;" id="fileInput">
                    <button type="button" onclick="document.getElementById('fileInput').click(); event.stopPropagation();" class="btn btn-primary" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); border: none; border-radius: 10px; padding: 12px 24px; font-weight: 600; box-shadow: 0 4px 12px rgba(0, 123, 255, 0.25); margin: 5px; color: white;">
                        <i class="fas fa-folder-open"></i> 选择文件
                    </button>
                    <a href="download_template.php" class="btn btn-success" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); border: none; border-radius: 10px; padding: 12px 24px; font-weight: 600; box-shadow: 0 4px 12px rgba(40, 167, 69, 0.25); color: white; text-decoration: none; margin: 5px; display: inline-block;">
                        <i class="fas fa-download"></i> 下载CSV模板
                    </a>
                    <a href="download_excel_template.php" class="btn btn-info" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); border: none; border-radius: 10px; padding: 12px 24px; font-weight: 600; box-shadow: 0 4px 12px rgba(23, 162, 184, 0.25); color: white; text-decoration: none; margin: 5px; display: inline-block;">
                        <i class="fas fa-file-excel"></i> 下载Excel模板
                    </a>
                </form>
            </div>

            <!-- 文件信息显示 -->
            <div id="fileInfo" class="file-info" style="display: none; background: #e8f5e9; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                <div class="file-details" style="display: flex; align-items: center; gap: 15px;">
                    <div class="file-icon" style="font-size: 24px; color: #28a745;">
                        <i class="fas fa-file-check"></i>
                    </div>
                    <div class="file-meta" style="flex: 1;">
                        <div class="file-name" id="fileName" style="font-weight: 600; color: #155724;"></div>
                        <div class="file-size" id="fileSize" style="font-size: 14px; color: #6c757d;"></div>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFile()" style="padding: 5px 10px; font-size: 12px; border-radius: 6px;">
                        <i class="fas fa-times"></i> 清除
                    </button>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="form-actions" style="text-align: center; margin-top: 20px;">
                <button type="button" id="submitBtn" onclick="submitImport()" class="btn btn-primary" disabled style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); border: none; border-radius: 10px; padding: 12px 24px; font-weight: 600; box-shadow: 0 4px 12px rgba(220, 53, 69, 0.25); color: white; margin: 5px; opacity: 0.6; cursor: not-allowed;">
                    <i class="fas fa-upload"></i> 开始导入
                </button>
                <a href="index.php" class="btn btn-outline-secondary" style="border-radius: 10px; padding: 12px 24px; margin: 5px; text-decoration: none;">
                    <i class="fas fa-times"></i> 取消
                </a>
            </div>

            <!-- 导入进度 -->
            <div id="importProgress" class="import-progress" style="display: none; margin-top: 20px;">
                <div class="progress-header" style="text-align: center; margin-bottom: 15px;">
                    <h4 style="color: #007bff;"><i class="fas fa-spinner fa-spin"></i> 正在导入...</h4>
                </div>
                <div class="progress-bar" style="background: #e9ecef; border-radius: 10px; height: 20px; overflow: hidden;">
                    <div class="progress-fill" id="progressFill" style="background: linear-gradient(90deg, #007bff, #0056b3); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                </div>
                <div class="progress-text" id="progressText" style="text-align: center; margin-top: 10px; color: #6c757d;">准备中...</div>
            </div>

            <!-- 导入结果 -->
            <div id="importResult" class="import-result" style="display: none; margin-top: 20px;">
                <div class="result-header" style="text-align: center; margin-bottom: 15px;">
                    <h4 id="resultTitle" style="color: #28a745;"></h4>
                </div>
                <div class="result-stats" style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
                    <div class="stat-item" style="text-align: center;">
                        <span class="stat-label" style="display: block; font-size: 14px; color: #6c757d;">总计:</span>
                        <span class="stat-value" id="totalCount" style="display: block; font-size: 24px; font-weight: 600; color: #495057;">0</span>
                    </div>
                    <div class="stat-item" style="text-align: center;">
                        <span class="stat-label" style="display: block; font-size: 14px; color: #6c757d;">成功:</span>
                        <span class="stat-value success" id="successCount" style="display: block; font-size: 24px; font-weight: 600; color: #28a745;">0</span>
                    </div>
                    <div class="stat-item" style="text-align: center;">
                        <span class="stat-label" style="display: block; font-size: 14px; color: #6c757d;">错误:</span>
                        <span class="stat-value error" id="errorCount" style="display: block; font-size: 24px; font-weight: 600; color: #dc3545;">0</span>
                    </div>
                </div>
                <div id="errorDetails" class="error-details" style="display: none; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px;">
                    <h5 style="color: #721c24; margin-bottom: 10px;">错误详情:</h5>
                    <ul id="errorList" style="margin: 0; padding-left: 20px; color: #721c24;"></ul>
                </div>
                <div class="result-actions" style="text-align: center; margin-top: 15px;">
                    <a href="index.php" class="btn btn-primary" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); border: none; border-radius: 10px; padding: 12px 24px; font-weight: 600; color: white; text-decoration: none; margin: 5px;">
                        <i class="fas fa-list"></i> 查看分类列表
                    </a>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetImport()" style="border-radius: 10px; padding: 12px 24px; margin: 5px;">
                        <i class="fas fa-redo"></i> 重新导入
                    </button>
                </div>
            </div>
        </div>

        <!-- 说明和示例 -->
        <div class="info-section" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <!-- 格式说明 -->
            <div class="info-card" style="background: #fff; border-radius: 12px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <div class="card-header" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; padding: 20px;">
                    <h4 style="margin: 0; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-info-circle"></i> 文件格式要求
                    </h4>
                </div>
                <div class="card-content" style="padding: 20px;">
                    <p style="margin-bottom: 15px;">CSV/Excel文件应包含以下列（按顺序）：</p>
                    <div class="format-list" style="display: grid; gap: 8px;">
                        <div class="format-item required" style="padding: 8px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #dc3545;">
                            分类代码 <span class="required-mark" style="color: #dc3545; font-weight: 600;">*</span>
                        </div>
                        <div class="format-item required" style="padding: 8px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #dc3545;">
                            分类名称 <span class="required-mark" style="color: #dc3545; font-weight: 600;">*</span>
                        </div>
                        <div class="format-item" style="padding: 8px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #28a745;">
                            分类级别
                        </div>
                    </div>
                    <div class="format-notes" style="margin-top: 15px; padding: 15px; background: #e3f2fd; border-radius: 6px;">
                        <p style="margin: 0 0 10px 0; font-weight: 600;">注意事项：</p>
                        <ul style="margin: 0; padding-left: 20px; font-size: 14px;">
                            <li>分类代码和名称为必填项</li>
                            <li>分类级别：1=一级分类，2=二级分类</li>
                            <li>第一行为标题行，导入时会自动跳过</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 示例数据 -->
            <div class="info-card" style="background: #fff; border-radius: 12px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <div class="card-header" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); color: white; padding: 20px;">
                    <h4 style="margin: 0; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-table"></i> 示例数据
                    </h4>
                </div>
                <div class="card-content" style="padding: 20px;">
                    <div class="example-table" style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 10px; border: 1px solid #dee2e6; text-align: left;">分类代码</th>
                                    <th style="padding: 10px; border: 1px solid #dee2e6; text-align: left;">分类名称</th>
                                    <th style="padding: 10px; border: 1px solid #dee2e6; text-align: left;">分类级别</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">VEG</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">蔬菜类</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">1</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">MEAT</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">肉类</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">1</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">VEG_LEAF</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">叶菜类</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">2</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../../assets/js/common.js"></script>
<script>
console.log('🚀 分类导入页面加载');

document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM加载完成');

    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const submitBtn = document.getElementById('submitBtn');
    const uploadArea = document.querySelector('.upload-area');

    if (!fileInput) {
        console.error('❌ 文件输入元素未找到');
        return;
    }

    console.log('✅ 所有元素找到');

    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        console.log('📁 文件选择事件触发');
        const file = e.target.files[0];

        if (file) {
            console.log('✅ 文件选择成功:', file.name);
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'block';
            submitBtn.disabled = false;
            submitBtn.style.opacity = '1';
            submitBtn.style.cursor = 'pointer';
        } else {
            console.log('⚠️ 没有选择文件');
            hideFileInfo();
        }
    });

    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.style.borderColor = '#007bff';
        uploadArea.style.background = '#e3f2fd';
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.style.borderColor = '#dee2e6';
        uploadArea.style.background = '#f8f9fa';
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.style.borderColor = '#dee2e6';
        uploadArea.style.background = '#f8f9fa';
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            const event = new Event('change', { bubbles: true });
            fileInput.dispatchEvent(event);
        }
    });

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function hideFileInfo() {
        fileInfo.style.display = 'none';
        submitBtn.disabled = true;
        submitBtn.style.opacity = '0.6';
        submitBtn.style.cursor = 'not-allowed';
    }

    // 全局函数
    window.clearFile = function() {
        console.log('🗑️ 清除文件');
        fileInput.value = '';
        hideFileInfo();
    };

    window.submitImport = function() {
        if (!fileInput.files.length) {
            alert('请先选择要导入的文件');
            return;
        }

        console.log('🚀 开始导入');
        
        // 显示进度
        document.getElementById('importProgress').style.display = 'block';
        document.getElementById('importResult').style.display = 'none';
        submitBtn.disabled = true;

        // 准备表单数据
        const formData = new FormData();
        formData.append('import_file', fileInput.files[0]);

        // 发送请求
        fetch('index.php?action=import', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            showImportResult(data);
        })
        .catch(error => {
            console.error('导入失败:', error);
            showImportResult({
                success: false,
                message: '导入失败: ' + error.message
            });
        });
    };

    function showImportResult(result) {
        document.getElementById('importProgress').style.display = 'none';
        document.getElementById('importResult').style.display = 'block';

        if (result.success) {
            document.getElementById('resultTitle').innerHTML = '<i class="fas fa-check-circle"></i> 导入完成';
            document.getElementById('totalCount').textContent = result.total || 0;
            document.getElementById('successCount').textContent = result.imported || 0;
            document.getElementById('errorCount').textContent = result.errors ? result.errors.length : 0;

            if (result.errors && result.errors.length > 0) {
                const errorList = document.getElementById('errorList');
                errorList.innerHTML = '';
                result.errors.forEach(error => {
                    const li = document.createElement('li');
                    li.textContent = error;
                    errorList.appendChild(li);
                });
                document.getElementById('errorDetails').style.display = 'block';
            }
        } else {
            document.getElementById('resultTitle').innerHTML = '<i class="fas fa-exclamation-triangle"></i> 导入失败';
            document.getElementById('errorDetails').style.display = 'block';
            document.getElementById('errorList').innerHTML = '<li>' + (result.message || '未知错误') + '</li>';
        }
    }

    window.resetImport = function() {
        document.getElementById('importResult').style.display = 'none';
        document.getElementById('importProgress').style.display = 'none';
        clearFile();
        submitBtn.disabled = false;
    };

    console.log('🎉 事件监听器设置完成');
});
</script>

</body>
</html>
