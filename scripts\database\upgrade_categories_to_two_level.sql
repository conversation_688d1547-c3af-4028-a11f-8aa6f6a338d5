-- 升级食材分类表支持两级分类
-- 执行前请备份数据库

-- 1. 为 ingredient_categories 表添加 parent_id 字段
ALTER TABLE `ingredient_categories` 
ADD COLUMN `parent_id` INT NULL DEFAULT NULL COMMENT '父分类ID（NULL表示一级分类）' AFTER `id`,
ADD COLUMN `level` TINYINT NOT NULL DEFAULT 1 COMMENT '分类级别（1=一级分类，2=二级分类）' AFTER `parent_id`,
ADD COLUMN `description` TEXT NULL COMMENT '分类描述' AFTER `code`,
ADD COLUMN `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态（1=启用，0=停用）' AFTER `sort_order`,
ADD COLUMN `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `created_at`;

-- 2. 添加外键约束
ALTER TABLE `ingredient_categories` 
ADD CONSTRAINT `fk_category_parent` 
FOREIGN KEY (`parent_id`) REFERENCES `ingredient_categories` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- 3. 添加索引
ALTER TABLE `ingredient_categories` 
ADD INDEX `idx_parent_id` (`parent_id`),
ADD INDEX `idx_level` (`level`),
ADD INDEX `idx_status` (`status`);

-- 4. 更新现有数据，将所有现有分类设置为一级分类
UPDATE `ingredient_categories` SET `level` = 1, `parent_id` = NULL WHERE `level` IS NULL OR `level` = 0;

-- 5. 插入一些示例二级分类
INSERT INTO `ingredient_categories` (`name`, `code`, `parent_id`, `level`, `description`, `sort_order`, `status`) VALUES
-- 蔬菜类的二级分类
('叶菜类', 'VEG_LEAF', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'VEG' LIMIT 1) AS t), 2, '包括白菜、菠菜、生菜等叶类蔬菜', 1, 1),
('根茎类', 'VEG_ROOT', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'VEG' LIMIT 1) AS t), 2, '包括萝卜、土豆、胡萝卜等根茎类蔬菜', 2, 1),
('瓜果类', 'VEG_FRUIT', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'VEG' LIMIT 1) AS t), 2, '包括黄瓜、茄子、西红柿等瓜果类蔬菜', 3, 1),

-- 肉类的二级分类
('猪肉类', 'MEAT_PORK', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'MEAT' LIMIT 1) AS t), 2, '各种猪肉制品', 1, 1),
('牛肉类', 'MEAT_BEEF', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'MEAT' LIMIT 1) AS t), 2, '各种牛肉制品', 2, 1),
('鸡肉类', 'MEAT_CHICKEN', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'MEAT' LIMIT 1) AS t), 2, '各种鸡肉制品', 3, 1),

-- 水产类的二级分类
('淡水鱼', 'SEAFOOD_FRESH', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'SEAFOOD' LIMIT 1) AS t), 2, '淡水鱼类', 1, 1),
('海水鱼', 'SEAFOOD_SEA', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'SEAFOOD' LIMIT 1) AS t), 2, '海水鱼类', 2, 1),
('虾蟹类', 'SEAFOOD_SHRIMP', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'SEAFOOD' LIMIT 1) AS t), 2, '虾、蟹等甲壳类', 3, 1),

-- 粮食类的二级分类
('大米类', 'GRAIN_RICE', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'GRAIN' LIMIT 1) AS t), 2, '各种大米', 1, 1),
('面粉类', 'GRAIN_FLOUR', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'GRAIN' LIMIT 1) AS t), 2, '各种面粉', 2, 1),
('杂粮类', 'GRAIN_MISC', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'GRAIN' LIMIT 1) AS t), 2, '小米、玉米等杂粮', 3, 1),

-- 调料类的二级分类
('基础调料', 'SEASONING_BASIC', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'SEASONING' LIMIT 1) AS t), 2, '盐、糖、醋等基础调料', 1, 1),
('香料类', 'SEASONING_SPICE', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'SEASONING' LIMIT 1) AS t), 2, '各种香料和调味品', 2, 1),
('酱料类', 'SEASONING_SAUCE', (SELECT id FROM (SELECT id FROM ingredient_categories WHERE code = 'SEASONING' LIMIT 1) AS t), 2, '酱油、豆瓣酱等酱料', 3, 1);

-- 6. 创建视图方便查询分类层级结构
CREATE OR REPLACE VIEW `v_category_tree` AS
SELECT 
    c1.id,
    c1.name,
    c1.code,
    c1.parent_id,
    c1.level,
    c1.description,
    c1.sort_order,
    c1.status,
    c1.created_at,
    c1.updated_at,
    c2.name AS parent_name,
    c2.code AS parent_code,
    CASE 
        WHEN c1.level = 1 THEN c1.name
        ELSE CONCAT(c2.name, ' > ', c1.name)
    END AS full_path
FROM ingredient_categories c1
LEFT JOIN ingredient_categories c2 ON c1.parent_id = c2.id
WHERE c1.status = 1
ORDER BY 
    COALESCE(c1.parent_id, c1.id), 
    c1.level, 
    c1.sort_order, 
    c1.name;

-- 7. 创建函数获取分类的完整路径
DELIMITER //
CREATE FUNCTION GetCategoryPath(category_id INT) 
RETURNS VARCHAR(255)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE path VARCHAR(255) DEFAULT '';
    DECLARE parent_name VARCHAR(50);
    DECLARE current_name VARCHAR(50);
    DECLARE parent_id INT;
    
    -- 获取当前分类信息
    SELECT name, parent_id INTO current_name, parent_id 
    FROM ingredient_categories 
    WHERE id = category_id;
    
    SET path = current_name;
    
    -- 如果有父分类，获取父分类名称
    IF parent_id IS NOT NULL THEN
        SELECT name INTO parent_name 
        FROM ingredient_categories 
        WHERE id = parent_id;
        SET path = CONCAT(parent_name, ' > ', path);
    END IF;
    
    RETURN path;
END //
DELIMITER ;

-- 8. 验证数据
SELECT 
    '数据验证' as info,
    (SELECT COUNT(*) FROM ingredient_categories WHERE level = 1) as level1_count,
    (SELECT COUNT(*) FROM ingredient_categories WHERE level = 2) as level2_count,
    (SELECT COUNT(*) FROM ingredient_categories WHERE parent_id IS NOT NULL) as has_parent_count;
