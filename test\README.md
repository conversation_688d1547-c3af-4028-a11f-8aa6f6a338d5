# 测试脚本目录

这个目录包含了学校食堂管理系统的所有调试和测试脚本。

## 📋 脚本清单

### 🐘 PHP环境测试
- `check-php.php` - 检查PHP版本、扩展和配置
- `check-php-extensions.php` - 详细检查PHP扩展信息

### 🗄️ 数据库测试
- `check-categories.php` - 检查分类数据完整性
- `debug-categories-list.php` - 调试分类列表显示问题，包含状态修复功能
- `check-duplicate-categories.php` - 检查和修复重复分类数据

### 🔌 API接口测试
- `test-api-direct.php` - 直接测试API端点，支持手动和自动测试
- `test-category-api.php` - 分类API功能完整测试，包含性能测试

### 🏷️ 分类功能测试
- `test-categories-query.php` - 测试修复后的分类查询，包含性能分析
- `test-category-select.php` - 测试分类选择功能，模拟表单提交

### 🥬 食材功能测试
- `debug-ingredient-create.php` - 调试食材创建页面的分类数据问题

### 🚚 供应商功能测试
- `test-supplier-fields.php` - 测试供应商字段完整性
- `fix-suppliers-table.php` - 修复供应商表结构，添加缺失字段
- `debug-supplier-insert.php` - 调试供应商插入操作，测试各种数据场景

## 🎯 使用方法

### 访问测试中心
```
http://localhost:8000/test/
```

### 单独运行脚本
```
http://localhost:8000/test/脚本名称.php
```

## 🔧 脚本功能说明

### 环境检查类
这类脚本用于检查系统环境是否满足运行要求：
- PHP版本和扩展
- 数据库连接
- 文件权限

### 数据验证类
这类脚本用于验证数据完整性：
- 检查重复数据
- 验证数据关联
- 修复数据问题

### 功能测试类
这类脚本用于测试具体功能：
- API端点测试
- 表单提交测试
- 业务逻辑测试

### 性能分析类
这类脚本用于分析系统性能：
- 查询性能测试
- 并发测试
- 响应时间分析

## 📝 开发规范

### 新增测试脚本
1. 文件命名规范：`动作-模块-功能.php`
2. 必须包含详细的注释说明
3. 提供清晰的错误信息和解决建议
4. 支持自动修复功能（如果适用）

### 脚本结构
```php
<?php
/**
 * 脚本功能描述
 */

// 引入必要的文件
require_once '../includes/Database.php';

// 页面标题
echo "<h2>脚本功能名称</h2>";

try {
    // 主要逻辑
    
} catch (Exception $e) {
    // 错误处理
}

// 导航链接
echo "<hr>";
echo "<p><a href='../index.php'>返回首页</a></p>";
?>
```

## 🚨 注意事项

1. **安全性** - 测试脚本仅用于开发和调试，生产环境应删除或限制访问
2. **数据安全** - 涉及数据修改的脚本会提供确认机制
3. **性能影响** - 性能测试脚本可能对系统造成负载，请谨慎使用
4. **依赖关系** - 某些脚本依赖特定的数据库表结构

## 📞 技术支持

如果测试脚本发现问题或需要添加新的测试功能，请：
1. 查看相关模块的文档
2. 检查数据库连接和表结构
3. 查看系统日志文件
4. 联系技术支持团队
