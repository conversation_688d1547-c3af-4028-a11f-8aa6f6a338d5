/**
 * 仪表板模块 JavaScript - 增强版
 */

// 全局变量
let refreshTimer = null;
let timeUpdateTimer = null;

// 配置对象
const DashboardConfig = {
    refreshInterval: 30000, // 30秒自动刷新
    animationDuration: 1000,
    chartColors: {
        primary: 'rgba(102, 126, 234, 1)',
        secondary: 'rgba(118, 75, 162, 1)',
        success: 'rgba(79, 172, 254, 1)',
        warning: 'rgba(240, 147, 251, 1)',
        danger: 'rgba(255, 154, 158, 1)'
    }
};

// 通知系统
class NotificationSystem {
    static show(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 12px;">
                <i class="fas fa-${this.getIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);

        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, duration);
    }

    static getIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// DOM 加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化仪表板...');

    try {
        initializeDashboard();
    } catch (error) {
        console.error('仪表板初始化失败:', error);
    }
});

/**
 * 初始化仪表板
 */
function initializeDashboard() {
    console.log('仪表板初始化开始...');

    // 基础功能初始化
    initializeTimeDisplay();
    initializeNumberAnimations();
    initializeStatCards();
    initializeHoverEffects();

    // 图表初始化
    setTimeout(() => {
        initializeChart();
        initializeChartTypeSelector();
    }, 500);

    // 按钮功能
    initializeRefreshButton();

    // 启动自动刷新
    startAutoRefresh();

    console.log('仪表板初始化完成');

    // 显示成功通知
    setTimeout(() => {
        NotificationSystem.show('仪表板已加载完成', 'success', 2000);
    }, 1000);
}

// 存储图表实例
let chartInstances = {};

/**
 * 初始化图表
 */
function initializeChart() {
    console.log('开始初始化图表...');
    console.log('当前URL:', window.location.href);
    console.log('DOM状态:', document.readyState);

    // 检查Chart.js是否可用
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js库未加载');
        return;
    }

    // 检查所有图表容器是否存在
    const containers = ['monthlyChart', 'categoryChart', 'supplierChart'];
    console.log('检查图表容器...');

    containers.forEach(id => {
        const element = document.getElementById(id);
        console.log(`容器 ${id}:`, element ? '存在' : '不存在');
        if (element) {
            console.log(`${id} 父元素:`, element.parentElement);
            console.log(`${id} 可见性:`, window.getComputedStyle(element).display);
        }
    });

    const missingContainers = containers.filter(id => !document.getElementById(id));

    if (missingContainers.length > 0) {
        console.warn('图表容器未找到:', missingContainers);
        console.log('页面HTML结构:', document.body.innerHTML.substring(0, 1000));
        return;
    }

    // 销毁已存在的图表
    destroyExistingCharts();

    // 初始化所有图表
    console.log('开始初始化各个图表...');
    initializeMonthlyChart();
    initializeCategoryChart();
    initializeSupplierChart();
    initializeInventoryChart();
    console.log('图表初始化完成');
}

/**
 * 销毁已存在的图表
 */
function destroyExistingCharts() {
    Object.keys(chartInstances).forEach(key => {
        if (chartInstances[key] && typeof chartInstances[key].destroy === 'function') {
            chartInstances[key].destroy();
        }
    });
    chartInstances = {};
}

/**
 * 初始化月度趋势图表
 */
function initializeMonthlyChart() {
    const ctx = document.getElementById('monthlyChart');
    if (!ctx) {
        console.warn('月度图表容器未找到');
        return;
    }

    const chartData = {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        data: [420000, 435000, 448000, 462000, 455000, 478000]
    };

    chartInstances.monthlyChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: '采购金额 (¥)',
                data: chartData.data,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#3b82f6',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#3b82f6',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return '采购金额: ¥' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#64748b',
                        font: {
                            size: 12
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(226, 232, 240, 0.5)',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#64748b',
                        font: {
                            size: 12
                        },
                        callback: function(value) {
                            return '¥' + (value / 1000) + 'K';
                        }
                    }
                }
            },
            animation: {
                duration: 1500,
                easing: 'easeOutQuart'
            }
        }
    });
}

/**
 * 初始化分类分布图表
 */
function initializeCategoryChart() {
    const ctx = document.getElementById('categoryChart');
    if (!ctx) {
        console.warn('分类图表容器未找到');
        return;
    }

    chartInstances.categoryChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['蔬菜类', '肉类', '水果类', '调料类', '粮食类', '其他'],
            datasets: [{
                data: [35, 25, 15, 10, 10, 5],
                backgroundColor: [
                    '#10b981',
                    '#3b82f6',
                    '#f59e0b',
                    '#ef4444',
                    '#8b5cf6',
                    '#6b7280'
                ],
                borderWidth: 0,
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.parsed + '%';
                        }
                    }
                }
            },
            animation: {
                duration: 1500,
                easing: 'easeOutQuart'
            }
        }
    });
}

/**
 * 初始化供应商排名图表
 */
function initializeSupplierChart() {
    const ctx = document.getElementById('supplierChart');
    if (!ctx) {
        console.warn('供应商图表容器未找到');
        return;
    }

    chartInstances.supplierChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['绿源农场', '新鲜食材', '优质供应', '天然农产', '健康食品'],
            datasets: [{
                label: '采购金额',
                data: [85000, 72000, 68000, 55000, 45000],
                backgroundColor: '#3b82f6',
                borderRadius: 4,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            return '采购金额: ¥' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#64748b',
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(226, 232, 240, 0.5)',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#64748b',
                        font: {
                            size: 12
                        },
                        callback: function(value) {
                            return '¥' + (value / 1000) + 'K';
                        }
                    }
                }
            },
            animation: {
                duration: 1500,
                easing: 'easeOutQuart'
            }
        }
    });
}

/**
 * 初始化库存状态显示
 */
function initializeInventoryChart() {
    // 库存状态已经在模板中以HTML形式存在，这里可以添加动画效果
    const progressBars = document.querySelectorAll('.progress-fill');

    if (progressBars.length === 0) {
        console.warn('库存进度条未找到');
        return;
    }

    // 添加动画效果
    progressBars.forEach((bar, index) => {
        const targetWidth = bar.style.width;
        bar.style.width = '0%';
        bar.style.transition = 'width 1.5s ease-out';

        setTimeout(() => {
            bar.style.width = targetWidth;
        }, 200 + index * 100); // 错开动画时间
    });

    console.log('库存状态显示初始化完成');
}

/**
 * 初始化图表类型选择器
 */
function initializeChartTypeSelector() {
    const selector = document.getElementById('chartTypeSelect');
    if (!selector) return;
    
    selector.addEventListener('change', function() {
        const chartType = this.value;
        loadChartData(chartType);
    });
}

/**
 * 初始化刷新按钮
 */
function initializeRefreshButton() {
    // 如果有刷新按钮，绑定点击事件
    const refreshBtn = document.querySelector('[onclick="refreshData()"]');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function(e) {
            e.preventDefault();
            refreshData();
        });
    }
}

/**
 * 初始化统计卡片
 */
function initializeStatCards() {
    const statCards = document.querySelectorAll('.stat-card');

    // 添加动画效果
    statCards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + 's';
        card.classList.add('slide-in-up');
    });

    // 添加点击效果
    statCards.forEach(card => {
        card.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

/**
 * 初始化时间显示
 */
function initializeTimeDisplay() {
    updateTime();
    timeUpdateTimer = setInterval(updateTime, 1000);
}

/**
 * 更新时间显示
 */
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

/**
 * 初始化悬停效果
 */
function initializeHoverEffects() {
    // 统计卡片悬停效果
    document.querySelectorAll('.stat-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.classList.add('card-glow');
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.classList.remove('card-glow');
        });
    });

    // 活动项悬停效果
    document.querySelectorAll('.activity-item, .alert-item, .order-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(4px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
}

/**
 * 初始化数字动画
 */
function initializeNumberAnimations() {
    console.log('初始化数字动画...');

    const elements = document.querySelectorAll('.stat-number[data-count]');
    console.log('找到', elements.length, '个需要动画的数字元素');

    elements.forEach((element, index) => {
        const targetValue = parseInt(element.getAttribute('data-count'));
        console.log(`元素 ${index}: 目标值 ${targetValue}`);
        animateNumber(element, 0, targetValue);
    });
}

/**
 * 数字动画函数
 */
function animateNumber(element, start, end, duration = DashboardConfig.animationDuration) {
    const startTime = performance.now();
    const isMoneyValue = element.textContent.includes('¥');

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = start + (end - start) * easeOutQuart;

        if (isMoneyValue) {
            element.textContent = '¥' + Math.floor(current).toLocaleString();
        } else {
            element.textContent = Math.floor(current).toLocaleString();
        }

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            if (isMoneyValue) {
                element.textContent = '¥' + end.toLocaleString();
            } else {
                element.textContent = end.toLocaleString();
            }

            // 添加成功动画
            element.closest('.stat-card').classList.add('success-pulse');
            setTimeout(() => {
                element.closest('.stat-card').classList.remove('success-pulse');
            }, 600);
        }
    }

    requestAnimationFrame(updateNumber);
}

/**
 * 启动自动刷新
 */
function startAutoRefresh() {
    refreshTimer = setInterval(() => {
        refreshData();
    }, DashboardConfig.refreshInterval);
}

/**
 * 停止自动刷新
 */
function stopAutoRefresh() {
    if (refreshTimer) {
        clearInterval(refreshTimer);
        refreshTimer = null;
    }
}









/**
 * 刷新数据
 */
function refreshData() {
    const refreshBtn = document.querySelector('[onclick="refreshData()"]');
    const icon = refreshBtn ? refreshBtn.querySelector('i') : null;

    // 添加加载状态
    if (refreshBtn) {
        refreshBtn.disabled = true;
        if (icon) {
            icon.style.animation = 'spin 1s linear infinite';
        }
    }

    // 模拟加载延迟
    setTimeout(() => {
        // 重新加载图表数据
        const chartTypeSelect = document.getElementById('chartTypeSelect');
        const currentType = chartTypeSelect ? chartTypeSelect.value : 'monthly_purchase';
        loadChartData(currentType);

        // 刷新统计数据
        refreshStatCards();

        // 恢复按钮状态
        if (refreshBtn) {
            refreshBtn.disabled = false;
            if (icon) {
                icon.style.animation = '';
            }
        }

        // 显示成功提示
        NotificationSystem.show('数据已刷新', 'success', 2000);
    }, 1000);
}

/**
 * 导出报告
 */
function exportReport() {
    const exportBtn = document.querySelector('[onclick="exportReport()"]');
    const icon = exportBtn ? exportBtn.querySelector('i') : null;

    // 添加加载状态
    if (exportBtn) {
        exportBtn.disabled = true;
        if (icon) {
            icon.style.animation = 'spin 1s linear infinite';
        }
    }

    // 模拟导出过程
    setTimeout(() => {
        // 恢复按钮状态
        if (exportBtn) {
            exportBtn.disabled = false;
            if (icon) {
                icon.style.animation = '';
            }
        }

        NotificationSystem.show('报告导出功能正在开发中...', 'info');
    }, 2000);
}

/**
 * 刷新统计卡片
 */
function refreshStatCards() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(element => {
        // 添加刷新动画
        element.style.transform = 'scale(1.1)';
        element.style.transition = 'transform 0.3s ease';
        
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 300);
    });
}

/**
 * 格式化数字显示
 */
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

/**
 * 初始化实时更新
 */
function initializeRealTimeUpdates() {
    // 模拟实时数据更新
    setInterval(() => {
        // 随机更新一些统计数据
        updateRandomStats();
    }, 30000); // 每30秒更新一次
}

/**
 * 随机更新统计数据（演示用）
 */
function updateRandomStats() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach((element, index) => {
        if (Math.random() > 0.7) { // 30%的概率更新
            const currentValue = parseInt(element.textContent.replace(/[^\d]/g, ''));
            const change = Math.floor(Math.random() * 10) - 5; // -5到+5的随机变化
            const newValue = Math.max(0, currentValue + change);
            
            // 动画更新数值
            animateNumberChange(element, currentValue, newValue);
        }
    });
}

/**
 * 数字变化动画
 */
function animateNumberChange(element, from, to) {
    const duration = 1000;
    const startTime = Date.now();
    const isDecimal = element.textContent.includes('¥');
    
    function update() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = from + (to - from) * progress;
        
        if (isDecimal) {
            element.textContent = '¥' + current.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        } else {
            element.textContent = Math.round(current).toLocaleString();
        }
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

// 页面卸载前的清理
window.addEventListener('beforeunload', function() {
    console.log('清理仪表板资源...');

    // 清理图表
    if (typeof destroyExistingCharts === 'function') {
        destroyExistingCharts();
    }

    // 清理定时器
    stopAutoRefresh();

    if (timeUpdateTimer) {
        clearInterval(timeUpdateTimer);
        timeUpdateTimer = null;
    }

    console.log('仪表板资源清理完成');
});

// 全局函数导出
window.refreshData = refreshData;
window.exportReport = exportReport;
