<?php
/**
 * 移动端 - 创建采购单页面
 */

require_once '../includes/Database.php';

// 检查用户是否登录
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../modules/users/index.php?action=login');
    exit;
}

$db = Database::getInstance();
$error = '';
$success = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $supplier_id = intval($_POST['supplier_id'] ?? 0);
        $notes = trim($_POST['notes'] ?? '');
        
        if (!$supplier_id) {
            throw new Exception('请选择供应商');
        }
        
        // 生成采购单号
        $order_number = 'PO' . date('YmdHis');
        
        // 插入采购单
        $db->query("
            INSERT INTO purchase_orders (order_number, supplier_id, status, notes, created_by, created_at)
            VALUES (?, ?, 1, ?, ?, NOW())
        ", [$order_number, $supplier_id, $notes, $_SESSION['user_id']]);
        
        $order_id = $db->lastInsertId();
        
        // 跳转到采购单详情页
        header('Location: purchase-detail.php?order_number=' . urlencode($order_number));
        exit;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 获取供应商列表
try {
    $suppliers = $db->query("SELECT * FROM suppliers ORDER BY name")->fetchAll();
} catch (Exception $e) {
    $suppliers = [];
    $error = '获取供应商列表失败：' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建采购单 - 学校食堂食材出入库管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <style>
        .create-form {
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .form-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-section h3 {
            margin: 0 0 16px 0;
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f9fafb;
            box-sizing: border-box;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-control select {
            cursor: pointer;
        }
        
        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }
        
        .btn-group {
            display: flex;
            gap: 12px;
            margin-top: 24px;
        }
        
        .btn {
            flex: 1;
            padding: 14px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-secondary {
            background: #f3f4f6;
            color: var(--text-primary);
            border: 2px solid #e5e7eb;
        }
        
        .btn-secondary:hover {
            background: #e5e7eb;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .alert-error {
            background: #fee2e2;
            color: #dc2626;
            border-left: 4px solid #dc2626;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border-left: 4px solid #10b981;
        }
        
        .page-header {
            background: white;
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .page-title {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .back-btn {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--primary-color);
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="page-header">
        <h1 class="page-title">
            <button class="back-btn" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            创建采购单
        </h1>
    </div>

    <!-- 主要内容 -->
    <main class="create-form">
        <?php if ($error): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error) ?>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?= htmlspecialchars($success) ?>
        </div>
        <?php endif; ?>

        <form method="POST">
            <!-- 基本信息 -->
            <div class="form-section">
                <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                
                <div class="form-group">
                    <label for="supplier_id" class="form-label">
                        <i class="fas fa-truck"></i>
                        选择供应商 *
                    </label>
                    <select id="supplier_id" name="supplier_id" class="form-control" required>
                        <option value="">请选择供应商</option>
                        <?php foreach ($suppliers as $supplier): ?>
                        <option value="<?= $supplier['id'] ?>" <?= (isset($_POST['supplier_id']) && $_POST['supplier_id'] == $supplier['id']) ? 'selected' : '' ?>>
                            <?= htmlspecialchars($supplier['name']) ?>
                            <?php if ($supplier['phone']): ?>
                            - <?= htmlspecialchars($supplier['phone']) ?>
                            <?php endif; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="notes" class="form-label">
                        <i class="fas fa-sticky-note"></i>
                        备注信息
                    </label>
                    <textarea id="notes" name="notes" class="form-control" 
                              placeholder="请输入采购单备注信息..."><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="btn-group">
                <a href="purchase.php" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    取消
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    创建采购单
                </button>
            </div>
        </form>
    </main>

    <!-- 底部间距 -->
    <div style="height: 40px;"></div>

    <script>
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const supplierSelect = document.getElementById('supplier_id');
            
            if (!supplierSelect.value) {
                e.preventDefault();
                alert('请选择供应商');
                supplierSelect.focus();
                return false;
            }
        });

        // 供应商选择变化时的处理
        document.getElementById('supplier_id').addEventListener('change', function() {
            if (this.value) {
                this.style.borderColor = 'var(--primary-color)';
            } else {
                this.style.borderColor = '#e5e7eb';
            }
        });
    </script>
</body>
</html>
