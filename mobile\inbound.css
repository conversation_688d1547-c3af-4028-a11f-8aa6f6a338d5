/* 入库页面专用样式 */

/* iOS样式重置和统一 */
input, select, textarea, button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* iOS输入框统一样式 */
input[type="text"],
input[type="number"],
input[type="date"],
select {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 16px;
    color: #333 !important;
    transition: all 0.3s ease;
    box-sizing: border-box;
    height: 48px;
    line-height: 1.2;
    vertical-align: top;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* iOS日期输入特殊处理 */
input[type="date"] {
    color: #333 !important;
    background: white !important;
    height: 48px !important;
    min-height: 48px;
    display: flex;
    align-items: center;
}

input[type="date"]::-webkit-datetime-edit {
    color: #333 !important;
    line-height: 1.2;
    padding: 0;
    margin: 0;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    color: #667eea;
    opacity: 1;
    height: 20px;
    width: 20px;
}

/* iOS选择框统一样式 */
select {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23667eea' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
    color: #333 !important;
    height: 48px !important;
    min-height: 48px;
    line-height: 1.2;
}

/* iOS按钮统一样式 */
button {
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    -webkit-tap-highlight-color: transparent;
}

/* iOS特定高度修复 */
@supports (-webkit-touch-callout: none) {
    /* 仅在iOS Safari中应用 */
    input[type="text"],
    input[type="number"],
    input[type="date"],
    select {
        height: 48px !important;
        min-height: 48px !important;
        max-height: 48px !important;
        line-height: 1.2 !important;
        padding: 12px 15px !important;
        box-sizing: border-box !important;
    }

    select {
        padding-right: 40px !important;
    }

    input[type="date"] {
        display: flex !important;
        align-items: center !important;
    }
}

/* 头部样式 */
.header-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
    font-weight: 700;
    color: #667eea;
}

.back-btn, .help-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: #667eea;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    text-decoration: none;
}

.back-btn:hover, .help-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.1);
    text-decoration: none;
    color: #667eea;
}

/* 进度指示器 */
.progress-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 100px 80px 10px 80px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    margin: 0px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.progress-step.active {
    opacity: 1;
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #e9ecef;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
}

.progress-step.active .step-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.step-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.progress-step.active .step-label {
    color: #667eea;
    font-weight: 600;
}

.progress-line {
    width: 40px;
    height: 2px;
    background: #e9ecef;
    margin: 0 10px;
}

/* 步骤内容 */
.step-section {
    display: none;
    padding: 0 5px;
    margin-bottom: 100px;
}

.step-section.active {
    display: block;
    animation: fadeInUp 0.5s ease-out;
}

.step-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 25px 5px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 20px;
}

.card-header {
    text-align: center;
    margin-bottom: 30px;
}

.card-header h2 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 8px;
}

.card-header p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* 添加食材表单 */
.add-item-form {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
}

/* 已添加食材 */
.added-items {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.items-header {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.items-header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.items-header h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.items-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.items-list {
    margin-bottom: 20px;
}

.item-card {
    background: white;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 12px;
    border: 1px solid #e9ecef;
    position: relative;
    transition: all 0.3s ease;
}

.item-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.item-card.from-order {
    border-left: 4px solid #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.order-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 8px;
    font-weight: 500;
    white-space: nowrap;
    flex-shrink: 0;
}

/* 商品表格样式 */
.items-table-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 8px;
    margin-bottom: 2px;
    border-radius: 8px 8px 0 0;
    display: grid;
    grid-template-columns: 1.5fr 1fr 1.8fr 1.2fr 0.6fr;
    gap: 8px;
    align-items: center;
    font-weight: 600;
    font-size: 12px;
    text-align: center;
}

.header-name {
    text-align: left;
    padding-left: 8px;
}

/* 商品表格行 */
.order-item-table-row {
    background: white;
    padding: 10px 8px;
    margin-bottom: 1px;
    border-left: 4px solid #667eea;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: grid;
    grid-template-columns: 1.5fr 1fr 1.8fr 1.2fr 0.6fr;
    gap: 8px;
    align-items: center;
    min-height: 45px;
}

.order-item-table-row:hover {
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.order-item-table-row:last-child {
    border-radius: 0 0 8px 8px;
    margin-bottom: 8px;
}

/* 表格单元格 */
.item-name-cell {
    font-weight: 600;
    color: #333;
    font-size: 13px;
    text-align: left;
    padding-left: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-quantity-cell {
    font-size: 12px;
    color: #666;
    text-align: center;
    white-space: nowrap;
}

.item-weight-cell {
    display: flex;
    justify-content: center;
    align-items: center;
}

.item-photo-cell {
    display: flex;
    justify-content: center;
    align-items: center;
}

.item-status-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
}

.item-status {
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
}

.text-warning { color: #f39c12; }
.text-info { color: #3498db; }
.text-success { color: #27ae60; }

/* 操作区域 */
.item-operations {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
}

/* 表格中的重量输入 */
.weight-input-compact {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    width: 90px;
    height: 32px;
}

.weight-input-small {
    width: 70px;
    padding: 4px 8px;
    border: none;
    font-size: 12px;
    text-align: center;
    background: transparent;
    color: #333;
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield;
}

.weight-input-small::-webkit-outer-spin-button,
.weight-input-small::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.weight-input-small:focus {
    outline: none;
    background: white;
}

.weight-unit-small {
    padding: 4px 6px;
    background: #e9ecef;
    color: #666;
    font-size: 10px;
    font-weight: 500;
    border-left: 1px solid #dee2e6;
    white-space: nowrap;
    line-height: 1;
}

/* 表格中的照片区域 */
.photo-section-compact {
    display: flex;
    align-items: center;
    justify-content: center;
}

.photo-actions-compact {
    display: flex;
    gap: 2px;
}

.photo-btn-small, .view-photo-btn-small {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.photo-btn-small {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.photo-btn-small:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: scale(1.05);
}

.photo-btn-small.has-photo {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.photo-btn-small.has-photo:hover {
    background: linear-gradient(135deg, #229954 0%, #28b463 100%);
}

.view-photo-btn-small {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.view-photo-btn-small:hover {
    background: linear-gradient(135deg, #3d8bfe 0%, #0dcaf0 100%);
    transform: scale(1.05);
}

/* 紧凑状态指示 */
.item-status-compact {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}



/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-state i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 16px;
}

.empty-state p {
    margin: 0;
    font-size: 16px;
    color: #999;
}

/* 商品统计样式 */
.items-stats {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: nowrap;
    margin-top: 10px;
    overflow-x: auto;
    padding-bottom: 2px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 12px;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    flex-shrink: 0;
    min-width: 0;
}

.stat-label {
    font-weight: 500;
    opacity: 0.95;
    color: rgba(255, 255, 255, 0.9);
    font-size: 10px;
}

.stat-value {
    font-weight: 700;
    font-size: 12px;
    color: white;
    padding: 3px 8px;
    border-radius: 10px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    min-width: 20px;
    text-align: center;
    display: inline-block;
}

.stat-value.total {
    background: #3498db;
}

.stat-value.accepted {
    background: #27ae60;
}

.stat-value.pending {
    background: #e67e22;
}

/* 称重输入区域 */
.weight-input-group {
    margin-bottom: 15px;
}

.weight-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
    font-size: 13px;
}

.weight-input-wrapper {
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: border-color 0.3s ease;
}

.weight-input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.weight-input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    background: transparent;
}

.weight-input:focus {
    outline: none;
}

.weight-unit {
    padding: 12px 15px;
    background: #f8f9fa;
    color: #666;
    font-weight: 600;
    font-size: 14px;
    border-left: 1px solid #e9ecef;
}

.photo-section .photo-btn {
    padding: 8px 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.photo-section .photo-btn:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.photo-section .photo-btn.has-photo {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.photo-section .photo-btn.has-photo:hover {
    background: linear-gradient(135deg, #229954 0%, #28b463 100%);
}

/* 照片操作按钮组 */
.photo-actions {
    display: flex;
    gap: 6px;
}

.view-photo-btn {
    padding: 8px 12px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
}

.view-photo-btn:hover {
    background: linear-gradient(135deg, #3d8bfe 0%, #0dcaf0 100%);
    transform: translateY(-1px);
}

.view-photo-btn:active {
    transform: translateY(0);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.item-name {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

.item-price {
    color: #667eea;
    font-weight: 600;
    font-size: 14px;
}

.item-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #666;
}

.item-quantity {
    display: flex;
    align-items: center;
    gap: 10px;
}

.item-quantity label {
    font-weight: 500;
    color: #333;
    min-width: 80px;
    font-size: 14px;
}

.item-quantity input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    text-align: center;
}

.item-photo {
    margin-top: 10px;
}

.photo-btn {
    width: 100%;
    padding: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.photo-btn:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.photo-btn.has-photo {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.remove-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ff4757;
    color: white;
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #ff3742;
    transform: scale(1.1);
}

.items-summary {
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.total-amount {
    color: #667eea;
    font-size: 18px;
}

/* 拍照区域 */
.photo-section {
    margin-bottom: 25px;
}

.photo-upload {
    text-align: center;
}

.photo-area {
    border: 3px dashed #667eea;
    border-radius: 16px;
    padding: 40px 20px;
    background: rgba(102, 126, 234, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.photo-area:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: #5a6fd8;
}

.photo-area.has-photo {
    border-color: #56ab2f;
    background: rgba(86, 171, 47, 0.05);
}

.photo-placeholder {
    color: #667eea;
    text-align: center;
}

.photo-placeholder i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.photo-placeholder h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.photo-placeholder p {
    font-size: 14px;
    opacity: 0.8;
    margin: 0;
}

.photo-preview {
    max-width: 100%;
    max-height: 200px;
    border-radius: 12px;
    margin-top: 15px;
}

/* 送货单照片操作按钮 */
.delivery-photo-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    justify-content: center;
}

.delivery-photo-actions .btn {
    flex: 1;
    max-width: 150px;
    padding: 10px 15px;
    font-size: 14px;
}

/* 步骤操作按钮 */
.step-actions {
    display: flex;
    gap: 15px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.step-actions .btn {
    flex: 1;
}

/* 空状态 */
.empty-items {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.empty-items i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-items p {
    font-size: 14px;
    margin: 0;
}

/* 采购单信息卡片 */
.order-info-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.order-header h4 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 700;
}

.order-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 14px;
    opacity: 0.9;
}

.order-details span {
    display: flex;
    align-items: center;
    gap: 8px;
}

.order-details span:before {
    content: "•";
    color: rgba(255, 255, 255, 0.7);
}

/* 采购单选择样式 */
#purchase_order_select option[value="create_new"] {
    background-color: #e6f3ff !important;
    font-weight: bold;
    color: #007cba;
}

/* 手动输入区域 */
#manual_input_area {
    transition: all 0.3s ease;
}

#manual_input_area.hidden {
    opacity: 0.5;
    pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 375px) {
    .progress-indicator {
        margin: 15px;
        padding: 15px;
    }

    .step-section {
        padding: 0 15px;
    }

    .step-card {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .step-actions {
        flex-direction: column;
        gap: 10px;
    }

    .progress-line {
        width: 30px;
        margin: 0 5px;
    }

    .order-info-card {
        padding: 15px;
        margin: 15px 0;
    }

    .order-header h4 {
        font-size: 16px;
    }

    .order-details {
        font-size: 13px;
    }

    /* 表格布局响应式 */
    .items-table-header {
        padding: 10px 6px;
        font-size: 11px;
        grid-template-columns: 2fr 0.8fr 1.4fr 0.8fr 0.5fr;
        gap: 6px;
    }

    .order-item-table-row {
        padding: 8px 6px;
        grid-template-columns: 2fr 0.8fr 1.4fr 0.8fr 0.5fr;
        gap: 6px;
        min-height: 40px;
    }

    .item-name-cell {
        font-size: 12px;
        padding-left: 6px;
    }

    .item-quantity-cell {
        font-size: 11px;
    }

    .weight-input-compact {
        width: 80px;
        height: 28px;
    }

    .weight-input-small {
        width: 60px;
        padding: 3px 6px;
        font-size: 11px;
        -webkit-appearance: textfield;
        -moz-appearance: textfield;
        appearance: textfield;
    }

    .weight-input-small::-webkit-outer-spin-button,
    .weight-input-small::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .weight-unit-small {
        padding: 3px 4px;
        font-size: 9px;
    }

    .photo-btn-small, .view-photo-btn-small {
        width: 24px;
        height: 24px;
        font-size: 10px;
    }

    .item-status-cell {
        font-size: 12px;
    }



    /* 统计项响应式优化 */
    .items-stats {
        gap: 6px;
        margin-top: 8px;
    }

    .stat-item {
        padding: 3px 6px;
        font-size: 10px;
        gap: 3px;
        border-radius: 10px;
    }

    .stat-label {
        font-size: 9px;
    }

    .stat-value {
        font-size: 11px;
        padding: 2px 6px;
        min-width: 18px;
    }


}
