<?php
/**
 * 入库单管理控制器
 */

require_once '../../includes/Database.php';
require_once '../../includes/BaseController.php';

class InboundOrderController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->setTemplateData(['page_title' => '入库单管理']);
    }

    /**
     * 处理请求
     */
    public function handleRequest()
    {
        $action = $this->request['get']['action'] ?? 'index';

        switch ($action) {
            case 'index':
                $this->index();
                break;
            case 'create':
                $this->create();
                break;
            case 'view':
                $this->view();
                break;
            case 'print':
                $this->printOrder();
                break;
            case 'delete':
                $this->delete();
                break;
            default:
                $this->index();
        }
    }

    /**
     * 入库单列表
     */
    private function index()
    {
        try {
            // 获取搜索参数
            $search = trim($this->request['get']['search'] ?? '');
            $supplier = intval($this->request['get']['supplier'] ?? 0);
            $status = $this->request['get']['status'] ?? '';
            $date_from = $this->request['get']['date_from'] ?? '';
            $date_to = $this->request['get']['date_to'] ?? '';

            // 构建查询条件
            $where = ['1=1'];
            $params = [];

            if ($search) {
                $where[] = 'io.order_number LIKE ?';
                $params[] = "%$search%";
            }

            if ($supplier > 0) {
                $where[] = 'io.supplier_id = ?';
                $params[] = $supplier;
            }

            if ($status) {
                $where[] = 'io.status = ?';
                $params[] = $status;
            }

            if ($date_from) {
                $where[] = 'io.inbound_date >= ?';
                $params[] = $date_from;
            }

            if ($date_to) {
                $where[] = 'io.inbound_date <= ?';
                $params[] = $date_to;
            }

            $whereClause = 'WHERE ' . implode(' AND ', $where);

            // 获取入库单列表
            $orders = $this->db->fetchAll("
                SELECT io.*, s.name as supplier_name, '系统' as operator_name,
                       COUNT(ioi.id) as item_count
                FROM inbound_orders io
                LEFT JOIN suppliers s ON io.supplier_id = s.id
                LEFT JOIN inbound_order_items ioi ON io.id = ioi.order_id
                $whereClause
                GROUP BY io.id
                ORDER BY io.created_at DESC
                LIMIT 50
            ", $params);

            // 获取供应商列表
            $suppliers = $this->db->fetchAll("
                SELECT id, name FROM suppliers 
                WHERE status = 1 
                ORDER BY name ASC
            ");

            $this->setTemplateData([
                'orders' => $orders,
                'suppliers' => $suppliers,
                'search' => $search,
                'selected_supplier' => $supplier,
                'selected_status' => $status,
                'date_from' => $date_from,
                'date_to' => $date_to
            ]);

        } catch (Exception $e) {
            $this->setTemplateData([
                'orders' => [],
                'suppliers' => [],
                'error_message' => $e->getMessage()
            ]);
        }

        $this->render('order-list-template.php');
    }

    /**
     * 创建入库单
     */
    private function create()
    {
        if ($this->request['method'] === 'POST') {
            try {
                $this->db->beginTransaction();

                // 获取表单数据
                $supplier_id = intval($this->request['post']['supplier_id'] ?? 0);
                $operator_id = intval($this->request['post']['operator_id'] ?? 1); // TODO: 从session获取
                $inbound_date = $this->request['post']['inbound_date'] ?? date('Y-m-d');
                $notes = trim($this->request['post']['notes'] ?? '');
                $items = $this->request['post']['items'] ?? [];

                if (!$supplier_id) {
                    throw new Exception('请选择供应商');
                }

                if (empty($items)) {
                    throw new Exception('请至少添加一个入库项目');
                }

                // 生成入库单号
                $order_number = $this->generateOrderNumber('inbound_order');

                // 计算总金额和总项目数
                $total_amount = 0;
                $total_items = count($items);

                foreach ($items as $item) {
                    $total_amount += floatval($item['total_price'] ?? 0);
                }

                // 插入入库单
                $order_data = [
                    'order_number' => $order_number,
                    'supplier_id' => $supplier_id,
                    'operator_id' => $operator_id,
                    'total_amount' => $total_amount,
                    'total_items' => $total_items,
                    'status' => 'pending',
                    'notes' => $notes,
                    'inbound_date' => $inbound_date,
                    'created_by' => $operator_id,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $order_id = $this->db->insert('inbound_orders', $order_data);

                // 插入入库单明细
                foreach ($items as $item) {
                    $ingredient_id = intval($item['ingredient_id'] ?? 0);
                    $planned_quantity = floatval($item['planned_quantity'] ?? 0);
                    $actual_quantity = floatval($item['actual_quantity'] ?? $planned_quantity);
                    $unit_price = floatval($item['unit_price'] ?? 0);
                    $total_price = $actual_quantity * $unit_price;

                    // 生成批次号
                    $batch_number = $this->generateBatchNumber();

                    // 计算过期日期
                    $production_date = $item['production_date'] ?? null;
                    $expired_at = $this->calculateExpiredDate($ingredient_id, $production_date);

                    $item_data = [
                        'order_id' => $order_id,
                        'ingredient_id' => $ingredient_id,
                        'batch_number' => $batch_number,
                        'planned_quantity' => $planned_quantity,
                        'actual_quantity' => $actual_quantity,
                        'unit_price' => $unit_price,
                        'total_price' => $total_price,
                        'production_date' => $production_date,
                        'expired_at' => $expired_at,
                        'acceptance_status' => 'accepted',
                        'quality_check' => 1,
                        'notes' => $item['notes'] ?? ''
                    ];

                    $item_id = $this->db->insert('inbound_order_items', $item_data);

                    // 同时插入入库记录（保持兼容性）
                    $record_data = [
                        'inbound_order_id' => $order_id,
                        'ingredient_id' => $ingredient_id,
                        'supplier_id' => $supplier_id,
                        'batch_number' => $batch_number,
                        'quantity' => $actual_quantity,
                        'unit_price' => $unit_price,
                        'production_date' => $production_date,
                        'expired_at' => $expired_at,
                        'purchase_invoice' => '',
                        'quality_check' => 1,
                        'notes' => $item['notes'] ?? '',
                        'status' => 1,
                        'created_by' => $operator_id
                    ];

                    $this->db->insert('inbound_records', $record_data);

                    // 更新库存
                    $this->updateIngredientStock($ingredient_id, $actual_quantity, 'add');
                }

                // 更新入库单状态为已完成
                $this->db->update('inbound_orders', [
                    'status' => 'completed',
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$order_id]);

                $this->db->commit();

                // 检查是否是AJAX请求
                if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                    strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'message' => '入库单创建成功',
                        'data' => [
                            'order_id' => $order_id,
                            'order_number' => $order_number,
                            'total_amount' => $total_amount
                        ]
                    ]);
                    exit;
                }

                $this->redirect('index.php?action=view&id=' . $order_id, '入库单创建成功', 'success');

            } catch (Exception $e) {
                $this->db->rollback();
                
                if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                    strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => false,
                        'message' => $e->getMessage()
                    ]);
                    exit;
                }
                
                $this->setTemplateData(['error_message' => $e->getMessage()]);
            }
        }

        $this->loadCreateFormData();
        $this->render('order-create-template.php');
    }

    /**
     * 查看入库单详情
     */
    private function view()
    {
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            // 获取入库单信息
            $order = $this->db->fetchOne("
                SELECT io.*, s.name as supplier_name, s.contact_person, s.phone,
                       '系统' as operator_name
                FROM inbound_orders io
                LEFT JOIN suppliers s ON io.supplier_id = s.id
                WHERE io.id = ?
            ", [$id]);

            if (!$order) {
                $this->redirect('index.php', '入库单不存在', 'error');
                return;
            }

            // 获取入库单明细
            $items = $this->db->fetchAll("
                SELECT ioi.*, i.name as ingredient_name, i.unit,
                       ic.name as category_name
                FROM inbound_order_items ioi
                LEFT JOIN ingredients i ON ioi.ingredient_id = i.id
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                WHERE ioi.order_id = ?
                ORDER BY ioi.id ASC
            ", [$id]);

            $this->setTemplateData([
                'order' => $order,
                'items' => $items
            ]);

        } catch (Exception $e) {
            $this->redirect('index.php', '获取入库单信息失败：' . $e->getMessage(), 'error');
            return;
        }

        $this->render('order-view-template.php');
    }

    /**
     * 打印入库单
     */
    private function printOrder()
    {
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            echo '<script>alert("参数错误"); window.close();</script>';
            return;
        }

        try {
            // 获取入库单信息
            $order = $this->db->fetchOne("
                SELECT io.*, s.name as supplier_name, s.contact_person, s.phone, s.address,
                       '系统' as operator_name
                FROM inbound_orders io
                LEFT JOIN suppliers s ON io.supplier_id = s.id
                WHERE io.id = ?
            ", [$id]);

            if (!$order) {
                echo '<script>alert("入库单不存在"); window.close();</script>';
                return;
            }

            // 获取入库单明细
            $items = $this->db->fetchAll("
                SELECT ioi.*, i.name as ingredient_name, i.unit,
                       ic.name as category_name
                FROM inbound_order_items ioi
                LEFT JOIN ingredients i ON ioi.ingredient_id = i.id
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                WHERE ioi.order_id = ?
                ORDER BY ioi.id ASC
            ", [$id]);

            $this->setTemplateData([
                'order' => $order,
                'items' => $items,
                'print_mode' => true
            ]);

            $this->render('order-print-template.php');

        } catch (Exception $e) {
            echo '<script>alert("获取入库单信息失败：' . htmlspecialchars($e->getMessage()) . '"); window.close();</script>';
        }
    }

    /**
     * 删除入库单
     */
    private function delete()
    {
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            $this->db->beginTransaction();

            // 获取入库单信息
            $order = $this->db->fetchOne("SELECT * FROM inbound_orders WHERE id = ?", [$id]);
            if (!$order) {
                throw new Exception('入库单不存在');
            }

            // 获取入库单明细
            $items = $this->db->fetchAll("SELECT * FROM inbound_order_items WHERE order_id = ?", [$id]);

            // 回滚库存
            foreach ($items as $item) {
                $this->updateIngredientStock($item['ingredient_id'], $item['actual_quantity'], 'subtract');
            }

            // 删除相关的入库记录
            $this->db->delete('inbound_records', 'inbound_order_id = ?', [$id]);

            // 删除入库单明细
            $this->db->delete('inbound_order_items', 'order_id = ?', [$id]);

            // 删除入库单
            $this->db->delete('inbound_orders', 'id = ?', [$id]);

            $this->db->commit();
            $this->redirect('index.php', '入库单删除成功', 'success');

        } catch (Exception $e) {
            $this->db->rollback();
            $this->redirect('index.php', '删除失败：' . $e->getMessage(), 'error');
        }
    }

    /**
     * 加载创建表单数据
     */
    private function loadCreateFormData()
    {
        try {
            // 获取供应商列表
            $suppliers = $this->db->fetchAll("
                SELECT id, name, contact_person, phone
                FROM suppliers
                WHERE status = 1
                ORDER BY name ASC
            ");

            // 获取食材列表
            $ingredients = $this->db->fetchAll("
                SELECT i.id, i.name, i.unit, i.shelf_life_days,
                       ic.name as category_name
                FROM ingredients i
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                WHERE i.status = 1
                ORDER BY ic.name ASC, i.name ASC
            ");

            $this->setTemplateData([
                'suppliers' => $suppliers,
                'ingredients' => $ingredients
            ]);

        } catch (Exception $e) {
            $this->setTemplateData([
                'suppliers' => [],
                'ingredients' => [],
                'error_message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 生成单据编号
     */
    private function generateOrderNumber($doc_type)
    {
        try {
            // 获取序列信息
            $sequence = $this->db->fetchOne("SELECT * FROM document_sequences WHERE doc_type = ?", [$doc_type]);

            if (!$sequence) {
                throw new Exception('单据类型配置不存在');
            }

            $prefix = $sequence['prefix'];
            $current_number = $sequence['current_number'];
            $date_format = $sequence['date_format'];

            // 生成编号
            $date_str = date($date_format);
            $number_str = str_pad($current_number, 4, '0', STR_PAD_LEFT);
            $order_number = $prefix . $date_str . $number_str;

            // 更新序列
            $this->db->update('document_sequences', [
                'current_number' => $current_number + 1,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'doc_type = ?', [$doc_type]);

            return $order_number;

        } catch (Exception $e) {
            // 如果序列表有问题，使用时间戳生成
            return strtoupper($doc_type) . date('YmdHis');
        }
    }

    /**
     * 生成批次号
     */
    private function generateBatchNumber()
    {
        return 'BATCH' . date('YmdHis') . rand(100, 999);
    }

    /**
     * 计算过期日期
     */
    private function calculateExpiredDate($ingredient_id, $production_date = null)
    {
        try {
            $ingredient = $this->db->fetchOne("SELECT shelf_life_days FROM ingredients WHERE id = ?", [$ingredient_id]);
            $shelf_life_days = $ingredient['shelf_life_days'] ?? 7;

            $base_date = $production_date ? $production_date : date('Y-m-d');
            return date('Y-m-d', strtotime($base_date . ' + ' . $shelf_life_days . ' days'));

        } catch (Exception $e) {
            return date('Y-m-d', strtotime('+7 days'));
        }
    }

    /**
     * 更新食材库存
     */
    private function updateIngredientStock($ingredient_id, $quantity, $operation = 'add')
    {
        try {
            // 获取当前库存
            $inventory = $this->db->fetchOne("SELECT * FROM inventory WHERE ingredient_id = ?", [$ingredient_id]);

            if ($inventory) {
                // 更新现有库存
                $new_quantity = $operation === 'add'
                    ? $inventory['current_quantity'] + $quantity
                    : $inventory['current_quantity'] - $quantity;

                $this->db->update('inventory', [
                    'current_quantity' => max(0, $new_quantity),
                    'last_inbound_at' => $operation === 'add' ? date('Y-m-d H:i:s') : $inventory['last_inbound_at'],
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'ingredient_id = ?', [$ingredient_id]);
            } else {
                // 创建新库存记录
                if ($operation === 'add') {
                    $this->db->insert('inventory', [
                        'ingredient_id' => $ingredient_id,
                        'current_quantity' => $quantity,
                        'last_inbound_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
            }

        } catch (Exception $e) {
            error_log("更新库存失败: " . $e->getMessage());
        }
    }
}
