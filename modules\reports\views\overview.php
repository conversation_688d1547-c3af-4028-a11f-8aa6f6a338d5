<!-- 概览报表 -->

<!-- 关键指标统计 -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-card-header">
            <div class="stat-card-title">
                <i class="fas fa-shopping-cart"></i>
                总采购金额
            </div>
        </div>
        <div class="stat-card-value">¥<?= number_format($data['summary']['total_purchase'], 2) ?></div>
        <div class="stat-card-change positive">
            <i class="fas fa-arrow-up"></i>
            +<?= $data['summary']['monthly_growth'] ?>%
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-card-header">
            <div class="stat-card-title">
                <i class="fas fa-file-invoice"></i>
                订单总数
            </div>
        </div>
        <div class="stat-card-value"><?= number_format($data['summary']['total_orders']) ?></div>
        <div class="stat-card-change positive">
            <i class="fas fa-arrow-up"></i>
            +8.2%
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-card-header">
            <div class="stat-card-title">
                <i class="fas fa-truck"></i>
                合作供应商
            </div>
        </div>
        <div class="stat-card-value"><?= $data['summary']['total_suppliers'] ?></div>
        <div class="stat-card-change positive">
            <i class="fas fa-arrow-up"></i>
            +2 家
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-card-header">
            <div class="stat-card-title">
                <i class="fas fa-calculator"></i>
                平均订单价值
            </div>
        </div>
        <div class="stat-card-value">¥<?= number_format($data['summary']['avg_order_value'], 2) ?></div>
        <div class="stat-card-change positive">
            <i class="fas fa-arrow-up"></i>
            +5.8%
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-card-header">
            <div class="stat-card-title">
                <i class="fas fa-warehouse"></i>
                库存总值
            </div>
        </div>
        <div class="stat-card-value">¥<?= number_format($data['summary']['inventory_value'], 2) ?></div>
        <div class="stat-card-change negative">
            <i class="fas fa-arrow-down"></i>
            -2.1%
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-card-header">
            <div class="stat-card-title">
                <i class="fas fa-piggy-bank"></i>
                成本节约
            </div>
        </div>
        <div class="stat-card-value">¥<?= number_format($data['summary']['cost_savings'], 2) ?></div>
        <div class="stat-card-change positive">
            <i class="fas fa-arrow-up"></i>
            +15.3%
        </div>
    </div>
</div>

<!-- 图表展示 -->
<div class="chart-grid">
    <!-- 月度采购趋势 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-chart-line"></i>
                月度采购趋势
            </div>
            <select class="form-control form-control-sm" style="width: auto;" onchange="updateChart('monthlyChart', this.value)">
                <option value="amount">采购金额</option>
                <option value="orders">订单数量</option>
            </select>
        </div>
        <div class="chart-container">
            <canvas id="monthlyChart"></canvas>
        </div>
    </div>

    <!-- 分类分布 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-chart-pie"></i>
                采购分类分布
            </div>
        </div>
        <div class="chart-container">
            <canvas id="categoryChart"></canvas>
        </div>
    </div>

    <!-- 供应商排名 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-chart-bar"></i>
                供应商采购排名
            </div>
        </div>
        <div class="chart-container">
            <canvas id="supplierChart"></canvas>
        </div>
    </div>

    <!-- 库存预警 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-exclamation-triangle"></i>
                库存状态概览
            </div>
        </div>
        <div style="padding: 20px;">
            <div style="margin-bottom: 15px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                    <span>正常库存</span>
                    <span>85%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 85%; background: #10b981;"></div>
                </div>
            </div>
            <div style="margin-bottom: 15px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                    <span>低库存预警</span>
                    <span>12%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 12%; background: #f59e0b;"></div>
                </div>
            </div>
            <div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                    <span>缺货</span>
                    <span>3%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 3%; background: #ef4444;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速洞察 -->
<div class="data-table">
    <div class="table-header">
        <h3 class="table-title">
            <i class="fas fa-lightbulb"></i>
            数据洞察
        </h3>
    </div>
    <div style="padding: 25px;">
        <div class="row">
            <div class="col-md-6">
                <h5 style="color: #374151; margin-bottom: 15px;">📈 趋势分析</h5>
                <ul style="list-style: none; padding: 0; margin: 0;">
                    <li style="margin-bottom: 10px; color: #6b7280;">
                        <i class="fas fa-check-circle" style="color: #10b981; margin-right: 8px;"></i>
                        本月采购金额较上月增长 <?= $data['summary']['monthly_growth'] ?>%
                    </li>
                    <li style="margin-bottom: 10px; color: #6b7280;">
                        <i class="fas fa-check-circle" style="color: #10b981; margin-right: 8px;"></i>
                        蔬菜类采购占比最高，达到 35.8%
                    </li>
                    <li style="margin-bottom: 10px; color: #6b7280;">
                        <i class="fas fa-exclamation-triangle" style="color: #f59e0b; margin-right: 8px;"></i>
                        <?= $data['summary']['low_stock_items'] ?> 种食材库存偏低，需要补货
                    </li>
                </ul>
            </div>
            <div class="col-md-6">
                <h5 style="color: #374151; margin-bottom: 15px;">💡 优化建议</h5>
                <ul style="list-style: none; padding: 0; margin: 0;">
                    <li style="margin-bottom: 10px; color: #6b7280;">
                        <i class="fas fa-lightbulb" style="color: #3b82f6; margin-right: 8px;"></i>
                        建议与绿色蔬菜供应商协商批量采购优惠
                    </li>
                    <li style="margin-bottom: 10px; color: #6b7280;">
                        <i class="fas fa-lightbulb" style="color: #3b82f6; margin-right: 8px;"></i>
                        可考虑增加水产类供应商以降低采购成本
                    </li>
                    <li style="margin-bottom: 10px; color: #6b7280;">
                        <i class="fas fa-lightbulb" style="color: #3b82f6; margin-right: 8px;"></i>
                        建议设置自动补货提醒，避免缺货情况
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// 图表数据
const chartData = <?= json_encode($data['charts']) ?>;

// 初始化图表
document.addEventListener('DOMContentLoaded', function() {
    initOverviewCharts();
});

function initOverviewCharts() {
    // 月度采购趋势图
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: chartData.monthly_purchase.labels,
            datasets: [{
                label: '采购金额 (¥)',
                data: chartData.monthly_purchase.data,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // 分类分布饼图
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: chartData.category_distribution.labels,
            datasets: [{
                data: chartData.category_distribution.data,
                backgroundColor: [
                    '#10b981', '#3b82f6', '#f59e0b', 
                    '#ef4444', '#8b5cf6', '#6b7280'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // 供应商排名柱状图
    const supplierCtx = document.getElementById('supplierChart').getContext('2d');
    new Chart(supplierCtx, {
        type: 'bar',
        data: {
            labels: chartData.supplier_ranking.labels,
            datasets: [{
                label: '采购金额 (¥)',
                data: chartData.supplier_ranking.data,
                backgroundColor: '#3b82f6',
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}
</script>
