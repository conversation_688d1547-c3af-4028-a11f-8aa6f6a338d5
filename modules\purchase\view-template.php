<?php
$order = $this->getTemplateData('order');
$items = $this->getTemplateData('items');
$page_title = '采购单详情 - ' . htmlspecialchars($order['order_number']);
$current_module = 'purchase';

// 状态配置
$statusConfig = [
    1 => ['text' => '待确认', 'class' => 'warning', 'icon' => 'clock'],
    2 => ['text' => '已确认', 'class' => 'info', 'icon' => 'check'],
    3 => ['text' => '已发货', 'class' => 'primary', 'icon' => 'truck'],
    4 => ['text' => '已完成', 'class' => 'success', 'icon' => 'check-double'],
    5 => ['text' => '已取消', 'class' => 'danger', 'icon' => 'times']
];

$status = $statusConfig[$order['status']] ?? $statusConfig[1];
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../includes/styles.css?v=<?= time() ?>" rel="stylesheet">
    <link href="../../assets/css/common.css?v=<?= time() ?>" rel="stylesheet">
    <link href="style.css?v=<?= time() ?>" rel="stylesheet">
    <style>
        /* 详情页面特定样式 */
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #64748b;
            font-weight: 500;
            min-width: 100px;
        }

        .info-value {
            color: #1e293b;
            font-weight: 600;
            text-align: right;
        }

        .badge {
            padding: 6px 12px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 12px;
        }

        .badge-warning { background: #fed7aa; color: #9a3412; }
        .badge-info { background: #bfdbfe; color: #1e40af; }
        .badge-primary { background: #ddd6fe; color: #5b21b6; }
        .badge-success { background: #bbf7d0; color: #166534; }
        .badge-danger { background: #fecaca; color: #dc2626; }

        .card-body {
            padding: 24px;
        }

        .table-responsive {
            overflow-x: auto;
            overflow-y: visible;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            padding: 30px;
        }
        
        .info-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e2e8f0;
        }
        
        .info-card h5 {
            color: #2d3748;
            margin-bottom: 15px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            color: #718096;
            font-weight: 500;
        }
        
        .info-value {
            color: #2d3748;
            font-weight: 600;
        }
        
        .items-table {
            margin: 0 30px 30px 30px;
        }
        
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }
        
        .table-header {
            background: #f8fafc;
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .table-header h5 {
            margin: 0;
            color: #2d3748;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .table {
            margin: 0;
        }

        /* 移动端适配样式 */
        @media (max-width: 768px) {
            /* 隐藏桌面端侧边栏 */
            .sidebar {
                display: none !important;
            }

            /* 主内容区占满全屏 */
            .main-content {
                margin-left: 0 !important;
                width: 100% !important;
                padding: 0 !important;
            }

            /* 顶部栏移动端样式 */
            .topbar {
                background: white;
                padding: 16px;
                border-bottom: 1px solid #e5e7eb;
                position: sticky;
                top: 0;
                z-index: 10;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .topbar-left h1 {
                font-size: 20px;
                margin: 0 0 4px 0;
                color: #1a202c;
            }

            .topbar-left p {
                font-size: 14px;
                color: #64748b;
                margin: 0;
            }

            .topbar-right {
                margin-top: 12px;
            }

            .btn-group {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
            }

            .btn {
                padding: 8px 16px;
                font-size: 14px;
                border-radius: 12px;
            }

            .container {
                padding: 0 !important;
                margin: 0 !important;
                max-width: 100% !important;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 16px;
                padding: 16px;
            }

            .info-card {
                border-radius: 16px;
                padding: 20px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.08);
                border: 1px solid #f1f5f9;
            }

            .info-card h5 {
                font-size: 18px;
                margin-bottom: 16px;
                color: #1a202c;
            }

            .info-item {
                padding: 12px 0;
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .info-label {
                font-size: 14px;
                color: #64748b;
                font-weight: 500;
            }

            .info-value {
                font-size: 16px;
                color: #1e293b;
                font-weight: 600;
            }

            .items-table {
                margin: 0 16px 20px 16px;
            }

            .table-container {
                border-radius: 16px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            }

            .table-header {
                padding: 16px 20px;
            }

            .table-header h5 {
                font-size: 18px;
            }

            /* 表格移动端优化 */
            .table-responsive {
                border-radius: 0 0 16px 16px;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .table {
                min-width: 600px; /* 确保表格有最小宽度，可以横向滚动 */
            }

            .table th,
            .table td {
                padding: 12px 8px;
                font-size: 14px;
                white-space: nowrap;
            }

            .table th {
                background: #f8fafc;
                color: #374151;
                font-weight: 600;
                border-bottom: 2px solid #e5e7eb;
                position: sticky;
                top: 0;
                z-index: 1;
            }

            .table td {
                border-bottom: 1px solid #f3f4f6;
            }

            /* 表格滚动提示 */
            .table-container::after {
                content: '← 左右滑动查看更多 →';
                display: block;
                text-align: center;
                padding: 8px;
                background: #f8fafc;
                color: #64748b;
                font-size: 12px;
                border-top: 1px solid #e5e7eb;
            }

            /* 状态徽章移动端优化 */
            .badge {
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 600;
            }

            /* 页面标题区域 */
            .page-header {
                background: white;
                padding: 16px;
                border-bottom: 1px solid #e5e7eb;
                position: sticky;
                top: 0;
                z-index: 10;
            }

            .page-title {
                font-size: 20px;
                font-weight: 700;
                color: #1a202c;
                margin: 0;
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .back-btn {
                color: #4299e1;
                text-decoration: none;
                font-size: 16px;
                display: flex;
                align-items: center;
                gap: 8px;
                margin-top: 8px;
            }

            .back-btn:hover {
                color: #2b6cb0;
            }
        }
        
        .table th {
            background: #f8fafc;
            border-top: none;
            color: #4a5568;
            font-weight: 600;
            padding: 15px;
        }
        
        .table td {
            padding: 15px;
            vertical-align: middle;
        }
        
        .badge {
            padding: 6px 12px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 12px;
        }
        
        .badge-warning { background: #fed7aa; color: #9a3412; }
        .badge-info { background: #bfdbfe; color: #1e40af; }
        .badge-primary { background: #ddd6fe; color: #5b21b6; }
        .badge-success { background: #bbf7d0; color: #166534; }
        .badge-danger { background: #fecaca; color: #dc2626; }
        
        .action-buttons {
            padding: 30px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }
        
        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            border: none;
        }
        
        .btn-outline-secondary {
            border: 2px solid #e2e8f0;
            color: #718096;
        }
        
        .btn-outline-secondary:hover {
            background: #f8fafc;
            border-color: #cbd5e0;
        }
        
        @media (max-width: 768px) {
            .detail-container {
                padding: 10px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .items-table {
                margin: 0 20px 20px 20px;
            }
            
            .action-buttons {
                flex-direction: column;
                padding: 20px;
            }
        }
    </style>
</head>
<body>

<?php include 'sidebar.php'; ?>

<!-- 主内容区 -->
<div class="main-content">
    <!-- 顶部栏 -->
    <div class="topbar">
        <div class="topbar-left">
            <h1><i class="fas fa-file-invoice"></i> 采购单详情</h1>
            <p style="margin: 0; color: #718096; font-size: 14px;">订单号：<?= htmlspecialchars($order['order_number']) ?></p>
        </div>
        <div class="topbar-right">
            <div class="btn-group">
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
                <?php if ($order['status'] == 1): ?>
                <a href="index.php?action=edit&id=<?= $order['id'] ?>" class="btn btn-primary">
                    <i class="fas fa-edit"></i> 编辑订单
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="content">
        <!-- 状态显示卡片 -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-info-circle"></i> 订单状态</h3>
                <span class="badge badge-<?= $status['class'] ?>" style="font-size: 14px; padding: 8px 16px;">
                    <i class="fas fa-<?= $status['icon'] ?>"></i> <?= $status['text'] ?>
                </span>
            </div>
        </div>

        <!-- 基本信息网格 -->
        <div class="info-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px;">
            <!-- 订单信息卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-info-circle"></i> 订单信息</h3>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <span class="info-label">订单号</span>
                        <span class="info-value"><?= htmlspecialchars($order['order_number']) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">订货日期</span>
                        <span class="info-value"><?= htmlspecialchars($order['order_date']) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">预期交货日期</span>
                        <span class="info-value"><?= htmlspecialchars($order['expected_delivery_date'] ?: '未设置') ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">创建时间</span>
                        <span class="info-value"><?= htmlspecialchars($order['created_at']) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">创建人</span>
                        <span class="info-value"><?= htmlspecialchars($order['creator_name'] ?: '系统') ?></span>
                    </div>
                </div>
            </div>

            <!-- 供应商信息卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-truck"></i> 供应商信息</h3>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <span class="info-label">供应商名称</span>
                        <span class="info-value"><?= htmlspecialchars($order['supplier_name'] ?: '未知') ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">联系人</span>
                        <span class="info-value"><?= htmlspecialchars($order['supplier_contact'] ?: '未设置') ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">联系电话</span>
                        <span class="info-value"><?= htmlspecialchars($order['supplier_phone'] ?: '未设置') ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">供应商地址</span>
                        <span class="info-value"><?= htmlspecialchars($order['supplier_address'] ?: '未设置') ?></span>
                    </div>
                </div>
            </div>

            <!-- 收货信息卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-map-marker-alt"></i> 收货信息</h3>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <span class="info-label">收货食堂</span>
                        <span class="info-value">
                            <?php
                            $canteens = [1 => '第一食堂', 2 => '第二食堂', 3 => '第三食堂', 4 => '教工食堂'];
                            echo htmlspecialchars($canteens[$order['canteen_id']] ?? '未设置');
                            ?>
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">收货联系人</span>
                        <span class="info-value"><?= htmlspecialchars($order['contact_person'] ?: '未设置') ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">收货地址</span>
                        <span class="info-value"><?= htmlspecialchars($order['delivery_address'] ?: '未设置') ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">联系电话</span>
                        <span class="info-value"><?= htmlspecialchars($order['contact_phone'] ?: '未设置') ?></span>
                    </div>
                </div>
            </div>

            <!-- 金额信息卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-money-bill-wave"></i> 金额信息</h3>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <span class="info-label">订单金额</span>
                        <span class="info-value">¥<?= number_format($order['order_amount'] ?: 0, 2) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">实际金额</span>
                        <span class="info-value">¥<?= number_format($order['actual_amount'] ?: 0, 2) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">总金额</span>
                        <span class="info-value">¥<?= number_format($order['total_amount'] ?: 0, 2) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">付款状态</span>
                        <span class="info-value">
                            <?php
                            $paymentStatus = ['unpaid' => '未付款', 'paid' => '已付款', 'partial' => '部分付款'];
                            echo htmlspecialchars($paymentStatus[$order['payment_status']] ?? '未知');
                            ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 采购明细卡片 -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-list"></i> 采购明细</h3>
            </div>
            <div class="card-body" style="padding: 0;">
                <div class="table-responsive">
                    <table class="table table-hover" style="margin: 0;">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>食材名称</th>
                                <th>分类</th>
                                <th>数量</th>
                                <th>单位</th>
                                <th>单价</th>
                                <th>小计</th>
                                <th>用途</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($items)): ?>
                            <tr>
                                <td colspan="8" class="text-center text-muted py-4">
                                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                    暂无采购明细
                                </td>
                            </tr>
                            <?php else: ?>
                                <?php foreach ($items as $index => $item): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <td><?= htmlspecialchars($item['ingredient_name'] ?: '未知食材') ?></td>
                                    <td><?= htmlspecialchars($item['category_name'] ?: '未分类') ?></td>
                                    <td><?= number_format($item['quantity'], 2) ?></td>
                                    <td><?= htmlspecialchars($item['unit'] ?: '个') ?></td>
                                    <td>¥<?= number_format($item['unit_price'], 2) ?></td>
                                    <td>¥<?= number_format($item['total_price'], 2) ?></td>
                                    <td><?= htmlspecialchars($item['notes'] ?: '-') ?></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 备注信息 -->
        <?php if (!empty($order['notes'])): ?>
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-sticky-note"></i> 备注信息</h3>
            </div>
            <div class="card-body">
                <p class="mb-0"><?= nl2br(htmlspecialchars($order['notes'])) ?></p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- 移动端专用返回按钮 -->
<div class="mobile-back-btn" style="display: none;">
    <a href="../../mobile/purchase.php" class="btn btn-primary btn-lg">
        <i class="fas fa-arrow-left"></i> 返回采购管理
    </a>
</div>

<style>
@media (max-width: 768px) {
    .mobile-back-btn {
        display: block !important;
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
    }

    .mobile-back-btn .btn {
        background: linear-gradient(135deg, #4299e1, #667eea);
        border: none;
        border-radius: 25px;
        padding: 12px 24px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(66, 153, 225, 0.4);
        transition: all 0.3s ease;
    }

    .mobile-back-btn .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(66, 153, 225, 0.6);
    }

    /* 为底部按钮留出空间 */
    .main-content {
        padding-bottom: 80px !important;
    }
}
</style>

<script src="../../assets/js/common.js"></script>
<script src="main.js"></script>
</body>
</html>
