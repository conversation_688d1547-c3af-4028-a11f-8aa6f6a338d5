[2025-07-28 15:00:02] BATCH INBOUND DEBUG:
{
    "supplier_id": 2,
    "order_id": 16,
    "items_count": 46,
    "batch_number": "TEST_46_17537148022719",
    "post_keys": [
        "batch_type",
        "supplier_id",
        "batch_number",
        "inbound_date",
        "operator_name",
        "notes",
        "order_id",
        "is_new_order",
        "items"
    ],
    "items_keys": [
        0,
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        24,
        25,
        26,
        27,
        28,
        29,
        30,
        31,
        32,
        33,
        34,
        35,
        36,
        37,
        38,
        39,
        40,
        41,
        42,
        43,
        44,
        45
    ],
    "max_input_vars": "1000",
    "post_max_size": "8M",
    "first_few_items": [
        {
            "ingredient_id": "141",
            "actual_quantity": "35.0",
            "unit_price": "1.11",
            "order_quantity": "40.00"
        },
        {
            "ingredient_id": "142",
            "actual_quantity": "35.5",
            "unit_price": "1.12",
            "order_quantity": "40.00"
        },
        {
            "ingredient_id": "143",
            "actual_quantity": "36.0",
            "unit_price": "1.13",
            "order_quantity": "40.00"
        }
    ]
}

[2025-07-28 15:03:55] BATCH INBOUND DEBUG:
{
    "supplier_id": 2,
    "order_id": 16,
    "items_count": 46,
    "batch_number": "FIXED_46_17537150359238",
    "post_keys": [
        "batch_type",
        "supplier_id",
        "batch_number",
        "inbound_date",
        "operator_name",
        "notes",
        "order_id",
        "is_new_order",
        "items"
    ],
    "items_keys": [
        0,
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        24,
        25,
        26,
        27,
        28,
        29,
        30,
        31,
        32,
        33,
        34,
        35,
        36,
        37,
        38,
        39,
        40,
        41,
        42,
        43,
        44,
        45
    ],
    "max_input_vars": "1000",
    "post_max_size": "8M",
    "first_few_items": [
        {
            "ingredient_id": "141",
            "actual_quantity": "35.0",
            "unit_price": "1.11",
            "order_quantity": "40.00"
        },
        {
            "ingredient_id": "142",
            "actual_quantity": "35.5",
            "unit_price": "1.12",
            "order_quantity": "40.00"
        },
        {
            "ingredient_id": "143",
            "actual_quantity": "36.0",
            "unit_price": "1.13",
            "order_quantity": "40.00"
        }
    ]
}

