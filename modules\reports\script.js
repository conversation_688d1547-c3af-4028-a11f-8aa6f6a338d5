// 数据报表交互脚本

// 导出报表功能
function exportReport() {
    // 获取当前报表类型和日期范围
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action') || 'overview';
    const startDate = urlParams.get('start_date') || '';
    const endDate = urlParams.get('end_date') || '';
    
    // 创建导出数据
    const exportData = {
        type: action,
        dateRange: {
            start: startDate,
            end: endDate
        },
        timestamp: new Date().toISOString(),
        data: gatherReportData()
    };
    
    // 生成文件名
    const fileName = `食堂管理系统_${getReportTypeName(action)}_${formatDate(new Date())}.json`;
    
    // 下载文件
    downloadJSON(exportData, fileName);
    
    // 显示成功消息
    showNotification('报表导出成功！', 'success');
}

// 获取报表类型名称
function getReportTypeName(action) {
    const typeNames = {
        'overview': '概览报表',
        'purchase': '采购报表',
        'inventory': '库存报表',
        'supplier': '供应商报表',
        'financial': '财务报表'
    };
    return typeNames[action] || '数据报表';
}

// 收集报表数据
function gatherReportData() {
    const data = {};
    
    // 收集统计卡片数据
    const statCards = document.querySelectorAll('.stat-card');
    data.statistics = [];
    statCards.forEach(card => {
        const title = card.querySelector('.stat-card-title')?.textContent?.trim();
        const value = card.querySelector('.stat-card-value')?.textContent?.trim();
        const change = card.querySelector('.stat-card-change')?.textContent?.trim();
        
        if (title && value) {
            data.statistics.push({
                title: title,
                value: value,
                change: change || ''
            });
        }
    });
    
    // 收集表格数据
    const tables = document.querySelectorAll('.data-table table');
    data.tables = [];
    tables.forEach((table, index) => {
        const tableData = {
            index: index,
            headers: [],
            rows: []
        };
        
        // 获取表头
        const headers = table.querySelectorAll('thead th');
        headers.forEach(header => {
            tableData.headers.push(header.textContent.trim());
        });
        
        // 获取数据行
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            const rowData = [];
            const cells = row.querySelectorAll('td');
            cells.forEach(cell => {
                rowData.push(cell.textContent.trim());
            });
            tableData.rows.push(rowData);
        });
        
        data.tables.push(tableData);
    });
    
    return data;
}

// 下载JSON文件
function downloadJSON(data, filename) {
    const jsonStr = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
}

// 格式化日期
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="closeNotification(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        display: flex;
        align-items: center;
        gap: 10px;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            closeNotification(notification.querySelector('.notification-close'));
        }
    }, 5000);
}

// 获取通知图标
function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// 获取通知颜色
function getNotificationColor(type) {
    const colors = {
        'success': '#10b981',
        'error': '#ef4444',
        'warning': '#f59e0b',
        'info': '#3b82f6'
    };
    return colors[type] || '#3b82f6';
}

// 关闭通知
function closeNotification(button) {
    const notification = button.closest('.notification');
    if (notification) {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        opacity: 0.8;
        transition: opacity 0.2s ease;
    }
    
    .notification-close:hover {
        opacity: 1;
        background: rgba(255,255,255,0.1);
    }
`;
document.head.appendChild(style);

// 图表更新函数
function updateChart(chartId, dataType) {
    // 这个函数会在具体的报表页面中实现
    console.log(`更新图表 ${chartId} 为 ${dataType} 类型`);
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化日期选择器
    initDatePickers();
    
    // 初始化工具提示
    initTooltips();
    
    // 初始化响应式表格
    initResponsiveTables();
});

// 初始化日期选择器
function initDatePickers() {
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        // 设置最大日期为今天
        input.max = new Date().toISOString().split('T')[0];
        
        // 添加变化监听
        input.addEventListener('change', function() {
            validateDateRange();
        });
    });
}

// 验证日期范围
function validateDateRange() {
    const startDate = document.querySelector('input[name="start_date"]');
    const endDate = document.querySelector('input[name="end_date"]');
    
    if (startDate && endDate && startDate.value && endDate.value) {
        if (new Date(startDate.value) > new Date(endDate.value)) {
            showNotification('开始日期不能晚于结束日期', 'warning');
            endDate.value = startDate.value;
        }
    }
}

// 初始化工具提示
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

// 显示工具提示
function showTooltip(event) {
    const text = event.target.getAttribute('data-tooltip');
    if (!text) return;
    
    const tooltip = document.createElement('div');
    tooltip.className = 'custom-tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: #1f2937;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        z-index: 1000;
        pointer-events: none;
        white-space: nowrap;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    document.body.appendChild(tooltip);
    
    const rect = event.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
    
    event.target._tooltip = tooltip;
}

// 隐藏工具提示
function hideTooltip(event) {
    if (event.target._tooltip) {
        document.body.removeChild(event.target._tooltip);
        delete event.target._tooltip;
    }
}

// 初始化响应式表格
function initResponsiveTables() {
    const tables = document.querySelectorAll('.data-table table');
    tables.forEach(table => {
        const wrapper = table.parentElement;
        if (wrapper && !wrapper.classList.contains('table-responsive')) {
            wrapper.style.overflowX = 'auto';
        }
    });
}
