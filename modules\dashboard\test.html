<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘测试页面</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="main-content">
        <div class="content">
            <div class="content-header">
                <div class="header-left">
                    <h1>
                        <i class="fas fa-tachometer-alt"></i>
                        仪表板测试
                    </h1>
                    <p class="header-subtitle">测试页面</p>
                </div>
                <div class="header-actions">
                    <div class="time-display">
                        <i class="fas fa-clock"></i>
                        <span id="currentTime">2024-12-27 10:30:00</span>
                    </div>
                    <button type="button" class="btn btn-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        刷新数据
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-carrot"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" data-count="25">0</div>
                        <div class="stat-label">食材种类</div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span class="text-success">+5.2%</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" data-count="8">0</div>
                        <div class="stat-label">供应商</div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span class="text-success">+2.1%</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" data-count="12">0</div>
                        <div class="stat-label">本月订单</div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-down text-danger"></i>
                            <span class="text-danger">-1.5%</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="stat-icon bg-info">
                        <i class="fas fa-yen-sign"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" data-count="15680">0</div>
                        <div class="stat-label">本月采购金额</div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span class="text-success">+8.3%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="dashboard-grid">
                <div class="card chart-card">
                    <div class="card-header">
                        <h3>数据分析</h3>
                        <select id="chartTypeSelect" class="form-select">
                            <option value="monthly_purchase">月度采购趋势</option>
                            <option value="category_distribution">分类分布</option>
                            <option value="supplier_ranking">供应商排名</option>
                        </select>
                    </div>
                    <div class="card-body">
                        <canvas id="mainChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简化的JavaScript测试
        console.log('测试页面加载完成');

        // 基本的数字动画
        function animateNumber(element, start, end) {
            const duration = 2000;
            const startTime = performance.now();
            
            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const current = start + (end - start) * progress;
                
                element.textContent = Math.floor(current);
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }
            
            requestAnimationFrame(update);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('开始初始化测试页面...');
            
            // 数字动画
            document.querySelectorAll('.stat-number[data-count]').forEach(element => {
                const targetValue = parseInt(element.getAttribute('data-count'));
                animateNumber(element, 0, targetValue);
            });

            // 时间更新
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleString('zh-CN');
                const timeElement = document.getElementById('currentTime');
                if (timeElement) {
                    timeElement.textContent = timeString;
                }
            }
            
            updateTime();
            setInterval(updateTime, 1000);

            // 图表初始化
            if (typeof Chart !== 'undefined') {
                const ctx = document.getElementById('mainChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                            datasets: [{
                                label: '采购金额',
                                data: [12000, 13500, 14800, 16200, 15500, 17800],
                                borderColor: '#667eea',
                                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                                borderWidth: 3,
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });
                }
            }

            console.log('测试页面初始化完成');
        });

        function refreshData() {
            console.log('刷新数据');
        }
    </script>
</body>
</html>
