/* 学校食堂管理系统 - 通用样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: #f5f7fa;
    color: #2d3748;
    line-height: 1.6;
    overflow-y: scroll;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 侧边栏 */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 260px;
    height: 100vh;
    background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%);
    color: white;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 20px 20px;
    border-bottom: 1px solid #4a5568;
    text-align: center;
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    position: relative;
    overflow: hidden;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    position: relative;
    z-index: 1;
}

.sidebar-logo-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.sidebar-logo-text {
    text-align: left;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

.sidebar-header h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 6px;
    color: #ffffff;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 0.2px;
    line-height: 1.1;
    white-space: nowrap;
}

.sidebar-header p {
    font-size: 10px;
    color: #cbd5e0;
    margin-top: 4px;
    position: relative;
    z-index: 1;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-section {
    margin-bottom: 25px;
    position: relative;
}

.nav-section::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 20px;
    right: 20px;
    height: 1px;
    background: linear-gradient(90deg, transparent, #4a5568, transparent);
}

.nav-section:last-child::after {
    display: none;
}

.nav-section-title {
    padding: 14px 25px 10px;
    font-size: 16px;
    font-weight: 700;
    color: #a0aec0;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    display: flex;
    align-items: center;
}

.nav-section-title::before {
    content: '';
    width: 3px;
    height: 3px;
    background: #667eea;
    border-radius: 50%;
    margin-right: 8px;
    box-shadow: 0 0 6px rgba(102, 126, 234, 0.5);
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 16px 25px;
    color: #e2e8f0;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 3px solid transparent;
    border-radius: 0 25px 25px 0;
    margin: 2px 8px 2px 0;
    position: relative;
    overflow: hidden;
    font-weight: 500;
    font-size: 15px;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s ease;
}

.nav-item:hover::before {
    left: 100%;
}

.nav-item:hover {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    border-left-color: #667eea;
    color: #ffffff;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.nav-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-left-color: #667eea;
    color: #ffffff;
    font-weight: 600;
    transform: translateX(5px);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.nav-item.active::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: #ffffff;
    border-radius: 2px 0 0 2px;
}

.nav-item i {
    width: 22px;
    margin-right: 14px;
    text-align: center;
    font-size: 17px;
    transition: transform 0.3s ease;
}

.nav-item:hover i,
.nav-item.active i {
    transform: scale(1.1);
}

/* 特殊样式：人员管理 - 仅在active状态下应用 */
.nav-item[href*="users"].active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-left-color: #667eea;
    color: #ffffff;
    font-weight: 600;
    transform: translateX(5px);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.nav-item[href*="users"].active::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.nav-item[href*="users"].active:hover::before {
    left: 100%;
}

.nav-item[href*="users"].active:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateX(8px) scale(1.02);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

.nav-item[href*="users"].active i {
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
    /* 移除可能导致跳动的动画 */
}

@keyframes userIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.nav-item[href*="users"].active:hover i {
    animation: userIconSpin 0.6s ease-in-out;
}

@keyframes userIconSpin {
    0% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.2) rotate(180deg); }
    100% { transform: scale(1.1) rotate(360deg); }
}

/* 主内容区 */
.main-content {
    margin-left: 260px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.content-area {
    flex: 1;
    padding: 30px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: calc(100vh - 60px);
    position: relative;
}

.content-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    z-index: 0;
}

.content-area > * {
    position: relative;
    z-index: 1;
}

/* 顶部栏 */
.topbar {
    background: white;
    padding: 15px 30px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.topbar-left h1 {
    font-size: 24px;
    font-weight: 600;
    color: #2d3748;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 内容区域 */
.content {
    padding: 30px;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 按钮样式 */
.btn {
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #4299e1;
    color: white;
}

.btn-primary:hover {
    background: #3182ce;
    color: white;
}

.btn-success {
    background: #38a169;
    color: white;
}

.btn-success:hover {
    background: #2f855a;
    color: white;
}

.btn-secondary {
    background: #718096;
    color: white;
}

.btn-secondary:hover {
    background: #4a5568;
    color: white;
}

.btn-warning {
    background: #ed8936;
    color: white;
}

.btn-warning:hover {
    background: #dd6b20;
    color: white;
}

.btn-info {
    background: #4299e1;
    color: white;
}

.btn-info:hover {
    background: #3182ce;
    color: white;
}

.btn-danger {
    background: #e53e3e;
    color: white;
}

.btn-danger:hover {
    background: #c53030;
    color: white;
}

/* 统一的操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

.action-buttons .btn {
    padding: 8px 14px;
    font-size: 13px;
    min-width: 36px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    border-radius: 6px;
    transition: all 0.2s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    text-align: center;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

/* 按钮颜色样式 */
.action-buttons .btn-info {
    background: #4299e1;
    color: white;
}

.action-buttons .btn-info:hover {
    background: #3182ce;
    color: white;
}

.action-buttons .btn-warning {
    background: #ed8936;
    color: white;
}

.action-buttons .btn-warning:hover {
    background: #dd6b20;
    color: white;
}

.action-buttons .btn-danger {
    background: #e53e3e;
    color: white;
}

.action-buttons .btn-danger:hover {
    background: #c53030;
    color: white;
}

.action-buttons .btn-success {
    background: #38a169;
    color: white;
}

.action-buttons .btn-success:hover {
    background: #2f855a;
    color: white;
}

/* 优化的表格样式 - 提高空间利用率 */
.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.table {
    margin: 0;
    width: 100%;
    border-collapse: collapse;
}

.table th {
    background: #dbeafe;
    border-bottom: 2px solid #bfdbfe;
    font-weight: 600;
    color: #1e40af;
    padding: 12px 8px;
    text-align: left;
    font-size: 13px;
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
    padding: 12px 8px;
    border-bottom: 1px solid #e2e8f0;
    font-size: 13px;
}

.table tbody tr:hover {
    background: #f7fafc;
}

/* 操作列优化 - 固定最小宽度确保按钮显示完整 */
.table th:last-child,
.table td:last-child {
    width: 200px;
    min-width: 200px;
    text-align: center;
    padding: 8px 4px;
}

/* 紧凑的按钮样式 */
.table .btn-sm {
    padding: 6px 10px;
    font-size: 12px;
    min-width: 60px;
    height: 30px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.table .btn-sm i {
    font-size: 11px;
    margin-right: 3px;
}

.table .btn-sm span {
    font-size: 11px;
    font-weight: 500;
}

/* 响应式表格优化 */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: 3px;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 1000px;
    }
    
    .table th:last-child,
    .table td:last-child {
        width: 150px;
        min-width: 150px;
    }
    
    .table .btn-sm {
        padding: 4px 8px;
        font-size: 11px;
        min-width: 50px;
        height: 26px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    
    .table .btn-sm span {
        font-size: 10px;
    }
}

/* 小尺寸按钮 */
.btn-sm {
    padding: 8px 14px;
    font-size: 13px;
    min-width: 36px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    border-radius: 6px;
    text-align: center;
}

.btn-outline-primary {
    background: transparent;
    color: #4299e1;
    border: 1px solid #4299e1;
}

.btn-outline-primary:hover {
    background: #4299e1;
    color: white;
}

.btn-outline-secondary {
    background: transparent;
    color: #718096;
    border: 1px solid #718096;
}

.btn-outline-secondary:hover {
    background: #718096;
    color: white;
}

.btn-outline-danger {
    background: transparent;
    color: #e53e3e;
    border: 1px solid #e53e3e;
}

.btn-outline-danger:hover {
    background: #e53e3e;
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.btn-group {
    display: flex;
    gap: 8px;
}

/* 搜索框样式 */
.search-box {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    align-items: end;
}

/* ========================================
   搜索栏强制单行显示 - 最高优先级样式重写
   针对所有模块页面的搜索栏进行强制单行布局
   ======================================== */

/* 全局搜索栏单行布局强制规则 */
.search-bar,
.search-bar * {
    box-sizing: border-box !important;
}

/* 强制搜索表单单行显示 - 覆盖所有可能的冲突样式 */
.search-bar .search-bar-form,
.search-bar form {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    gap: 8px !important;
    align-items: flex-end !important;
    width: 100% !important;
    overflow-x: auto !important;
    justify-content: flex-start !important;
}

/* 搜索表单字段布局 */
.search-bar .form-field {
    display: flex !important;
    flex-direction: column !important;
    flex: 1 1 0 !important;
    min-width: 100px !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 强制标签样式 */
.search-bar .form-field label {
    font-size: 12px !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin-bottom: 4px !important;
    line-height: 1 !important;
    white-space: nowrap !important;
    display: block !important;
}

/* 强制输入框和选择框样式 */
.search-bar .form-control {
    width: 100% !important;
    flex: none !important;
    min-width: 0 !important;
    height: 40px !important;
    padding: 0 12px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    background: #ffffff !important;
    box-sizing: border-box !important;
}

/* 强制按钮样式 */
.search-bar .btn {
    flex: 0 0 auto !important;
    margin: 0 !important;
    white-space: nowrap !important;
    height: 40px !important;
    padding: 0 12px !important;
    min-width: 70px !important;
    border: none !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 6px !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

/* 主要按钮颜色 */
.search-bar .btn-primary {
    background: #3b82f6 !important;
    color: #ffffff !important;
}

.search-bar .btn-primary:hover {
    background: #2563eb !important;
    color: #ffffff !important;
}

/* 次要按钮颜色 */
.search-bar .btn-secondary {
    background: #6b7280 !important;
    color: #ffffff !important;
}

.search-bar .btn-secondary:hover {
    background: #4b5563 !important;
    color: #ffffff !important;
}

/* ========================================
   全新统一搜索栏组件样式 - 强制覆盖所有冲突样式
   ======================================== */

/* 搜索栏容器 */
.search-bar {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 12px !important;
    padding: 20px !important;
    margin-bottom: 24px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    position: relative !important;
}

.search-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6 0%, #10b981 50%, #f59e0b 100%);
    border-radius: 12px 12px 0 0;
}

/* 搜索表单布局 - 强制单行显示，覆盖所有冲突样式 */
.search-bar-form {
    display: flex !important;
    gap: 12px !important;
    align-items: flex-end !important;
    flex-wrap: nowrap !important; /* 强制不换行 */
    overflow-x: auto !important; /* 在极端情况下允许水平滚动 */
}

/* 确保所有搜索表单都使用单行布局 */
.search-bar .search-bar-form,
.search-bar form {
    display: flex !important;
    gap: 12px !important;
    align-items: flex-end !important;
    flex-wrap: nowrap !important;
    overflow-x: auto !important;
}

/* 表单组样式 - 强制覆盖Bootstrap等框架样式 */
.search-bar .form-field {
    display: flex !important;
    flex-direction: column !important;
    min-width: 0 !important;
    position: relative !important;
    flex: 1 !important;
}

/* 所有表单字段统一宽度 - 优化单行显示，强制覆盖所有冲突样式 */
.search-bar .form-field.text-input,
.search-bar .form-field.select-input,
.search-bar .form-field.date-input {
    flex: 1 1 0 !important;
    min-width: 120px !important; /* 减少最小宽度以确保单行显示 */
    max-width: none !important;
}

/* 标签样式 */
.search-bar .form-field label {
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 4px;
    line-height: 1;
}

/* 输入框和下拉框统一样式 */
.search-bar .form-control {
    height: 40px !important;
    padding: 0 12px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    background: #ffffff !important;
    transition: all 0.2s ease !important;
    box-sizing: border-box !important;
    width: 100% !important;
    min-width: 120px; /* 确保最小宽度 */
}

.search-bar .form-control:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.search-bar .form-control:hover:not(:focus) {
    border-color: #9ca3af !important;
}

.search-bar .form-control::placeholder {
    color: #9ca3af !important;
    font-weight: 400 !important;
}

/* 按钮样式 - 优化单行显示 */
.search-bar .btn {
    height: 40px !important;
    padding: 0 12px !important; /* 减少内边距 */
    border: none !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 6px !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    box-sizing: border-box !important;
    min-width: 70px !important; /* 减少最小宽度 */
    flex-shrink: 0; /* 防止按钮被压缩 */
}

.search-bar .btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.search-bar .btn:active {
    transform: translateY(0) !important;
}

/* 主要按钮 */
.search-bar .btn-primary {
    background: #3b82f6 !important;
    color: #ffffff !important;
}

.search-bar .btn-primary:hover {
    background: #2563eb !important;
    color: #ffffff !important;
}

/* 次要按钮 */
.search-bar .btn-secondary {
    background: #6b7280 !important;
    color: #ffffff !important;
}

.search-bar .btn-secondary:hover {
    background: #4b5563 !important;
    color: #ffffff !important;
}

/* 按钮图标 */
.search-bar .btn i {
    font-size: 12px !important;
}

/* 响应式设计 - 在大屏幕设备上保持一行显示 */
@media (min-width: 1200px) {
    .search-bar-form {
        flex-wrap: nowrap !important;
        gap: 12px; /* 恢复正常间距 */
    }
    
    .search-bar .form-field.text-input,
    .search-bar .form-field.select-input,
    .search-bar .form-field.date-input {
        flex: 1 !important;
        min-width: 140px !important;
    }
    
    .search-bar .btn {
        min-width: 80px !important; /* 恢复正常按钮宽度 */
        padding: 0 16px !important; /* 恢复正常内边距 */
    }
}

@media (min-width: 1400px) {
    .search-bar .form-field.text-input,
    .search-bar .form-field.select-input,
    .search-bar .form-field.date-input {
        min-width: 160px !important;
    }
}

/* 中等屏幕设备 - 强制单行显示 */
@media (max-width: 1199px) and (min-width: 769px) {
    .search-bar-form {
        flex-wrap: nowrap !important;
        gap: 8px; /* 减少间距以容纳更多元素 */
    }
    
    .search-bar .form-field.text-input,
    .search-bar .form-field.select-input,
    .search-bar .form-field.date-input {
        flex: 1 1 0;
        min-width: 100px; /* 减少最小宽度 */
    }
    
    .search-bar .btn {
        margin-top: 0;
        min-width: 70px !important; /* 减少按钮最小宽度 */
        padding: 0 12px !important; /* 减少按钮内边距 */
    }
}

/* 小屏幕设备 */
@media (max-width: 768px) {
    .search-bar {
        padding: 16px;
        margin-bottom: 20px;
    }
    
    .search-bar-form {
        flex-direction: column;
        gap: 16px;
        flex-wrap: nowrap;
    }
    
    .search-bar .form-field {
        width: 100% !important;
        min-width: auto !important;
        max-width: none !important;
        flex: none !important;
    }
    
    .search-bar .btn {
        width: 100% !important;
        min-width: auto !important;
        height: 44px !important;
    }
}

@media (max-width: 480px) {
    .search-bar {
        margin-left: -16px;
        margin-right: -16px;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 14px;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 6px;
}

.form-control {
    width: 100% !important;
    padding: 10px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
    box-sizing: border-box !important;
}

.form-control:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

/* 表格样式 */
.table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.table th {
    background: #dbeafe;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #1e40af;
    border-bottom: 1px solid #e2e8f0;
    font-size: 14px;
}

.table td {
    padding: 15px;
    border-bottom: 1px solid #f1f5f9;
    font-size: 14px;
}

.table tbody tr:hover {
    background: #f7fafc;
}

/* 徽章样式 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

.badge-warning {
    background: #fed7aa;
    color: #c05621;
}

.badge-info {
    background: #bfdbfe;
    color: #1e40af;
}

.badge-primary {
    background: #dbeafe;
    color: #1d4ed8;
}

.badge-success {
    background: #bbf7d0;
    color: #166534;
}

.badge-danger {
    background: #fecaca;
    color: #dc2626;
}

.badge-secondary {
    background: #e5e7eb;
    color: #374151;
}

/* 进度条 */
.progress {
    width: 100%;
    height: 6px;
    background: #f1f5f9;
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: #4299e1;
    transition: width 0.3s ease;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h5 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #4a5568;
}

.empty-state p {
    font-size: 14px;
}

/* 消息提示 */
.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid;
}

.alert-success {
    background: #f0fff4;
    border-color: #9ae6b4;
    color: #22543d;
}

.alert-danger {
    background: #fed7d7;
    border-color: #feb2b2;
    color: #742a2a;
}

.alert-info {
    background: #ebf8ff;
    border-color: #90cdf4;
    color: #2a4365;
}

.alert-warning {
    background: #fffbeb;
    border-color: #f6e05e;
    color: #744210;
}

.alert-dismissible {
    position: relative;
    padding-right: 40px;
}

.btn-close {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    opacity: 0.5;
}

.btn-close:hover {
    opacity: 1;
}

/* 统计卡片网格 - 全局统一样式 */
.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 30px !important;
}

.stat-card {
    background: white !important;
    border-radius: 12px !important;
    padding: 20px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    display: flex !important;
    align-items: center !important;
    transition: all 0.3s ease !important;
    border: 1px solid #e2e8f0 !important;
    position: relative !important;
    overflow: hidden !important;
}

.stat-card::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 3px !important;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
}

.stat-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.stat-icon {
    width: 60px !important;
    height: 60px !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-right: 16px !important;
    font-size: 24px !important;
    color: white !important;
    flex-shrink: 0 !important;
}

.stat-icon.bg-primary { 
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
}

.stat-icon.bg-success { 
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); 
}

.stat-icon.bg-warning { 
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%); 
}

.stat-icon.bg-danger { 
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); 
}

.stat-icon.bg-info { 
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%); 
}

.stat-icon.bg-secondary { 
    background: linear-gradient(135deg, #a0aec0 0%, #718096 100%); 
}

.stat-content {
    flex: 1 !important;
    min-width: 0 !important;
}

.stat-number {
    font-size: 28px !important;
    font-weight: 700 !important;
    color: #2d3748 !important;
    margin-bottom: 4px !important;
    line-height: 1 !important;
}

.stat-label {
    font-size: 14px !important;
    color: #718096 !important;
    font-weight: 500 !important;
    margin: 0 !important;
}

/* 模态框全局样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050;
    backdrop-filter: blur(4px);
}

.modal-dialog {
    max-width: 500px;
    width: 90%;
    margin: 20px;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8fafc;
}

.modal-title {
    margin: 0;
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    background: #f8fafc;
}

.btn-close {
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    font-size: 20px;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.btn-close:hover {
    color: #718096;
    background: #e2e8f0;
}

/* 分页全局样式 */
.pagination {
    display: flex;
    gap: 4px;
    list-style: none;
    margin: 0;
    padding: 0;
    justify-content: center;
    flex-wrap: wrap;
}

.page-item {
    display: block;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 8px 12px;
    color: #4a5568;
    text-decoration: none;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.page-link:hover {
    color: #2d3748;
    background: #f7fafc;
    border-color: #cbd5e0;
    text-decoration: none;
}

.page-item.active .page-link {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.page-item.active .page-link:hover {
    background: #5a67d8;
    border-color: #5a67d8;
    color: white;
}

.pagination-info {
    text-align: center;
    margin-top: 16px;
    color: #718096;
    font-size: 14px;
}

/* 表单标签样式 */
.form-label {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
    display: block;
}

.form-group {
    margin-bottom: 20px;
}

/* 卡片动作样式 */
.card-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.select-all-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4a5568;
    font-size: 14px;
    cursor: pointer;
    margin: 0;
    font-weight: 500;
}

.selected-count {
    color: #667eea;
    font-size: 14px;
    font-weight: 600;
}

/* 仪表板模块特定样式强制覆盖 */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-top: 20px;
}

/* 强制仪表板统计卡片使用网格布局 */
body .main-content .content .stats-grid {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 20px !important;
    margin-bottom: 30px !important;
}

/* 强制仪表板统计卡片样式 */
body .main-content .content .stats-grid .stat-card {
    background: white !important;
    border-radius: 12px !important;
    padding: 20px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    display: flex !important;
    align-items: center !important;
    transition: all 0.3s ease !important;
    border: 1px solid #e2e8f0 !important;
    position: relative !important;
    overflow: hidden !important;
}

@media (max-width: 1200px) {
    body .main-content .content .stats-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

@media (max-width: 768px) {
    body .main-content .content .stats-grid {
        grid-template-columns: 1fr !important;
    }
}
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }
    
    .stat-card {
        padding: 16px;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .stat-number {
        font-size: 24px;
    }
    
    .modal-dialog {
        width: 95%;
        margin: 10px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px;
    }
    
    .card-actions {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
        transition: transform 0.3s ease;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .content-area {
        padding: 20px 15px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    /* 响应式操作按钮 */
    .action-buttons {
        flex-direction: column;
        gap: 6px;
    }

    .action-buttons .btn {
        width: 100%;
        min-width: auto;
        padding: 10px 12px;
        font-size: 14px;
        height: auto;
    }

    .action-buttons .btn span {
        font-size: 13px;
    }

    .topbar {
        padding: 15px 20px;
    }

    .content {
        padding: 20px;
    }

    /* 人员管理特殊样式在移动端的调整 */
    .nav-item[href*="users"].active:hover {
        transform: translateX(4px) scale(1.01);
    }

    /* 移动端菜单按钮 */
    .mobile-menu-btn {
        display: block;
        position: fixed;
        top: 15px;
        left: 15px;
        z-index: 1001;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px;
        font-size: 18px;
        box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .mobile-menu-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.5);
    }
}

@media (min-width: 769px) {
    .mobile-menu-btn {
        display: none;
    }
}
