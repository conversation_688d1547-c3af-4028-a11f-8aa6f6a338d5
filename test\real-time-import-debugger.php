<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时导入调试器</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .upload-area { border: 2px dashed #007cba; padding: 20px; text-align: center; margin: 10px 0; border-radius: 5px; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 5px; border: none; cursor: pointer; }
        .btn:hover { background: #005a8b; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; max-height: 500px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .log-line { margin: 2px 0; padding: 2px 5px; }
        .log-success { background: #d4edda; color: #155724; }
        .log-error { background: #f8d7da; color: #721c24; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🔧 实时导入调试器</h1>
    
    <div class="test-section">
        <h2>📁 上传Excel文件进行调试</h2>
        <div class="step">
            <form method="post" enctype="multipart/form-data">
                <div class="upload-area">
                    <p>选择您的Excel文件进行实时调试分析</p>
                    <input type="file" name="excel_file" accept=".xlsx,.xls,.csv" required>
                    <br><br>
                    <button type="submit" name="debug_upload" class="btn">🔍 开始调试分析</button>
                </div>
            </form>
            
            <p class="info">或者调试现有的test.xlsx文件：</p>
            <form method="post">
                <button type="submit" name="debug_test_file" class="btn">🔍 调试test.xlsx</button>
            </form>
        </div>
    </div>
    
    <?php
    if (isset($_POST['debug_upload']) || isset($_POST['debug_test_file'])) {
        require_once '../includes/Database.php';
        require_once '../includes/ExcelReader.php';
        
        try {
            $db = Database::getInstance();
            
            // 确定要调试的文件
            $debugFile = '';
            $fileName = '';
            
            if (isset($_POST['debug_upload']) && isset($_FILES['excel_file'])) {
                if ($_FILES['excel_file']['error'] === UPLOAD_ERR_OK) {
                    $debugFile = $_FILES['excel_file']['tmp_name'];
                    $fileName = $_FILES['excel_file']['name'];
                } else {
                    throw new Exception('文件上传失败');
                }
            } elseif (isset($_POST['debug_test_file'])) {
                $debugFile = '../test.xlsx';
                $fileName = 'test.xlsx';
                if (!file_exists($debugFile)) {
                    throw new Exception('test.xlsx文件不存在');
                }
            }
            
            echo "<div class='test-section'>";
            echo "<h2>🔍 调试文件: {$fileName}</h2>";
            
            echo "<div class='debug-log'>";
            
            function debugLog($message, $type = 'info') {
                $class = "log-{$type}";
                echo "<div class='log-line {$class}'>[" . date('H:i:s') . "] {$message}</div>";
                flush();
            }
            
            debugLog("开始调试文件: {$fileName}", 'info');
            
            // 读取Excel文件
            debugLog("正在读取Excel文件...", 'info');
            $reader = new ExcelReader();
            $data = $reader->read($debugFile, $fileName);
            
            if (empty($data)) {
                debugLog("Excel文件为空或无法读取", 'error');
                exit;
            }
            
            debugLog("Excel文件读取成功，总行数: " . count($data) . "，总列数: " . count($data[0]), 'success');
            
            // 格式检测
            debugLog("开始格式检测...", 'info');
            
            $importType = 'simple_list';
            if (count($data) >= 4) {
                $firstRow = $data[0] ?? [];
                $secondRow = $data[1] ?? [];
                
                debugLog("第1行列数: " . count($firstRow) . "，第2行列数: " . count($secondRow), 'info');
                
                if (count($firstRow) > 4 && count($secondRow) > 11) {
                    $orderNumber = trim($firstRow[1] ?? '');
                    debugLog("第1行第2列内容: '" . $orderNumber . "'，长度: " . strlen($orderNumber), 'info');
                    
                    $lengthCheck = strlen($orderNumber) > 10;
                    $patternCheck = preg_match('/^[A-Z0-9]+/', $orderNumber);
                    
                    debugLog("长度检查(>10): " . ($lengthCheck ? '✅通过' : '❌失败'), $lengthCheck ? 'success' : 'warning');
                    debugLog("模式检查(A-Z0-9开头): " . ($patternCheck ? '✅通过' : '❌失败'), $patternCheck ? 'success' : 'warning');
                    
                    if (!empty($orderNumber) && ($lengthCheck || $patternCheck)) {
                        $importType = 'order_form';
                        debugLog("格式检测结果: 订货单格式", 'success');
                    } else {
                        debugLog("格式检测结果: 简单列表格式（订单号不符合条件）", 'warning');
                    }
                } else {
                    debugLog("格式检测结果: 简单列表格式（列数不足）", 'warning');
                }
            } else {
                debugLog("格式检测结果: 简单列表格式（行数不足）", 'warning');
            }
            
            if ($importType === 'order_form') {
                debugLog("开始处理订货单格式...", 'info');
                
                // 头部信息检查
                debugLog("检查头部信息...", 'info');
                $headerFields = [
                    ['订单号', $data[0][1] ?? '', '[0][1]'],
                    ['订单日期', $data[0][4] ?? '', '[0][4]'],
                    ['联系人', $data[1][11] ?? '', '[1][11]'],
                    ['送货地址', $data[2][1] ?? '', '[2][1]'],
                    ['联系电话', $data[2][11] ?? '', '[2][11]'],
                    ['订单金额', $data[3][1] ?? '', '[3][1]'],
                    ['实际金额', $data[3][5] ?? '', '[3][5]'],
                    ['预期交货日期', $data[3][11] ?? '', '[3][11]']
                ];
                
                foreach ($headerFields as $field) {
                    $value = trim($field[1]);
                    $status = empty($value) ? '❌空值' : '✅有值';
                    debugLog("{$field[0]} {$field[2]}: '{$value}' - {$status}", empty($value) ? 'warning' : 'success');
                }
                
                // 明细数据检查
                debugLog("检查明细数据（从第7行开始）...", 'info');
                $validItems = 0;
                $totalItems = 0;
                
                for ($i = 6; $i < count($data); $i++) {
                    $row = $data[$i];
                    $rowNum = $i + 1;
                    $totalItems++;
                    
                    $itemCode = trim($row[0] ?? '');
                    $itemName = trim($row[1] ?? '');
                    $unitPrice = trim($row[7] ?? '');
                    $quantity = trim($row[8] ?? '');
                    $totalPrice = trim($row[9] ?? '');
                    
                    debugLog("第{$rowNum}行: 编码='{$itemCode}', 名称='{$itemName}', 单价='{$unitPrice}', 数量='{$quantity}'", 'info');
                    
                    if (empty($itemCode)) {
                        debugLog("  ❌ 商品编码为空", 'error');
                        continue;
                    }
                    
                    if (empty($quantity) || !is_numeric($quantity) || floatval($quantity) <= 0) {
                        debugLog("  ❌ 数量无效: '{$quantity}'", 'error');
                        continue;
                    }
                    
                    if (!empty($unitPrice) && (!is_numeric($unitPrice) || floatval($unitPrice) < 0)) {
                        debugLog("  ❌ 单价无效: '{$unitPrice}'", 'error');
                        continue;
                    }
                    
                    // 检查食材是否存在
                    $ingredient = $db->fetchOne("SELECT id, name FROM ingredients WHERE code = ?", [$itemCode]);
                    if ($ingredient) {
                        debugLog("  ✅ 找到食材: ID={$ingredient['id']}, 名称={$ingredient['name']}", 'success');
                    } else {
                        debugLog("  ⚠️ 食材不存在，将自动创建: 编码={$itemCode}", 'warning');
                    }
                    
                    $validItems++;
                    debugLog("  ✅ 第{$rowNum}行验证通过", 'success');
                    
                    if ($totalItems >= 10) {
                        debugLog("（只显示前10行明细，实际还有更多...）", 'info');
                        break;
                    }
                }
                
                debugLog("明细数据统计: 总行数={$totalItems}, 有效行数={$validItems}", $validItems > 0 ? 'success' : 'error');
                
                if ($validItems === 0) {
                    debugLog("❌ 没有有效的明细数据，这就是导入0条记录的原因！", 'error');
                } else {
                    debugLog("✅ 预计可以导入 {$validItems} 条明细记录", 'success');
                }
                
            } else {
                debugLog("❌ 文件被识别为简单列表格式，但您要求使用订货单格式", 'error');
                debugLog("解决方案：", 'info');
                debugLog("1. 确保第1行第2列有订单号（长度>10或以字母数字开头）", 'info');
                debugLog("2. 确保至少有4行数据，第1行至少5列，第2行至少12列", 'info');
                debugLog("3. 使用标准模板重新创建文件", 'info');
            }
            
            debugLog("调试完成", 'info');
            
            echo "</div>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='test-section'>";
            echo "<p class='error'>❌ 调试过程出错: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    ?>
    
    <div class="test-section">
        <h2>🛠️ 调试工具集</h2>
        <div class="step">
            <h4>📋 其他调试工具：</h4>
            <p>
                <a href="debug-order-form-import.php" class="btn">📊 详细格式分析</a>
                <a href="simulate-import-process.php" class="btn">🔄 模拟导入过程</a>
                <a href="../modules/purchase/download_template_based_on_test.php" class="btn">📥 下载标准模板</a>
            </p>
            
            <h4>🎯 常见问题解决：</h4>
            <ul>
                <li><strong>格式检测失败</strong>：确保订单号在第1行第2列，且符合格式要求</li>
                <li><strong>明细数据无效</strong>：确保从第7行开始有明细，商品编码不为空，数量大于0</li>
                <li><strong>导入0条记录</strong>：通常是格式问题或明细数据问题</li>
            </ul>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
