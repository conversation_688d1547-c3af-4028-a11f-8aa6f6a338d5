<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试采购单数据</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        .data-table th { background: #f5f5f5; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔍 调试采购单数据</h1>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>📊 采购单表数据检查</h2>";
        
        // 检查purchase_orders表是否存在
        $tables = $db->fetchAll("SHOW TABLES LIKE 'purchase_orders'");
        if (empty($tables)) {
            echo "<p class='error'>❌ purchase_orders表不存在</p>";
        } else {
            echo "<p class='success'>✅ purchase_orders表存在</p>";
            
            // 检查表结构
            $columns = $db->fetchAll("DESCRIBE purchase_orders");
            echo "<h4>表结构：</h4>";
            echo "<table class='data-table'>";
            echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>{$column['Field']}</td>";
                echo "<td>{$column['Type']}</td>";
                echo "<td>{$column['Null']}</td>";
                echo "<td>{$column['Key']}</td>";
                echo "<td>{$column['Default']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // 检查数据总数
            $count = $db->fetchOne("SELECT COUNT(*) as count FROM purchase_orders")['count'];
            echo "<p class='info'>采购单总数: <strong>{$count}</strong></p>";
            
            if ($count > 0) {
                // 按状态统计
                $statusStats = $db->fetchAll("
                    SELECT status, COUNT(*) as count 
                    FROM purchase_orders 
                    GROUP BY status 
                    ORDER BY status
                ");
                
                echo "<h4>按状态统计：</h4>";
                echo "<table class='data-table'>";
                echo "<tr><th>状态值</th><th>数量</th><th>状态说明</th></tr>";
                foreach ($statusStats as $stat) {
                    $statusName = '';
                    switch ($stat['status']) {
                        case 1: $statusName = '待确认'; break;
                        case 2: $statusName = '已确认'; break;
                        case 3: $statusName = '已发货'; break;
                        case 4: $statusName = '已完成'; break;
                        case 5: $statusName = '已取消'; break;
                        default: $statusName = '未知状态';
                    }
                    
                    $rowClass = ($stat['status'] == 1 || $stat['status'] == 2) ? 'success' : '';
                    echo "<tr class='{$rowClass}'>";
                    echo "<td>{$stat['status']}</td>";
                    echo "<td>{$stat['count']}</td>";
                    echo "<td>{$statusName}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // 显示所有采购单
                $allOrders = $db->fetchAll("
                    SELECT id, order_number, supplier_id, status, order_date, order_amount, created_at
                    FROM purchase_orders 
                    ORDER BY created_at DESC 
                    LIMIT 10
                ");
                
                echo "<h4>最近的10个采购单：</h4>";
                echo "<table class='data-table'>";
                echo "<tr><th>ID</th><th>订单号</th><th>供应商ID</th><th>状态</th><th>订单日期</th><th>金额</th><th>创建时间</th></tr>";
                foreach ($allOrders as $order) {
                    $rowClass = ($order['status'] == 1 || $order['status'] == 2) ? 'success' : '';
                    echo "<tr class='{$rowClass}'>";
                    echo "<td>{$order['id']}</td>";
                    echo "<td>{$order['order_number']}</td>";
                    echo "<td>{$order['supplier_id']}</td>";
                    echo "<td>{$order['status']}</td>";
                    echo "<td>{$order['order_date']}</td>";
                    echo "<td>" . number_format($order['order_amount'], 2) . "</td>";
                    echo "<td>{$order['created_at']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>📦 采购单商品数据检查</h2>";
        
        // 检查purchase_order_items表
        $itemTables = $db->fetchAll("SHOW TABLES LIKE 'purchase_order_items'");
        if (empty($itemTables)) {
            echo "<p class='error'>❌ purchase_order_items表不存在</p>";
        } else {
            echo "<p class='success'>✅ purchase_order_items表存在</p>";
            
            $itemCount = $db->fetchOne("SELECT COUNT(*) as count FROM purchase_order_items")['count'];
            echo "<p class='info'>采购单商品总数: <strong>{$itemCount}</strong></p>";
            
            if ($itemCount > 0) {
                // 显示采购单商品
                $items = $db->fetchAll("
                    SELECT poi.*, po.order_number, po.status as order_status
                    FROM purchase_order_items poi
                    LEFT JOIN purchase_orders po ON poi.order_id = po.id
                    ORDER BY poi.order_id DESC, poi.id ASC
                    LIMIT 10
                ");
                
                echo "<h4>最近的10个采购单商品：</h4>";
                echo "<table class='data-table'>";
                echo "<tr><th>ID</th><th>订单号</th><th>订单状态</th><th>食材ID</th><th>数量</th><th>单价</th><th>用途</th></tr>";
                foreach ($items as $item) {
                    $rowClass = ($item['order_status'] == 1 || $item['order_status'] == 2) ? 'success' : '';
                    echo "<tr class='{$rowClass}'>";
                    echo "<td>{$item['id']}</td>";
                    echo "<td>{$item['order_number']}</td>";
                    echo "<td>{$item['order_status']}</td>";
                    echo "<td>{$item['ingredient_id']}</td>";
                    echo "<td>{$item['quantity']}</td>";
                    echo "<td>{$item['unit_price']}</td>";
                    echo "<td>{$item['purpose']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 入库模块查询测试</h2>";
        
        // 测试入库模块使用的查询
        $purchaseOrders = $db->fetchAll("
            SELECT 
                po.id,
                po.order_number,
                po.supplier_id,
                s.name as supplier_name,
                po.order_date,
                po.order_amount,
                poi.ingredient_id,
                i.name as ingredient_name,
                i.unit,
                poi.quantity,
                poi.unit_price,
                poi.purpose
            FROM purchase_orders po
            LEFT JOIN purchase_order_items poi ON po.id = poi.order_id
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN ingredients i ON poi.ingredient_id = i.id
            WHERE po.status IN (1, 2) 
            AND poi.ingredient_id IS NOT NULL
            ORDER BY po.order_date DESC, po.order_number ASC
        ");
        
        echo "<p class='info'>入库模块查询结果: <strong>" . count($purchaseOrders) . "</strong> 条记录</p>";
        
        if (empty($purchaseOrders)) {
            echo "<p class='warning'>⚠️ 查询结果为空，可能的原因：</p>";
            echo "<ul>";
            echo "<li>没有状态为1或2的采购单</li>";
            echo "<li>采购单没有关联的商品</li>";
            echo "<li>suppliers表或ingredients表数据缺失</li>";
            echo "<li>外键关联有问题</li>";
            echo "</ul>";
            
            // 分步检查
            echo "<h4>分步检查：</h4>";
            
            // 1. 检查状态为1或2的采购单
            $validOrders = $db->fetchAll("SELECT * FROM purchase_orders WHERE status IN (1, 2)");
            echo "<p>状态为1或2的采购单: " . count($validOrders) . " 个</p>";
            
            if (!empty($validOrders)) {
                echo "<table class='data-table'>";
                echo "<tr><th>ID</th><th>订单号</th><th>状态</th><th>供应商ID</th></tr>";
                foreach ($validOrders as $order) {
                    echo "<tr>";
                    echo "<td>{$order['id']}</td>";
                    echo "<td>{$order['order_number']}</td>";
                    echo "<td>{$order['status']}</td>";
                    echo "<td>{$order['supplier_id']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // 2. 检查这些采购单的商品
                $orderIds = array_column($validOrders, 'id');
                if (!empty($orderIds)) {
                    $orderIdList = implode(',', $orderIds);
                    $orderItems = $db->fetchAll("SELECT * FROM purchase_order_items WHERE order_id IN ({$orderIdList})");
                    echo "<p>这些采购单的商品数: " . count($orderItems) . " 个</p>";
                    
                    if (!empty($orderItems)) {
                        echo "<table class='data-table'>";
                        echo "<tr><th>订单ID</th><th>食材ID</th><th>数量</th><th>单价</th></tr>";
                        foreach (array_slice($orderItems, 0, 5) as $item) {
                            echo "<tr>";
                            echo "<td>{$item['order_id']}</td>";
                            echo "<td>{$item['ingredient_id']}</td>";
                            echo "<td>{$item['quantity']}</td>";
                            echo "<td>{$item['unit_price']}</td>";
                            echo "</tr>";
                        }
                        echo "</table>";
                    }
                }
            }
            
        } else {
            echo "<h4>查询结果预览（前5条）：</h4>";
            echo "<table class='data-table'>";
            echo "<tr><th>订单号</th><th>供应商</th><th>食材</th><th>数量</th><th>单价</th></tr>";
            foreach (array_slice($purchaseOrders, 0, 5) as $order) {
                echo "<tr>";
                echo "<td>{$order['order_number']}</td>";
                echo "<td>{$order['supplier_name']}</td>";
                echo "<td>{$order['ingredient_name']}</td>";
                echo "<td>{$order['quantity']}</td>";
                echo "<td>{$order['unit_price']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-section'>";
        echo "<h2 class='error'>❌ 数据库连接失败</h2>";
        echo "<p class='error'>错误信息: " . $e->getMessage() . "</p>";
        echo "<p class='info'>使用模拟数据进行测试...</p>";
        
        echo "<h4>模拟的采购单数据：</h4>";
        $mockOrders = [
            ['id' => 1, 'order_number' => 'PO20241201001', 'status' => 2, 'supplier_name' => '绿色蔬菜供应商'],
            ['id' => 2, 'order_number' => 'PO20241201002', 'status' => 1, 'supplier_name' => '优质肉类供应商']
        ];
        
        echo "<table class='data-table'>";
        echo "<tr><th>ID</th><th>订单号</th><th>状态</th><th>供应商</th></tr>";
        foreach ($mockOrders as $order) {
            echo "<tr>";
            echo "<td>{$order['id']}</td>";
            echo "<td>{$order['order_number']}</td>";
            echo "<td>{$order['status']}</td>";
            echo "<td>{$order['supplier_name']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    }
    ?>
    
    <div class="test-section">
        <h2>🛠️ 解决方案</h2>
        
        <h4>如果没有显示采购单，可能的解决方案：</h4>
        <ol>
            <li><strong>创建测试采购单</strong>：
                <a href="../modules/purchase/index.php?action=create" class="btn">创建采购单</a>
            </li>
            <li><strong>确认采购单状态</strong>：确保采购单状态为1（待确认）或2（已确认）</li>
            <li><strong>添加采购单商品</strong>：确保采购单包含商品项目</li>
            <li><strong>检查数据关联</strong>：确保supplier_id和ingredient_id正确关联</li>
        </ol>
        
        <h4>快速测试：</h4>
        <p>
            <a href="../modules/inbound/index.php?action=create" class="btn">测试入库页面</a>
            <a href="../modules/purchase/index.php" class="btn">查看采购单列表</a>
        </p>
    </div>
    
    <p><a href="../modules/inbound/index.php">返回入库管理</a></p>
</body>
</html>
