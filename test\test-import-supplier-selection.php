<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试导入供应商选择功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn-success { background: #28a745; }
        .feature-box { background: #e6f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 15px 0; border-radius: 5px; }
        .step-box { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; }
        .code-box { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>✅ 导入供应商选择功能测试</h1>
    
    <div class="test-section">
        <h2>🎯 功能概述</h2>
        
        <div class="feature-box">
            <h4>✨ 新增功能：供应商选择</h4>
            <p>在采购单导入页面的"开始导入"按钮上方增加了供应商选择功能，解决Excel文件中没有供应商信息的问题。</p>
            
            <h5>主要特性：</h5>
            <ul>
                <li>📁 <strong>文件选择后显示</strong>：只有选择文件后才显示供应商选择区域</li>
                <li>🏢 <strong>供应商下拉列表</strong>：显示所有启用状态的供应商</li>
                <li>✅ <strong>必填验证</strong>：必须同时选择文件和供应商才能开始导入</li>
                <li>🔄 <strong>智能按钮状态</strong>：根据选择状态动态更新按钮文本和可用性</li>
                <li>🎨 <strong>美观界面</strong>：统一的设计风格和用户体验</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🧪 测试步骤</h2>
        
        <div class="step-box">
            <h4>步骤1：访问导入页面</h4>
            <p><a href="../modules/purchase/index.php?action=import" class="btn btn-success">🚀 打开导入页面</a></p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>页面正常加载</li>
                <li>供应商选择区域默认隐藏</li>
                <li>"开始导入"按钮显示"请先选择文件"</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h4>步骤2：选择Excel文件</h4>
            <p>点击"选择文件"按钮，选择一个Excel文件（.xlsx或.xls）</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>文件信息正确显示</li>
                <li>供应商选择区域自动显示</li>
                <li>"开始导入"按钮显示"请选择供应商"</li>
                <li>按钮仍然处于禁用状态</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h4>步骤3：选择供应商</h4>
            <p>从供应商下拉列表中选择一个供应商</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>供应商选择成功</li>
                <li>"开始导入"按钮显示"开始导入"</li>
                <li>按钮变为可用状态（不再禁用）</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h4>步骤4：测试导入功能</h4>
            <p>点击"开始导入"按钮</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>表单正常提交</li>
                <li>供应商ID正确传递到后端</li>
                <li>导入过程中使用选择的供应商</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h4>步骤5：测试移除文件</h4>
            <p>点击文件信息区域的"×"按钮移除文件</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>文件信息隐藏</li>
                <li>供应商选择区域隐藏</li>
                <li>"开始导入"按钮重置为"请先选择文件"</li>
                <li>按钮重新禁用</li>
            </ul>
        </div>
    </div>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>📊 供应商数据检查</h2>";
        
        // 检查供应商数据
        $suppliers = $db->fetchAll("
            SELECT id, name, contact_person, phone, status 
            FROM suppliers 
            ORDER BY status DESC, name ASC
        ");
        
        echo "<p class='info'>供应商总数: <strong>" . count($suppliers) . "</strong></p>";
        
        if (!empty($suppliers)) {
            $enabledSuppliers = array_filter($suppliers, function($s) { return $s['status'] == 1; });
            echo "<p class='success'>启用的供应商: <strong>" . count($enabledSuppliers) . "</strong> 个</p>";
            
            echo "<h4>供应商列表（前10个）：</h4>";
            echo "<div class='code-box'>";
            foreach (array_slice($suppliers, 0, 10) as $supplier) {
                $statusText = $supplier['status'] == 1 ? '✅ 启用' : '❌ 禁用';
                echo "<div>";
                echo "ID: {$supplier['id']} | ";
                echo "名称: {$supplier['name']} | ";
                echo "联系人: " . ($supplier['contact_person'] ?: '无') . " | ";
                echo "状态: {$statusText}";
                echo "</div>";
            }
            echo "</div>";
        } else {
            echo "<p class='error'>❌ 没有供应商数据</p>";
            echo "<p><a href='../modules/suppliers/index.php' class='btn'>前往供应商管理</a></p>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-section'>";
        echo "<h2 class='error'>❌ 数据库连接失败</h2>";
        echo "<p class='error'>错误信息: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
    
    <div class="test-section">
        <h2>🔧 技术实现详情</h2>
        
        <h4>前端实现：</h4>
        <div class="code-box">
// 文件选择后显示供应商选择区域<br>
function handleFileSelect(file) {<br>
&nbsp;&nbsp;&nbsp;&nbsp;// 显示供应商选择区域<br>
&nbsp;&nbsp;&nbsp;&nbsp;document.getElementById('supplierSelection').style.display = 'block';<br>
&nbsp;&nbsp;&nbsp;&nbsp;// 更新提交按钮状态<br>
&nbsp;&nbsp;&nbsp;&nbsp;updateSubmitButton();<br>
}<br><br>
// 动态更新按钮状态<br>
function updateSubmitButton() {<br>
&nbsp;&nbsp;&nbsp;&nbsp;const hasFile = fileInput.files.length > 0;<br>
&nbsp;&nbsp;&nbsp;&nbsp;const hasSupplier = supplierSelect.value !== '';<br>
&nbsp;&nbsp;&nbsp;&nbsp;submitBtn.disabled = !(hasFile && hasSupplier);<br>
}
        </div>
        
        <h4>后端实现：</h4>
        <div class="code-box">
// 验证供应商选择<br>
$supplierId = isset($this->request['post']['supplier_id']) ? intval($this->request['post']['supplier_id']) : 0;<br>
if ($supplierId <= 0) {<br>
&nbsp;&nbsp;&nbsp;&nbsp;throw new Exception('请选择供应商');<br>
}<br><br>
// 传递供应商ID到导入方法<br>
$result = $this->importFromExcel($file['tmp_name'], $file['name'], $supplierId);
        </div>
        
        <h4>样式设计：</h4>
        <div class="code-box">
.supplier-selection {<br>
&nbsp;&nbsp;&nbsp;&nbsp;background: #f8f9fa;<br>
&nbsp;&nbsp;&nbsp;&nbsp;border: 1px solid #e9ecef;<br>
&nbsp;&nbsp;&nbsp;&nbsp;border-radius: 8px;<br>
&nbsp;&nbsp;&nbsp;&nbsp;padding: 20px;<br>
&nbsp;&nbsp;&nbsp;&nbsp;margin: 20px 0;<br>
&nbsp;&nbsp;&nbsp;&nbsp;transition: all 0.3s ease;<br>
}
        </div>
    </div>
    
    <div class="test-section">
        <h2>✅ 功能验证清单</h2>
        
        <h4>界面功能：</h4>
        <ul>
            <li>□ 供应商选择区域默认隐藏</li>
            <li>□ 选择文件后供应商区域显示</li>
            <li>□ 供应商下拉列表正确加载</li>
            <li>□ 按钮状态根据选择动态更新</li>
            <li>□ 移除文件后供应商区域隐藏</li>
        </ul>
        
        <h4>数据处理：</h4>
        <ul>
            <li>□ 供应商ID正确传递到后端</li>
            <li>□ 导入时使用选择的供应商</li>
            <li>□ 错误处理和验证正常</li>
        </ul>
        
        <h4>用户体验：</h4>
        <ul>
            <li>□ 界面美观统一</li>
            <li>□ 操作流程直观</li>
            <li>□ 提示信息清晰</li>
            <li>□ 响应速度快</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🚀 开始测试</h2>
        
        <p>现在您可以开始测试新增的供应商选择功能：</p>
        
        <p style="text-align: center;">
            <a href="../modules/purchase/index.php?action=import" class="btn btn-success" style="font-size: 1.1rem; padding: 12px 24px;">
                🎯 开始测试导入功能
            </a>
        </p>
        
        <h4>测试建议：</h4>
        <ol>
            <li>准备一个Excel文件（可以是之前的test.xlsx）</li>
            <li>确保数据库中有供应商数据</li>
            <li>按照上述测试步骤逐一验证</li>
            <li>测试不同的文件格式和供应商选择</li>
        </ol>
    </div>
</body>
</html>
