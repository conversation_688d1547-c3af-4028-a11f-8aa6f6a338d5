<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索栏单行显示测试</title>
    <link rel="stylesheet" href="includes/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { padding: 20px; background: #f5f7fa; }
        .test-section { margin-bottom: 40px; }
        .test-title { margin-bottom: 20px; color: #2d3748; font-size: 18px; font-weight: 600; }
    </style>
</head>
<body>
    <div class="test-section">
        <h1>搜索栏单行显示测试</h1>
        <p>以下是不同模块的搜索栏测试，应该在所有屏幕尺寸（除移动端外）都保持单行显示</p>
    </div>

    <!-- 食材管理搜索栏 -->
    <div class="test-section">
        <div class="test-title">食材管理搜索栏</div>
        <div class="search-bar">
            <form method="GET" class="search-bar-form">
                <div class="form-field text-input">
                    <label>搜索食材</label>
                    <input type="text" class="form-control" name="search" placeholder="输入食材名称...">
                </div>
                <div class="form-field select-input">
                    <label>分类</label>
                    <select class="form-control" name="category">
                        <option value="">全部分类</option>
                        <option value="1">蔬菜类</option>
                        <option value="2">肉类</option>
                    </select>
                </div>
                <div class="form-field select-input">
                    <label>状态</label>
                    <select class="form-control" name="status">
                        <option value="">全部状态</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                <a href="#" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </form>
        </div>
    </div>

    <!-- 供应商管理搜索栏 -->
    <div class="test-section">
        <div class="test-title">供应商管理搜索栏</div>
        <div class="search-bar">
            <form method="GET" class="search-bar-form">
                <div class="form-field text-input">
                    <label>供应商信息</label>
                    <input type="text" class="form-control" name="search" placeholder="搜索供应商名称或联系人">
                </div>
                <div class="form-field select-input">
                    <label>状态</label>
                    <select class="form-control" name="status">
                        <option value="">全部状态</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                <div class="form-field select-input">
                    <label>城市</label>
                    <select class="form-control" name="city">
                        <option value="">全部城市</option>
                        <option value="北京">北京</option>
                        <option value="上海">上海</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                <a href="#" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </form>
        </div>
    </div>

    <!-- 用户管理搜索栏 -->
    <div class="test-section">
        <div class="test-title">用户管理搜索栏</div>
        <div class="search-bar">
            <form method="GET" class="search-bar-form">
                <div class="form-field text-input">
                    <label>用户信息</label>
                    <input type="text" class="form-control" name="search" placeholder="用户名、姓名、邮箱或手机号...">
                </div>
                <div class="form-field select-input">
                    <label>角色</label>
                    <select class="form-control" name="role">
                        <option value="">全部角色</option>
                        <option value="admin">管理员</option>
                        <option value="user">普通用户</option>
                    </select>
                </div>
                <div class="form-field select-input">
                    <label>状态</label>
                    <select class="form-control" name="status">
                        <option value="">全部状态</option>
                        <option value="1">启用</option>
                        <option value="0">停用</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                <a href="#" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </form>
        </div>
    </div>

    <div class="test-section">
        <p><strong>测试说明：</strong></p>
        <ul>
            <li>在桌面屏幕（1200px以上）：所有搜索栏元素应该在一行内显示，具有足够的间距</li>
            <li>在中等屏幕（769px-1199px）：所有搜索栏元素应该紧凑地在一行内显示</li>
            <li>在小屏幕（768px以下）：搜索栏元素应该垂直排列</li>
        </ul>
    </div>
</body>
</html>