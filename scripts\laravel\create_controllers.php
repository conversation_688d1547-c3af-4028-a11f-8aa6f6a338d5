<?php

/**
 * 学校食堂食材出入库管理系统 - 控制器创建脚本
 * 根据开发文档自动生成Laravel控制器文件
 */

// 食材控制器
$ingredientController = '<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Ingredient;
use App\Models\IngredientCategory;
use App\Http\Requests\IngredientRequest;
use App\Http\Resources\IngredientResource;
use App\Services\IngredientService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class IngredientController extends Controller
{
    protected $ingredientService;

    public function __construct(IngredientService $ingredientService)
    {
        $this->ingredientService = $ingredientService;
    }

    /**
     * 获取食材列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = Ingredient::with([\'category\', \'creator\', \'inventory\']);

        // 分类筛选
        if ($request->has(\'category_id\')) {
            $query->byCategory($request->category_id);
        }

        // 状态筛选
        if ($request->has(\'status\')) {
            $query->where(\'status\', $request->status);
        }

        // 搜索
        if ($request->has(\'search\')) {
            $query->where(\'name\', \'like\', \'%\' . $request->search . \'%\');
        }

        $ingredients = $query->paginate($request->get(\'per_page\', 15));

        return response()->json([
            \'success\' => true,
            \'data\' => IngredientResource::collection($ingredients),
            \'meta\' => [
                \'total\' => $ingredients->total(),
                \'per_page\' => $ingredients->perPage(),
                \'current_page\' => $ingredients->currentPage(),
                \'last_page\' => $ingredients->lastPage()
            ]
        ]);
    }

    /**
     * 创建食材
     */
    public function store(IngredientRequest $request): JsonResponse
    {
        try {
            $ingredient = $this->ingredientService->create($request->validated());
            
            return response()->json([
                \'success\' => true,
                \'message\' => \'食材创建成功\',
                \'data\' => new IngredientResource($ingredient)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                \'success\' => false,
                \'message\' => \'食材创建失败：\' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取食材详情
     */
    public function show(Ingredient $ingredient): JsonResponse
    {
        $ingredient->load([\'category\', \'creator\', \'inventory\', \'inventoryBatches\']);
        
        return response()->json([
            \'success\' => true,
            \'data\' => new IngredientResource($ingredient)
        ]);
    }

    /**
     * 更新食材
     */
    public function update(IngredientRequest $request, Ingredient $ingredient): JsonResponse
    {
        try {
            $ingredient = $this->ingredientService->update($ingredient, $request->validated());
            
            return response()->json([
                \'success\' => true,
                \'message\' => \'食材更新成功\',
                \'data\' => new IngredientResource($ingredient)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                \'success\' => false,
                \'message\' => \'食材更新失败：\' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除食材
     */
    public function destroy(Ingredient $ingredient): JsonResponse
    {
        try {
            $this->ingredientService->delete($ingredient);
            
            return response()->json([
                \'success\' => true,
                \'message\' => \'食材删除成功\'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                \'success\' => false,
                \'message\' => \'食材删除失败：\' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 上传食材图片
     */
    public function uploadImage(Request $request, Ingredient $ingredient): JsonResponse
    {
        $request->validate([
            \'image\' => \'required|image|mimes:jpeg,png,jpg,webp|max:2048\'
        ]);

        try {
            $imagePath = $this->ingredientService->uploadImage($ingredient, $request->file(\'image\'));
            
            return response()->json([
                \'success\' => true,
                \'message\' => \'图片上传成功\',
                \'data\' => [
                    \'image_path\' => $imagePath,
                    \'image_url\' => asset(\'storage/\' . $imagePath)
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                \'success\' => false,
                \'message\' => \'图片上传失败：\' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取低库存食材
     */
    public function lowStock(): JsonResponse
    {
        $lowStockIngredients = $this->ingredientService->getLowStockIngredients();
        
        return response()->json([
            \'success\' => true,
            \'data\' => IngredientResource::collection($lowStockIngredients),
            \'count\' => $lowStockIngredients->count()
        ]);
    }

    /**
     * 批量导入食材
     */
    public function import(Request $request): JsonResponse
    {
        $request->validate([
            \'file\' => \'required|file|mimes:xlsx,xls,csv\'
        ]);

        try {
            $result = $this->ingredientService->importFromFile($request->file(\'file\'));
            
            return response()->json([
                \'success\' => true,
                \'message\' => \'食材导入成功\',
                \'data\' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                \'success\' => false,
                \'message\' => \'食材导入失败：\' . $e->getMessage()
            ], 500);
        }
    }
}';

// 入库控制器
$inboundController = '<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\InboundRecord;
use App\Http\Requests\InboundRequest;
use App\Http\Resources\InboundResource;
use App\Services\InboundService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class InboundController extends Controller
{
    protected $inboundService;

    public function __construct(InboundService $inboundService)
    {
        $this->inboundService = $inboundService;
    }

    /**
     * 获取入库记录列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = InboundRecord::with([\'ingredient\', \'supplier\', \'creator\', \'approver\']);

        // 日期范围筛选
        if ($request->has(\'start_date\') && $request->has(\'end_date\')) {
            $query->dateRange($request->start_date, $request->end_date);
        }

        // 食材筛选
        if ($request->has(\'ingredient_id\')) {
            $query->where(\'ingredient_id\', $request->ingredient_id);
        }

        // 供应商筛选
        if ($request->has(\'supplier_id\')) {
            $query->where(\'supplier_id\', $request->supplier_id);
        }

        // 状态筛选
        if ($request->has(\'status\')) {
            $query->where(\'status\', $request->status);
        }

        // 审核状态筛选
        if ($request->has(\'approved\')) {
            if ($request->approved) {
                $query->approved();
            } else {
                $query->whereNull(\'approved_by\');
            }
        }

        $records = $query->orderBy(\'created_at\', \'desc\')
                        ->paginate($request->get(\'per_page\', 15));

        return response()->json([
            \'success\' => true,
            \'data\' => InboundResource::collection($records),
            \'meta\' => [
                \'total\' => $records->total(),
                \'per_page\' => $records->perPage(),
                \'current_page\' => $records->currentPage(),
                \'last_page\' => $records->lastPage()
            ]
        ]);
    }

    /**
     * 创建入库记录
     */
    public function store(InboundRequest $request): JsonResponse
    {
        try {
            $record = $this->inboundService->create($request->validated());
            
            return response()->json([
                \'success\' => true,
                \'message\' => \'入库记录创建成功\',
                \'data\' => new InboundResource($record)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                \'success\' => false,
                \'message\' => \'入库记录创建失败：\' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取入库记录详情
     */
    public function show(InboundRecord $inboundRecord): JsonResponse
    {
        $inboundRecord->load([\'ingredient\', \'supplier\', \'creator\', \'approver\']);
        
        return response()->json([
            \'success\' => true,
            \'data\' => new InboundResource($inboundRecord)
        ]);
    }

    /**
     * 审核入库记录
     */
    public function approve(Request $request, InboundRecord $inboundRecord): JsonResponse
    {
        $request->validate([
            \'approved\' => \'required|boolean\',
            \'notes\' => \'nullable|string|max:500\'
        ]);

        try {
            $result = $this->inboundService->approve(
                $inboundRecord, 
                $request->approved, 
                $request->notes
            );
            
            return response()->json([
                \'success\' => true,
                \'message\' => $request->approved ? \'入库记录审核通过\' : \'入库记录审核拒绝\',
                \'data\' => new InboundResource($result)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                \'success\' => false,
                \'message\' => \'审核失败：\' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 上传称重图片
     */
    public function uploadWeightImages(Request $request, InboundRecord $inboundRecord): JsonResponse
    {
        $request->validate([
            \'images\' => \'required|array|max:3\',
            \'images.*\' => \'image|mimes:jpeg,png,jpg,webp|max:2048\'
        ]);

        try {
            $imagePaths = $this->inboundService->uploadWeightImages(
                $inboundRecord, 
                $request->file(\'images\')
            );
            
            return response()->json([
                \'success\' => true,
                \'message\' => \'称重图片上传成功\',
                \'data\' => [
                    \'image_paths\' => $imagePaths,
                    \'image_urls\' => array_map(function($path) {
                        return asset(\'storage/\' . $path);
                    }, $imagePaths)
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                \'success\' => false,
                \'message\' => \'图片上传失败：\' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 上传采购单据
     */
    public function uploadInvoice(Request $request, InboundRecord $inboundRecord): JsonResponse
    {
        $request->validate([
            \'invoice\' => \'required|file|mimes:pdf,jpeg,png,jpg|max:5120\'
        ]);

        try {
            $invoicePath = $this->inboundService->uploadInvoice(
                $inboundRecord, 
                $request->file(\'invoice\')
            );
            
            return response()->json([
                \'success\' => true,
                \'message\' => \'采购单据上传成功\',
                \'data\' => [
                    \'invoice_path\' => $invoicePath,
                    \'invoice_url\' => asset(\'storage/\' . $invoicePath)
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                \'success\' => false,
                \'message\' => \'单据上传失败：\' . $e->getMessage()
            ], 500);
        }
    }
}';

// 创建文件
file_put_contents('app/Http/Controllers/Api/IngredientController.php', $ingredientController);
file_put_contents('app/Http/Controllers/Api/InboundController.php', $inboundController);

echo "✅ 控制器文件创建完成！\n";
echo "已创建以下控制器：\n";
echo "- IngredientController (食材管理)\n";
echo "- InboundController (入库管理)\n";
echo "\n继续创建其他控制器...\n";
