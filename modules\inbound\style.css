/* 入库管理模块样式 */

/* 食材信息 */
.ingredient-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.ingredient-unit {
    font-size: 12px;
    color: #718096;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

/* 数量徽章 */
.quantity-badge {
    background: #e6fffa;
    color: #234e52;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
}

/* 批次号 */
.batch-number {
    background: #f7fafc;
    color: #4a5568;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    border: 1px solid #e2e8f0;
}

/* 保质期 */
.expiry-date {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.days-left {
    font-size: 11px;
    font-weight: 600;
}

/* 日期时间信息 */
.datetime-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.datetime-info .date {
    font-weight: 600;
    color: #2d3748;
}

.datetime-info .time {
    font-size: 12px;
    color: #718096;
}

/* 统计行 */
.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stat-icon.bg-primary { background: #4299e1; }
.stat-icon.bg-success { background: #38a169; }
.stat-icon.bg-info { background: #4299e1; }

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #718096;
    margin-top: 4px;
}

/* 搜索表单 */
.search-box .form-row {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1fr 1fr auto;
    gap: 15px;
    align-items: end;
}

/* 表格样式增强 */
.table th {
    background: #dbeafe;
    border-bottom: 2px solid #bfdbfe;
    font-weight: 600;
    color: #1e40af;
    padding: 12px 8px;
    font-size: 13px;
}

.table td {
    padding: 12px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #e2e8f0;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* 状态颜色 */
.text-danger {
    color: #e53e3e !important;
}

.text-warning {
    color: #d69e2e !important;
}

.text-success {
    color: #38a169 !important;
}

.text-primary {
    color: #4299e1 !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .search-box .form-row {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 10px;
    }
    
    .search-box .form-group:nth-child(4),
    .search-box .form-group:nth-child(5) {
        grid-column: span 3;
    }
}

@media (max-width: 768px) {
    .stats-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .search-box .form-row {
        grid-template-columns: 1fr;
    }
    
    .search-box .form-group {
        grid-column: span 1 !important;
    }
    
    /* 表格在移动端的处理 */
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 1000px;
    }
}

/* 动画效果 */
.stat-item {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
    color: #4299e1;
}

.empty-state h5 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #2d3748;
}

.empty-state p {
    font-size: 16px;
    margin-bottom: 20px;
}

/* 分隔线样式 */
.divider-section {
    margin: 30px 0;
    text-align: center;
}

.divider-line {
    position: relative;
    height: 1px;
    background: #e2e8f0;
    margin: 20px 0;
}

.divider-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 0 15px;
    color: #718096;
    font-size: 14px;
    font-weight: 500;
}

/* 自建采购单样式 */
.items-add-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.items-add-section h4 {
    color: #495057;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.add-item-form {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.added-items-list {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
}

.added-items-list .table {
    margin-bottom: 0;
}

#no_items_added {
    margin: 0;
    text-align: center;
}

/* 新采购单选项样式 */
#purchase_order_select option[value="create_new"] {
    background-color: #e6f3ff !important;
    font-weight: bold;
    color: #007cba;
}

/* 确认按钮样式 */
#confirm_new_order:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#confirm_new_order:not(:disabled) {
    background: #28a745;
    border-color: #28a745;
}

#confirm_new_order:not(:disabled):hover {
    background: #218838;
    border-color: #1e7e34;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* 商品行删除按钮 */
.remove-item-btn {
    background: #dc3545;
    border: none;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.remove-item-btn:hover {
    background: #c82333;
    transform: scale(1.05);
}

/* 商品称重照片样式 */
.weight-photo-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.weight-photo-input {
    display: none;
}

.weight-photo-preview {
    width: 80px;
    height: 60px;
    border: 2px dashed #dee2e6;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.weight-photo-preview:hover {
    border-color: #007cba;
    background: #e6f3ff;
}

.weight-photo-preview .photo-placeholder {
    text-align: center;
    color: #6c757d;
    font-size: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.weight-photo-preview .photo-placeholder i {
    font-size: 16px;
    margin-bottom: 2px;
}

.weight-photo-preview .photo-placeholder span {
    font-size: 9px;
    line-height: 1;
}

.weight-photo-preview.has-photo {
    border-color: #28a745;
    background: #d4edda;
}

.weight-photo-preview.has-photo .photo-placeholder {
    color: #155724;
}

.weight-photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

/* 照片预览状态 */
.weight-photo-preview.uploading {
    border-color: #ffc107;
    background: #fff3cd;
}

.weight-photo-preview.uploading .photo-placeholder {
    color: #856404;
}

.weight-photo-preview.error {
    border-color: #dc3545;
    background: #f8d7da;
}

.weight-photo-preview.error .photo-placeholder {
    color: #721c24;
}

/* 照片操作按钮 */
.weight-photo-preview .photo-actions {
    position: absolute;
    top: 2px;
    right: 2px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.weight-photo-preview:hover .photo-actions {
    opacity: 1;
}

.weight-photo-preview .photo-actions .btn {
    padding: 2px 4px;
    font-size: 10px;
    line-height: 1;
    border-radius: 2px;
}

/* 表格响应式优化 */
@media (max-width: 1200px) {
    .items-table th:nth-child(7),
    .items-table td:nth-child(7) {
        display: none;
    }

    .items-table th:nth-child(1) { width: 25%; }
    .items-table th:nth-child(2) { width: 10%; }
    .items-table th:nth-child(3) { width: 15%; }
    .items-table th:nth-child(4) { width: 15%; }
    .items-table th:nth-child(5) { width: 15%; }
    .items-table th:nth-child(6) { width: 15%; }
    .items-table th:nth-child(8) { width: 5%; }
}

@media (max-width: 768px) {
    .weight-photo-preview {
        width: 60px;
        height: 45px;
    }

    .weight-photo-preview .photo-placeholder i {
        font-size: 12px;
    }

    .weight-photo-preview .photo-placeholder span {
        font-size: 8px;
    }
}
