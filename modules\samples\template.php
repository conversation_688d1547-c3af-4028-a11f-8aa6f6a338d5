<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<!-- 确保样式被加载 -->
<link rel="stylesheet" href="style.css">

<!-- Font Awesome 图标库 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <div class="header-left">
                <h1>
                    <i class="fas fa-vial"></i>
                    食品留样管理
                </h1>
                <p class="header-subtitle">食品安全留样记录与管理</p>
            </div>
            <div class="header-actions">
                <a href="index.php?action=create" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    新增留样
                </a>
                <button type="button" class="btn btn-success" onclick="batchProcess()">
                    <i class="fas fa-tasks"></i>
                    批量处理
                </button>
                <button type="button" class="btn btn-info" onclick="exportData()">
                    <i class="fas fa-download"></i>
                    导出数据
                </button>
                <button type="button" class="btn btn-warning" onclick="printLabels()">
                    <i class="fas fa-print"></i>
                    打印标签
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-vial"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= number_format($data['stats']['total_samples'] ?? 0) ?></div>
                    <div class="stat-label">总留样数</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= number_format($data['stats']['active_samples'] ?? 0) ?></div>
                    <div class="stat-label">有效留样</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= number_format($data['stats']['expired_samples'] ?? 0) ?></div>
                    <div class="stat-label">已过期</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= number_format($data['stats']['today_samples'] ?? 0) ?></div>
                    <div class="stat-label">今日留样</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-hourglass-half"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= number_format($data['stats']['expiring_soon'] ?? 0) ?></div>
                    <div class="stat-label">48小时内到期</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-thermometer-half"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= number_format($data['stats']['cold_storage'] ?? 0) ?></div>
                    <div class="stat-label">冷藏留样</div>
                </div>
            </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="search-bar">
            <form method="GET" action="index.php" class="search-bar-form">
                <div class="form-field text-input">
                    <label>搜索内容</label>
                    <input type="text" name="search" placeholder="食材名称、留样编号或批次号..."
                           value="<?= htmlspecialchars($data['search'] ?? '') ?>" class="form-control">
                </div>

                <div class="form-field select-input">
                    <label>餐次</label>
                    <select name="meal_type" class="form-control">
                        <option value="">全部餐次</option>
                        <option value="breakfast" <?= ($data['meal_type'] ?? '') === 'breakfast' ? 'selected' : '' ?>>早餐</option>
                        <option value="lunch" <?= ($data['meal_type'] ?? '') === 'lunch' ? 'selected' : '' ?>>午餐</option>
                        <option value="dinner" <?= ($data['meal_type'] ?? '') === 'dinner' ? 'selected' : '' ?>>晚餐</option>
                        <option value="other" <?= ($data['meal_type'] ?? '') === 'other' ? 'selected' : '' ?>>其他</option>
                    </select>
                </div>

                <div class="form-field select-input">
                    <label>状态</label>
                    <select name="status" class="form-control">
                        <option value="">全部状态</option>
                        <option value="1" <?= ($data['status'] ?? '') === '1' ? 'selected' : '' ?>>有效</option>
                        <option value="0" <?= ($data['status'] ?? '') === '0' ? 'selected' : '' ?>>已处理</option>
                    </select>
                </div>

                <div class="form-field date-input">
                    <label>开始日期</label>
                    <input type="date" name="date_from"
                           value="<?= htmlspecialchars($data['date_from'] ?? '') ?>" class="form-control">
                </div>

                <div class="form-field date-input">
                    <label>结束日期</label>
                    <input type="date" name="date_to"
                           value="<?= htmlspecialchars($data['date_to'] ?? '') ?>" class="form-control">
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    搜索
                </button>

                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-sync-alt"></i>
                    重置
                </a>
            </form>
        </div>

        <!-- 留样记录列表 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <i class="iconfont icon-liebiao"></i>
                    留样记录列表
                </div>
                <div class="card-actions">
                    <label class="select-all-label">
                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        全选
                    </label>
                    <span class="selected-count">已选择 <span id="selectedCount">0</span> 项</span>
                </div>
            </div>
            <div class="card-body">
            
            <table class="table">
                <thead>
                    <tr>
                        <th width="40px">
                            <input type="checkbox" id="selectAllHeader" onchange="toggleSelectAll()">
                        </th>
                        <th>留样信息</th>
                        <th>食材信息</th>
                        <th>餐次</th>
                        <th>留样时间</th>
                        <th>过期日期</th>
                        <th>存储信息</th>
                        <th>责任人</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($data['samples'])): ?>
                        <?php foreach ($data['samples'] as $sample): ?>
                            <tr class="<?= strtotime($sample['expiration_date']) < time() && $sample['sample_status'] == 1 ? 'row-expired' : '' ?>">
                                <td>
                                    <input type="checkbox" class="row-checkbox" value="<?= $sample['id'] ?>" onchange="updateSelectedCount()">
                                </td>
                                <td>
                                    <div class="sample-info">
                                        <div class="sample-code">
                                            <i class="iconfont icon-tiaoxingma"></i>
                                            <?= htmlspecialchars($sample['sample_code']) ?>
                                        </div>
                                        <div class="batch-number">批次: <?= htmlspecialchars($sample['batch_number']) ?></div>
                                        <div class="sample-quantity">
                                            <i class="iconfont icon-zhongliang"></i>
                                            <?= $sample['sample_quantity'] ?> <?= htmlspecialchars($sample['sample_unit']) ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="ingredient-info">
                                        <div class="ingredient-name">
                                            <i class="iconfont icon-shucai"></i>
                                            <?= htmlspecialchars($sample['ingredient_name'] ?? '未知食材') ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $mealTypes = [
                                        'breakfast' => '<span class="meal-badge meal-breakfast"><i class="iconfont icon-taiyang"></i> 早餐</span>',
                                        'lunch' => '<span class="meal-badge meal-lunch"><i class="iconfont icon-taiyang"></i> 午餐</span>',
                                        'dinner' => '<span class="meal-badge meal-dinner"><i class="iconfont icon-yueliang"></i> 晚餐</span>',
                                        'other' => '<span class="meal-badge meal-other"><i class="iconfont icon-shucai"></i> 其他</span>'
                                    ];
                                    echo $mealTypes[$sample['meal_type']] ?? '未知';
                                    ?>
                                </td>
                                <td>
                                    <div class="time-info">
                                        <div class="sample-time">
                                            <i class="fas fa-clock"></i>
                                            <?= date('m-d H:i', strtotime($sample['sample_time'])) ?>
                                        </div>
                                        <div class="meal-date">用餐: <?= date('m-d', strtotime($sample['meal_date'])) ?></div>
                                    </div>
                                </td>
                                <td>
                                    <div class="expiration-info <?= strtotime($sample['expiration_date']) < time() && $sample['sample_status'] == 1 ? 'expired' : (strtotime($sample['expiration_date']) < time() + 48*3600 ? 'expiring-soon' : '') ?>">
                                        <i class="fas fa-calendar-times"></i>
                                        <?= date('m-d', strtotime($sample['expiration_date'])) ?>
                                        <?php if (strtotime($sample['expiration_date']) < time() && $sample['sample_status'] == 1): ?>
                                            <span class="expire-label">已过期</span>
                                        <?php elseif (strtotime($sample['expiration_date']) < time() + 48*3600): ?>
                                            <span class="expire-label">即将过期</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="storage-info">
                                        <?php if (!empty($sample['storage_location'])): ?>
                                            <div class="storage-location">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <?= htmlspecialchars($sample['storage_location']) ?>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($sample['storage_temperature'])): ?>
                                            <div class="storage-temperature">
                                                <i class="fas fa-thermometer-half"></i>
                                                <?= htmlspecialchars($sample['storage_temperature']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="responsible-person">
                                        <i class="fas fa-user"></i>
                                        <?= htmlspecialchars($sample['responsible_person']) ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($sample['sample_status'] == 1): ?>
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check-circle"></i>
                                            有效
                                        </span>
                                    <?php else: ?>
                                        <span class="status-badge status-inactive">
                                            <i class="fas fa-times-circle"></i>
                                            已处理
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="index.php?action=view&id=<?= $sample['id'] ?>" class="btn btn-sm btn-info" title="查看详情">
                                            <i class="iconfont icon-chakan"></i>
                                        </a>
                                        <a href="index.php?action=edit&id=<?= $sample['id'] ?>" class="btn btn-sm btn-primary" title="编辑">
                                            <i class="iconfont icon-bianji"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-success" onclick="printLabel(<?= $sample['id'] ?>)" title="打印标签">
                                            <i class="iconfont icon-dayin"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-warning" onclick="generateQR(<?= $sample['id'] ?>)" title="生成二维码">
                                            <i class="iconfont icon-erweima"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="deleteSample(<?= $sample['id'] ?>)" title="删除">
                                            <i class="iconfont icon-shanchu"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="10" class="text-center empty-state">
                                <div class="empty-icon">
                                    <i class="iconfont icon-kongbai"></i>
                                </div>
                                <div class="empty-text">暂无留样记录</div>
                                <div class="empty-action">
                                    <a href="index.php?action=create" class="btn btn-primary btn-sm">
                                        <i class="iconfont icon-tianjia"></i>
                                        立即创建留样记录
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            </div>
        </div>

        <!-- 分页 -->
        <?php if (!empty($data['samples']) && $data['totalPages'] > 1): ?>
        <div class="card">
            <div class="card-body">
                <nav aria-label="分页导航">
                    <ul class="pagination">
                        <?php if ($data['currentPage'] > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $data['currentPage'] - 1 ?>&search=<?= urlencode($data['search'] ?? '') ?>&meal_type=<?= urlencode($data['meal_type'] ?? '') ?>&date_from=<?= urlencode($data['date_from'] ?? '') ?>&date_to=<?= urlencode($data['date_to'] ?? '') ?>&status=<?= urlencode($data['status'] ?? '') ?>" aria-label="上一页">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $data['currentPage'] - 2); $i <= min($data['totalPages'], $data['currentPage'] + 2); $i++): ?>
                            <li class="page-item <?= $i == $data['currentPage'] ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($data['search'] ?? '') ?>&meal_type=<?= urlencode($data['meal_type'] ?? '') ?>&date_from=<?= urlencode($data['date_from'] ?? '') ?>&date_to=<?= urlencode($data['date_to'] ?? '') ?>&status=<?= urlencode($data['status'] ?? '') ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($data['currentPage'] < $data['totalPages']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $data['currentPage'] + 1 ?>&search=<?= urlencode($data['search'] ?? '') ?>&meal_type=<?= urlencode($data['meal_type'] ?? '') ?>&date_from=<?= urlencode($data['date_from'] ?? '') ?>&date_to=<?= urlencode($data['date_to'] ?? '') ?>&status=<?= urlencode($data['status'] ?? '') ?>" aria-label="下一页">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <div class="pagination-info">
                    显示第 <?= (($data['currentPage'] - 1) * 20) + 1 ?> - <?= min($data['currentPage'] * 20, $data['totalRecords'] ?? 0) ?> 条，共 <?= $data['totalRecords'] ?? 0 ?> 条记录
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- 批量处理模态框 -->
<div id="batchModal" class="modal" style="display: none;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    <i class="fas fa-tasks"></i>
                    批量处理留样记录
                </h4>
                <button type="button" class="btn-close" onclick="closeModal('batchModal')" aria-label="关闭">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="batchAction" class="form-label">选择操作类型：</label>
                    <select id="batchAction" class="form-control">
                        <option value="">请选择操作</option>
                        <option value="mark_processed">标记为已处理</option>
                        <option value="mark_active">标记为有效</option>
                        <option value="extend_expiry">延长48小时保质期</option>
                        <option value="delete">删除选中记录</option>
                    </select>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    批量操作将应用到所有选中的留样记录，请谨慎操作。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="confirmBatchProcess()">
                    <i class="fas fa-check"></i>
                    确认执行
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal('batchModal')">
                    <i class="fas fa-times"></i>
                    取消
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 二维码模态框 -->
<div id="qrModal" class="modal" style="display: none;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    <i class="fas fa-qrcode"></i>
                    留样记录二维码
                </h4>
                <button type="button" class="btn-close" onclick="closeModal('qrModal')" aria-label="关闭">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="qrContainer" class="text-center">
                    <!-- 二维码将在这里生成 -->
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i>
                    扫描二维码可快速查看留样记录信息
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('qrModal')">
                    <i class="fas fa-times"></i>
                    关闭
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 批量选择相关功能
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const selectAllHeader = document.getElementById('selectAllHeader');
    const checkboxes = document.querySelectorAll('.row-checkbox');
    
    // 同步两个全选框的状态
    selectAll.checked = selectAllHeader.checked;
    selectAllHeader.checked = selectAll.checked;
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedCount();
}

function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.row-checkbox:checked');
    const count = checkboxes.length;
    document.getElementById('selectedCount').textContent = count;
    
    // 更新全选框状态
    const allCheckboxes = document.querySelectorAll('.row-checkbox');
    const selectAll = document.getElementById('selectAll');
    const selectAllHeader = document.getElementById('selectAllHeader');
    
    selectAll.checked = count === allCheckboxes.length && count > 0;
    selectAllHeader.checked = selectAll.checked;
    selectAll.indeterminate = count > 0 && count < allCheckboxes.length;
    selectAllHeader.indeterminate = selectAll.indeterminate;
}

// 批量处理功能
function batchProcess() {
    const selected = getSelectedIds();
    if (selected.length === 0) {
        alert('请选择要处理的记录');
        return;
    }
    
    const modal = document.getElementById('batchModal');
    modal.style.display = 'flex';
}

function getSelectedIds() {
    const checkboxes = document.querySelectorAll('.row-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// 批量处理确认
function confirmBatchProcess() {
    const selected = getSelectedIds();
    const action = document.getElementById('batchAction').value;
    
    if (!action) {
        alert('请选择操作类型');
        return;
    }
    
    if (!confirm(`确定要对选中的 ${selected.length} 条记录执行"${document.querySelector('#batchAction option:checked').text}"操作吗？`)) {
        return;
    }
    
    fetch('index.php?action=batch_process', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `ids=${selected.join(',')}&batch_action=${action}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            alert('批量处理成功');
            location.reload();
        } else {
            alert('批量处理失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('批量处理失败: ' + error.message);
    });
    
    closeModal('batchModal');
}

// 导出数据
function exportData() {
    const selected = getSelectedIds();
    let url = 'index.php?action=export';
    
    if (selected.length > 0) {
        if (confirm(`是否只导出选中的 ${selected.length} 条记录？点击"确定"导出选中记录，点击"取消"导出全部记录。`)) {
            url += '&ids=' + selected.join(',');
        }
    }
    
    window.open(url, '_blank');
}

// 批量打印标签
function printLabels() {
    const selected = getSelectedIds();
    if (selected.length === 0) {
        alert('请选择要打印的记录');
        return;
    }
    
    const url = `index.php?action=print_labels&ids=${selected.join(',')}`;
    window.open(url, '_blank', 'width=800,height=600');
}

// 单个打印标签
function printLabel(id) {
    const url = `index.php?action=print_label&id=${id}`;
    window.open(url, '_blank', 'width=800,height=600');
}

// 生成二维码
function generateQR(id) {
    const modal = document.getElementById('qrModal');
    const qrContainer = document.getElementById('qrContainer');
    
    // 清空之前的二维码
    qrContainer.innerHTML = '<div class="loading">正在生成二维码...</div>';
    
    modal.style.display = 'flex';
    
    // 获取留样信息并生成二维码
    fetch(`index.php?action=get_sample_info&id=${id}`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            const qrData = {
                code: data.sample.sample_code,
                ingredient: data.sample.ingredient_name,
                batch: data.sample.batch_number,
                date: data.sample.meal_date,
                expire: data.sample.expiration_date
            };
            
            // 使用qrcode.js生成二维码
            qrContainer.innerHTML = '';
            QRCode.toCanvas(qrContainer, JSON.stringify(qrData), {
                width: 200,
                height: 200,
                margin: 2,
            }, function (error) {
                if (error) {
                    qrContainer.innerHTML = '<div class="error">二维码生成失败</div>';
                } else {
                    // 添加下载按钮
                    const canvas = qrContainer.querySelector('canvas');
                    const downloadBtn = document.createElement('a');
                    downloadBtn.href = canvas.toDataURL();
                    downloadBtn.download = `留样二维码_${data.sample.sample_code}.png`;
                    downloadBtn.className = 'btn btn-primary btn-sm mt-2';
                    downloadBtn.innerHTML = '<i class="fas fa-download"></i> 下载二维码';
                    qrContainer.appendChild(downloadBtn);
                }
            });
        } else {
            qrContainer.innerHTML = '<div class="error">获取留样信息失败</div>';
        }
    })
    .catch(error => {
        qrContainer.innerHTML = '<div class="error">网络错误</div>';
    });
}

// 删除留样记录
function deleteSample(id) {
    if (confirm('确定要删除这条留样记录吗？')) {
        fetch('index.php?action=delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id=' + id
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('删除成功');
                location.reload();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error.message);
        });
    }
}

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// 点击模态框背景关闭
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
    }
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化选择计数
    updateSelectedCount();
    
    // 检查过期提醒
    checkExpirationAlerts();
    
    console.log('食品留样管理模块已加载');
});

// 检查过期提醒
function checkExpirationAlerts() {
    const expiredRows = document.querySelectorAll('.row-expired');
    const expiringSoonRows = document.querySelectorAll('.expiring-soon');
    
    if (expiredRows.length > 0) {
        const message = `发现 ${expiredRows.length} 条已过期的留样记录，请及时处理！`;
        showNotification(message, 'error');
    } else if (expiringSoonRows.length > 0) {
        const message = `有 ${expiringSoonRows.length} 条留样记录将在48小时内过期，请注意！`;
        showNotification(message, 'warning');
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button type="button" class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // 自动隐藏
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>