<?php
// 学校食堂食材出入库管理系统 - MySQLi版本（不依赖PDO）

// 数据库配置
$host = '************';
$port = 3306;
$dbname = 'sc';
$username = 'sc';
$password = 'pw5K4SsM7kZsjdxy';

// 测试数据库连接
$mysqli = null;
$connection_status = "";
$connection_class = "";

if (extension_loaded('mysqli')) {
    try {
        $mysqli = new mysqli($host, $username, $password, $dbname, $port);
        
        if ($mysqli->connect_error) {
            $connection_status = "数据库连接失败: " . $mysqli->connect_error;
            $connection_class = "error";
            $mysqli = null;
        } else {
            $connection_status = "数据库连接成功 (使用MySQLi)";
            $connection_class = "success";
            $mysqli->set_charset("utf8");
        }
    } catch(Exception $e) {
        $connection_status = "数据库连接失败: " . $e->getMessage();
        $connection_class = "error";
        $mysqli = null;
    }
} else {
    $connection_status = "MySQLi扩展未安装";
    $connection_class = "error";
}

// 获取当前页面
$page = $_GET['page'] ?? 'home';

// 辅助函数：安全执行查询
function safe_query($mysqli, $sql) {
    if (!$mysqli) return false;
    $result = $mysqli->query($sql);
    if (!$result) {
        return false;
    }
    return $result;
}

// 辅助函数：获取查询结果
function fetch_all($result) {
    if (!$result) return [];
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    return $data;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学校食堂食材出入库管理系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }
        .header-logo-img {
            width: 60px;
            height: 60px;
            object-fit: contain;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        .header-logo-text {
            text-align: left;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .nav {
            background: #34495e;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }
        .nav a {
            display: block;
            padding: 15px 25px;
            color: white;
            text-decoration: none;
            transition: background 0.3s;
            border-right: 1px solid #2c3e50;
        }
        .nav a:hover, .nav a.active {
            background: #2c3e50;
        }
        .content {
            padding: 30px;
            min-height: 400px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        .btn-warning { background: #f39c12; }
        .btn-warning:hover { background: #e67e22; }
        .btn-danger { background: #e74c3c; }
        .btn-danger:hover { background: #c0392b; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-logo">
                <img src="logo.png" alt="系统Logo" class="header-logo-img">
                <div class="header-logo-text">
                    <h1>学校食堂食材出入库管理系统</h1>
                    <p>School Canteen Ingredient Management System v2.0 (MySQLi版本)</p>
                </div>
            </div>
        </div>

        <div class="nav">
            <a href="?page=home" class="<?php echo $page == 'home' ? 'active' : ''; ?>">🏠 首页</a>
            <a href="?page=ingredients" class="<?php echo $page == 'ingredients' ? 'active' : ''; ?>">🥬 食材管理</a>
            <a href="?page=suppliers" class="<?php echo $page == 'suppliers' ? 'active' : ''; ?>">🏪 供应商</a>
            <a href="?page=inbound" class="<?php echo $page == 'inbound' ? 'active' : ''; ?>">📥 入库记录</a>
            <a href="?page=inventory" class="<?php echo $page == 'inventory' ? 'active' : ''; ?>">📦 库存查询</a>
            <a href="?page=reports" class="<?php echo $page == 'reports' ? 'active' : ''; ?>">📊 报表统计</a>
            <a href="create-tables.php" class="btn-warning">🔧 创建数据表</a>
        </div>

        <div class="content">
            <div class="status <?php echo $connection_class; ?>">
                <strong>数据库连接状态:</strong> <?php echo $connection_status; ?>
            </div>

            <?php if (!extension_loaded('mysqli')): ?>
                <div class="error">
                    <h3>❌ MySQLi扩展未安装</h3>
                    <p>请按以下步骤安装MySQLi扩展：</p>
                    <ol style="margin: 15px 0; padding-left: 30px;">
                        <li>运行 <code>install-pdo.bat</code> 脚本</li>
                        <li>或者安装XAMPP：<a href="https://www.apachefriends.org/" target="_blank">https://www.apachefriends.org/</a></li>
                        <li>或者编辑php.ini文件，取消注释：<code>extension=mysqli</code></li>
                    </ol>
                    <a href="install-pdo.bat" class="btn btn-danger">运行安装脚本</a>
                    <a href="check-php.php" class="btn">检查PHP环境</a>
                </div>
            <?php endif; ?>

            <?php
            switch($page) {
                case 'ingredients':
                    echo "<h2>🥬 食材管理</h2>";
                    if($mysqli) {
                        $result = safe_query($mysqli, "SELECT i.*, c.name as category_name FROM ingredients i LEFT JOIN ingredient_categories c ON i.category_id = c.id ORDER BY i.id DESC LIMIT 20");
                        if ($result && $result->num_rows > 0) {
                            echo "<table>";
                            echo "<tr><th>ID</th><th>食材名称</th><th>分类</th><th>单位</th><th>保质期(天)</th><th>最低库存</th><th>状态</th></tr>";
                            while($row = $result->fetch_assoc()) {
                                $status = $row['status'] ? '<span style="color: green;">✅ 启用</span>' : '<span style="color: red;">❌ 停用</span>';
                                echo "<tr>";
                                echo "<td>{$row['id']}</td>";
                                echo "<td>{$row['name']}</td>";
                                echo "<td>" . ($row['category_name'] ?? '未分类') . "</td>";
                                echo "<td>{$row['unit']}</td>";
                                echo "<td>{$row['shelf_life_days']}</td>";
                                echo "<td>{$row['min_stock']}</td>";
                                echo "<td>$status</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                        } else {
                            echo '<div class="warning">暂无食材数据。<a href="create-tables.php" class="btn">创建数据表</a></div>';
                        }
                    }
                    break;

                case 'suppliers':
                    echo "<h2>🏪 供应商管理</h2>";
                    if($mysqli) {
                        $result = safe_query($mysqli, "SELECT * FROM suppliers ORDER BY id DESC LIMIT 20");
                        if ($result && $result->num_rows > 0) {
                            echo "<table>";
                            echo "<tr><th>ID</th><th>供应商名称</th><th>联系人</th><th>电话</th><th>地址</th><th>评级</th><th>状态</th></tr>";
                            while($row = $result->fetch_assoc()) {
                                $rating = str_repeat('⭐', $row['rating']);
                                $status = $row['status'] ? '<span style="color: green;">✅ 正常</span>' : '<span style="color: red;">❌ 黑名单</span>';
                                echo "<tr>";
                                echo "<td>{$row['id']}</td>";
                                echo "<td>{$row['name']}</td>";
                                echo "<td>{$row['contact_person']}</td>";
                                echo "<td>{$row['phone']}</td>";
                                echo "<td>{$row['address']}</td>";
                                echo "<td>$rating</td>";
                                echo "<td>$status</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                        } else {
                            echo '<div class="warning">暂无供应商数据。<a href="create-tables.php" class="btn">创建数据表</a></div>';
                        }
                    }
                    break;

                case 'inbound':
                    echo "<h2>📥 入库记录</h2>";
                    if($mysqli) {
                        $result = safe_query($mysqli, "SELECT ir.*, i.name as ingredient_name, s.name as supplier_name FROM inbound_records ir LEFT JOIN ingredients i ON ir.ingredient_id = i.id LEFT JOIN suppliers s ON ir.supplier_id = s.id ORDER BY ir.created_at DESC LIMIT 20");
                        if ($result && $result->num_rows > 0) {
                            echo "<table>";
                            echo "<tr><th>批次号</th><th>食材</th><th>供应商</th><th>数量</th><th>单价</th><th>总价</th><th>过期日期</th><th>状态</th></tr>";
                            while($row = $result->fetch_assoc()) {
                                $total = $row['quantity'] * $row['unit_price'];
                                $status = $row['status'] ? '✅ 正常' : '❌ 作废';
                                echo "<tr>";
                                echo "<td>{$row['batch_number']}</td>";
                                echo "<td>" . ($row['ingredient_name'] ?? '未知') . "</td>";
                                echo "<td>" . ($row['supplier_name'] ?? '未知') . "</td>";
                                echo "<td>{$row['quantity']}</td>";
                                echo "<td>¥{$row['unit_price']}</td>";
                                echo "<td>¥" . number_format($total, 2) . "</td>";
                                echo "<td>{$row['expired_at']}</td>";
                                echo "<td>$status</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                        } else {
                            echo '<div class="warning">暂无入库记录。<a href="create-tables.php" class="btn">创建数据表</a></div>';
                        }
                    }
                    break;

                case 'inventory':
                    echo "<h2>📦 库存查询</h2>";
                    if($mysqli) {
                        $result = safe_query($mysqli, "SELECT inv.*, i.name as ingredient_name, i.unit, i.min_stock FROM inventory inv LEFT JOIN ingredients i ON inv.ingredient_id = i.id WHERE inv.current_quantity >= 0 ORDER BY inv.current_quantity ASC LIMIT 20");
                        if ($result && $result->num_rows > 0) {
                            echo "<table>";
                            echo "<tr><th>食材名称</th><th>当前库存</th><th>单位</th><th>库存价值</th><th>最低库存</th><th>库存状态</th></tr>";
                            while($row = $result->fetch_assoc()) {
                                $is_low = $row['current_quantity'] <= $row['min_stock'];
                                $status = $is_low ? '<span style="color: red;">⚠️ 低库存</span>' : '<span style="color: green;">✅ 正常</span>';
                                echo "<tr>";
                                echo "<td>" . ($row['ingredient_name'] ?? '未知') . "</td>";
                                echo "<td>{$row['current_quantity']}</td>";
                                echo "<td>" . ($row['unit'] ?? '') . "</td>";
                                echo "<td>¥" . number_format($row['total_value'], 2) . "</td>";
                                echo "<td>{$row['min_stock']}</td>";
                                echo "<td>$status</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                        } else {
                            echo '<div class="warning">暂无库存数据。<a href="create-tables.php" class="btn">创建数据表</a></div>';
                        }
                    }
                    break;

                case 'reports':
                    echo "<h2>📊 报表统计</h2>";
                    if($mysqli) {
                        // 统计数据
                        $stats = [];
                        $result = safe_query($mysqli, "SELECT COUNT(*) as count FROM ingredients");
                        $stats['ingredients'] = $result ? $result->fetch_assoc()['count'] : 0;
                        
                        $result = safe_query($mysqli, "SELECT COUNT(*) as count FROM suppliers");
                        $stats['suppliers'] = $result ? $result->fetch_assoc()['count'] : 0;
                        
                        $result = safe_query($mysqli, "SELECT COUNT(*) as count FROM inbound_records WHERE status = 1");
                        $stats['inbound'] = $result ? $result->fetch_assoc()['count'] : 0;
                        
                        $result = safe_query($mysqli, "SELECT SUM(total_value) as total FROM inventory");
                        $stats['inventory_value'] = $result ? ($result->fetch_assoc()['total'] ?? 0) : 0;
                        
                        echo '<div class="stats">';
                        echo '<div class="stat-card"><div class="stat-number">' . $stats['ingredients'] . '</div><div>食材种类</div></div>';
                        echo '<div class="stat-card"><div class="stat-number">' . $stats['suppliers'] . '</div><div>供应商数量</div></div>';
                        echo '<div class="stat-card"><div class="stat-number">' . $stats['inbound'] . '</div><div>入库记录</div></div>';
                        echo '<div class="stat-card"><div class="stat-number">¥' . number_format($stats['inventory_value'], 0) . '</div><div>库存总价值</div></div>';
                        echo '</div>';
                    }
                    break;

                default:
                    echo "<h2>🏠 系统概览</h2>";
                    echo '<div class="info">';
                    echo '<h3>系统信息</h3>';
                    echo '<p><strong>PHP版本:</strong> ' . PHP_VERSION . '</p>';
                    echo '<p><strong>服务器时间:</strong> ' . date('Y-m-d H:i:s') . '</p>';
                    echo '<p><strong>数据库:</strong> ' . $host . ':' . $port . '/' . $dbname . '</p>';
                    echo '<p><strong>MySQLi扩展:</strong> ' . (extension_loaded('mysqli') ? '✅ 已安装' : '❌ 未安装') . '</p>';
                    echo '</div>';
                    
                    if (!$mysqli) {
                        echo '<div class="error">';
                        echo '<h3>⚠️ 数据库连接问题</h3>';
                        echo '<p>请先解决数据库连接问题：</p>';
                        echo '<ol style="margin: 15px 0; padding-left: 30px;">';
                        echo '<li>运行 <strong>install-pdo.bat</strong> 安装MySQL扩展</li>';
                        echo '<li>或者安装XAMPP获得完整的PHP环境</li>';
                        echo '<li>确保数据库服务器正常运行</li>';
                        echo '</ol>';
                        echo '<a href="install-pdo.bat" class="btn btn-danger">安装MySQL扩展</a>';
                        echo '<a href="check-php.php" class="btn">检查PHP环境</a>';
                        echo '</div>';
                    } else {
                        echo '<div class="card">';
                        echo '<h3>快速操作</h3>';
                        echo '<a href="create-tables.php" class="btn btn-success">创建数据表</a>';
                        echo '<a href="?page=ingredients" class="btn">查看食材</a>';
                        echo '<a href="?page=suppliers" class="btn">查看供应商</a>';
                        echo '<a href="?page=inventory" class="btn btn-warning">检查库存</a>';
                        echo '</div>';
                    }
            }
            
            if ($mysqli) {
                $mysqli->close();
            }
            ?>
        </div>

        <div class="footer">
            <p>学校食堂食材出入库管理系统 v2.0 (MySQLi版本) | 技术支持: <EMAIL></p>
        </div>
    </div>
</body>
</html>
