<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试导入修复效果</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
    </style>
</head>
<body>
    <h1>测试导入修复效果</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../includes/ExcelReader.php';
    
    try {
        $db = Database::getInstance();
        echo "<p class='success'>✅ 数据库连接成功</p>";
        
        echo "<div class='test-section'>";
        echo "<h2>1. 修复内容说明</h2>";
        echo "<div class='step'>";
        echo "<h4>🔧 修复的问题：</h4>";
        echo "<ul>";
        echo "<li><strong>改进空行检测</strong>：使用更精确的空行判断逻辑</li>";
        echo "<li><strong>关键字段检查</strong>：检查必要字段是否有内容</li>";
        echo "<li><strong>字符串处理</strong>：使用trim()去除空白字符</li>";
        echo "<li><strong>双重验证</strong>：既检查整行是否为空，也检查关键字段</li>";
        echo "</ul>";
        
        echo "<h4>🎯 修复前后对比：</h4>";
        echo "<div class='code-block'>";
        echo "修复前：\n";
        echo "if (empty(array_filter(\$row))) {\n";
        echo "    continue;\n";
        echo "}\n\n";
        
        echo "修复后：\n";
        echo "\$filteredRow = array_filter(\$row, function(\$cell) {\n";
        echo "    return !empty(trim(\$cell));\n";
        echo "});\n";
        echo "if (empty(\$filteredRow)) {\n";
        echo "    continue;\n";
        echo "}\n";
        echo "// 额外检查关键字段...\n";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>2. 创建测试Excel文件</h2>";
        
        // 创建测试数据
        $testData = [
            ['供应商名称', '订单日期', '食材名称', '数量', '单价', '备注'],
            ['新鲜蔬菜供应商', '2024-01-15', '白菜', '50', '2.50', '早餐用'],
            ['', '', '', '', '', ''], // 空行
            ['肉类批发商', '2024-01-15', '猪肉', '20', '25.00', '午餐用'],
            ['   ', '   ', '   ', '   ', '   ', '   '], // 只有空格的行
            ['水产供应商', '2024-01-15', '鲫鱼', '15', '12.00', '晚餐用'],
        ];
        
        echo "<div class='step'>";
        echo "<h4>📋 测试数据内容：</h4>";
        echo "<div class='code-block'>";
        foreach ($testData as $index => $row) {
            echo "行" . ($index + 1) . ": " . implode(' | ', $row) . "\n";
        }
        echo "</div>";
        
        echo "<p class='info'>测试数据包含：</p>";
        echo "<ul>";
        echo "<li>1行标题</li>";
        echo "<li>3行有效数据</li>";
        echo "<li>1行完全空行</li>";
        echo "<li>1行只有空格的行</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>3. 模拟导入处理</h2>";
        
        echo "<div class='step'>";
        echo "<h4>🔍 空行检测测试：</h4>";
        
        // 跳过标题行
        $dataRows = array_slice($testData, 1);
        $validRows = 0;
        $skippedRows = [];
        
        foreach ($dataRows as $index => $row) {
            $rowNumber = $index + 2; // 实际行号
            
            // 使用修复后的逻辑
            $filteredRow = array_filter($row, function($cell) {
                return !empty(trim($cell));
            });
            
            if (empty($filteredRow)) {
                $skippedRows[] = "第{$rowNumber}行：完全空行";
                continue;
            }
            
            // 检查关键字段
            $supplier = trim($row[0] ?? '');
            $ingredient = trim($row[2] ?? '');
            $quantity = trim($row[3] ?? '');
            $price = trim($row[4] ?? '');
            
            if (empty($supplier) && empty($ingredient) && empty($quantity) && empty($price)) {
                $skippedRows[] = "第{$rowNumber}行：关键字段为空";
                continue;
            }
            
            $validRows++;
            echo "<p class='success'>✅ 第{$rowNumber}行：有效数据 - 供应商={$supplier}, 食材={$ingredient}, 数量={$quantity}, 单价={$price}</p>";
        }
        
        echo "<h4>📊 处理结果：</h4>";
        echo "<p class='info'>有效数据行：{$validRows}</p>";
        echo "<p class='warning'>跳过的行：" . count($skippedRows) . "</p>";
        
        if (!empty($skippedRows)) {
            echo "<div class='code-block'>";
            foreach ($skippedRows as $skip) {
                echo $skip . "\n";
            }
            echo "</div>";
        }
        echo "</div>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>4. 实际测试建议</h2>";
        
        echo "<div class='step'>";
        echo "<h4>🧪 测试步骤：</h4>";
        echo "<ol>";
        echo "<li><strong>下载模板</strong>：<a href='../modules/purchase/download_excel_template.php' target='_blank'>下载Excel模板</a></li>";
        echo "<li><strong>添加测试数据</strong>：在模板中添加几行测试数据</li>";
        echo "<li><strong>故意添加空行</strong>：在数据中间添加一些空行</li>";
        echo "<li><strong>测试导入</strong>：<a href='../modules/purchase/index.php?action=import' target='_blank'>访问导入页面</a></li>";
        echo "<li><strong>检查结果</strong>：查看是否正确跳过空行并导入有效数据</li>";
        echo "</ol>";
        
        echo "<h4>📝 推荐测试数据：</h4>";
        echo "<div class='code-block'>";
        echo "供应商名称        订单日期      食材名称    数量    单价    备注\n";
        echo "新鲜蔬菜供应商    2024-01-15   白菜       50     2.50   早餐用\n";
        echo "                                                        (空行)\n";
        echo "肉类批发商        2024-01-15   猪肉       20     25.00  午餐用\n";
        echo "                                                        (只有空格的行)\n";
        echo "水产供应商        2024-01-15   鲫鱼       15     12.00  晚餐用\n";
        echo "</div>";
        
        echo "<h4>🎯 预期结果：</h4>";
        echo "<ul>";
        echo "<li>应该导入3条有效记录</li>";
        echo "<li>自动跳过空行和只有空格的行</li>";
        echo "<li>显示"导入成功！共导入 3 条记录"</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>5. 检查现有test.xlsx</h2>";
        
        $testFile = '../test.xlsx';
        if (file_exists($testFile)) {
            echo "<div class='step'>";
            echo "<p class='info'>正在分析您的test.xlsx文件...</p>";
            
            try {
                $reader = new ExcelReader();
                $data = $reader->read($testFile, 'test.xlsx');
                
                echo "<p class='success'>✅ 文件读取成功，总行数: " . count($data) . "</p>";
                
                if (!empty($data)) {
                    // 跳过标题行，分析数据行
                    $dataRows = array_slice($data, 1);
                    $validCount = 0;
                    $emptyCount = 0;
                    
                    foreach ($dataRows as $index => $row) {
                        $filteredRow = array_filter($row, function($cell) {
                            return !empty(trim($cell));
                        });
                        
                        if (empty($filteredRow)) {
                            $emptyCount++;
                        } else {
                            // 检查关键字段
                            $supplier = trim($row[0] ?? '');
                            $ingredient = trim($row[2] ?? '');
                            $quantity = trim($row[3] ?? '');
                            $price = trim($row[4] ?? '');
                            
                            if (!empty($supplier) || !empty($ingredient) || !empty($quantity) || !empty($price)) {
                                $validCount++;
                            } else {
                                $emptyCount++;
                            }
                        }
                    }
                    
                    echo "<p class='info'>分析结果：</p>";
                    echo "<ul>";
                    echo "<li>有效数据行：{$validCount}</li>";
                    echo "<li>空行或无效行：{$emptyCount}</li>";
                    echo "</ul>";
                    
                    if ($validCount === 0) {
                        echo "<p class='error'>❌ 您的test.xlsx文件中没有有效数据，这就是导入0条记录的原因</p>";
                        echo "<p class='warning'>建议使用标准模板重新创建Excel文件</p>";
                    } else {
                        echo "<p class='success'>✅ 修复后应该能导入 {$validCount} 条记录</p>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ 文件分析失败: " . $e->getMessage() . "</p>";
            }
            echo "</div>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 测试失败: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div class="test-section">
        <h2>6. 总结</h2>
        <div class="step">
            <h4>✅ 修复完成：</h4>
            <ul>
                <li>改进了空行检测逻辑，使用trim()处理空白字符</li>
                <li>添加了关键字段验证，确保有意义的数据才会被处理</li>
                <li>同时修复了简单列表格式和订货单格式的导入</li>
                <li>提供了更准确的数据行计数</li>
            </ul>
            
            <h4>🎯 现在请重新测试：</h4>
            <ol>
                <li>使用标准模板创建Excel文件</li>
                <li>确保添加了有效的数据行</li>
                <li>重新导入test.xlsx或新创建的文件</li>
                <li>应该能看到正确的导入数量</li>
            </ol>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a> | <a href="../modules/purchase/index.php?action=import">重新测试导入</a></p>
</body>
</html>
