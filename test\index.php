<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试脚本中心 - 学校食堂管理系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .test-category {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e9ecef;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .test-category:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .test-category h3 {
            margin: 0 0 15px;
            color: #2d3748;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-category .icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }
        
        .icon-php { background: linear-gradient(135deg, #8993be 0%, #6f7ad3 100%); }
        .icon-db { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .icon-api { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .icon-category { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .icon-ingredient { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .icon-supplier { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        
        .test-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .test-list li {
            margin: 8px 0;
        }
        
        .test-list a {
            display: block;
            padding: 10px 15px;
            background: white;
            border-radius: 8px;
            text-decoration: none;
            color: #4a5568;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }
        
        .test-list a:hover {
            background: #667eea;
            color: white;
            transform: translateX(5px);
        }
        
        .stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <h1>🧪 测试脚本中心</h1>
        <p>学校食堂管理系统 - 调试与测试工具集</p>
    </div>
    
    <div class="content">
        <div class="stats">
            <h3>测试统计</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?= count(glob('*.php')) - 1 ?></div>
                    <div>测试脚本</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">6</div>
                    <div>测试分类</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= date('Y-m-d') ?></div>
                    <div>最后更新</div>
                </div>
            </div>
        </div>
        
        <div class="test-grid">
            <!-- PHP环境测试 -->
            <div class="test-category">
                <h3>
                    <div class="icon icon-php">🐘</div>
                    PHP环境测试
                </h3>
                <ul class="test-list">
                    <li><a href="check-php.php">PHP环境检查</a></li>
                    <li><a href="check-php-extensions.php">PHP扩展详细检查</a></li>
                </ul>
            </div>
            
            <!-- 数据库测试 -->
            <div class="test-category">
                <h3>
                    <div class="icon icon-db">🗄️</div>
                    数据库测试
                </h3>
                <ul class="test-list">
                    <li><a href="check-categories.php">检查分类数据</a></li>
                    <li><a href="debug-categories-list.php">调试分类列表</a></li>
                    <li><a href="check-duplicate-categories.php">检查重复分类</a></li>
                </ul>
            </div>
            
            <!-- API测试 -->
            <div class="test-category">
                <h3>
                    <div class="icon icon-api">🔌</div>
                    API接口测试
                </h3>
                <ul class="test-list">
                    <li><a href="test-api-direct.php">直接API测试</a></li>
                    <li><a href="test-category-api.php">分类API功能测试</a></li>
                </ul>
            </div>
            
            <!-- 分类功能测试 -->
            <div class="test-category">
                <h3>
                    <div class="icon icon-category">🏷️</div>
                    分类功能测试
                </h3>
                <ul class="test-list">
                    <li><a href="test-categories-query.php">分类查询测试</a></li>
                    <li><a href="test-category-select.php">分类选择测试</a></li>
                </ul>
            </div>
            
            <!-- 食材功能测试 -->
            <div class="test-category">
                <h3>
                    <div class="icon icon-ingredient">🥬</div>
                    食材功能测试
                </h3>
                <ul class="test-list">
                    <li><a href="debug-ingredient-create.php">调试食材创建</a></li>
                </ul>
            </div>
            
            <!-- 供应商功能测试 -->
            <div class="test-category">
                <h3>
                    <div class="icon icon-supplier">🚚</div>
                    供应商功能测试
                </h3>
                <ul class="test-list">
                    <li><a href="test-supplier-fields.php">测试供应商字段</a></li>
                    <li><a href="fix-suppliers-table.php">修复供应商表</a></li>
                    <li><a href="debug-supplier-insert.php">调试供应商插入</a></li>
                </ul>
            </div>
        </div>
        
        <div style="background: #e3f2fd; padding: 20px; border-radius: 12px; border-left: 4px solid #2196f3;">
            <h3 style="margin-top: 0; color: #1976d2;">📋 使用说明</h3>
            <ul style="color: #424242; line-height: 1.6;">
                <li><strong>PHP环境测试</strong> - 检查PHP版本、扩展和配置</li>
                <li><strong>数据库测试</strong> - 验证数据库连接和表结构</li>
                <li><strong>API接口测试</strong> - 测试各种API端点的功能</li>
                <li><strong>功能模块测试</strong> - 测试具体业务功能的实现</li>
            </ul>
            <p style="margin-bottom: 0; color: #666;"><strong>提示：</strong> 所有测试脚本都是独立的，可以单独运行。建议按顺序执行测试以获得最佳效果。</p>
        </div>
    </div>
    
    <div class="footer">
        <p>
            <a href="../index.php">🏠 返回系统首页</a> | 
            <a href="../modules/dashboard/index.php">📊 系统仪表板</a> | 
            <a href="../README.md" target="_blank">📖 查看文档</a>
        </p>
        <p style="margin-top: 10px; color: #666; font-size: 0.9em;">
            学校食堂管理系统 v1.0 - 测试工具集
        </p>
    </div>
</div>

</body>
</html>
