<?php
/**
 * 食品留样管理控制器
 */
require_once dirname(__DIR__, 2) . '/includes/BaseController.php';
require_once dirname(__DIR__, 2) . '/includes/helpers.php';

class SamplesController extends BaseController
{
    protected function init()
    {
        $this->setTemplateData([
            'page_title' => '食品留样管理 - ' . $this->config['name'],
            'current_module' => 'samples'
        ]);
    }

    public function handleRequest()
    {
        switch ($this->request['action']) {
            case 'create':
                return $this->create();
            case 'edit':
                return $this->edit();
            case 'delete':
                return $this->delete();
            case 'view':
                return $this->view();
            case 'save':
                return $this->save();
            case 'batch_process':
                return $this->batchProcess();
            case 'export':
                return $this->export();
            case 'print_label':
                return $this->printLabel();
            case 'print_labels':
                return $this->printLabels();
            case 'get_sample_info':
                return $this->getSampleInfo();
            case 'index':
            default:
                return $this->index();
        }
    }

    /**
     * 食品留样记录列表页面
     */
    private function index()
    {
        try {
            // 获取搜索参数
            $search = $this->request['get']['search'] ?? '';
            $meal_type = $this->request['get']['meal_type'] ?? '';
            $date_from = $this->request['get']['date_from'] ?? '';
            $date_to = $this->request['get']['date_to'] ?? '';
            $status = $this->request['get']['status'] ?? '';

            // 构建查询条件
            $where = [];
            $params = [];

            if ($search) {
                $where[] = "(i.name LIKE ? OR fs.sample_code LIKE ? OR fs.batch_number LIKE ?)";
                $params[] = "%$search%";
                $params[] = "%$search%";
                $params[] = "%$search%";
            }

            if ($meal_type) {
                $where[] = "fs.meal_type = ?";
                $params[] = $meal_type;
            }

            if ($date_from) {
                $where[] = "fs.meal_date >= ?";
                $params[] = $date_from;
            }

            if ($date_to) {
                $where[] = "fs.meal_date <= ?";
                $params[] = $date_to;
            }

            if ($status !== '') {
                $where[] = "fs.sample_status = ?";
                $params[] = $status;
            }

            $whereClause = '';
            if (!empty($where)) {
                $whereClause = 'WHERE ' . implode(' AND ', $where);
            }

            // 查询总数
            $countSql = "SELECT COUNT(*) as count FROM food_samples fs 
                         LEFT JOIN ingredients i ON fs.ingredient_id = i.id 
                         $whereClause";
            $countResult = $this->db->fetchOne($countSql, $params);
            $totalCount = $countResult['count'];

            // 分页
            $page = max(1, intval($this->request['get']['page'] ?? 1));
            $perPage = 10;
            $offset = ($page - 1) * $perPage;

            // 查询留样记录
            $sql = "SELECT fs.*, i.name as ingredient_name FROM food_samples fs 
                    LEFT JOIN ingredients i ON fs.ingredient_id = i.id 
                    $whereClause 
                    ORDER BY fs.created_at DESC 
                    LIMIT $offset, $perPage";
            
            $samples = $this->db->fetchAll($sql, $params);

            // 获取统计信息
            $statsSql = "SELECT 
                            COUNT(*) as total_samples,
                            SUM(CASE WHEN fs.sample_status = 1 THEN 1 ELSE 0 END) as active_samples,
                            SUM(CASE WHEN fs.expiration_date < CURDATE() AND fs.sample_status = 1 THEN 1 ELSE 0 END) as expired_samples,
                            SUM(CASE WHEN DATE(fs.created_at) = CURDATE() THEN 1 ELSE 0 END) as today_samples,
                            SUM(CASE WHEN fs.expiration_date <= DATE_ADD(CURDATE(), INTERVAL 2 DAY) AND fs.expiration_date >= CURDATE() AND fs.sample_status = 1 THEN 1 ELSE 0 END) as expiring_soon,
                            SUM(CASE WHEN fs.storage_temperature IS NOT NULL AND fs.storage_temperature LIKE '%℃%' THEN 1 ELSE 0 END) as cold_storage
                         FROM food_samples fs 
                         LEFT JOIN ingredients i ON fs.ingredient_id = i.id 
                         $whereClause";
            
            $stats = $this->db->fetchOne($statsSql, $params);

            // 传递数据到模板
            $this->setTemplateData([
                'samples' => $samples,
                'stats' => $stats,
                'search' => $search,
                'meal_type' => $meal_type,
                'date_from' => $date_from,
                'date_to' => $date_to,
                'status' => $status,
                'currentPage' => $page,
                'perPage' => $perPage,
                'totalCount' => $totalCount,
                'totalPages' => ceil($totalCount / $perPage)
            ]);

            // 渲染模板
            $this->render('template.php');
        } catch (Exception $e) {
            $this->setTemplateData('error', '获取留样记录列表失败: ' . $e->getMessage());
            $this->render('template.php');
        }
    }

    /**
     * 创建留样记录页面
     */
    private function create()
    {
        try {
            // 获取食材列表
            $ingredientsSql = "SELECT id, name, code FROM ingredients WHERE status = 1 ORDER BY name";
            $ingredients = $this->db->fetchAll($ingredientsSql);

            $this->setTemplateData([
                'ingredients' => $ingredients,
                'action' => 'create'
            ]);

            $this->render('form-template.php');
        } catch (Exception $e) {
            $this->setTemplateData('error', '加载创建页面失败: ' . $e->getMessage());
            $this->render('form-template.php');
        }
    }

    /**
     * 编辑留样记录页面
     */
    private function edit()
    {
        try {
            $id = intval($this->request['get']['id'] ?? 0);
            if (!$id) {
                throw new Exception('无效的留样记录ID');
            }

            // 获取留样记录详情
            $sampleSql = "SELECT * FROM food_samples WHERE id = ?";
            $sample = $this->db->fetchOne($sampleSql, [$id]);

            if (!$sample) {
                throw new Exception('留样记录不存在');
            }

            // 获取食材列表
            $ingredientsSql = "SELECT id, name, code FROM ingredients WHERE status = 1 ORDER BY name";
            $ingredients = $this->db->fetchAll($ingredientsSql);

            $this->setTemplateData([
                'sample' => $sample,
                'ingredients' => $ingredients,
                'action' => 'edit'
            ]);

            $this->render('form-template.php');
        } catch (Exception $e) {
            $this->setTemplateData('error', '加载编辑页面失败: ' . $e->getMessage());
            $this->render('form-template.php');
        }
    }

    /**
     * 查看留样记录详情
     */
    private function view()
    {
        try {
            $id = intval($this->request['get']['id'] ?? 0);
            if (!$id) {
                throw new Exception('无效的留样记录ID');
            }

            // 获取留样记录详情
            $sampleSql = "SELECT fs.*, i.name as ingredient_name FROM food_samples fs 
                          LEFT JOIN ingredients i ON fs.ingredient_id = i.id 
                          WHERE fs.id = ?";
            $sample = $this->db->fetchOne($sampleSql, [$id]);

            if (!$sample) {
                throw new Exception('留样记录不存在');
            }

            $this->setTemplateData([
                'sample' => $sample,
                'action' => 'view'
            ]);

            $this->render('view-template.php');
        } catch (Exception $e) {
            $this->setTemplateData('error', '查看留样记录失败: ' . $e->getMessage());
            $this->render('view-template.php');
        }
    }

    /**
     * 保存留样记录（新增或更新）
     */
    private function save()
    {
        try {
            if ($this->request['method'] !== 'POST') {
                throw new Exception('无效的请求方法');
            }

            $postData = $this->request['post'];
            $id = intval($postData['id'] ?? 0);
            
            // 验证必填字段
            $requiredFields = ['ingredient_id', 'batch_number', 'sample_code', 'sample_quantity', 'sample_unit', 'meal_type', 'meal_date', 'expiration_date', 'responsible_person'];
            foreach ($requiredFields as $field) {
                if (empty($postData[$field])) {
                    throw new Exception('字段 ' . $field . ' 不能为空');
                }
            }

            // 验证日期逻辑
            if ($postData['expiration_date'] <= $postData['meal_date']) {
                throw new Exception('过期日期必须晚于用餐日期');
            }

            // 准备数据
            $data = [
                'ingredient_id' => intval($postData['ingredient_id']),
                'batch_number' => $postData['batch_number'],
                'sample_code' => $postData['sample_code'],
                'sample_quantity' => floatval($postData['sample_quantity']),
                'sample_unit' => $postData['sample_unit'],
                'meal_type' => $postData['meal_type'],
                'meal_date' => $postData['meal_date'],
                'sample_time' => $postData['sample_time'] ?? date('Y-m-d H:i:s'),
                'storage_location' => $postData['storage_location'] ?? null,
                'storage_temperature' => $postData['storage_temperature'] ?? null,
                'expiration_date' => $postData['expiration_date'],
                'responsible_person' => $postData['responsible_person'],
                'sample_status' => intval($postData['sample_status'] ?? 1),
                'notes' => $postData['notes'] ?? null,
                'created_by' => $_SESSION['user']['id'] ?? 1 // 默认用户ID
            ];

            if ($id) {
                // 更新记录
                $where = "id = ?";
                $whereParams = [$id];
                $this->db->update('food_samples', $data, $where, $whereParams);
                json_response(['status' => 'success', 'message' => '更新成功']);
            } else {
                // 新增记录
                $this->db->insert('food_samples', $data);
                json_response(['status' => 'success', 'message' => '新增成功']);
            }
        } catch (Exception $e) {
            json_response(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    /**
     * 删除留样记录
     */
    private function delete()
    {
        try {
            if ($this->request['method'] !== 'POST') {
                throw new Exception('无效的请求方法');
            }

            $id = intval($this->request['post']['id'] ?? 0);
            if (!$id) {
                throw new Exception('无效的留样记录ID');
            }

            // 删除留样记录
            $where = "id = ?";
            $affectedRows = $this->db->delete('food_samples', $where, [$id]);

            if ($affectedRows > 0) {
                json_response(['status' => 'success', 'message' => '删除成功']);
            } else {
                throw new Exception('删除失败或记录不存在');
            }
        } catch (Exception $e) {
            json_response(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    /**
     * 批量处理留样记录
     */
    private function batchProcess()
    {
        try {
            if ($this->request['method'] !== 'POST') {
                throw new Exception('无效的请求方法');
            }

            $postData = $this->request['post'];
            $ids = explode(',', $postData['ids'] ?? '');
            $action = $postData['batch_action'] ?? '';

            if (empty($ids) || empty($action)) {
                throw new Exception('请选择要处理的记录和操作类型');
            }

            // 验证ID格式
            $validIds = array_filter($ids, function($id) {
                return is_numeric($id) && intval($id) > 0;
            });

            if (empty($validIds)) {
                throw new Exception('无效的记录ID');
            }

            $placeholders = implode(',', array_fill(0, count($validIds), '?'));
            $affectedRows = 0;

            switch ($action) {
                case 'mark_processed':
                    $sql = "UPDATE food_samples SET sample_status = 0, updated_at = NOW() WHERE id IN ($placeholders)";
                    $affectedRows = $this->db->execute($sql, $validIds);
                    break;

                case 'mark_active':
                    $sql = "UPDATE food_samples SET sample_status = 1, updated_at = NOW() WHERE id IN ($placeholders)";
                    $affectedRows = $this->db->execute($sql, $validIds);
                    break;

                case 'extend_expiry':
                    $sql = "UPDATE food_samples SET expiration_date = DATE_ADD(expiration_date, INTERVAL 2 DAY), updated_at = NOW() WHERE id IN ($placeholders)";
                    $affectedRows = $this->db->execute($sql, $validIds);
                    break;

                case 'delete':
                    $sql = "DELETE FROM food_samples WHERE id IN ($placeholders)";
                    $affectedRows = $this->db->execute($sql, $validIds);
                    break;

                default:
                    throw new Exception('不支持的操作类型');
            }

            json_response(['status' => 'success', 'message' => "成功处理 {$affectedRows} 条记录"]);
        } catch (Exception $e) {
            json_response(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    /**
     * 导出留样记录
     */
    private function export()
    {
        try {
            $ids = $this->request['get']['ids'] ?? '';
            $where = [];
            $params = [];

            if ($ids) {
                $idArray = explode(',', $ids);
                $validIds = array_filter($idArray, function($id) {
                    return is_numeric($id) && intval($id) > 0;
                });
                
                if (!empty($validIds)) {
                    $placeholders = implode(',', array_fill(0, count($validIds), '?'));
                    $where[] = "fs.id IN ($placeholders)";
                    $params = $validIds;
                }
            }

            $whereClause = '';
            if (!empty($where)) {
                $whereClause = 'WHERE ' . implode(' AND ', $where);
            }

            // 查询数据
            $sql = "SELECT fs.*, i.name as ingredient_name 
                    FROM food_samples fs 
                    LEFT JOIN ingredients i ON fs.ingredient_id = i.id 
                    $whereClause 
                    ORDER BY fs.created_at DESC";
            
            $samples = $this->db->fetchAll($sql, $params);

            // 设置CSV头部
            header('Content-Type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename="留样记录_' . date('Y-m-d_H-i-s') . '.csv"');
            
            // 输出BOM以支持中文
            echo "\xEF\xBB\xBF";
            
            $output = fopen('php://output', 'w');
            
            // CSV表头
            fputcsv($output, [
                '留样编号', '食材名称', '批次号', '留样数量', '单位', '餐次', 
                '用餐日期', '留样时间', '过期日期', '存储位置', '存储温度', 
                '责任人', '状态', '备注', '创建时间'
            ]);
            
            // 数据行
            foreach ($samples as $sample) {
                $mealTypes = [
                    'breakfast' => '早餐',
                    'lunch' => '午餐', 
                    'dinner' => '晚餐',
                    'other' => '其他'
                ];
                
                fputcsv($output, [
                    $sample['sample_code'],
                    $sample['ingredient_name'] ?? '未知食材',
                    $sample['batch_number'],
                    $sample['sample_quantity'],
                    $sample['sample_unit'],
                    $mealTypes[$sample['meal_type']] ?? $sample['meal_type'],
                    $sample['meal_date'],
                    $sample['sample_time'],
                    $sample['expiration_date'],
                    $sample['storage_location'] ?? '',
                    $sample['storage_temperature'] ?? '',
                    $sample['responsible_person'],
                    $sample['sample_status'] == 1 ? '有效' : '已处理',
                    $sample['notes'] ?? '',
                    $sample['created_at']
                ]);
            }
            
            fclose($output);
            exit;
        } catch (Exception $e) {
            $this->setTemplateData('error', '导出失败: ' . $e->getMessage());
            $this->render('template.php');
        }
    }

    /**
     * 打印单个标签
     */
    private function printLabel()
    {
        try {
            $id = intval($this->request['get']['id'] ?? 0);
            if (!$id) {
                throw new Exception('无效的留样记录ID');
            }

            // 获取留样记录详情
            $sampleSql = "SELECT fs.*, i.name as ingredient_name FROM food_samples fs 
                          LEFT JOIN ingredients i ON fs.ingredient_id = i.id 
                          WHERE fs.id = ?";
            $sample = $this->db->fetchOne($sampleSql, [$id]);

            if (!$sample) {
                throw new Exception('留样记录不存在');
            }

            $this->setTemplateData([
                'sample' => $sample,
                'print_mode' => 'single'
            ]);

            $this->render('print-label-template.php');
        } catch (Exception $e) {
            echo '<script>alert("' . htmlspecialchars($e->getMessage()) . '"); window.close();</script>';
        }
    }

    /**
     * 批量打印标签
     */
    private function printLabels()
    {
        try {
            $ids = $this->request['get']['ids'] ?? '';
            $idArray = explode(',', $ids);
            $validIds = array_filter($idArray, function($id) {
                return is_numeric($id) && intval($id) > 0;
            });

            if (empty($validIds)) {
                throw new Exception('请选择要打印的记录');
            }

            $placeholders = implode(',', array_fill(0, count($validIds), '?'));
            
            // 查询留样记录
            $sql = "SELECT fs.*, i.name as ingredient_name FROM food_samples fs 
                    LEFT JOIN ingredients i ON fs.ingredient_id = i.id 
                    WHERE fs.id IN ($placeholders) 
                    ORDER BY fs.created_at DESC";
            
            $samples = $this->db->fetchAll($sql, $validIds);

            if (empty($samples)) {
                throw new Exception('未找到要打印的记录');
            }

            $this->setTemplateData([
                'samples' => $samples,
                'print_mode' => 'batch'
            ]);

            $this->render('print-label-template.php');
        } catch (Exception $e) {
            echo '<script>alert("' . htmlspecialchars($e->getMessage()) . '"); window.close();</script>';
        }
    }

    /**
     * 获取留样记录信息（用于二维码生成）
     */
    private function getSampleInfo()
    {
        try {
            $id = intval($this->request['get']['id'] ?? 0);
            if (!$id) {
                throw new Exception('无效的留样记录ID');
            }

            // 获取留样记录详情
            $sampleSql = "SELECT fs.*, i.name as ingredient_name FROM food_samples fs 
                          LEFT JOIN ingredients i ON fs.ingredient_id = i.id 
                          WHERE fs.id = ?";
            $sample = $this->db->fetchOne($sampleSql, [$id]);

            if (!$sample) {
                throw new Exception('留样记录不存在');
            }

            json_response(['status' => 'success', 'sample' => $sample]);
        } catch (Exception $e) {
            json_response(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }
}