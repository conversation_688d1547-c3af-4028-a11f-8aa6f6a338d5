/* 出库管理模块样式 */

/* 主要布局 */
.main-content {
    margin-left: 250px;
    min-height: 100vh;
    background: #f8fafc;
}

.content {
    padding: 20px;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.content-header h1 {
    margin: 0;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: #4299e1;
    color: white;
}

.btn-primary:hover {
    background: #3182ce;
    color: white;
    text-decoration: none;
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover {
    background: #38a169;
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: #a0aec0;
    color: white;
}

.btn-secondary:hover {
    background: #718096;
    color: white;
    text-decoration: none;
}

.btn-danger {
    background: #f56565;
    color: white;
}

.btn-danger:hover {
    background: #e53e3e;
    color: white;
    text-decoration: none;
}

.btn-info {
    background: #4299e1;
    color: white;
}

.btn-info:hover {
    background: #3182ce;
    color: white;
    text-decoration: none;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.table th {
    background: #f7fafc;
    font-weight: 600;
    color: #4a5568;
}

.table tbody tr:hover {
    background: #f7fafc;
}

/* 表单样式 */
.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
    }
    
    .content-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .header-actions {
        justify-content: center;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 800px;
    }
}

/* 辅助类 */
.text-center {
    text-align: center;
}

.text-muted {
    color: #718096;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mt-3 {
    margin-top: 1rem;
}