<?php
// 侧边栏组件
// 使用方法：在页面中 include 'includes/sidebar.php';
// 可以通过 $current_module 变量指定当前激活的模块

// 引入辅助函数
require_once dirname(__DIR__) . '/includes/helpers.php';

// 如果没有设置当前模块，尝试从路径获取
if (!isset($current_module)) {
    $scriptPath = $_SERVER['SCRIPT_NAME'];
    $pathParts = explode('/', trim($scriptPath, '/'));
    $current_module = isset($pathParts[1]) ? $pathParts[1] : 'dashboard';
}

// 获取模块配置
$modules = config('modules', []);

// 获取基础路径 - 使用与header.php相同的逻辑
$currentScript = $_SERVER['SCRIPT_NAME'];
$scriptDir = dirname($currentScript);

// 计算相对于项目根目录的深度
$pathParts = explode('/', trim($scriptDir, '/'));
$depth = count($pathParts) - 1; // 减1因为第一个部分是项目名

// 如果在根目录，深度为0
if ($scriptDir === '/' || $scriptDir === '') {
    $depth = 0;
}

$relativePath = $depth > 0 ? str_repeat('../', $depth) : '';

// 模块分组
$moduleGroups = [
    '主要功能' => ['dashboard'],
    '基础数据' => ['ingredients', 'categories', 'suppliers', 'purchase'],
    '库存管理' => ['inbound', 'outbound', 'stocktaking', 'inventory', 'damage'],
    '报表分析' => ['reports', 'analytics'],
    '系统管理' => ['users', 'settings']
];
?>

<!-- 侧边栏 -->
<div class="sidebar">
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <img src="<?= $relativePath ?>logo.png" alt="系统Logo" class="sidebar-logo-img">
            <div class="sidebar-logo-text">
                <h2><?= config('name', '食堂管理系统') ?></h2>
                <p>Canteen Management System</p>
            </div>
        </div>
    </div>

    <nav class="sidebar-nav">
        <?php foreach ($moduleGroups as $groupName => $groupModules): ?>
        <div class="nav-section">
            <div class="nav-section-title"><?= $groupName ?></div>
            <?php foreach ($groupModules as $moduleKey): ?>
                <?php if (isset($modules[$moduleKey]) && $modules[$moduleKey]['enabled']): ?>
                    <?php
                    $module = $modules[$moduleKey];
                    $isActive = $current_module === $moduleKey;

                    // 根据当前位置计算正确的模块URL
                    $currentPath = $_SERVER['SCRIPT_NAME'];
                    if (strpos($currentPath, '/modules/') !== false) {
                        // 在模块目录中
                        $moduleUrl = $moduleKey === 'dashboard' ?
                            '../dashboard/index.php' :
                            '../' . $moduleKey . '/index.php';
                    } else {
                        // 在根目录中
                        $moduleUrl = $moduleKey === 'dashboard' ?
                            'modules/dashboard/index.php' :
                            'modules/' . $moduleKey . '/index.php';
                    }
                    ?>
                    <a href="<?= $moduleUrl ?>" class="nav-item <?= $isActive ? 'active' : '' ?>">
                        <i class="<?= $module['icon'] ?>"></i>
                        <?= $module['name'] ?>
                    </a>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        <?php endforeach; ?>

        <!-- 开发工具 -->
        <div class="nav-section">
            <div class="nav-section-title">开发工具</div>
            <a href="<?= $base_url ?>/test/" class="nav-item" target="_blank">
                <i class="fas fa-bug"></i> 测试中心
            </a>
        </div>
    </nav>
</div>
