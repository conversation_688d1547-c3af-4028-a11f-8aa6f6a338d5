<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>点击测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-area { margin: 20px 0; padding: 20px; border: 2px solid #ddd; border-radius: 8px; }
        .btn { padding: 12px 24px; margin: 10px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .log { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 4px; font-family: monospace; max-height: 300px; overflow-y: auto; }
        .upload-area { border: 2px dashed #ccc; padding: 30px; text-align: center; margin: 20px 0; border-radius: 8px; cursor: pointer; }
        .upload-area:hover { border-color: #007bff; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <h1>🔍 文件选择点击测试</h1>
    
    <div class="test-area">
        <h3>测试1: 基础内联onclick</h3>
        <input type="file" id="file1" style="display: none;" accept=".csv,.xlsx,.xls">
        <button class="btn btn-primary" onclick="testClick1()">
            📁 点击测试1
        </button>
        <p id="result1">等待点击...</p>
    </div>
    
    <div class="test-area">
        <h3>测试2: 直接调用click()</h3>
        <input type="file" id="file2" style="display: none;" accept=".csv,.xlsx,.xls">
        <button class="btn btn-success" onclick="document.getElementById('file2').click()">
            📁 点击测试2
        </button>
        <p id="result2">等待点击...</p>
    </div>
    
    <div class="test-area">
        <h3>测试3: 事件监听器</h3>
        <input type="file" id="file3" style="display: none;" accept=".csv,.xlsx,.xls">
        <button class="btn btn-warning" id="btn3">
            📁 点击测试3
        </button>
        <p id="result3">等待点击...</p>
    </div>
    
    <div class="test-area">
        <h3>测试4: 模拟导入页面结构</h3>
        <div class="upload-area" onclick="testClick4()">
            <div style="font-size: 48px; margin-bottom: 10px;">📁</div>
            <h4>点击此处选择文件</h4>
            <p>模拟导入页面的上传区域</p>
            <input type="file" id="file4" style="display: none;" accept=".csv,.xlsx,.xls">
            <button type="button" class="btn btn-primary" onclick="document.getElementById('file4').click(); event.stopPropagation();">
                📂 选择文件
            </button>
        </div>
        <p id="result4">等待点击...</p>
    </div>
    
    <div class="log" id="log">
        <strong>📋 测试日志:</strong><br>
        页面加载完成...<br>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function testClick1() {
            log('🔘 测试1: testClick1() 被调用');
            try {
                const fileInput = document.getElementById('file1');
                log('📁 文件输入元素: ' + (fileInput ? '找到' : '未找到'));
                fileInput.click();
                log('✅ 测试1: fileInput.click() 执行成功');
                document.getElementById('result1').textContent = '✅ 点击成功';
                document.getElementById('result1').style.color = 'green';
            } catch (error) {
                log('❌ 测试1: 错误 - ' + error.message);
                document.getElementById('result1').textContent = '❌ 点击失败: ' + error.message;
                document.getElementById('result1').style.color = 'red';
            }
        }
        
        function testClick4() {
            log('🔘 测试4: testClick4() 被调用');
            try {
                const fileInput = document.getElementById('file4');
                log('📁 文件输入元素: ' + (fileInput ? '找到' : '未找到'));
                fileInput.click();
                log('✅ 测试4: fileInput.click() 执行成功');
                document.getElementById('result4').textContent = '✅ 区域点击成功';
                document.getElementById('result4').style.color = 'green';
            } catch (error) {
                log('❌ 测试4: 错误 - ' + error.message);
                document.getElementById('result4').textContent = '❌ 区域点击失败: ' + error.message;
                document.getElementById('result4').style.color = 'red';
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 DOM加载完成');
            
            // 测试2的文件选择事件
            document.getElementById('file2').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    log('✅ 测试2: 文件选择成功 - ' + e.target.files[0].name);
                    document.getElementById('result2').textContent = '✅ 文件已选择: ' + e.target.files[0].name;
                    document.getElementById('result2').style.color = 'green';
                }
            });
            
            // 测试3的事件监听器
            document.getElementById('btn3').addEventListener('click', function() {
                log('🔘 测试3: 事件监听器被触发');
                try {
                    const fileInput = document.getElementById('file3');
                    log('📁 文件输入元素: ' + (fileInput ? '找到' : '未找到'));
                    fileInput.click();
                    log('✅ 测试3: fileInput.click() 执行成功');
                    document.getElementById('result3').textContent = '✅ 事件监听器成功';
                    document.getElementById('result3').style.color = 'green';
                } catch (error) {
                    log('❌ 测试3: 错误 - ' + error.message);
                    document.getElementById('result3').textContent = '❌ 事件监听器失败: ' + error.message;
                    document.getElementById('result3').style.color = 'red';
                }
            });
            
            // 为所有文件输入添加change事件
            ['file1', 'file2', 'file3', 'file4'].forEach(function(id) {
                document.getElementById(id).addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        log(`✅ ${id} 文件选择成功: ${e.target.files[0].name}`);
                    }
                });
            });
            
            log('🎉 所有事件监听器已设置');
            
            // 浏览器信息
            log('🌐 浏览器: ' + navigator.userAgent.split(' ').pop());
            log('📱 平台: ' + navigator.platform);
            
            // 检查文件API支持
            if (window.File && window.FileReader && window.FileList && window.Blob) {
                log('✅ 浏览器支持文件API');
            } else {
                log('❌ 浏览器不支持文件API');
            }
        });
        
        // 全局错误处理
        window.addEventListener('error', function(e) {
            log('❌ 全局错误: ' + e.message);
        });
        
        // 检查是否有其他脚本干扰
        setTimeout(function() {
            log('🧪 延迟检查: 页面是否有其他脚本干扰');
            if (typeof jQuery !== 'undefined') {
                log('📚 检测到 jQuery: ' + jQuery.fn.jquery);
            }
            if (typeof $ !== 'undefined') {
                log('💲 检测到 $ 变量');
            }
        }, 1000);
    </script>
</body>
</html>
