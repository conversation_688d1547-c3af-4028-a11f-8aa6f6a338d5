<?php
/**
 * 出库单管理控制器
 */

require_once '../../includes/Database.php';
require_once '../../includes/BaseController.php';

class OutboundOrderController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->setTemplateData(['page_title' => '出库单管理']);
    }

    /**
     * 处理请求
     */
    public function handleRequest()
    {
        $action = $this->request['get']['action'] ?? 'index';

        switch ($action) {
            case 'index':
                $this->index();
                break;
            case 'create':
                $this->create();
                break;
            case 'view':
                $this->view();
                break;
            case 'print':
                $this->printOrder();
                break;
            case 'delete':
                $this->delete();
                break;
            default:
                $this->index();
        }
    }

    /**
     * 出库单列表
     */
    private function index()
    {
        try {
            // 获取搜索参数
            $search = trim($this->request['get']['search'] ?? '');
            $meal_type = $this->request['get']['meal_type'] ?? '';
            $status = $this->request['get']['status'] ?? '';
            $date_from = $this->request['get']['date_from'] ?? '';
            $date_to = $this->request['get']['date_to'] ?? '';

            // 构建查询条件
            $where = ['1=1'];
            $params = [];

            if ($search) {
                $where[] = 'oo.order_number LIKE ?';
                $params[] = "%$search%";
            }

            if ($meal_type) {
                $where[] = 'oo.meal_type = ?';
                $params[] = $meal_type;
            }

            if ($status) {
                $where[] = 'oo.status = ?';
                $params[] = $status;
            }

            if ($date_from) {
                $where[] = 'oo.meal_date >= ?';
                $params[] = $date_from;
            }

            if ($date_to) {
                $where[] = 'oo.meal_date <= ?';
                $params[] = $date_to;
            }

            $whereClause = 'WHERE ' . implode(' AND ', $where);

            // 获取出库单列表
            $orders = $this->db->fetchAll("
                SELECT oo.*, '系统' as operator_name,
                       COUNT(ooi.id) as item_count
                FROM outbound_orders oo
                LEFT JOIN outbound_order_items ooi ON oo.id = ooi.order_id
                $whereClause
                GROUP BY oo.id
                ORDER BY oo.created_at DESC
                LIMIT 50
            ", $params);

            $this->setTemplateData([
                'orders' => $orders,
                'search' => $search,
                'selected_meal_type' => $meal_type,
                'selected_status' => $status,
                'date_from' => $date_from,
                'date_to' => $date_to
            ]);

        } catch (Exception $e) {
            $this->setTemplateData([
                'orders' => [],
                'error_message' => $e->getMessage()
            ]);
        }

        $this->render('outbound-order-list-template.php');
    }

    /**
     * 创建出库单
     */
    private function create()
    {
        if ($this->request['method'] === 'POST') {
            try {
                $this->db->beginTransaction();

                // 获取表单数据
                $meal_type = $this->request['post']['meal_type'] ?? '';
                $meal_date = $this->request['post']['meal_date'] ?? date('Y-m-d');
                $operator_id = intval($this->request['post']['operator_id'] ?? 1); // TODO: 从session获取
                $purpose = trim($this->request['post']['purpose'] ?? '');
                $notes = trim($this->request['post']['notes'] ?? '');
                $items = $this->request['post']['items'] ?? [];

                if (!$meal_type) {
                    throw new Exception('请选择餐次类型');
                }

                if (empty($items)) {
                    throw new Exception('请至少添加一个出库项目');
                }

                // 生成出库单号
                $order_number = $this->generateOrderNumber('outbound_order');

                // 计算总金额和总项目数
                $total_amount = 0;
                $total_items = count($items);

                foreach ($items as $item) {
                    $total_amount += floatval($item['total_price'] ?? 0);
                }

                // 插入出库单
                $order_data = [
                    'order_number' => $order_number,
                    'meal_type' => $meal_type,
                    'meal_date' => $meal_date,
                    'operator_id' => $operator_id,
                    'total_amount' => $total_amount,
                    'total_items' => $total_items,
                    'status' => 'pending',
                    'purpose' => $purpose,
                    'notes' => $notes,
                    'created_by' => $operator_id,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $order_id = $this->db->insert('outbound_orders', $order_data);

                // 插入出库单明细
                foreach ($items as $item) {
                    $ingredient_id = intval($item['ingredient_id'] ?? 0);
                    $quantity = floatval($item['quantity'] ?? 0);
                    $unit_price = floatval($item['unit_price'] ?? 0);
                    $total_price = $quantity * $unit_price;

                    // 生成批次号
                    $batch_number = $this->generateBatchNumber();

                    $item_data = [
                        'order_id' => $order_id,
                        'ingredient_id' => $ingredient_id,
                        'batch_number' => $batch_number,
                        'quantity' => $quantity,
                        'unit_price' => $unit_price,
                        'total_price' => $total_price,
                        'purpose' => $item['purpose'] ?? $purpose,
                        'notes' => $item['notes'] ?? ''
                    ];

                    $item_id = $this->db->insert('outbound_order_items', $item_data);

                    // 同时插入出库记录（保持兼容性）
                    $record_data = [
                        'outbound_order_id' => $order_id,
                        'ingredient_id' => $ingredient_id,
                        'batch_number' => $batch_number,
                        'quantity' => $quantity,
                        'unit_price' => $unit_price,
                        'used_for' => $purpose,
                        'meal_date' => $meal_date,
                        'notes' => $item['notes'] ?? '',
                        'created_by' => $operator_id
                    ];

                    $this->db->insert('outbound_records', $record_data);

                    // 更新库存
                    $this->updateIngredientStock($ingredient_id, $quantity, 'subtract');
                }

                // 更新出库单状态为已完成
                $this->db->update('outbound_orders', [
                    'status' => 'completed',
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$order_id]);

                $this->db->commit();

                // 检查是否是AJAX请求
                if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                    strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'message' => '出库单创建成功',
                        'data' => [
                            'order_id' => $order_id,
                            'order_number' => $order_number,
                            'total_amount' => $total_amount
                        ]
                    ]);
                    exit;
                }

                $this->redirect('index.php?action=view&id=' . $order_id, '出库单创建成功', 'success');

            } catch (Exception $e) {
                $this->db->rollback();
                
                if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                    strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => false,
                        'message' => $e->getMessage()
                    ]);
                    exit;
                }
                
                $this->setTemplateData('error_message', $e->getMessage());
            }
        }

        $this->loadCreateFormData();
        $this->render('outbound-order-create-template.php');
    }

    /**
     * 查看出库单详情
     */
    private function view()
    {
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            // 获取出库单信息
            $order = $this->db->fetchOne("
                SELECT oo.*, '系统' as operator_name
                FROM outbound_orders oo
                WHERE oo.id = ?
            ", [$id]);

            if (!$order) {
                $this->redirect('index.php', '出库单不存在', 'error');
                return;
            }

            // 获取出库单明细
            $items = $this->db->fetchAll("
                SELECT ooi.*, i.name as ingredient_name, i.unit,
                       ic.name as category_name
                FROM outbound_order_items ooi
                LEFT JOIN ingredients i ON ooi.ingredient_id = i.id
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                WHERE ooi.order_id = ?
                ORDER BY ooi.id ASC
            ", [$id]);

            $this->setTemplateData([
                'order' => $order,
                'items' => $items
            ]);

        } catch (Exception $e) {
            $this->redirect('index.php', '获取出库单信息失败：' . $e->getMessage(), 'error');
            return;
        }

        $this->render('outbound-order-view-template.php');
    }

    /**
     * 打印出库单
     */
    private function printOrder()
    {
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            echo '<script>alert("参数错误"); window.close();</script>';
            return;
        }

        try {
            // 获取出库单信息
            $order = $this->db->fetchOne("
                SELECT oo.*, '系统' as operator_name
                FROM outbound_orders oo
                WHERE oo.id = ?
            ", [$id]);

            if (!$order) {
                echo '<script>alert("出库单不存在"); window.close();</script>';
                return;
            }

            // 获取出库单明细
            $items = $this->db->fetchAll("
                SELECT ooi.*, i.name as ingredient_name, i.unit,
                       ic.name as category_name
                FROM outbound_order_items ooi
                LEFT JOIN ingredients i ON ooi.ingredient_id = i.id
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                WHERE ooi.order_id = ?
                ORDER BY ooi.id ASC
            ", [$id]);

            $this->setTemplateData([
                'order' => $order,
                'items' => $items,
                'print_mode' => true
            ]);

            $this->render('outbound-order-print-template.php');

        } catch (Exception $e) {
            echo '<script>alert("获取出库单信息失败：' . htmlspecialchars($e->getMessage()) . '"); window.close();</script>';
        }
    }

    /**
     * 删除出库单
     */
    private function delete()
    {
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            $this->db->beginTransaction();

            // 获取出库单信息
            $order = $this->db->fetchOne("SELECT * FROM outbound_orders WHERE id = ?", [$id]);
            if (!$order) {
                throw new Exception('出库单不存在');
            }

            // 获取出库单明细
            $items = $this->db->fetchAll("SELECT * FROM outbound_order_items WHERE order_id = ?", [$id]);

            // 回滚库存
            foreach ($items as $item) {
                $this->updateIngredientStock($item['ingredient_id'], $item['quantity'], 'add');
            }

            // 删除相关的出库记录
            $this->db->delete('outbound_records', 'outbound_order_id = ?', [$id]);

            // 删除出库单明细
            $this->db->delete('outbound_order_items', 'order_id = ?', [$id]);

            // 删除出库单
            $this->db->delete('outbound_orders', 'id = ?', [$id]);

            $this->db->commit();
            $this->redirect('index.php', '出库单删除成功', 'success');

        } catch (Exception $e) {
            $this->db->rollback();
            $this->redirect('index.php', '删除失败：' . $e->getMessage(), 'error');
        }
    }

    /**
     * 加载创建表单数据
     */
    private function loadCreateFormData()
    {
        try {
            // 获取食材列表
            $ingredients = $this->db->fetchAll("
                SELECT i.id, i.name, i.unit,
                       ic.name as category_name,
                       inv.current_quantity
                FROM ingredients i
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                LEFT JOIN inventory inv ON i.id = inv.ingredient_id
                WHERE i.status = 1
                ORDER BY ic.name ASC, i.name ASC
            ");

            $this->setTemplateData([
                'ingredients' => $ingredients
            ]);

        } catch (Exception $e) {
            $this->setTemplateData([
                'ingredients' => [],
                'error_message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 生成单据编号
     */
    private function generateOrderNumber($doc_type)
    {
        try {
            // 获取序列信息
            $sequence = $this->db->fetchOne("SELECT * FROM document_sequences WHERE doc_type = ?", [$doc_type]);

            if (!$sequence) {
                throw new Exception('单据类型配置不存在');
            }

            $prefix = $sequence['prefix'];
            $current_number = $sequence['current_number'];
            $date_format = $sequence['date_format'];

            // 生成编号
            $date_str = date($date_format);
            $number_str = str_pad($current_number, 4, '0', STR_PAD_LEFT);
            $order_number = $prefix . $date_str . $number_str;

            // 更新序列
            $this->db->update('document_sequences', [
                'current_number' => $current_number + 1,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'doc_type = ?', [$doc_type]);

            return $order_number;

        } catch (Exception $e) {
            // 如果序列表有问题，使用时间戳生成
            return strtoupper($doc_type) . date('YmdHis');
        }
    }

    /**
     * 生成批次号
     */
    private function generateBatchNumber()
    {
        return 'OUT' . date('YmdHis') . rand(100, 999);
    }

    /**
     * 更新食材库存
     */
    private function updateIngredientStock($ingredient_id, $quantity, $operation = 'subtract')
    {
        try {
            // 获取当前库存
            $inventory = $this->db->fetchOne("SELECT * FROM inventory WHERE ingredient_id = ?", [$ingredient_id]);

            if ($inventory) {
                // 更新现有库存
                $new_quantity = $operation === 'add'
                    ? $inventory['current_quantity'] + $quantity
                    : $inventory['current_quantity'] - $quantity;

                $this->db->update('inventory', [
                    'current_quantity' => max(0, $new_quantity),
                    'last_outbound_at' => $operation === 'subtract' ? date('Y-m-d H:i:s') : $inventory['last_outbound_at'],
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'ingredient_id = ?', [$ingredient_id]);
            }

        } catch (Exception $e) {
            error_log("更新库存失败: " . $e->getMessage());
        }
    }
}
