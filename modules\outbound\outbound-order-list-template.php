<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../inbound/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <?php include '../inbound/sidebar.php'; ?>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <div class="content-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1><i class="fas fa-file-export"></i> 出库单管理</h1>
                        <div>
                            <a href="index.php?action=create" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 新建出库单
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 搜索筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <input type="hidden" name="action" value="index">
                            
                            <div class="col-md-3">
                                <label class="form-label">出库单号</label>
                                <input type="text" name="search" class="form-control" 
                                       value="<?= htmlspecialchars($search ?? '') ?>" 
                                       placeholder="输入出库单号">
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">餐次类型</label>
                                <select name="meal_type" class="form-select">
                                    <option value="">全部餐次</option>
                                    <option value="breakfast" <?= ($selected_meal_type == 'breakfast') ? 'selected' : '' ?>>早餐</option>
                                    <option value="lunch" <?= ($selected_meal_type == 'lunch') ? 'selected' : '' ?>>午餐</option>
                                    <option value="dinner" <?= ($selected_meal_type == 'dinner') ? 'selected' : '' ?>>晚餐</option>
                                    <option value="other" <?= ($selected_meal_type == 'other') ? 'selected' : '' ?>>其他</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">状态</label>
                                <select name="status" class="form-select">
                                    <option value="">全部状态</option>
                                    <option value="pending" <?= ($selected_status == 'pending') ? 'selected' : '' ?>>待处理</option>
                                    <option value="completed" <?= ($selected_status == 'completed') ? 'selected' : '' ?>>已完成</option>
                                    <option value="cancelled" <?= ($selected_status == 'cancelled') ? 'selected' : '' ?>>已取消</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">开始日期</label>
                                <input type="date" name="date_from" class="form-control" 
                                       value="<?= htmlspecialchars($date_from ?? '') ?>">
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">结束日期</label>
                                <input type="date" name="date_to" class="form-control" 
                                       value="<?= htmlspecialchars($date_to ?? '') ?>">
                            </div>
                            
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary d-block">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 错误提示 -->
                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <?= htmlspecialchars($error_message) ?>
                </div>
                <?php endif; ?>

                <!-- 出库单列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list"></i> 出库单列表
                            <span class="badge bg-primary ms-2"><?= count($orders) ?></span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>出库单号</th>
                                        <th>餐次类型</th>
                                        <th>用餐日期</th>
                                        <th>操作员</th>
                                        <th>项目数</th>
                                        <th>总金额</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($orders)): ?>
                                        <?php foreach ($orders as $order): ?>
                                            <tr>
                                                <td>
                                                    <div class="order-number">
                                                        <strong><?= htmlspecialchars($order['order_number']) ?></strong>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php
                                                    $meal_types = [
                                                        'breakfast' => '早餐',
                                                        'lunch' => '午餐',
                                                        'dinner' => '晚餐',
                                                        'other' => '其他'
                                                    ];
                                                    ?>
                                                    <span class="badge bg-info"><?= $meal_types[$order['meal_type']] ?? $order['meal_type'] ?></span>
                                                </td>
                                                <td>
                                                    <div class="date-info"><?= date('Y-m-d', strtotime($order['meal_date'])) ?></div>
                                                </td>
                                                <td>
                                                    <div class="operator-info"><?= htmlspecialchars($order['operator_name'] ?? '系统') ?></div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?= $order['item_count'] ?> 项</span>
                                                </td>
                                                <td>
                                                    <div class="amount-info">
                                                        <strong>¥<?= number_format($order['total_amount'], 2) ?></strong>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = [
                                                        'pending' => 'warning',
                                                        'completed' => 'success',
                                                        'cancelled' => 'danger'
                                                    ];
                                                    $status_text = [
                                                        'pending' => '待处理',
                                                        'completed' => '已完成',
                                                        'cancelled' => '已取消'
                                                    ];
                                                    ?>
                                                    <span class="badge bg-<?= $status_class[$order['status']] ?? 'secondary' ?>">
                                                        <?= $status_text[$order['status']] ?? $order['status'] ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="time-info"><?= date('m-d H:i', strtotime($order['created_at'])) ?></div>
                                                </td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <a href="index.php?action=view&id=<?= $order['id'] ?>"
                                                           class="btn btn-sm btn-info" title="查看详情">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="index.php?action=print&id=<?= $order['id'] ?>"
                                                           class="btn btn-sm btn-primary" title="打印出库单"
                                                           target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                        <?php if ($order['status'] !== 'completed'): ?>
                                                        <a href="index.php?action=delete&id=<?= $order['id'] ?>"
                                                           class="btn btn-sm btn-danger" title="删除出库单"
                                                           onclick="return confirm('确定要删除这个出库单吗？删除后会回滚相应的库存。')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="9" class="text-center text-muted py-4">
                                                <i class="fas fa-inbox fa-2x mb-2"></i>
                                                <div>暂无出库单记录</div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<style>
.order-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #dc3545;
}

.amount-info {
    font-family: 'Courier New', monospace;
    color: #28a745;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.action-buttons .btn {
    padding: 4px 8px;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.content-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
}
</style>
