<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试采购单详情页面统一风格</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .comparison-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .comparison-table th { background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>测试采购单详情页面统一风格修复</h1>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        echo "<p class='success'>✅ 数据库连接成功</p>";
        
        // 检查采购单数据
        echo "<div class='test-section'>";
        echo "<h2>1. 检查测试数据</h2>";
        
        $orders = $db->fetchAll("SELECT id, order_number, status FROM purchase_orders LIMIT 3");
        if (empty($orders)) {
            echo "<p class='warning'>⚠️ 没有找到采购单数据，创建测试数据...</p>";
            
            // 创建测试采购单
            $testOrder = [
                'order_number' => 'UNIFIED_TEST_' . date('YmdHis'),
                'supplier_id' => 1,
                'order_date' => date('Y-m-d'),
                'canteen_id' => 1,
                'contact_person' => '测试联系人',
                'delivery_address' => '测试地址',
                'contact_phone' => '13800138000',
                'order_amount' => 150.00,
                'actual_amount' => 150.00,
                'payment_status' => 'unpaid',
                'status' => 1,
                'created_by' => 1,
                'notes' => '这是一个用于测试统一风格的采购单详情页面'
            ];
            
            $orderId = $db->insert('purchase_orders', $testOrder);
            echo "<p class='success'>✅ 创建测试采购单成功，ID: {$orderId}</p>";
            
            // 添加测试明细
            $testItems = [
                [
                    'order_id' => $orderId,
                    'ingredient_id' => 1,
                    'quantity' => 20.00,
                    'unit_price' => 3.50,
                    'total_price' => 70.00,
                    'notes' => '早餐用'
                ],
                [
                    'order_id' => $orderId,
                    'ingredient_id' => 2,
                    'quantity' => 15.00,
                    'unit_price' => 5.00,
                    'total_price' => 75.00,
                    'notes' => '午餐用'
                ]
            ];
            
            foreach ($testItems as $item) {
                $db->insert('purchase_order_items', $item);
            }
            echo "<p class='success'>✅ 创建测试明细成功 (" . count($testItems) . " 项)</p>";
            
            $orders = [['id' => $orderId, 'order_number' => $testOrder['order_number'], 'status' => 1]];
        }
        
        echo "<p class='info'>找到 " . count($orders) . " 个采购单用于测试</p>";
        foreach ($orders as $order) {
            echo "<p>- 订单 #{$order['id']}: {$order['order_number']} (状态: {$order['status']})</p>";
        }
        echo "</div>";
        
        // 检查模板文件修改
        echo "<div class='test-section'>";
        echo "<h2>2. 检查模板文件修改</h2>";
        
        $templateFile = '../modules/purchase/view-template.php';
        if (file_exists($templateFile)) {
            $content = file_get_contents($templateFile);
            
            // 检查关键元素
            $checks = [
                'sidebar.php' => '侧边栏引入',
                'main-content' => '主内容区域',
                'topbar' => '顶部栏',
                'card' => '卡片布局',
                'info-item' => '信息项样式',
                'table-responsive' => '响应式表格'
            ];
            
            echo "<table class='comparison-table'>";
            echo "<tr><th>检查项</th><th>状态</th><th>说明</th></tr>";
            
            foreach ($checks as $keyword => $description) {
                $found = strpos($content, $keyword) !== false;
                $status = $found ? '✅ 通过' : '❌ 未找到';
                $class = $found ? 'success' : 'error';
                echo "<tr><td>{$description}</td><td class='{$class}'>{$status}</td><td>{$keyword}</td></tr>";
            }
            echo "</table>";
            
            // 检查样式统一性
            if (strpos($content, 'includes/styles.css') !== false) {
                echo "<p class='success'>✅ 引用了系统统一样式文件</p>";
            } else {
                echo "<p class='error'>❌ 未引用系统统一样式文件</p>";
            }
            
        } else {
            echo "<p class='error'>❌ view-template.php 文件不存在</p>";
        }
        echo "</div>";
        
        // 检查侧边栏文件
        echo "<div class='test-section'>";
        echo "<h2>3. 检查侧边栏文件</h2>";
        
        $sidebarFile = '../modules/purchase/sidebar.php';
        if (file_exists($sidebarFile)) {
            echo "<p class='success'>✅ sidebar.php 文件存在</p>";
            
            $sidebarContent = file_get_contents($sidebarFile);
            if (strpos($sidebarContent, 'nav-item active') !== false) {
                echo "<p class='success'>✅ 侧边栏包含当前模块高亮</p>";
            } else {
                echo "<p class='warning'>⚠️ 侧边栏可能缺少当前模块高亮</p>";
            }
            
            if (strpos($sidebarContent, '采购管理') !== false) {
                echo "<p class='success'>✅ 侧边栏包含采购管理模块</p>";
            } else {
                echo "<p class='error'>❌ 侧边栏缺少采购管理模块</p>";
            }
        } else {
            echo "<p class='error'>❌ sidebar.php 文件不存在</p>";
        }
        echo "</div>";
        
        // 检查布局一致性
        echo "<div class='test-section'>";
        echo "<h2>4. 检查布局一致性</h2>";
        
        $otherModules = ['dashboard', 'ingredients', 'categories', 'suppliers'];
        $layoutElements = [];
        
        foreach ($otherModules as $module) {
            $moduleTemplate = "../modules/{$module}/template.php";
            if (file_exists($moduleTemplate)) {
                $moduleContent = file_get_contents($moduleTemplate);
                $hasMainContent = strpos($moduleContent, 'main-content') !== false;
                $hasTopbar = strpos($moduleContent, 'topbar') !== false;
                $hasSidebar = strpos($moduleContent, 'sidebar.php') !== false;
                
                $layoutElements[$module] = [
                    'main-content' => $hasMainContent,
                    'topbar' => $hasTopbar,
                    'sidebar' => $hasSidebar
                ];
            }
        }
        
        if (!empty($layoutElements)) {
            echo "<table class='comparison-table'>";
            echo "<tr><th>模块</th><th>主内容区</th><th>顶部栏</th><th>侧边栏</th></tr>";
            
            foreach ($layoutElements as $module => $elements) {
                echo "<tr>";
                echo "<td>{$module}</td>";
                echo "<td>" . ($elements['main-content'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($elements['topbar'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($elements['sidebar'] ? '✅' : '❌') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p class='info'>采购详情页面现在使用与其他模块相同的布局结构</p>";
        }
        echo "</div>";
        
        // 生成测试链接
        echo "<div class='test-section'>";
        echo "<h2>5. 测试链接和对比</h2>";
        
        if (!empty($orders)) {
            $firstOrder = $orders[0];
            echo "<p class='info'>测试链接：</p>";
            echo "<ul>";
            echo "<li><a href='../modules/purchase/index.php' target='_blank'>采购管理首页</a></li>";
            echo "<li><a href='../modules/purchase/index.php?action=view&id={$firstOrder['id']}' target='_blank'>查看采购单详情 (统一风格)</a></li>";
            echo "<li><a href='../modules/dashboard/index.php' target='_blank'>仪表板 (对比参考)</a></li>";
            echo "<li><a href='../modules/ingredients/index.php' target='_blank'>食材管理 (对比参考)</a></li>";
            echo "</ul>";
            
            echo "<p class='info'>对比测试步骤：</p>";
            echo "<ol>";
            echo "<li>打开采购单详情页面，检查是否有左侧边栏</li>";
            echo "<li>对比详情页面与其他模块页面的布局结构</li>";
            echo "<li>检查顶部栏、卡片样式、按钮样式是否一致</li>";
            echo "<li>测试响应式布局在不同屏幕尺寸下的表现</li>";
            echo "<li>验证侧边栏导航是否正常工作</li>";
            echo "</ol>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 测试失败: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div class="test-section">
        <h2>6. 修复总结</h2>
        <p><strong>已完成的统一风格修复：</strong></p>
        <ul>
            <li>✅ 添加了左侧边栏导航</li>
            <li>✅ 使用了统一的主内容区域布局</li>
            <li>✅ 添加了标准的顶部栏</li>
            <li>✅ 采用了系统统一的卡片样式</li>
            <li>✅ 使用了一致的信息项显示格式</li>
            <li>✅ 引用了系统统一的CSS样式文件</li>
            <li>✅ 保持了响应式设计</li>
            <li>✅ 统一了按钮和操作样式</li>
        </ul>
        
        <p><strong>布局结构对比：</strong></p>
        <table class="comparison-table">
            <tr>
                <th>元素</th>
                <th>修复前</th>
                <th>修复后</th>
            </tr>
            <tr>
                <td>侧边栏</td>
                <td>❌ 无</td>
                <td>✅ 统一侧边栏</td>
            </tr>
            <tr>
                <td>主布局</td>
                <td>❌ 独立容器</td>
                <td>✅ main-content 布局</td>
            </tr>
            <tr>
                <td>顶部栏</td>
                <td>❌ 自定义头部</td>
                <td>✅ 标准 topbar</td>
            </tr>
            <tr>
                <td>卡片样式</td>
                <td>❌ 自定义样式</td>
                <td>✅ 系统统一卡片</td>
            </tr>
            <tr>
                <td>操作按钮</td>
                <td>❌ 底部固定</td>
                <td>✅ 顶部栏集成</td>
            </tr>
        </table>
        
        <p><strong>用户体验改进：</strong></p>
        <ul>
            <li>🎨 视觉风格与系统其他页面完全一致</li>
            <li>🧭 左侧导航便于在不同模块间切换</li>
            <li>📱 保持了完整的响应式设计</li>
            <li>⚡ 操作按钮位置更加合理</li>
            <li>🔧 信息展示更加清晰有序</li>
        </ul>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
