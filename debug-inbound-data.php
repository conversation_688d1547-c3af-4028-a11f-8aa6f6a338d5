<?php
/**
 * 调试入库数据
 */

require_once 'includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>调试入库数据</h2>";
    
    // 1. 检查入库单表数据
    echo "<h3>1. 入库单表数据</h3>";
    $inbound_orders = $db->fetchAll("SELECT * FROM inbound_orders ORDER BY created_at DESC LIMIT 5");
    
    if (!empty($inbound_orders)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>单号</th><th>供应商ID</th><th>状态</th><th>总金额</th><th>项目数</th><th>入库日期</th></tr>";
        foreach ($inbound_orders as $order) {
            echo "<tr>";
            echo "<td>{$order['id']}</td>";
            echo "<td>{$order['order_number']}</td>";
            echo "<td>{$order['supplier_id']}</td>";
            echo "<td>{$order['status']}</td>";
            echo "<td>{$order['total_amount']}</td>";
            echo "<td>{$order['total_items']}</td>";
            echo "<td>{$order['inbound_date']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ 入库单表没有数据</p>";
    }
    
    // 2. 检查入库记录表数据
    echo "<h3>2. 入库记录表数据</h3>";
    $inbound_records = $db->fetchAll("SELECT * FROM inbound_records ORDER BY created_at DESC LIMIT 5");
    
    if (!empty($inbound_records)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>入库单ID</th><th>食材ID</th><th>供应商ID</th><th>数量</th><th>单价</th><th>批次号</th></tr>";
        foreach ($inbound_records as $record) {
            echo "<tr>";
            echo "<td>{$record['id']}</td>";
            echo "<td>" . ($record['inbound_order_id'] ?? 'NULL') . "</td>";
            echo "<td>{$record['ingredient_id']}</td>";
            echo "<td>{$record['supplier_id']}</td>";
            echo "<td>{$record['quantity']}</td>";
            echo "<td>{$record['unit_price']}</td>";
            echo "<td>{$record['batch_number']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ 入库记录表没有数据</p>";
    }
    
    // 3. 测试控制器查询
    echo "<h3>3. 测试控制器查询</h3>";
    
    $where = ['1=1'];
    $params = [];
    $whereClause = 'WHERE ' . implode(' AND ', $where);
    
    $query = "
        SELECT io.*, s.name as supplier_name, u.name as operator_name,
               COUNT(ioi.id) as item_count,
               SUM(ioi.actual_quantity) as total_quantity,
               io.total_amount
        FROM inbound_orders io
        LEFT JOIN suppliers s ON io.supplier_id = s.id
        LEFT JOIN users u ON io.operator_id = u.id
        LEFT JOIN inbound_order_items ioi ON io.id = ioi.order_id
        $whereClause
        GROUP BY io.id
        ORDER BY io.inbound_date DESC, io.created_at DESC
        LIMIT 5
    ";
    
    echo "<p><strong>查询SQL:</strong></p>";
    echo "<pre>" . htmlspecialchars($query) . "</pre>";
    
    try {
        $records = $db->fetchAll($query, $params);
        
        if (!empty($records)) {
            echo "<p style='color: green;'>✅ 查询成功，返回 " . count($records) . " 条记录</p>";
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>字段名</th><th>第一条记录的值</th></tr>";
            foreach ($records[0] as $key => $value) {
                echo "<tr>";
                echo "<td><strong>$key</strong></td>";
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
        } else {
            echo "<p style='color: red;'>❌ 查询返回空结果</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 查询失败: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 4. 检查是否需要从入库记录迁移数据
    echo "<h3>4. 数据迁移建议</h3>";
    
    $record_count = $db->fetchOne("SELECT COUNT(*) as count FROM inbound_records")['count'];
    $order_count = $db->fetchOne("SELECT COUNT(*) as count FROM inbound_orders")['count'];
    
    echo "<p>入库记录数量: <strong>$record_count</strong></p>";
    echo "<p>入库单数量: <strong>$order_count</strong></p>";
    
    if ($record_count > 0 && $order_count == 0) {
        echo "<p style='color: orange;'>⚠️ 建议：有入库记录但没有入库单，需要进行数据迁移</p>";
        echo "<p><a href='migrate-inbound-data.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>执行数据迁移</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 调试失败：" . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    background: white;
    margin: 15px 0;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
}

th {
    background: #007bff;
    color: white;
    padding: 10px;
}

td {
    padding: 8px 10px;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
}

p {
    margin: 10px 0;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}
</style>
