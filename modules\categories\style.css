/* 食材分类管理模块样式 */

/* 两级分类样式 */
.level-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.level-badge.level-1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
}

.level-badge.level-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: #fff;
}

.category-card.subcategory {
    margin-left: 1rem;
    border-left: 3px solid #f093fb;
}

.category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.category-level {
    margin-left: auto;
    margin-right: 1rem;
}

.parent-path {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.parent-path i {
    font-size: 0.75rem;
}

.category-title {
    margin-bottom: 1rem;
}

.level-display {
    margin-top: 0.5rem;
}

/* 统计行 */
.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stat-icon.bg-primary { background: #4299e1; }
.stat-icon.bg-success { background: #38a169; }
.stat-icon.bg-info { background: #4299e1; }

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #718096;
    margin-top: 4px;
}

/* 分类列表容器 */
.categories-list {
    width: 100%;
    margin-top: 20px;
}

/* 确保所有主要容器宽度一致 */
.search-box,
.stats-row,
.categories-list {
    width: 100%;
    max-width: none;
}

.search-box {
    margin-bottom: 20px;
}

/* 分类卡片 */
.category-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 分类卡片头部 */
.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0;
}

.category-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4299e1, #3182ce);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.category-actions {
    position: relative;
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    border: none;
    background: transparent;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.dropdown-toggle:hover {
    background: #f7fafc;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 120px;
    z-index: 1000;
    display: none;
}

.dropdown.active .dropdown-menu {
    display: block;
}

.dropdown-item {
    display: block;
    padding: 10px 15px;
    color: #2d3748;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: #f7fafc;
    text-decoration: none;
}

.dropdown-item.text-danger {
    color: #e53e3e;
}

.dropdown-item.text-danger:hover {
    background: #fed7d7;
}

.dropdown-item i {
    width: 16px;
    margin-right: 8px;
}

/* 分类卡片内容 */
.category-content {
    padding: 20px;
}

.category-name {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 10px 0;
}

.category-description {
    color: #718096;
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 20px 0;
    min-height: 42px;
}

.category-stats {
    border-top: 1px solid #e2e8f0;
    padding-top: 15px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.stat-row:last-child {
    margin-bottom: 0;
}

.stat-label {
    color: #718096;
}

.stat-value {
    font-weight: 600;
    color: #2d3748;
}

.stat-value.text-primary {
    color: #4299e1;
}

/* 分类卡片底部 */
.category-footer {
    padding: 15px 20px;
    background: #f7fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 10px;
}

.category-footer .btn {
    flex: 1;
    justify-content: center;
}

/* 空状态 */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h5 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #2d3748;
}

.empty-state p {
    font-size: 16px;
    margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .categories-list {
        margin-top: 15px;
    }
    
    .stats-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .category-card {
        margin: 0 10px;
    }
    
    .category-footer {
        flex-direction: column;
    }
    
    .category-footer .btn {
        width: 100%;
    }
}

/* 动画效果 */
.category-card {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态 */
.category-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.category-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== 表格列表样式 ===== */

/* 表格容器 */
.table-responsive {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    background: white;
    margin-bottom: 20px;
    width: 100%;
}

.table {
    margin-bottom: 0;
    font-size: 14px;
}

.table thead th {
    background: #dbeafe;
    border-bottom: 2px solid #bfdbfe;
    font-weight: 600;
    color: #1e40af;
    padding: 12px 8px;
    vertical-align: middle;
    white-space: nowrap;
}

.table tbody td {
    padding: 12px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

/* 确保描述列纵向居中 */
.table tbody td.align-middle {
    vertical-align: middle !important;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

/* 交替行背景色 */
.table tbody tr:nth-child(odd) {
    background-color: #f8f9fa;
}

.table tbody tr:nth-child(even) {
    background-color: #ffffff;
}

.table tbody tr:hover {
    background-color: #e3f2fd !important;
    transition: background-color 0.2s ease;
}

/* 级别徽章样式 */
.level-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.level-badge.level-1 {
    background: #e3f2fd;
    color: #1976d2;
}

.level-badge.level-2 {
    background: #f3e5f5;
    color: #7b1fa2;
}

/* 序号样式 */
.row-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: #e9ecef;
    border-radius: 50%;
    font-size: 12px;
    font-weight: 600;
    color: #495057;
}

/* 编码样式 */
.category-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 13px;
}

/* 分类名称单元格 */
.category-name {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

/* 描述单元格 */
.category-description {
    color: #6c757d;
    font-size: 14px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 徽章样式 */
.badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 600;
    display: inline-block;
}

.bg-info {
    background-color: #17a2b8 !important;
    color: white !important;
}

.bg-success {
    background-color: #28a745 !important;
    color: white !important;
}

.text-white {
    color: white !important;
}

/* 子分类数量徽章特殊样式 */
.subcategory-count-badge {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
    color: white !important;
    font-size: 11px;
    padding: 3px 8px;
    border-radius: 5px;
    font-weight: 600;
    margin-left: 4px;
    display: inline-block;
    min-width: 20px;
    text-align: center;
}

/* 食材数量徽章样式 */
.ingredient-count-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 6px 12px;
    border-radius: 5px;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    transition: all 0.2s ease;
}

.ingredient-count-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.ingredient-count-badge .badge-icon {
    width: 16px;
    height: 16px;
    filter: brightness(0) invert(1);
}

.ingredient-count-badge .badge-text {
    font-weight: 700;
}

/* 状态徽章样式 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 5px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.status-badge.status-active {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.status-badge.status-inactive {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.status-badge .status-icon {
    width: 16px;
    height: 16px;
    filter: brightness(0) invert(1);
}

.status-badge.status-inactive .status-icon {
    filter: brightness(0) invert(0.4);  /* 深灰色图标 */
}

.status-badge .status-text {
    font-weight: 700;
}

/* 父分类信息卡片 */
.parent-category-info {
    margin-bottom: 0;
}

.parent-category-card {
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    padding: 16px;
    margin-top: 8px;
}

.category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.category-name {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
    font-size: 18px;
}

.level-badge.level-1 {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.category-content .category-description {
    color: #6c757d;
    margin-bottom: 12px;
    font-size: 14px;
    line-height: 1.5;
}

.category-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.category-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #495057;
    font-size: 14px;
}

.category-stats .stat-item i {
    color: #6c757d;
    width: 16px;
}

.category-stats .stat-label {
    color: #6c757d;
}

.category-stats .stat-value {
    color: #2c3e50;
    font-weight: 600;
}

/* 子分类管理区域 */
.subcategories-management {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    padding: 20px;
    margin-bottom: 20px;
}

.subcategories-management .stats-row {
    margin-bottom: 20px;
}

/* 添加分类按钮美化 */
.btn-add-category {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
    border: none !important;
    border-radius: 10px !important;
    padding: 10px 20px !important;
    font-weight: 600 !important;
    color: white !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn-add-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-add-category:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(72, 187, 120, 0.35) !important;
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%) !important;
}

.btn-add-category:hover::before {
    left: 100%;
}

.btn-add-category:active {
    transform: translateY(-1px) !important;
}

.btn-add-category i {
    font-size: 14px;
    margin-right: 6px;
}

/* 导入按钮美化 */
.btn-import {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
    border: none !important;
    border-radius: 10px !important;
    padding: 10px 20px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.25) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    color: white !important;
}

.btn-import::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-import:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(66, 153, 225, 0.35) !important;
    background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%) !important;
}

.btn-import:hover::before {
    left: 100%;
}

.btn-import:active {
    transform: translateY(-1px) !important;
}

/* 按钮组样式优化 */
.btn-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.btn {
    padding: 10px 18px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: #4299e1;
    color: white;
    border-color: #4299e1;
}

.btn-primary:hover {
    background: #3182ce;
    border-color: #3182ce;
    color: white;
    text-decoration: none;
}

.btn-outline-secondary {
    background: transparent;
    color: #718096;
    border-color: #e2e8f0;
}

.btn-outline-secondary:hover {
    background: #f7fafc;
    color: #4a5568;
    border-color: #cbd5e0;
    text-decoration: none;
}

.btn-outline-info {
    background: transparent;
    color: #4299e1;
    border-color: #4299e1;
}

.btn-outline-info:hover {
    background: #4299e1;
    color: white;
    text-decoration: none;
}

.btn-outline-primary {
    background: transparent;
    color: #4299e1;
    border-color: #4299e1;
}

.btn-outline-primary:hover {
    background: #4299e1;
    color: white;
    text-decoration: none;
}

.btn-outline-danger {
    background: transparent;
    color: #e53e3e;
    border-color: #e53e3e;
}

.btn-outline-danger:hover {
    background: #e53e3e;
    color: white;
    text-decoration: none;
}

/* 小尺寸按钮样式 */
.btn-sm {
    padding: 6px 12px;
    font-size: 13px;
    line-height: 1.4;
    border-radius: 6px;
}

.btn-sm i {
    font-size: 12px;
    margin-right: 4px;
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

.action-buttons .btn {
    padding: 8px 14px;
    font-size: 13px;
    min-width: 36px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    border-radius: 6px;
    transition: all 0.2s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
    font-weight: 500;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    text-decoration: none;
}

.action-buttons .btn i {
    font-size: 12px;
}

.action-buttons .btn span {
    font-size: 12px;
    white-space: nowrap;
}

/* 按钮组样式 */
.btn-group .btn {
    padding: 4px 8px;
    font-size: 16px;
    border-radius: 4px;
    margin-right: 2px;
    border: 1px solid #dee2e6;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 响应式表格 */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 12px;
    }

    .table thead th,
    .table tbody td {
        padding: 8px 4px;
    }

    .category-description {
        max-width: 120px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 6px;
    }

    .action-buttons .btn {
        width: 100%;
        min-width: auto;
        padding: 10px 12px;
        font-size: 14px;
        height: auto;
    }

    .action-buttons .btn span {
        font-size: 13px;
    }

    .btn-group .btn {
        padding: 2px 6px;
        font-size: 11px;
    }

    /* 隐藏一些列在小屏幕上 */
    .table th:nth-child(3),
    .table td:nth-child(3),
    .table th:nth-child(6),
    .table td:nth-child(6),
    .table th:nth-child(7),
    .table td:nth-child(7) {
        display: none;
    }
}

@media (max-width: 576px) {
    .table th:nth-child(4),
    .table td:nth-child(4) {
        display: none;
    }

    .subcategory-row td:first-child {
        padding-left: 12px;
    }
}
