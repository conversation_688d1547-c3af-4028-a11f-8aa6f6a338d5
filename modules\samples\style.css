/* 食品留样管理模块特定样式 */

/* ==================== 表单页面样式 ==================== */

/* 表单容器 */
.form-container {
    background: #ffffff !important;
    border-radius: 12px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    padding: 0 !important;
    margin-bottom: 30px !important;
    overflow: hidden !important;
}

/* 表单分组 */
.form-section {
    padding: 24px !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.form-section:last-child {
    border-bottom: none !important;
}

.section-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    margin: 0 0 20px 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding-bottom: 12px !important;
    border-bottom: 2px solid #f3f4f6 !important;
}

.section-title i {
    color: #6b7280 !important;
    font-size: 16px !important;
}

/* 表单行 */
.form-row {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 20px !important;
}

.form-row:last-child {
    margin-bottom: 0 !important;
}

/* 表单字段 */
.form-field {
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
}

.form-field label {
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin-bottom: 6px !important;
    line-height: 1.4 !important;
}

.form-field.required label::after {
    content: ' *' !important;
    color: #ef4444 !important;
    font-weight: 700 !important;
}

/* 表单控件 */
.form-control {
    width: 100% !important;
    padding: 12px 16px !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: #1f2937 !important;
    background-color: #ffffff !important;
    transition: all 0.2s ease !important;
    box-sizing: border-box !important;
}

.form-control:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.form-control:hover:not(:focus) {
    border-color: #d1d5db !important;
}

.form-control::placeholder {
    color: #9ca3af !important;
}

/* 输入组 */
.input-group {
    display: flex !important;
    align-items: stretch !important;
    gap: 0 !important;
}

.input-group .form-control {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-right: none !important;
    flex: 1 !important;
}

.input-group .btn {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    border-left: 2px solid #e5e7eb !important;
    white-space: nowrap !important;
}

/* 表单文本提示 */
.form-text {
    font-size: 12px !important;
    color: #6b7280 !important;
    margin-top: 4px !important;
    line-height: 1.4 !important;
}

.form-text.text-muted {
    color: #9ca3af !important;
}

/* 文件上传区域 */
.file-upload-area {
    border: 2px dashed #d1d5db !important;
    border-radius: 8px !important;
    padding: 24px !important;
    text-align: center !important;
    background-color: #f9fafb !important;
    transition: all 0.2s ease !important;
    cursor: pointer !important;
}

.file-upload-area:hover {
    border-color: #3b82f6 !important;
    background-color: #eff6ff !important;
}

.file-upload-area.dragover {
    border-color: #3b82f6 !important;
    background-color: #dbeafe !important;
}

.file-upload-icon {
    font-size: 32px !important;
    color: #9ca3af !important;
    margin-bottom: 12px !important;
}

.file-upload-text {
    font-size: 14px !important;
    color: #6b7280 !important;
    margin-bottom: 8px !important;
}

.file-upload-hint {
    font-size: 12px !important;
    color: #9ca3af !important;
}

/* 已上传文件列表 */
.uploaded-files {
    margin-top: 16px !important;
}

.file-item {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 8px 12px !important;
    background-color: #f3f4f6 !important;
    border-radius: 6px !important;
    margin-bottom: 8px !important;
}

.file-item:last-child {
    margin-bottom: 0 !important;
}

.file-info {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    flex: 1 !important;
}

.file-icon {
    color: #6b7280 !important;
    font-size: 14px !important;
}

.file-name {
    font-size: 14px !important;
    color: #374151 !important;
    font-weight: 500 !important;
}

.file-size {
    font-size: 12px !important;
    color: #9ca3af !important;
}

.file-remove {
    color: #ef4444 !important;
    cursor: pointer !important;
    padding: 4px !important;
    border-radius: 4px !important;
    transition: background-color 0.2s ease !important;
}

.file-remove:hover {
    background-color: #fee2e2 !important;
}

/* 图片上传特定样式 */
.upload-placeholder {
    text-align: center !important;
    padding: 20px !important;
}

.upload-placeholder i {
    font-size: 48px !important;
    color: #9ca3af !important;
    margin-bottom: 16px !important;
    display: block !important;
}

.upload-placeholder p {
    font-size: 16px !important;
    color: #6b7280 !important;
    margin: 0 0 8px 0 !important;
    font-weight: 500 !important;
}

.upload-placeholder small {
    font-size: 14px !important;
    color: #9ca3af !important;
}

.image-preview {
    position: relative !important;
    text-align: center !important;
    padding: 16px !important;
}

.image-preview img {
    max-width: 100% !important;
    max-height: 300px !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.image-actions {
    margin-top: 12px !important;
    display: flex !important;
    justify-content: center !important;
    gap: 8px !important;
}

.current-image {
    margin-top: 12px !important;
    padding: 12px !important;
    background-color: #f9fafb !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
}

.current-image img {
    border: 1px solid #e5e7eb !important;
}

/* 危险按钮样式 */
.btn-danger {
    background-color: #ef4444 !important;
    color: #ffffff !important;
    border-color: #ef4444 !important;
}

.btn-danger:hover {
    background-color: #dc2626 !important;
    border-color: #dc2626 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3) !important;
}

/* 加载状态样式 */
.btn.loading {
    position: relative !important;
    color: transparent !important;
    pointer-events: none !important;
}

.btn.loading::after {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 16px !important;
    height: 16px !important;
    margin: -8px 0 0 -8px !important;
    border: 2px solid transparent !important;
    border-top: 2px solid currentColor !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 表单提交状态 */
.form-container.submitting {
    opacity: 0.7 !important;
    pointer-events: none !important;
}

.form-container.submitting::before {
    content: '正在保存...' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background: rgba(255, 255, 255, 0.95) !important;
    padding: 20px 40px !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #374151 !important;
    z-index: 1000 !important;
}

/* 成功提示样式 */
.success-message {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    background: #10b981 !important;
    color: white !important;
    padding: 16px 24px !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3) !important;
    z-index: 1001 !important;
    animation: slideInRight 0.3s ease-out !important;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 字段聚焦效果增强 */
.form-field:focus-within label {
    color: #3b82f6 !important;
    transform: translateY(-2px) !important;
    transition: all 0.2s ease !important;
}

/* 必填字段标识增强 */
.form-field.required::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -8px !important;
    width: 3px !important;
    height: 100% !important;
    background: linear-gradient(to bottom, #ef4444, #f97316) !important;
    border-radius: 2px !important;
    opacity: 0.6 !important;
}

/* 表单按钮区域 */
.form-actions {
    background-color: #f9fafb !important;
    padding: 20px 24px !important;
    border-top: 1px solid #e5e7eb !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 12px !important;
    margin: 0 !important;
}

.form-actions .btn {
    min-width: 100px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    transition: all 0.2s ease !important;
    border: 2px solid transparent !important;
    cursor: pointer !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
}

.btn-primary {
    background-color: #3b82f6 !important;
    color: #ffffff !important;
    border-color: #3b82f6 !important;
}

.btn-primary:hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.btn-secondary {
    background-color: #ffffff !important;
    color: #6b7280 !important;
    border-color: #d1d5db !important;
}

.btn-secondary:hover {
    background-color: #f9fafb !important;
    border-color: #9ca3af !important;
    color: #374151 !important;
}

.btn-success {
    background-color: #10b981 !important;
    color: #ffffff !important;
    border-color: #10b981 !important;
}

.btn-success:hover {
    background-color: #059669 !important;
    border-color: #059669 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
}

.btn-warning {
    background-color: #f59e0b !important;
    color: #ffffff !important;
    border-color: #f59e0b !important;
}

.btn-warning:hover {
    background-color: #d97706 !important;
    border-color: #d97706 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3) !important;
}

.btn-sm {
    padding: 8px 16px !important;
    font-size: 13px !important;
    min-width: auto !important;
}

/* 表单验证样式 */
.form-control.is-invalid {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.form-control.is-valid {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

.invalid-feedback {
    display: block !important;
    font-size: 12px !important;
    color: #ef4444 !important;
    margin-top: 4px !important;
}

.valid-feedback {
    display: block !important;
    font-size: 12px !important;
    color: #10b981 !important;
    margin-top: 4px !important;
}

/* 警告框样式 */
.alert {
    padding: 16px 20px !important;
    border-radius: 8px !important;
    margin-bottom: 24px !important;
    border: 1px solid transparent !important;
    display: flex !important;
    align-items: flex-start !important;
    gap: 12px !important;
}

.alert-danger {
    background-color: #fef2f2 !important;
    border-color: #fecaca !important;
    color: #dc2626 !important;
}

.alert-success {
    background-color: #f0fdf4 !important;
    border-color: #bbf7d0 !important;
    color: #16a34a !important;
}

.alert-warning {
    background-color: #fffbeb !important;
    border-color: #fed7aa !important;
    color: #d97706 !important;
}

.alert-info {
    background-color: #eff6ff !important;
    border-color: #bfdbfe !important;
    color: #2563eb !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr !important;
        gap: 16px !important;
    }

    .form-section {
        padding: 20px 16px !important;
    }

    .section-title {
        font-size: 16px !important;
    }

    .form-actions {
        padding: 16px !important;
        flex-direction: column-reverse !important;
    }

    .form-actions .btn {
        width: 100% !important;
        justify-content: center !important;
    }

    .input-group {
        flex-direction: column !important;
        gap: 8px !important;
    }

    .input-group .form-control {
        border-radius: 8px !important;
        border-right: 2px solid #e5e7eb !important;
    }

    .input-group .btn {
        border-radius: 8px !important;
        border-left: 2px solid #e5e7eb !important;
    }
}

@media (max-width: 480px) {
    .content-header {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 16px !important;
    }

    .header-actions {
        justify-content: stretch !important;
    }

    .header-actions .btn {
        flex: 1 !important;
        justify-content: center !important;
    }
}

/* 页面头部样式 */
.content-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    margin-bottom: 30px !important;
    padding-bottom: 20px !important;
    border-bottom: 1px solid #e2e8f0 !important;
}

.header-left h1 {
    font-size: 28px !important;
    font-weight: 700 !important;
    color: #1f2937 !important;
    margin: 0 0 8px 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
}

.header-left h1 i {
    color: #6b7280 !important;
    font-size: 24px !important;
}

.header-subtitle {
    color: #6b7280 !important;
    font-size: 14px !important;
    margin: 0 !important;
    font-weight: 400 !important;
}

.header-actions {
    display: flex !important;
    gap: 12px !important;
    flex-wrap: wrap !important;
}

/* 统计卡片样式 - 与仪表盘保持一致 */
.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 30px !important;
}

.stat-card {
    background: #ffffff !important;
    border-radius: 8px !important;
    padding: 20px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e2e8f0 !important;
    display: flex !important;
    align-items: center !important;
    transition: all 0.2s ease !important;
}

.stat-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.stat-icon {
    width: 40px !important;
    height: 40px !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 18px !important;
    color: #6b7280 !important;
    background: #f3f4f6 !important;
    margin-right: 12px !important;
    flex-shrink: 0 !important;
}

.stat-content {
    flex: 1 !important;
    position: relative !important;
}

.stat-number {
    font-size: 28px !important;
    font-weight: 700 !important;
    color: #1f2937 !important;
    line-height: 1 !important;
}

.stat-label {
    font-size: 14px !important;
    color: #64748b !important;
    margin-top: 6px !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* 留样信息样式 */
.sample-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.sample-code {
    font-weight: 600;
    color: #2d3748;
    font-size: 15px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.batch-number {
    font-size: 12px;
    color: #718096;
    background: #edf2f7;
    padding: 2px 8px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

.sample-quantity {
    font-size: 13px;
    color: #4a5568;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 食材信息样式 */
.ingredient-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.ingredient-name {
    font-weight: 500;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 6px;
}

/* 餐次标签样式 */
.meal-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.meal-breakfast {
    background: #fef5e7;
    color: #c05621;
}

.meal-lunch {
    background: #f0fff4;
    color: #22543d;
}

.meal-dinner {
    background: #e6fffa;
    color: #234e52;
}

.meal-other {
    background: #f7fafc;
    color: #4a5568;
}

/* 时间信息样式 */
.time-info {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.sample-time {
    font-weight: 500;
    color: #2d3748;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.meal-date {
    font-size: 12px;
    color: #718096;
}

/* 过期信息样式 */
.expiration-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    text-align: center;
}

.expiration-info.expired {
    color: #c53030;
}

.expiration-info.expiring-soon {
    color: #c05621;
}

.expire-label {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

.expiration-info.expired .expire-label {
    background: #fed7d7;
    color: #c53030;
}

.expiration-info.expiring-soon .expire-label {
    background: #fef5e7;
    color: #c05621;
}

/* 存储信息样式 */
.storage-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.storage-location,
.storage-temperature {
    font-size: 12px;
    color: #4a5568;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 责任人样式 */
.responsible-person {
    font-weight: 500;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 6px;
}

/* 状态标签样式 */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.status-active {
    background: #c6f6d5;
    color: #22543d;
}

.status-inactive {
    background: #fed7d7;
    color: #c53030;
}

/* 表格行状态 */
.table tbody tr.row-expired {
    background: #fed7d7;
}

.table tbody tr.row-expired:hover {
    background: #feb2b2;
}

/* 按钮样式优化 */
.btn {
    padding: 10px 16px !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    border: none !important;
    cursor: pointer !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
    text-decoration: none !important;
}

.btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.btn-primary {
    background: #3b82f6 !important;
    color: white !important;
}

.btn-success {
    background: #10b981 !important;
    color: white !important;
}

.btn-info {
    background: #06b6d4 !important;
    color: white !important;
}

.btn-warning {
    background: #f59e0b !important;
    color: white !important;
}

.btn-danger {
    background: #ef4444 !important;
    color: white !important;
}

.btn-secondary {
    background: #6b7280 !important;
    color: white !important;
}

/* 表格样式优化 */
.table {
    background: white !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e2e8f0 !important;
}

.table th {
    background: #dbeafe !important;
    color: #1e40af !important;
    font-weight: 600 !important;
    padding: 12px 16px !important;
    border-bottom: 1px solid #bfdbfe !important;
}

.table td {
    padding: 12px 16px !important;
    border-bottom: 1px solid #f1f5f9 !important;
    vertical-align: middle !important;
}

.table tbody tr:hover {
    background: #f8fafc !important;
}

/* 卡片样式优化 */
.card {
    background: #ffffff !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e2e8f0 !important;
    transition: all 0.2s ease !important;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.card-header {
    background: #f8fafc !important;
    border-bottom: 1px solid #e2e8f0 !important;
    padding: 16px 20px !important;
    border-radius: 8px 8px 0 0 !important;
}

.card-body {
    padding: 20px !important;
}

/* 表单样式优化 */
.form-control {
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    padding: 10px 12px !important;
    transition: all 0.2s ease !important;
    background: white !important;
}

.form-control:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
}

.form-group {
    margin-bottom: 16px !important;
}

.form-label {
    font-weight: 500 !important;
    color: #374151 !important;
    margin-bottom: 6px !important;
    display: block !important;
}

/* 状态标签样式 */
.status-badge {
    padding: 4px 8px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.status-active {
    background: #dcfce7 !important;
    color: #166534 !important;
}

.status-expired {
    background: #fee2e2 !important;
    color: #991b1b !important;
}

.status-pending {
    background: #fef3c7 !important;
    color: #92400e !important;
}

/* 空状态样式 */
.empty-state {
    padding: 60px 20px !important;
}

.empty-icon {
    font-size: 48px !important;
    color: #d1d5db !important;
    margin-bottom: 16px !important;
}

.empty-text {
    font-size: 16px !important;
    color: #6b7280 !important;
    margin-bottom: 20px !important;
}

/* 操作按钮组样式 */
.action-buttons {
    display: flex !important;
    gap: 4px !important;
    flex-wrap: wrap !important;
}

.btn-sm {
    padding: 6px 10px !important;
    font-size: 12px !important;
    border-radius: 4px !important;
}

/* 餐次标签样式 */
.meal-badge {
    padding: 4px 8px !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    font-weight: 500 !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 4px !important;
}

.meal-breakfast {
    background: #fef3c7 !important;
    color: #92400e !important;
}

.meal-lunch {
    background: #dbeafe !important;
    color: #1e40af !important;
}

.meal-dinner {
    background: #e0e7ff !important;
    color: #3730a3 !important;
}

.meal-other {
    background: #f3f4f6 !important;
    color: #374151 !important;
}

/* 时间信息样式 */
.time-info {
    display: flex !important;
    flex-direction: column !important;
    gap: 4px !important;
}

.sample-time {
    font-weight: 500 !important;
    color: #374151 !important;
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
}

.meal-date {
    font-size: 12px !important;
    color: #6b7280 !important;
}

/* 过期信息样式 */
.expiration-info {
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
    font-weight: 500 !important;
}

.expiration-info.expired {
    color: #dc2626 !important;
}

.expiration-info.expiring-soon {
    color: #d97706 !important;
}

.expire-label {
    font-size: 10px !important;
    padding: 2px 4px !important;
    border-radius: 2px !important;
    background: #fee2e2 !important;
    color: #991b1b !important;
}

/* 搜索栏样式强制覆盖 - 确保与全局样式一致 */
.search-bar {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 12px !important;
    padding: 20px !important;
    margin-bottom: 24px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    position: relative !important;
}

.search-bar::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 2px !important;
    background: linear-gradient(90deg, #3b82f6 0%, #10b981 50%, #f59e0b 100%) !important;
    border-radius: 12px 12px 0 0 !important;
}

.search-bar-form {
    display: flex !important;
    gap: 12px !important;
    align-items: flex-end !important;
    flex-wrap: nowrap !important;
    overflow-x: auto !important;
}

.search-bar .form-field {
    display: flex !important;
    flex-direction: column !important;
    min-width: 0 !important;
    position: relative !important;
    flex: 1 !important;
}

.search-bar .form-field.text-input,
.search-bar .form-field.select-input,
.search-bar .form-field.date-input {
    flex: 1 1 0 !important;
    min-width: 120px !important;
    max-width: none !important;
}

.search-bar .form-field label {
    font-size: 12px !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin-bottom: 4px !important;
    line-height: 1 !important;
}

.search-bar .form-control {
    height: 40px !important;
    padding: 0 12px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    background: #ffffff !important;
    transition: all 0.2s ease !important;
    box-sizing: border-box !important;
    width: 100% !important;
    min-width: 120px !important;
}

.search-bar .form-control:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.search-bar .btn {
    height: 40px !important;
    padding: 0 12px !important;
    border: none !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 6px !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    box-sizing: border-box !important;
    min-width: 70px !important;
    flex-shrink: 0 !important;
}

.search-bar .btn-primary {
    background: #3b82f6 !important;
    color: white !important;
}

.search-bar .btn-secondary {
    background: #6b7280 !important;
    color: #ffffff !important;
}

.search-bar .btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr !important;
        gap: 16px !important;
    }

    .content-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 16px !important;
    }

    .header-actions {
        width: 100% !important;
        justify-content: flex-start !important;
    }

    .btn {
        font-size: 14px !important;
        padding: 8px 12px !important;
    }

    .table {
        font-size: 12px !important;
    }

    .action-buttons {
        flex-direction: column !important;
        gap: 2px !important;
    }

    .btn-sm {
        width: 100% !important;
        justify-content: center !important;
    }

    /* 搜索栏移动端样式 */
    .search-bar {
        padding: 16px !important;
        margin-bottom: 20px !important;
    }

    .search-bar-form {
        flex-direction: column !important;
        gap: 16px !important;
        flex-wrap: nowrap !important;
    }

    .search-bar .form-field {
        width: 100% !important;
        min-width: auto !important;
        max-width: none !important;
        flex: none !important;
    }

    .search-bar .btn {
        width: 100% !important;
        min-width: auto !important;
        height: 44px !important;
    }
}

@media (max-width: 480px) {
    .stat-card {
        padding: 16px !important;
    }

    .stat-number {
        font-size: 24px !important;
    }

    .stat-icon {
        width: 36px !important;
        height: 36px !important;
        font-size: 16px !important;
    }
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    animation: slideIn 0.3s ease;
}

.notification-content {
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-error {
    background: #fed7d7;
    color: #c53030;
    border-left: 4px solid #e53e3e;
}

.notification-warning {
    background: #fef5e7;
    color: #c05621;
    border-left: 4px solid #ed8936;
}

.notification-info {
    background: #bee3f8;
    color: #2b6cb0;
    border-left: 4px solid #3182ce;
}

.notification-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    margin-left: auto;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .notification {
        left: 10px;
        right: 10px;
        min-width: auto;
    }
}