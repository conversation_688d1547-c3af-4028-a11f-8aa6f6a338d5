<?php
/**
 * 检查数据库状态
 */

require_once 'includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>数据库状态检查</h2>";
    
    // 1. 检查表是否存在
    echo "<h3>1. 检查数据表</h3>";
    
    $tables = [
        'inbound_orders' => '入库单表',
        'inbound_order_items' => '入库单明细表',
        'outbound_orders' => '出库单表',
        'outbound_order_items' => '出库单明细表',
        'document_sequences' => '单据序列表'
    ];
    
    foreach ($tables as $table => $desc) {
        $result = $db->query("SHOW TABLES LIKE '$table'")->fetch();
        if ($result) {
            echo "<p style='color: green;'>✅ $desc ($table) 存在</p>";
        } else {
            echo "<p style='color: red;'>❌ $desc ($table) 不存在</p>";
        }
    }
    
    // 2. 检查入库记录表字段
    echo "<h3>2. 检查入库记录表字段</h3>";
    
    $columns = $db->query("SHOW COLUMNS FROM inbound_records")->fetchAll();
    $hasInboundOrderId = false;
    
    foreach ($columns as $col) {
        if ($col['Field'] === 'inbound_order_id') {
            $hasInboundOrderId = true;
            echo "<p style='color: green;'>✅ inbound_records 表有 inbound_order_id 字段 ({$col['Type']})</p>";
            break;
        }
    }
    
    if (!$hasInboundOrderId) {
        echo "<p style='color: red;'>❌ inbound_records 表缺少 inbound_order_id 字段</p>";
    }
    
    // 3. 检查出库记录表字段
    echo "<h3>3. 检查出库记录表字段</h3>";
    
    $columns = $db->query("SHOW COLUMNS FROM outbound_records")->fetchAll();
    $hasOutboundOrderId = false;
    
    foreach ($columns as $col) {
        if ($col['Field'] === 'outbound_order_id') {
            $hasOutboundOrderId = true;
            echo "<p style='color: green;'>✅ outbound_records 表有 outbound_order_id 字段 ({$col['Type']})</p>";
            break;
        }
    }
    
    if (!$hasOutboundOrderId) {
        echo "<p style='color: red;'>❌ outbound_records 表缺少 outbound_order_id 字段</p>";
    }
    
    // 4. 检查单据序列配置
    echo "<h3>4. 检查单据序列配置</h3>";
    
    $sequences = $db->fetchAll("SELECT * FROM document_sequences");
    if (!empty($sequences)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>单据类型</th><th>前缀</th><th>当前编号</th><th>日期格式</th></tr>";
        foreach ($sequences as $seq) {
            echo "<tr>";
            echo "<td>{$seq['doc_type']}</td>";
            echo "<td>{$seq['prefix']}</td>";
            echo "<td>{$seq['current_number']}</td>";
            echo "<td>{$seq['date_format']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ 没有单据序列配置</p>";
    }
    
    // 5. 检查现有数据
    echo "<h3>5. 检查现有数据</h3>";
    
    $inbound_orders_count = $db->fetchOne("SELECT COUNT(*) as count FROM inbound_orders")['count'];
    $outbound_orders_count = $db->fetchOne("SELECT COUNT(*) as count FROM outbound_orders")['count'];
    $inbound_records_count = $db->fetchOne("SELECT COUNT(*) as count FROM inbound_records")['count'];
    $outbound_records_count = $db->fetchOne("SELECT COUNT(*) as count FROM outbound_records")['count'];
    
    echo "<p>入库单数量: <strong>$inbound_orders_count</strong></p>";
    echo "<p>出库单数量: <strong>$outbound_orders_count</strong></p>";
    echo "<p>入库记录数量: <strong>$inbound_records_count</strong></p>";
    echo "<p>出库记录数量: <strong>$outbound_records_count</strong></p>";
    
    // 6. 检查关联关系
    echo "<h3>6. 检查关联关系</h3>";
    
    $linked_inbound = $db->fetchOne("SELECT COUNT(*) as count FROM inbound_records WHERE inbound_order_id IS NOT NULL")['count'];
    $linked_outbound = $db->fetchOne("SELECT COUNT(*) as count FROM outbound_records WHERE outbound_order_id IS NOT NULL")['count'];
    
    echo "<p>已关联入库单的入库记录: <strong>$linked_inbound</strong></p>";
    echo "<p>已关联出库单的出库记录: <strong>$linked_outbound</strong></p>";
    
    // 7. 检查控制器文件
    echo "<h3>7. 检查控制器文件</h3>";
    
    $files = [
        'modules/inbound/InboundOrderController.php' => '入库单控制器',
        'modules/outbound/OutboundOrderController.php' => '出库单控制器',
        'modules/inbound/orders.php' => '入库单入口',
        'modules/outbound/orders.php' => '出库单入口'
    ];
    
    foreach ($files as $file => $desc) {
        if (file_exists($file)) {
            echo "<p style='color: green;'>✅ $desc ($file) 存在</p>";
        } else {
            echo "<p style='color: red;'>❌ $desc ($file) 不存在</p>";
        }
    }
    
    // 8. 检查模板文件
    echo "<h3>8. 检查模板文件</h3>";
    
    $templates = [
        'modules/inbound/order-list-template.php' => '入库单列表模板',
        'modules/inbound/order-view-template.php' => '入库单详情模板',
        'modules/inbound/order-print-template.php' => '入库单打印模板'
    ];
    
    foreach ($templates as $file => $desc) {
        if (file_exists($file)) {
            echo "<p style='color: green;'>✅ $desc ($file) 存在</p>";
        } else {
            echo "<p style='color: red;'>❌ $desc ($file) 不存在</p>";
        }
    }
    
    echo "<h3>9. 总体状态</h3>";
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 系统检查完成！</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 检查失败：" . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

p {
    margin: 10px 0;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

table {
    background: white;
    margin: 15px 0;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
}

th {
    background: #007bff;
    color: white;
    padding: 10px;
}

td {
    padding: 8px 10px;
}
</style>
