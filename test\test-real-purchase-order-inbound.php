<?php
/**
 * 测试特定采购单的移动端入库功能
 */

// 构造采购单数据（基于真实采购单DD250622131033389315）
$orderGroup = [
    'order_info' => [
        'id' => 16,
        'order_number' => 'DD250622131033389315',
        'supplier_id' => 2,
        'supplier_name' => '测试供应商',
        'order_date' => '2025-06-22',
        'order_amount' => 11660.66,
        'notes' => ''
    ],
    'items' => [
        // 只取前3个商品进行测试
        [
            'id' => 286,
            'ingredient_id' => 141,
            'ingredient_name' => '大白菜',
            'ingredient_unit' => '斤',
            'quantity' => 40.00,
            'unit_price' => 1.11,
            'total_price' => 44.40,
            'purpose' => ''
        ],
        [
            'id' => 287,
            'ingredient_id' => 142,
            'ingredient_name' => '莲花白（平顶）',
            'ingredient_unit' => '斤',
            'quantity' => 40.00,
            'unit_price' => 1.07,
            'total_price' => 42.80,
            'purpose' => ''
        ],
        [
            'id' => 288,
            'ingredient_id' => 143,
            'ingredient_name' => '西兰花',
            'ingredient_unit' => '斤',
            'quantity' => 40.00,
            'unit_price' => 4.05,
            'total_price' => 162.00,
            'purpose' => ''
        ]
    ]
];

// 模拟移动端入库POST数据
$testData = [
    'batch_type' => 'batch',
    'supplier_id' => '2',
    'batch_number' => 'MOBILE' . microtime(true) * 10000,
    'inbound_date' => date('Y-m-d'),
    'operator_name' => '移动端操作员',
    'notes' => '移动端采购单入库测试',
    'order_id' => '16',
    'is_new_order' => '0', // 使用现有采购单
    'items' => [
        '0' => [
            'ingredient_id' => '141',
            'actual_quantity' => '38.5', // 实际重量
            'unit_price' => '1.11',
            'order_quantity' => '40.00'
        ],
        '1' => [
            'ingredient_id' => '142',
            'actual_quantity' => '39.2',
            'unit_price' => '1.07', 
            'order_quantity' => '40.00'
        ],
        '2' => [
            'ingredient_id' => '143',
            'actual_quantity' => '38.8',
            'unit_price' => '4.05',
            'order_quantity' => '40.00'
        ]
    ]
];

echo "测试采购单入库 - 采购单号: DD250622131033389315\n";
echo "供应商ID: 2\n";
echo "商品数量: 3个\n\n";

// 使用 file_get_contents 进行POST请求
$postData = http_build_query($testData);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/x-www-form-urlencoded',
            'X-Requested-With: XMLHttpRequest'
        ],
        'content' => $postData
    ]
]);

// 执行请求
$response = file_get_contents('http://localhost:8000/mobile/inbound.php', false, $context);

// 输出结果
if ($response === false) {
    echo "请求失败\n";
    exit;
}

echo "服务器响应:\n";
echo $response . "\n\n";

// 尝试解析JSON响应
$jsonResponse = json_decode($response, true);
if ($jsonResponse) {
    echo "解析后的响应:\n";
    echo "成功: " . ($jsonResponse['success'] ? '是' : '否') . "\n";
    echo "消息: " . $jsonResponse['message'] . "\n";
    if (isset($jsonResponse['data'])) {
        echo "数据: " . print_r($jsonResponse['data'], true) . "\n";
    }
} else {
    echo "无法解析为JSON，可能是HTML响应\n";
    // 如果包含错误信息，尝试提取
    if (strpos($response, 'Fatal error') !== false || strpos($response, 'Notice') !== false) {
        echo "检测到PHP错误信息，请检查代码\n";
    }
}
?>