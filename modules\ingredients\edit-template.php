<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-edit"></i>
                编辑食材
            </h1>
            <div class="header-actions">
                <a href="?action=view&id=<?= $ingredient['id'] ?>" class="btn btn-info">
                    <i class="fas fa-eye"></i>
                    查看详情
                </a>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <!-- 编辑食材表单 -->
        <div class="form-container">
            <div class="form-header">
                <div class="form-icon">
                    <i class="fas fa-edit"></i>
                </div>
                <div class="form-title">
                    <h2>编辑食材信息</h2>
                    <p>修改食材的基本信息</p>
                </div>
            </div>

            <form method="POST" class="ingredient-form">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name" class="form-label">
                            <i class="fas fa-tag"></i>
                            食材名称 <span class="required">*</span>
                        </label>
                        <input type="text" id="name" name="name" class="form-control" 
                               value="<?= htmlspecialchars($ingredient['name'] ?? '') ?>" 
                               placeholder="请输入食材名称" required>
                    </div>

                    <div class="form-group">
                        <label for="code" class="form-label">
                            <i class="fas fa-barcode"></i>
                            食材编码
                        </label>
                        <input type="text" id="code" name="code" class="form-control" 
                               value="<?= htmlspecialchars($ingredient['code'] ?? '') ?>" 
                               placeholder="请输入食材编码">
                    </div>

                    <div class="form-group">
                        <label for="category_id" class="form-label">
                            <i class="fas fa-folder"></i>
                            食材分类
                        </label>
                        <select id="category_id" name="category_id" class="form-control">
                            <option value="">请选择分类</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>" 
                                        <?= ($ingredient['category_id'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($category['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="unit" class="form-label">
                            <i class="fas fa-balance-scale"></i>
                            计量单位 <span class="required">*</span>
                        </label>
                        <input type="text" id="unit" name="unit" class="form-control" 
                               value="<?= htmlspecialchars($ingredient['unit'] ?? '') ?>" 
                               placeholder="如：公斤、个、袋" required>
                    </div>

                    <div class="form-group">
                        <label for="unit_price" class="form-label">
                            <i class="fas fa-yen-sign"></i>
                            单价（元）
                        </label>
                        <input type="number" id="unit_price" name="unit_price" class="form-control" 
                               value="<?= $ingredient['unit_price'] ?? '' ?>" 
                               step="0.01" min="0" placeholder="0.00">
                    </div>

                    <div class="form-group">
                        <label for="shelf_life_days" class="form-label">
                            <i class="fas fa-calendar-alt"></i>
                            保质期（天）
                        </label>
                        <input type="number" id="shelf_life_days" name="shelf_life_days" class="form-control" 
                               value="<?= $ingredient['shelf_life_days'] ?? 30 ?>" 
                               min="1" placeholder="30">
                    </div>

                    <div class="form-group">
                        <label for="min_stock" class="form-label">
                            <i class="fas fa-exclamation-triangle"></i>
                            最低库存
                        </label>
                        <input type="number" id="min_stock" name="min_stock" class="form-control" 
                               value="<?= $ingredient['min_stock'] ?? '' ?>" 
                               step="0.1" min="0" placeholder="0">
                    </div>

                    <div class="form-group">
                        <label for="specification" class="form-label">
                            <i class="fas fa-ruler"></i>
                            规格
                        </label>
                        <input type="text" id="specification" name="specification" class="form-control" 
                               value="<?= htmlspecialchars($ingredient['specification'] ?? '') ?>" 
                               placeholder="如：500g/袋、1kg/盒">
                    </div>

                    <div class="form-group full-width">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left"></i>
                            描述
                        </label>
                        <textarea id="description" name="description" class="form-control" 
                                  rows="3" placeholder="请输入食材描述"><?= htmlspecialchars($ingredient['description'] ?? '') ?></textarea>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-submit">
                        <i class="fas fa-save"></i>
                        保存修改
                    </button>
                    <a href="?action=view&id=<?= $ingredient['id'] ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                        取消
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* 表单样式 */
.form-container {
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px 30px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.form-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.form-title h2 {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: 600;
}

.form-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 30px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-label {
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-label .required {
    color: #e53e3e;
}

.form-control {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: #fafbfc;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
    padding: 20px 30px;
    background: #f8f9fa;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.btn-submit {
    min-width: 150px;
    padding: 12px 28px;
    font-size: 15px;
    font-weight: 600;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-submit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 20px rgba(72, 187, 120, 0.3);
}

.btn-outline-secondary {
    padding: 12px 24px;
    border: 2px solid #e2e8f0;
    background: white;
    color: #718096;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-outline-secondary:hover {
    border-color: #cbd5e0;
    background: #f7fafc;
    text-decoration: none;
}

.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert-danger {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .form-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .form-container {
        margin: 0 10px;
        border-radius: 12px;
    }

    .form-header {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .form-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        padding: 20px;
        gap: 15px;
    }

    .form-actions {
        padding: 15px 20px;
        flex-direction: column-reverse;
        gap: 10px;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
        padding: 14px 20px;
        font-size: 16px;
    }
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
