<!-- 库存趋势分析 -->

<!-- 趋势概览 -->
<div class="analytics-stats-grid">
    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-boxes"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">当前库存总量</div>
            <div class="stat-value"><?= number_format($data['trends']['summary']['total_inventory'] ?? 15420) ?></div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +3.2%
            </div>
        </div>
    </div>

    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">低库存预警</div>
            <div class="stat-value"><?= number_format($data['trends']['summary']['low_stock_items'] ?? 8) ?></div>
            <div class="stat-change negative">
                <i class="fas fa-arrow-down"></i>
                需补货
            </div>
        </div>
    </div>

    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-chart-pie"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">库存周转率</div>
            <div class="stat-value"><?= number_format($data['trends']['summary']['turnover_rate'] ?? 2.8, 1) ?></div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +0.3
            </div>
        </div>
    </div>

    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">库存价值</div>
            <div class="stat-value">¥<?= number_format($data['trends']['summary']['inventory_value'] ?? 285600) ?></div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +5.1%
            </div>
        </div>
    </div>
</div>

<!-- 主要趋势图表 -->
<div class="trend-chart-container">
    <div class="trend-chart-header">
        <div class="trend-chart-title">
            <i class="fas fa-chart-area"></i>
            库存变化趋势分析
        </div>
        <div class="chart-controls">
            <button class="chart-toggle active" onclick="toggleInventoryChart('trend')">
                <i class="fas fa-chart-line"></i>
                趋势图
            </button>
            <button class="chart-toggle" onclick="toggleInventoryChart('category')">
                <i class="fas fa-chart-bar"></i>
                分类对比
            </button>
        </div>
    </div>
    <div class="trend-chart-content">
        <canvas id="inventoryTrendChart"></canvas>
    </div>
</div>

<!-- 图表网格 -->
<div class="chart-grid">
    <!-- 库存分布饼图 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-chart-pie"></i>
                库存分布
            </div>
        </div>
        <div class="chart-container">
            <canvas id="inventoryDistributionChart"></canvas>
        </div>
    </div>

    <!-- 库存周转分析 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-sync-alt"></i>
                周转率分析
            </div>
        </div>
        <div class="chart-container">
            <canvas id="turnoverChart"></canvas>
        </div>
    </div>

    <!-- 库存预警雷达图 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-radar"></i>
                库存健康度
            </div>
        </div>
        <div class="chart-container">
            <canvas id="inventoryHealthChart"></canvas>
        </div>
    </div>

    <!-- 库存价值趋势 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-money-bill-wave"></i>
                价值变化
            </div>
        </div>
        <div class="chart-container">
            <canvas id="inventoryValueChart"></canvas>
        </div>
    </div>

    <!-- 入库出库对比 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-exchange-alt"></i>
                进出库对比
            </div>
        </div>
        <div class="chart-container">
            <canvas id="inOutChart"></canvas>
        </div>
    </div>

    <!-- 库存预测 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-crystal-ball"></i>
                库存预测
            </div>
        </div>
        <div class="chart-container">
            <canvas id="inventoryForecastChart"></canvas>
        </div>
    </div>
</div>

<script>
// 库存趋势数据
const inventoryData = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [{
        label: '库存数量',
        data: [15200, 14800, 15600, 15100, 15800, 15420],
        borderColor: '#3b82f6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4
    }]
};

let inventoryTrendChart;

// 初始化库存趋势图表
function initInventoryTrendChart() {
    const canvas = document.getElementById('inventoryTrendChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    inventoryTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: inventoryData.labels,
            datasets: [{
                label: '库存数量 (件)',
                data: inventoryData.datasets[0].data,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: '入库数量 (件)',
                data: [1200, 1100, 1350, 1180, 1420, 1280],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            }, {
                label: '出库数量 (件)',
                data: [1150, 1180, 1200, 1220, 1380, 1320],
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return '库存数量: ' + context.parsed.y.toLocaleString() + ' 件';
                            } else if (context.datasetIndex === 1) {
                                return '入库数量: ' + context.parsed.y + ' 件';
                            } else {
                                return '出库数量: ' + context.parsed.y + ' 件';
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '库存数量 (件)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' 件';
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '进出库数量 (件)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: function(value) {
                            return value + ' 件';
                        }
                    }
                }
            }
        }
    });
}

// 切换图表类型
function toggleInventoryChart(type) {
    // 更新按钮状态
    document.querySelectorAll('.chart-toggle').forEach(btn => btn.classList.remove('active'));
    event.target.closest('.chart-toggle').classList.add('active');
    
    if (type === 'category') {
        // 切换为分类对比图
        inventoryTrendChart.data.datasets = [{
            label: '蔬菜类',
            data: [5200, 4800, 5600, 5100, 5800, 5420],
            backgroundColor: '#10b981'
        }, {
            label: '肉类',
            data: [3200, 3100, 3400, 3200, 3600, 3320],
            backgroundColor: '#f59e0b'
        }, {
            label: '粮食类',
            data: [4800, 4900, 4600, 4800, 4400, 4680],
            backgroundColor: '#3b82f6'
        }, {
            label: '调料类',
            data: [2000, 2000, 2000, 2000, 2000, 2000],
            backgroundColor: '#ef4444'
        }];
        inventoryTrendChart.config.type = 'bar';
    } else {
        // 切换回趋势图
        inventoryTrendChart.data.datasets = inventoryData.datasets;
        inventoryTrendChart.config.type = 'line';
    }
    
    inventoryTrendChart.update();
}

// 初始化所有库存图表
function initInventoryCharts() {
    initInventoryDistributionChart();
    initTurnoverChart();
    initInventoryHealthChart();
    initInventoryValueChart();
    initInOutChart();
    initInventoryForecastChart();
}

// 库存分布饼图
function initInventoryDistributionChart() {
    const canvas = document.getElementById('inventoryDistributionChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['蔬菜类', '肉类', '粮食类', '调料类', '其他'],
            datasets: [{
                data: [35, 22, 30, 8, 5],
                backgroundColor: [
                    '#10b981',
                    '#f59e0b',
                    '#3b82f6',
                    '#ef4444',
                    '#8b5cf6'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

// 周转率分析
function initTurnoverChart() {
    const canvas = document.getElementById('turnoverChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['蔬菜类', '肉类', '粮食类', '调料类'],
            datasets: [{
                label: '周转率',
                data: [4.2, 3.8, 2.1, 1.5],
                backgroundColor: ['#10b981', '#f59e0b', '#3b82f6', '#ef4444'],
                borderRadius: 4,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '周转率 (次/月)'
                    }
                }
            }
        }
    });
}

// 库存健康度雷达图
function initInventoryHealthChart() {
    const canvas = document.getElementById('inventoryHealthChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['库存充足度', '周转效率', '成本控制', '预警及时性', '供应稳定性'],
            datasets: [{
                label: '当前状态',
                data: [85, 72, 90, 68, 88],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                borderWidth: 2,
                pointBackgroundColor: '#3b82f6',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: '#3b82f6'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        stepSize: 20
                    }
                }
            }
        }
    });
}

// 库存价值趋势
function initInventoryValueChart() {
    const canvas = document.getElementById('inventoryValueChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '库存价值',
                data: [275000, 268000, 282000, 276000, 289000, 285600],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '¥' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });
}

// 进出库对比
function initInOutChart() {
    const canvas = document.getElementById('inOutChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '入库',
                data: [1200, 1100, 1350, 1180, 1420, 1280],
                backgroundColor: '#10b981'
            }, {
                label: '出库',
                data: [1150, 1180, 1200, 1220, 1380, 1320],
                backgroundColor: '#f59e0b'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '数量 (件)'
                    }
                }
            }
        }
    });
}

// 库存预测
function initInventoryForecastChart() {
    const canvas = document.getElementById('inventoryForecastChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['4月', '5月', '6月', '7月(预测)', '8月(预测)', '9月(预测)'],
            datasets: [{
                label: '实际库存',
                data: [15100, 15800, 15420, null, null, null],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4
            }, {
                label: '预测库存',
                data: [null, null, 15420, 15600, 15800, 16000],
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                borderWidth: 3,
                borderDash: [5, 5],
                fill: false,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    title: {
                        display: true,
                        text: '库存数量 (件)'
                    }
                }
            }
        }
    });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initInventoryTrendChart();
    initInventoryCharts();
});
</script>
