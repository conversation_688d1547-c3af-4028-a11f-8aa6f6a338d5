<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-list"></i>
                批量出库
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <?php if (!empty($data['error_message'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <?= htmlspecialchars($data['error_message']) ?>
            </div>
        <?php endif; ?>

        <div class="form-container">
            <form method="POST" action="index.php?action=batch_create" class="form" id="batch-form">
                <!-- 基本信息 -->
                <div class="section">
                    <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="meal_type">
                                <i class="fas fa-utensils"></i>
                                餐次 <span class="required">*</span>
                            </label>
                            <select name="meal_type" id="meal_type" class="form-control" required>
                                <option value="">请选择餐次</option>
                                <option value="breakfast">早餐</option>
                                <option value="lunch">午餐</option>
                                <option value="dinner">晚餐</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="meal_date">
                                <i class="fas fa-calendar-alt"></i>
                                用餐日期 <span class="required">*</span>
                            </label>
                            <input type="date" name="meal_date" id="meal_date" class="form-control" 
                                   value="<?= date('Y-m-d') ?>" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="operator_name">
                                <i class="fas fa-user"></i>
                                操作员 <span class="required">*</span>
                            </label>
                            <input type="text" name="operator_name" id="operator_name" class="form-control" 
                                   value="管理员" required>
                        </div>

                        <div class="form-group">
                            <label for="notes">
                                <i class="fas fa-sticky-note"></i>
                                备注信息
                            </label>
                            <input type="text" name="notes" id="notes" class="form-control" 
                                   placeholder="批量出库备注...">
                        </div>
                    </div>
                </div>

                <!-- 食材列表 -->
                <div class="section">
                    <div class="section-header">
                        <h3><i class="fas fa-list"></i> 出库食材</h3>
                        <button type="button" class="btn btn-success btn-sm" onclick="addItem()">
                            <i class="fas fa-plus"></i>
                            添加食材
                        </button>
                    </div>

                    <div id="items-container">
                        <!-- 食材项目将通过JavaScript动态添加 -->
                    </div>

                    <div class="empty-state" id="empty-state">
                        <i class="fas fa-inbox"></i>
                        <p>请点击"添加食材"按钮添加需要出库的食材</p>
                    </div>
                </div>

                <!-- 汇总信息 -->
                <div class="section">
                    <h3><i class="fas fa-calculator"></i> 汇总信息</h3>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <label>食材种类：</label>
                            <span id="total-items">0</span> 种
                        </div>
                        <div class="summary-item">
                            <label>预估总价值：</label>
                            ¥<span id="total-amount">0.00</span>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        确认批量出库
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        取消
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 食材项目模板 -->
<template id="item-template">
    <div class="item-row" data-index="">
        <div class="item-content">
            <div class="form-group">
                <label>食材</label>
                <select name="items[][ingredient_id]" class="form-control ingredient-select" required onchange="updateItemInfo(this)">
                    <option value="">请选择食材</option>
                    <?php if (!empty($data['ingredients'])): ?>
                        <?php 
                        $currentCategory = '';
                        foreach ($data['ingredients'] as $ingredient): 
                            if ($currentCategory !== $ingredient['category_name']):
                                if ($currentCategory !== '') echo '</optgroup>';
                                $currentCategory = $ingredient['category_name'];
                                echo '<optgroup label="' . htmlspecialchars($currentCategory) . '">';
                            endif;
                        ?>
                            <option value="<?= $ingredient['id'] ?>" 
                                    data-stock="<?= $ingredient['current_stock'] ?>"
                                    data-unit="<?= htmlspecialchars($ingredient['unit']) ?>"
                                    data-name="<?= htmlspecialchars($ingredient['name']) ?>">
                                <?= htmlspecialchars($ingredient['name']) ?> 
                                (库存: <?= number_format($ingredient['current_stock'], 1) ?><?= htmlspecialchars($ingredient['unit']) ?>)
                            </option>
                        <?php endforeach; ?>
                        <?php if ($currentCategory !== '') echo '</optgroup>'; ?>
                    <?php endif; ?>
                </select>
            </div>

            <div class="form-group">
                <label>出库数量</label>
                <div class="input-group">
                    <input type="number" name="items[][quantity]" class="form-control quantity-input" 
                           step="0.1" min="0.1" required placeholder="数量" onchange="updateSummary()">
                    <span class="input-group-text unit-display">单位</span>
                </div>
            </div>

            <div class="form-group">
                <label>用途</label>
                <input type="text" name="items[][purpose]" class="form-control" placeholder="如：炒白菜">
            </div>

            <div class="form-group">
                <button type="button" class="btn btn-danger btn-sm remove-item" onclick="removeItem(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
        
        <div class="item-info">
            <span class="stock-info"></span>
        </div>
    </div>
</template>

<style>
.form-container {
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.section:last-child {
    border-bottom: none;
}

.section h3 {
    margin-bottom: 20px;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    margin: 0;
    flex: 1;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
}

.form-group label i {
    margin-right: 8px;
    color: #6b7280;
}

.required {
    color: #ef4444;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-group {
    display: flex;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group-text {
    padding: 12px;
    background: #f9fafb;
    border: 1px solid #d1d5db;
    border-left: none;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    font-size: 14px;
    color: #6b7280;
}

.item-row {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
}

.item-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 15px;
    align-items: end;
}

.item-info {
    margin-top: 10px;
    font-size: 12px;
    color: #6b7280;
}

.stock-info.warning {
    color: #f59e0b;
}

.stock-info.error {
    color: #ef4444;
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: #6b7280;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-item {
    background: #f9fafb;
    padding: 15px;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.summary-item label {
    font-weight: 600;
    color: #374151;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.alert {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.alert-danger {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.alert i {
    margin-right: 10px;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .item-content {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<script>
let itemIndex = 0;

function addItem() {
    const container = document.getElementById('items-container');
    const template = document.getElementById('item-template');
    const emptyState = document.getElementById('empty-state');
    
    const clone = template.content.cloneNode(true);
    const itemRow = clone.querySelector('.item-row');
    itemRow.dataset.index = itemIndex++;
    
    // 更新name属性
    const inputs = clone.querySelectorAll('input, select');
    inputs.forEach(input => {
        if (input.name.includes('[][]')) {
            input.name = input.name.replace('[][]', `[${itemRow.dataset.index}]`);
        }
    });
    
    container.appendChild(clone);
    emptyState.style.display = 'none';
    
    updateSummary();
}

function removeItem(button) {
    const itemRow = button.closest('.item-row');
    itemRow.remove();
    
    const container = document.getElementById('items-container');
    const emptyState = document.getElementById('empty-state');
    
    if (container.children.length === 0) {
        emptyState.style.display = 'block';
    }
    
    updateSummary();
}

function updateItemInfo(select) {
    const itemRow = select.closest('.item-row');
    const stockInfo = itemRow.querySelector('.stock-info');
    const unitDisplay = itemRow.querySelector('.unit-display');
    const quantityInput = itemRow.querySelector('.quantity-input');
    
    if (select.value) {
        const option = select.options[select.selectedIndex];
        const stock = parseFloat(option.dataset.stock);
        const unit = option.dataset.unit;
        const name = option.dataset.name;
        
        unitDisplay.textContent = unit;
        quantityInput.setAttribute('max', stock);
        
        if (stock > 0) {
            stockInfo.innerHTML = `库存：${stock} ${unit}`;
            stockInfo.className = 'stock-info';
        } else {
            stockInfo.innerHTML = `库存不足`;
            stockInfo.className = 'stock-info error';
        }
    } else {
        stockInfo.innerHTML = '';
        unitDisplay.textContent = '单位';
        quantityInput.removeAttribute('max');
    }
    
    updateSummary();
}

function updateSummary() {
    const itemRows = document.querySelectorAll('.item-row');
    let totalItems = 0;
    let totalAmount = 0;
    
    itemRows.forEach(row => {
        const select = row.querySelector('.ingredient-select');
        const quantityInput = row.querySelector('.quantity-input');
        
        if (select.value && quantityInput.value) {
            totalItems++;
            // 这里可以根据需要计算总价值，需要获取单价信息
        }
    });
    
    document.getElementById('total-items').textContent = totalItems;
    document.getElementById('total-amount').textContent = totalAmount.toFixed(2);
}

// 页面加载时添加一个空的食材项目
document.addEventListener('DOMContentLoaded', function() {
    addItem();
});
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>