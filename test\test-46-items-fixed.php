<?php
/**
 * 测试修复后的46个商品入库功能
 */

// 构造固定的46个商品数据
$testData = [
    'batch_type' => 'batch',
    'supplier_id' => '2',
    'batch_number' => 'FIXED_46_' . microtime(true) * 10000,
    'inbound_date' => date('Y-m-d'),
    'operator_name' => '修复测试操作员',
    'notes' => '修复后46个商品入库测试',
    'order_id' => '16',
    'is_new_order' => '0'
];

// 使用连续的数组索引添加46个商品
for ($i = 0; $i < 46; $i++) {
    $testData["items"][$i] = [
        'ingredient_id' => 141 + $i,
        'actual_quantity' => number_format(35.0 + ($i * 0.5), 1),
        'unit_price' => number_format(1.11 + ($i * 0.01), 2),
        'order_quantity' => '40.00'
    ];
}

echo "=== 测试修复后的46个商品入库 ===\n";
echo "商品数量: " . count($testData['items']) . "\n";
echo "数据结构: items[0] to items[45]\n";
echo "批次号: " . $testData['batch_number'] . "\n\n";

// 发送POST请求
$postData = http_build_query($testData);
echo "POST数据大小: " . strlen($postData) . " 字节\n";

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/x-www-form-urlencoded',
            'X-Requested-With: XMLHttpRequest'
        ],
        'content' => $postData
    ]
]);

echo "\n发送请求到移动端入库页面...\n";
$response = file_get_contents('http://localhost:8000/mobile/inbound.php', false, $context);

if ($response === false) {
    echo "❌ 请求失败\n";
    exit;
}

echo "服务器响应:\n";
echo "响应长度: " . strlen($response) . " 字符\n";

// 尝试解析JSON响应
$jsonResponse = json_decode($response, true);
if ($jsonResponse) {
    echo "✅ JSON响应解析成功\n";
    echo "成功状态: " . ($jsonResponse['success'] ? '✅ 成功' : '❌ 失败') . "\n";
    echo "消息: " . $jsonResponse['message'] . "\n";
    
    if (isset($jsonResponse['data'])) {
        echo "返回数据:\n";
        foreach ($jsonResponse['data'] as $key => $value) {
            echo "  $key: $value\n";
        }
    }
    
    if (isset($jsonResponse['error_code'])) {
        echo "错误代码: " . $jsonResponse['error_code'] . "\n";
    }
} else {
    echo "❌ 无法解析为JSON响应\n";
    echo "前500字符: " . substr($response, 0, 500) . "\n";
    
    // 检查是否包含PHP错误
    if (strpos($response, 'Fatal error') !== false || 
        strpos($response, 'Parse error') !== false || 
        strpos($response, 'Notice') !== false) {
        echo "检测到PHP错误，请检查日志\n";
    }
}

// 检查服务器端调试日志
$logFile = dirname(__DIR__) . '/logs/inbound_debug_' . date('Y-m-d') . '.log';
if (file_exists($logFile)) {
    echo "\n=== 最新的服务器端调试信息 ===\n";
    $logContent = file_get_contents($logFile);
    $logLines = explode("\n", $logContent);
    $lastLines = array_slice($logLines, -50); // 最后50行
    echo implode("\n", $lastLines);
}

echo "\n=== 测试完成 ===\n";
?>