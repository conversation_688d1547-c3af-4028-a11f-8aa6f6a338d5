<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>升级分类表支持二级分类</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>升级分类表支持二级分类</h1>
    
    <?php
    require_once 'includes/Database.php';
    
    try {
        $db = Database::getInstance();
        echo "<p class='success'>✅ 数据库连接成功</p>";
        
        // 检查当前表结构
        echo "<h2>检查当前表结构</h2>";
        $columns = $db->fetchAll("SHOW COLUMNS FROM ingredient_categories");
        $existingColumns = array_column($columns, 'Field');
        
        echo "<p>当前字段：" . implode(', ', $existingColumns) . "</p>";
        
        // 需要添加的字段
        $fieldsToAdd = [
            'parent_id' => "ADD COLUMN `parent_id` INT NULL DEFAULT NULL COMMENT '父分类ID（NULL表示一级分类）' AFTER `id`",
            'level' => "ADD COLUMN `level` TINYINT NOT NULL DEFAULT 1 COMMENT '分类级别（1=一级分类，2=二级分类）' AFTER `parent_id`"
        ];
        
        // 检查并添加缺失字段
        echo "<h2>添加缺失字段</h2>";
        foreach ($fieldsToAdd as $field => $sql) {
            if (!in_array($field, $existingColumns)) {
                echo "<p class='info'>🔧 添加字段 {$field}...</p>";
                try {
                    $db->query("ALTER TABLE `ingredient_categories` {$sql}");
                    echo "<p class='success'>✅ 字段 {$field} 添加成功</p>";
                } catch (Exception $e) {
                    echo "<p class='error'>❌ 字段 {$field} 添加失败: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p class='info'>ℹ️ 字段 {$field} 已存在</p>";
            }
        }
        
        // 检查description字段是否存在
        if (!in_array('description', $existingColumns)) {
            echo "<p class='info'>🔧 添加description字段...</p>";
            try {
                $db->query("ALTER TABLE `ingredient_categories` ADD COLUMN `description` TEXT NULL COMMENT '分类描述' AFTER `code`");
                echo "<p class='success'>✅ description字段添加成功</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ description字段添加失败: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p class='info'>ℹ️ description字段已存在</p>";
        }
        
        // 检查status字段是否存在
        if (!in_array('status', $existingColumns)) {
            echo "<p class='info'>🔧 添加status字段...</p>";
            try {
                $db->query("ALTER TABLE `ingredient_categories` ADD COLUMN `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态（1=启用，0=停用）' AFTER `sort_order`");
                echo "<p class='success'>✅ status字段添加成功</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ status字段添加失败: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p class='info'>ℹ️ status字段已存在</p>";
        }
        
        // 检查updated_at字段是否存在
        if (!in_array('updated_at', $existingColumns)) {
            echo "<p class='info'>🔧 添加updated_at字段...</p>";
            try {
                $db->query("ALTER TABLE `ingredient_categories` ADD COLUMN `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `created_at`");
                echo "<p class='success'>✅ updated_at字段添加成功</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ updated_at字段添加失败: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p class='info'>ℹ️ updated_at字段已存在</p>";
        }
        
        // 添加索引
        echo "<h2>添加索引</h2>";
        $indexes = [
            'idx_parent_id' => "ADD INDEX `idx_parent_id` (`parent_id`)",
            'idx_level' => "ADD INDEX `idx_level` (`level`)",
            'idx_status' => "ADD INDEX `idx_status` (`status`)"
        ];
        
        // 检查现有索引
        $existingIndexes = $db->fetchAll("SHOW INDEX FROM ingredient_categories");
        $existingIndexNames = array_column($existingIndexes, 'Key_name');
        
        foreach ($indexes as $indexName => $sql) {
            if (!in_array($indexName, $existingIndexNames)) {
                echo "<p class='info'>🔧 添加索引 {$indexName}...</p>";
                try {
                    $db->query("ALTER TABLE `ingredient_categories` {$sql}");
                    echo "<p class='success'>✅ 索引 {$indexName} 添加成功</p>";
                } catch (Exception $e) {
                    echo "<p class='error'>❌ 索引 {$indexName} 添加失败: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p class='info'>ℹ️ 索引 {$indexName} 已存在</p>";
            }
        }
        
        // 更新现有数据
        echo "<h2>更新现有数据</h2>";
        try {
            $result = $db->query("UPDATE `ingredient_categories` SET `level` = 1, `parent_id` = NULL WHERE `level` IS NULL OR `level` = 0");
            echo "<p class='success'>✅ 现有分类已设置为一级分类</p>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ 更新现有数据失败: " . $e->getMessage() . "</p>";
        }
        
        // 显示最终表结构
        echo "<h2>升级后表结构</h2>";
        $finalColumns = $db->fetchAll("SHOW COLUMNS FROM ingredient_categories");
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>字段名</th><th>类型</th><th>空值</th><th>默认值</th><th>注释</th></tr>";
        foreach ($finalColumns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>" . ($column['Comment'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 验证升级结果
        echo "<h2>验证升级结果</h2>";
        $level1Count = $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories WHERE level = 1")['count'];
        $level2Count = $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories WHERE level = 2")['count'];
        $hasParentCount = $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories WHERE parent_id IS NOT NULL")['count'];
        
        echo "<p>一级分类数量: {$level1Count}</p>";
        echo "<p>二级分类数量: {$level2Count}</p>";
        echo "<p>有父分类的分类数量: {$hasParentCount}</p>";
        
        echo "<h2>升级完成</h2>";
        echo "<p class='success'>🎉 分类表升级完成！现在支持二级分类功能。</p>";
        echo "<p><a href='modules/categories/index.php'>返回分类管理</a></p>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 升级失败: " . $e->getMessage() . "</p>";
        echo "<p>请检查数据库连接和权限设置。</p>";
    }
    ?>
</body>
</html>
