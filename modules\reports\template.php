<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<!-- 确保样式被加载 -->
<link rel="stylesheet" href="style.css">

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <div class="header-left">
                <h1>
                    <i class="fas fa-chart-bar"></i>
                    数据报表
                </h1>
                <p class="header-subtitle">全面的数据分析与报表展示</p>
            </div>
            <div class="header-actions">
                <div class="date-range-picker">
                    <form method="GET" class="date-form">
                        <input type="hidden" name="action" value="<?= $data['current_tab'] ?>">
                        <div class="date-inputs">
                            <input type="date" name="start_date" value="<?= $data['date_range']['start'] ?>" class="form-control">
                            <span>至</span>
                            <input type="date" name="end_date" value="<?= $data['date_range']['end'] ?>" class="form-control">
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            查询
                        </button>
                    </form>
                </div>
                <button type="button" class="btn btn-success" onclick="exportReport()">
                    <i class="fas fa-download"></i>
                    导出报表
                </button>
            </div>
        </div>

        <!-- 报表导航标签 -->
        <div class="report-tabs">
            <a href="?action=overview" class="tab-item <?= $data['current_tab'] === 'overview' ? 'active' : '' ?>">
                <i class="fas fa-tachometer-alt"></i>
                概览
            </a>
            <a href="?action=purchase" class="tab-item <?= $data['current_tab'] === 'purchase' ? 'active' : '' ?>">
                <i class="fas fa-shopping-cart"></i>
                采购报表
            </a>
            <a href="?action=inventory" class="tab-item <?= $data['current_tab'] === 'inventory' ? 'active' : '' ?>">
                <i class="fas fa-warehouse"></i>
                库存报表
            </a>
            <a href="?action=supplier" class="tab-item <?= $data['current_tab'] === 'supplier' ? 'active' : '' ?>">
                <i class="fas fa-truck"></i>
                供应商报表
            </a>
            <a href="?action=financial" class="tab-item <?= $data['current_tab'] === 'financial' ? 'active' : '' ?>">
                <i class="fas fa-dollar-sign"></i>
                财务报表
            </a>
        </div>

        <!-- 报表内容 -->
        <div class="report-content">
            <?php if ($data['current_tab'] === 'overview'): ?>
                <?php include 'views/overview.php'; ?>
            <?php elseif ($data['current_tab'] === 'purchase'): ?>
                <?php include 'views/purchase.php'; ?>
            <?php elseif ($data['current_tab'] === 'inventory'): ?>
                <?php include 'views/inventory.php'; ?>
            <?php elseif ($data['current_tab'] === 'supplier'): ?>
                <?php include 'views/supplier.php'; ?>
            <?php elseif ($data['current_tab'] === 'financial'): ?>
                <?php include 'views/financial.php'; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Chart.js 库 -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="script.js"></script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
