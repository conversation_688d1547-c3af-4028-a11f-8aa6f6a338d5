<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复采购单商品项目</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; text-decoration: none; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        .data-table th { background: #f5f5f5; }
        .form-group { margin: 15px 0; }
        .form-control { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🔧 修复采购单商品项目</h1>
    
    <?php
    require_once '../includes/Database.php';
    
    $message = '';
    $messageType = '';
    
    // 处理添加商品项目的请求
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_items') {
        try {
            $db = Database::getInstance();
            $orderId = intval($_POST['order_id']);
            
            // 获取一些示例食材
            $ingredients = $db->fetchAll("SELECT id, name, unit FROM ingredients LIMIT 5");
            
            if (!empty($ingredients)) {
                $db->beginTransaction();
                
                foreach ($ingredients as $index => $ingredient) {
                    $quantity = 10 + ($index * 5); // 10, 15, 20, 25, 30
                    $unitPrice = 5.00 + ($index * 2.50); // 5.00, 7.50, 10.00, 12.50, 15.00
                    $totalPrice = $quantity * $unitPrice;
                    
                    $itemData = [
                        'order_id' => $orderId,
                        'ingredient_id' => $ingredient['id'],
                        'quantity' => $quantity,
                        'unit_price' => $unitPrice,
                        'total_price' => $totalPrice,
                        'notes' => '测试商品项目'
                    ];
                    
                    $db->insert('purchase_order_items', $itemData);
                }
                
                // 更新采购单总金额
                $totalAmount = $db->fetchOne("
                    SELECT SUM(total_price) as total 
                    FROM purchase_order_items 
                    WHERE order_id = ?
                ", [$orderId])['total'];
                
                $db->update('purchase_orders', ['order_amount' => $totalAmount], 'id = ?', [$orderId]);
                
                $db->commit();
                $message = "成功为采购单添加了 " . count($ingredients) . " 个商品项目！";
                $messageType = 'success';
            } else {
                $message = "没有找到食材数据，请先在食材管理中添加食材";
                $messageType = 'error';
            }
            
        } catch (Exception $e) {
            if (isset($db)) {
                $db->rollback();
            }
            $message = "添加商品项目失败: " . $e->getMessage();
            $messageType = 'error';
        }
    }
    
    try {
        $db = Database::getInstance();
        
        if ($message) {
            echo "<div class='test-section'>";
            echo "<div class='alert alert-{$messageType}'>";
            echo "<p class='{$messageType}'>{$message}</p>";
            echo "</div>";
            echo "</div>";
        }
        
        echo "<div class='test-section'>";
        echo "<h2>📊 采购单状态检查</h2>";
        
        // 查找状态为1或2的采购单
        $orders = $db->fetchAll("
            SELECT po.*, s.name as supplier_name,
                   COUNT(poi.id) as item_count
            FROM purchase_orders po
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN purchase_order_items poi ON po.id = poi.order_id
            WHERE po.status IN (1, 2)
            GROUP BY po.id
            ORDER BY po.created_at DESC
        ");
        
        echo "<p class='info'>状态为1或2的采购单数量: <strong>" . count($orders) . "</strong></p>";
        
        if (!empty($orders)) {
            echo "<table class='data-table'>";
            echo "<tr><th>ID</th><th>订单号</th><th>供应商</th><th>状态</th><th>商品数量</th><th>总金额</th><th>操作</th></tr>";
            foreach ($orders as $order) {
                $statusText = $order['status'] == 1 ? '待确认' : '已确认';
                $rowClass = $order['item_count'] == 0 ? 'error' : 'success';
                echo "<tr class='{$rowClass}'>";
                echo "<td>{$order['id']}</td>";
                echo "<td>{$order['order_number']}</td>";
                echo "<td>{$order['supplier_name']}</td>";
                echo "<td>{$statusText}</td>";
                echo "<td>" . ($order['item_count'] ?: '❌ 无商品') . "</td>";
                echo "<td>" . number_format($order['order_amount'] ?? 0, 2) . "</td>";
                echo "<td>";
                if ($order['item_count'] == 0) {
                    echo "<form method='POST' style='display: inline;'>";
                    echo "<input type='hidden' name='action' value='add_items'>";
                    echo "<input type='hidden' name='order_id' value='{$order['id']}'>";
                    echo "<button type='submit' class='btn btn-warning'>添加商品</button>";
                    echo "</form>";
                } else {
                    echo "<a href='../purchase/index.php?action=view&id={$order['id']}' class='btn btn-success'>查看详情</a>";
                }
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ 没有找到状态为1或2的采购单</p>";
            echo "<p><a href='../modules/purchase/index.php?action=create' class='btn'>创建新采购单</a></p>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 入库模块查询测试</h2>";
        
        // 测试入库模块的查询
        $inboundQuery = $db->fetchAll("
            SELECT 
                po.id,
                po.order_number,
                po.supplier_id,
                s.name as supplier_name,
                po.order_date,
                po.order_amount,
                poi.ingredient_id,
                i.name as ingredient_name,
                i.unit,
                poi.quantity,
                poi.unit_price,
                COALESCE(poi.purpose, poi.notes, '') as purpose
            FROM purchase_orders po
            INNER JOIN purchase_order_items poi ON po.id = poi.order_id
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN ingredients i ON poi.ingredient_id = i.id
            WHERE po.status IN (1, 2) 
            AND poi.ingredient_id IS NOT NULL
            AND i.id IS NOT NULL
            ORDER BY po.order_date DESC, po.order_number ASC
        ");
        
        echo "<p class='info'>入库模块查询结果: <strong>" . count($inboundQuery) . "</strong> 条记录</p>";
        
        if (!empty($inboundQuery)) {
            echo "<p class='success'>✅ 查询成功！入库页面应该能显示采购单了</p>";
            
            // 按采购单分组显示
            $groupedOrders = [];
            foreach ($inboundQuery as $order) {
                $key = $order['id'] . '_' . $order['order_number'];
                if (!isset($groupedOrders[$key])) {
                    $groupedOrders[$key] = [
                        'order_info' => $order,
                        'items' => []
                    ];
                }
                $groupedOrders[$key]['items'][] = $order;
            }
            
            echo "<h4>可用于入库的采购单（" . count($groupedOrders) . "个）：</h4>";
            foreach ($groupedOrders as $key => $group) {
                $orderInfo = $group['order_info'];
                echo "<div style='background: #e6f3ff; padding: 15px; margin: 10px 0; border-radius: 8px;'>";
                echo "<h5><strong>{$orderInfo['order_number']}</strong> - {$orderInfo['supplier_name']}</h5>";
                echo "<p>商品数量: " . count($group['items']) . " 种</p>";
                echo "<ul>";
                foreach ($group['items'] as $item) {
                    echo "<li>{$item['ingredient_name']} - {$item['quantity']}{$item['unit']} - ¥{$item['unit_price']}/{$item['unit']}</li>";
                }
                echo "</ul>";
                echo "</div>";
            }
            
            echo "<p><a href='../modules/inbound/index.php?action=create' class='btn btn-success'>测试入库页面</a></p>";
            
        } else {
            echo "<p class='error'>❌ 入库模块查询结果为空</p>";
            echo "<p>请确保采购单有商品项目，并且相关的食材和供应商数据存在</p>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🌿 食材数据检查</h2>";
        
        $ingredientCount = $db->fetchOne("SELECT COUNT(*) as count FROM ingredients")['count'];
        echo "<p class='info'>食材总数: <strong>{$ingredientCount}</strong></p>";
        
        if ($ingredientCount == 0) {
            echo "<p class='error'>❌ 没有食材数据</p>";
            echo "<p><a href='../modules/ingredients/index.php' class='btn'>前往食材管理</a></p>";
        } else {
            $ingredients = $db->fetchAll("SELECT id, name, unit FROM ingredients LIMIT 5");
            echo "<h4>示例食材（前5个）：</h4>";
            echo "<table class='data-table'>";
            echo "<tr><th>ID</th><th>名称</th><th>单位</th></tr>";
            foreach ($ingredients as $ingredient) {
                echo "<tr>";
                echo "<td>{$ingredient['id']}</td>";
                echo "<td>{$ingredient['name']}</td>";
                echo "<td>{$ingredient['unit']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🏢 供应商数据检查</h2>";
        
        $supplierCount = $db->fetchOne("SELECT COUNT(*) as count FROM suppliers")['count'];
        echo "<p class='info'>供应商总数: <strong>{$supplierCount}</strong></p>";
        
        if ($supplierCount == 0) {
            echo "<p class='error'>❌ 没有供应商数据</p>";
            echo "<p><a href='../modules/suppliers/index.php' class='btn'>前往供应商管理</a></p>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-section'>";
        echo "<h2 class='error'>❌ 数据库连接失败</h2>";
        echo "<p class='error'>错误信息: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
    
    <div class="test-section">
        <h2>📋 操作指南</h2>
        
        <h4>如果采购单没有商品项目：</h4>
        <ol>
            <li>点击上面表格中的"添加商品"按钮，系统会自动添加示例商品</li>
            <li>或者手动编辑采购单，添加真实的商品项目</li>
        </ol>
        
        <h4>如果没有食材或供应商数据：</h4>
        <ol>
            <li>先前往食材管理添加食材</li>
            <li>前往供应商管理添加供应商</li>
            <li>然后创建包含商品的采购单</li>
        </ol>
        
        <h4>完成后测试：</h4>
        <p><a href="../modules/inbound/index.php?action=create" class="btn btn-success">测试入库页面</a></p>
    </div>
    
    <p><a href="../modules/inbound/index.php">返回入库管理</a></p>
</body>
</html>
