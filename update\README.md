# 数据库升级文件目录

本目录包含学校食材管理系统的数据库升级脚本，用于在系统更新时对数据库结构进行必要的修改。

## 升级文件说明

### 1. update-tables.php
- **功能**: 基础数据表结构升级
- **用途**: 创建和更新系统核心数据表
- **执行时机**: 系统初始化或基础表结构变更时

### 2. update-suppliers-table.php
- **功能**: 供应商表结构升级
- **用途**: 更新供应商管理相关的数据表结构
- **执行时机**: 供应商模块功能更新时

### 3. update-purchase-tables.php
- **功能**: 采购相关表结构升级
- **用途**: 更新采购订单、入库等相关数据表
- **执行时机**: 采购管理模块功能更新时

### 4. upgrade-categories-table.php
- **功能**: 食材分类表升级支持二级分类
- **用途**: 将分类表升级为支持两级分类结构
- **执行时机**: 启用二级分类功能时

## 使用方法

1. **备份数据库**: 在执行任何升级脚本前，请先备份现有数据库
2. **按顺序执行**: 建议按照文件编号顺序执行升级脚本
3. **检查结果**: 每次升级后检查数据库结构和数据完整性
4. **测试功能**: 升级完成后测试相关功能是否正常

## 注意事项

- 升级脚本具有幂等性，可以重复执行而不会造成数据损坏
- 每个脚本都会检查当前数据库状态，只执行必要的升级操作
- 如果升级过程中出现错误，请检查数据库连接和权限设置
- 建议在测试环境中先验证升级脚本的正确性

## 访问方式

通过浏览器访问对应的PHP文件来执行升级：
- http://localhost:8000/update/update-tables.php
- http://localhost:8000/update/update-suppliers-table.php
- http://localhost:8000/update/update-purchase-tables.php
- http://localhost:8000/update/upgrade-categories-table.php

## 版本记录

- v1.0: 基础表结构
- v1.1: 供应商管理功能
- v1.2: 采购管理功能
- v1.3: 二级分类支持
