<?php
/**
 * 下载分类导入Excel模板
 */

// 设置Excel文件头
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="分类导入模板.xlsx"');
header('Cache-Control: no-cache, must-revalidate');

// 创建简化的XLSX文件
function createSimpleXlsx($data) {
    $tempDir = sys_get_temp_dir() . '/xlsx_create_' . uniqid();
    mkdir($tempDir);
    mkdir($tempDir . '/xl');
    mkdir($tempDir . '/xl/worksheets');
    mkdir($tempDir . '/_rels');
    mkdir($tempDir . '/xl/_rels');
    
    // 创建 [Content_Types].xml
    $contentTypes = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml"/>
    <Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>
    <Override PartName="/xl/sharedStrings.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml"/>
</Types>';
    file_put_contents($tempDir . '/[Content_Types].xml', $contentTypes);
    
    // 创建 _rels/.rels
    $rels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/>
</Relationships>';
    file_put_contents($tempDir . '/_rels/.rels', $rels);
    
    // 创建 xl/_rels/workbook.xml.rels
    $workbookRels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/>
    <Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings" Target="sharedStrings.xml"/>
</Relationships>';
    file_put_contents($tempDir . '/xl/_rels/workbook.xml.rels', $workbookRels);
    
    // 创建 xl/workbook.xml
    $workbook = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
    <sheets>
        <sheet name="分类数据" sheetId="1" r:id="rId1"/>
    </sheets>
</workbook>';
    file_put_contents($tempDir . '/xl/workbook.xml', $workbook);
    
    // 收集所有字符串
    $strings = [];
    $stringMap = [];
    foreach ($data as $row) {
        foreach ($row as $cell) {
            if (is_string($cell) && !is_numeric($cell)) {
                if (!in_array($cell, $strings)) {
                    $strings[] = $cell;
                    $stringMap[$cell] = count($strings) - 1;
                }
            }
        }
    }
    
    // 创建 xl/sharedStrings.xml
    $sharedStrings = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="' . count($strings) . '" uniqueCount="' . count($strings) . '">';
    foreach ($strings as $string) {
        $sharedStrings .= '<si><t>' . htmlspecialchars($string, ENT_XML1, 'UTF-8') . '</t></si>';
    }
    $sharedStrings .= '</sst>';
    file_put_contents($tempDir . '/xl/sharedStrings.xml', $sharedStrings);
    
    // 创建 xl/worksheets/sheet1.xml
    $worksheet = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
    <sheetData>';
    
    foreach ($data as $rowIndex => $row) {
        $worksheet .= '<row r="' . ($rowIndex + 1) . '">';
        foreach ($row as $colIndex => $cell) {
            $colLetter = chr(65 + $colIndex); // A, B, C...
            $cellRef = $colLetter . ($rowIndex + 1);
            
            if (is_string($cell) && !is_numeric($cell)) {
                // 字符串类型，使用共享字符串
                $stringIndex = $stringMap[$cell];
                $worksheet .= '<c r="' . $cellRef . '" t="s"><v>' . $stringIndex . '</v></c>';
            } else {
                // 数字类型
                $worksheet .= '<c r="' . $cellRef . '"><v>' . htmlspecialchars($cell, ENT_XML1, 'UTF-8') . '</v></c>';
            }
        }
        $worksheet .= '</row>';
    }
    
    $worksheet .= '</sheetData></worksheet>';
    file_put_contents($tempDir . '/xl/worksheets/sheet1.xml', $worksheet);
    
    // 创建ZIP文件
    $zip = new ZipArchive();
    $zipFile = tempnam(sys_get_temp_dir(), 'xlsx');
    
    if ($zip->open($zipFile, ZipArchive::CREATE) !== TRUE) {
        throw new Exception('无法创建Excel文件');
    }
    
    // 添加所有文件到ZIP
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($tempDir),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($files as $file) {
        if (!$file->isDir()) {
            $filePath = $file->getRealPath();
            $relativePath = substr($filePath, strlen($tempDir) + 1);
            $zip->addFile($filePath, $relativePath);
        }
    }
    
    $zip->close();
    
    // 清理临时目录
    removeDirectory($tempDir);
    
    return $zipFile;
}

function removeDirectory($dir) {
    if (!is_dir($dir)) {
        return;
    }
    
    $files = array_diff(scandir($dir), ['.', '..']);
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            removeDirectory($path);
        } else {
            unlink($path);
        }
    }
    rmdir($dir);
}

// 准备模板数据
$templateData = [
    ['分类代码', '分类名称', '分类级别'], // 标题行
    ['VEG', '蔬菜类', '1'],
    ['MEAT', '肉类', '1'],
    ['FISH', '水产类', '1'],
    ['GRAIN', '粮食类', '1'],
    ['SEASONING', '调料类', '1'],
    ['DAIRY', '乳制品', '1'],
    ['VEG_LEAF', '叶菜类', '2'],
    ['VEG_ROOT', '根茎类', '2'],
    ['VEG_FRUIT', '果菜类', '2'],
    ['MEAT_PORK', '猪肉类', '2'],
    ['MEAT_BEEF', '牛肉类', '2'],
    ['MEAT_CHICKEN', '鸡肉类', '2'],
    ['FISH_FRESH', '淡水鱼', '2'],
    ['FISH_SEA', '海水鱼', '2'],
    ['GRAIN_RICE', '米类', '2'],
    ['GRAIN_FLOUR', '面类', '2'],
    ['SEASONING_SALT', '盐类', '2'],
    ['SEASONING_OIL', '油类', '2'],
    ['DAIRY_MILK', '牛奶类', '2'],
    ['DAIRY_CHEESE', '奶酪类', '2']
];

try {
    // 创建Excel文件
    $excelFile = createSimpleXlsx($templateData);
    
    // 输出文件内容
    readfile($excelFile);
    
    // 清理临时文件
    unlink($excelFile);
    
} catch (Exception $e) {
    // 如果Excel创建失败，返回错误信息
    header('Content-Type: text/plain; charset=utf-8');
    echo 'Excel模板创建失败: ' . $e->getMessage();
}
?>
