/* 供应商管理模块样式 */

/* 添加供应商按钮美化 */
.btn-add-supplier {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
    border: none !important;
    border-radius: 10px !important;
    padding: 10px 20px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.25) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn-add-supplier::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-add-supplier:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(72, 187, 120, 0.35) !important;
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%) !important;
}

.btn-add-supplier:hover::before {
    left: 100%;
}

.btn-add-supplier:active {
    transform: translateY(-1px) !important;
}

.btn-add-supplier i {
    font-size: 14px;
    margin-right: 6px;
}

/* 空状态下的添加按钮 */
.empty-state .btn-add-supplier {
    margin-top: 20px;
    padding: 14px 28px !important;
    font-size: 16px !important;
}

/* 供应商信息 */
.supplier-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.supplier-address {
    font-size: 12px;
    color: #718096;
}

/* 联系方式链接 */
.phone-link, .email-link {
    color: #4299e1;
    text-decoration: none;
    transition: color 0.2s ease;
}

.phone-link:hover, .email-link:hover {
    color: #3182ce;
    text-decoration: underline;
}

/* 日期时间 */
.date-text {
    font-weight: 500;
    color: #2d3748;
}

.time-ago {
    font-size: 12px;
    color: #718096;
}

/* 视图切换 */
.view-toggle {
    display: flex;
    gap: 5px;
}

.view-toggle .btn.active {
    background: #4299e1;
    color: white;
    border-color: #4299e1;
}

/* 供应商网格 */
.suppliers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

/* 供应商卡片 */
.supplier-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.supplier-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 供应商卡片头部 */
.supplier-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0;
}

.supplier-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4299e1, #3182ce);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.supplier-status {
    position: relative;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-dot.status-active {
    background: #38a169;
    box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.2);
}

.status-dot.status-inactive {
    background: #e53e3e;
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.2);
}

/* 供应商卡片内容 */
.supplier-content {
    padding: 20px;
}

.supplier-name {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 15px 0;
}

.supplier-contact {
    margin-bottom: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #4a5568;
}

.contact-item:last-child {
    margin-bottom: 0;
}

.contact-item i {
    width: 16px;
    color: #718096;
}

/* 供应商统计 */
.supplier-stats {
    display: flex;
    justify-content: space-around;
    padding: 15px 0;
    border-top: 1px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
}

.supplier-stats .stat-item {
    text-align: center;
}

.supplier-stats .stat-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
}

.supplier-stats .stat-label {
    font-size: 12px;
    color: #718096;
    margin-top: 2px;
}

/* 供应商操作 */
.supplier-actions {
    padding: 15px 20px;
    background: #f7fafc;
    display: flex;
    gap: 10px;
}

.supplier-actions .btn {
    flex: 1;
    justify-content: center;
}

/* 统计行样式 */
.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stat-icon.bg-primary { background: #4299e1; }
.stat-icon.bg-success { background: #38a169; }
.stat-icon.bg-info { background: #4299e1; }

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #718096;
    margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .suppliers-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .stats-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .supplier-card {
        margin: 0 10px;
    }
    
    .supplier-actions {
        flex-direction: column;
    }
    
    .supplier-actions .btn {
        width: 100%;
    }
    
    .view-toggle {
        flex-direction: column;
        width: 100%;
    }
    
    .view-toggle .btn {
        width: 100%;
    }
}

/* 动画效果 */
.supplier-card {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 表单容器样式 */
.form-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    padding: 30px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.form-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.form-title h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
}

.form-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

/* 表单网格 */
.form-grid {
    padding: 30px;
}

.form-section {
    margin-bottom: 30px;
    padding-bottom: 25px;
    border-bottom: 1px solid #e2e8f0;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: #4299e1;
    font-size: 16px;
}

/* 表单行和组 */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 20px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-label.required::after {
    content: ' *';
    color: #e53e3e;
}

.form-control {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-control:focus {
    outline: none;
    border-color: #4299e1;
    background: white;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.form-control::placeholder {
    color: #a0aec0;
}

/* 表单操作按钮 */
.form-actions {
    padding: 25px 30px;
    background: #f7fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.btn-submit {
    min-width: 150px;
    padding: 12px 28px;
    font-size: 15px;
    font-weight: 600;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border: none;
    border-radius: 10px;
    color: white;
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.btn-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(72, 187, 120, 0.35);
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
}

.btn-submit:hover::before {
    left: 100%;
}

.btn-submit:active {
    transform: translateY(-1px);
}

/* 响应式表单 */
@media (max-width: 768px) {
    .form-container {
        margin: 0 10px;
        border-radius: 12px;
    }

    .form-header {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .form-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .form-title h2 {
        font-size: 20px;
    }

    .form-grid {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-section {
        margin-bottom: 25px;
        padding-bottom: 20px;
    }

    .form-actions {
        padding: 20px;
        flex-direction: column-reverse;
        gap: 10px;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
        padding: 14px 20px;
        font-size: 16px;
    }

    /* 移动端按钮优化 */
    .btn-add-supplier {
        padding: 12px 24px !important;
        font-size: 16px !important;
    }

    .empty-state .btn-add-supplier {
        padding: 16px 32px !important;
        font-size: 18px !important;
    }
}

@media (max-width: 480px) {
    .form-header {
        padding: 15px;
    }

    .form-grid {
        padding: 15px;
    }

    .form-actions {
        padding: 15px;
    }
}
