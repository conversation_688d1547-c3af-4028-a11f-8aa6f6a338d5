<?php
/**
 * 应用修复：不关联用户表
 */

echo "<h2>应用修复：不关联用户表</h2>";

// 修复入库控制器
$inbound_controller_file = 'modules/inbound/InboundController.php';
$inbound_controller_content = file_get_contents($inbound_controller_file);

// 替换查询，不关联用户表
$old_query = 'SELECT io.*, s.name as supplier_name, u.name as operator_name,
                       COUNT(ioi.id) as item_count,
                       SUM(ioi.actual_quantity) as total_quantity,
                       io.total_amount
                FROM inbound_orders io
                LEFT JOIN suppliers s ON io.supplier_id = s.id
                LEFT JOIN users u ON io.created_by = u.id
                LEFT JOIN inbound_order_items ioi ON io.id = ioi.order_id';

$new_query = 'SELECT io.*, s.name as supplier_name, \'系统\' as operator_name,
                       COUNT(ioi.id) as item_count,
                       SUM(ioi.actual_quantity) as total_quantity,
                       io.total_amount
                FROM inbound_orders io
                LEFT JOIN suppliers s ON io.supplier_id = s.id
                LEFT JOIN inbound_order_items ioi ON io.id = ioi.order_id';

$inbound_controller_content = str_replace($old_query, $new_query, $inbound_controller_content);

if (file_put_contents($inbound_controller_file, $inbound_controller_content)) {
    echo "<p style='color: green;'>✅ 已修复入库控制器</p>";
} else {
    echo "<p style='color: red;'>❌ 修复入库控制器失败</p>";
}

// 修复出库控制器
$outbound_controller_file = 'modules/outbound/OutboundController.php';
$outbound_controller_content = file_get_contents($outbound_controller_file);

// 替换查询，不关联用户表
$old_query = 'SELECT oo.*, u.name as operator_name,
                       COUNT(ooi.id) as item_count,
                       SUM(ooi.quantity) as total_quantity,
                       oo.total_amount
                FROM outbound_orders oo
                LEFT JOIN users u ON oo.created_by = u.id
                LEFT JOIN outbound_order_items ooi ON oo.id = ooi.order_id';

$new_query = 'SELECT oo.*, \'系统\' as operator_name,
                       COUNT(ooi.id) as item_count,
                       SUM(ooi.quantity) as total_quantity,
                       oo.total_amount
                FROM outbound_orders oo
                LEFT JOIN outbound_order_items ooi ON oo.id = ooi.order_id';

$outbound_controller_content = str_replace($old_query, $new_query, $outbound_controller_content);

if (file_put_contents($outbound_controller_file, $outbound_controller_content)) {
    echo "<p style='color: green;'>✅ 已修复出库控制器</p>";
} else {
    echo "<p style='color: red;'>❌ 修复出库控制器失败</p>";
}

echo "<h3>修复完成</h3>";
echo "<p>现在所有查询都不会关联用户表，操作员显示为'系统'</p>";
echo "<p><a href='modules/inbound/index.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>测试入库页面</a></p>";
echo "<p><a href='modules/outbound/index.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>测试出库页面</a></p>";

echo "<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

p {
    margin: 10px 0;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}
</style>";
?>
