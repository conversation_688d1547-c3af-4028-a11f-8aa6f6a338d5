<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-vial"></i>
                留样记录详情
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
                <a href="index.php?action=edit&id=<?= $data['sample']['id'] ?>" class="btn btn-primary">
                    <i class="fas fa-edit"></i>
                    编辑
                </a>
            </div>
        </div>

        <?php if (!empty($data['error'])): ?>
        <div class="alert alert-danger">
            <?= htmlspecialchars($data['error']) ?>
        </div>
        <?php endif; ?>

        <div class="detail-container">
            <div class="detail-card">
                <h3 class="detail-title">基本信息</h3>
                <div class="detail-content">
                    <div class="detail-row">
                        <span class="detail-label">留样编号:</span>
                        <span class="detail-value"><?= htmlspecialchars($data['sample']['sample_code']) ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">食材名称:</span>
                        <span class="detail-value"><?= htmlspecialchars($data['sample']['ingredient_name'] ?? '未知') ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">批次号:</span>
                        <span class="detail-value"><?= htmlspecialchars($data['sample']['batch_number']) ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">留样数量:</span>
                        <span class="detail-value"><?= $data['sample']['sample_quantity'] ?> <?= htmlspecialchars($data['sample']['sample_unit']) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="detail-card">
                <h3 class="detail-title">时间信息</h3>
                <div class="detail-content">
                    <div class="detail-row">
                        <span class="detail-label">用餐日期:</span>
                        <span class="detail-value"><?= date('Y年m月d日', strtotime($data['sample']['meal_date'])) ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">留样时间:</span>
                        <span class="detail-value"><?= date('Y年m月d日 H:i:s', strtotime($data['sample']['sample_time'])) ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">过期日期:</span>
                        <span class="detail-value <?= strtotime($data['sample']['expiration_date']) < time() && $data['sample']['sample_status'] == 1 ? 'text-danger' : '' ?>">
                            <?= date('Y年m月d日', strtotime($data['sample']['expiration_date'])) ?>
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="detail-card">
                <h3 class="detail-title">存储信息</h3>
                <div class="detail-content">
                    <div class="detail-row">
                        <span class="detail-label">餐次:</span>
                        <span class="detail-value">
                            <?php
                            $mealTypes = [
                                'breakfast' => '早餐',
                                'lunch' => '午餐',
                                'dinner' => '晚餐',
                                'other' => '其他'
                            ];
                            echo $mealTypes[$data['sample']['meal_type']] ?? '未知';
                            ?>
                        </span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">存储位置:</span>
                        <span class="detail-value"><?= htmlspecialchars($data['sample']['storage_location'] ?? '未指定') ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">存储温度:</span>
                        <span class="detail-value"><?= htmlspecialchars($data['sample']['storage_temperature'] ?? '未指定') ?></span>
                    </div>
                </div>
            </div>
            
            <div class="detail-card">
                <h3 class="detail-title">管理信息</h3>
                <div class="detail-content">
                    <div class="detail-row">
                        <span class="detail-label">责任人:</span>
                        <span class="detail-value"><?= htmlspecialchars($data['sample']['responsible_person']) ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">状态:</span>
                        <span class="detail-value">
                            <?php if ($data['sample']['sample_status'] == 1): ?>
                                <span class="status-badge status-active">有效</span>
                            <?php else: ?>
                                <span class="status-badge status-inactive">已处理</span>
                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">创建时间:</span>
                        <span class="detail-value"><?= date('Y年m月d日 H:i:s', strtotime($data['sample']['created_at'])) ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">更新时间:</span>
                        <span class="detail-value"><?= date('Y年m月d日 H:i:s', strtotime($data['sample']['updated_at'])) ?></span>
                    </div>
                </div>
            </div>
            
            <?php if (!empty($data['sample']['notes'])): ?>
            <div class="detail-card">
                <h3 class="detail-title">备注信息</h3>
                <div class="detail-content">
                    <div class="detail-row">
                        <span class="detail-value"><?= nl2br(htmlspecialchars($data['sample']['notes'])) ?></span>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>