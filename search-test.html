<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索栏测试页面</title>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="includes/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body style="padding: 20px; background: #f8fafc;">
    <h1>搜索栏单行显示测试</h1>
    
    <div class="search-bar">
        <form method="GET" class="search-bar-form">
            <div class="form-field text-input">
                <label>搜索食材</label>
                <input type="text" class="form-control" name="search" placeholder="输入食材名称...">
            </div>
            <div class="form-field select-input">
                <label>分类</label>
                <select class="form-control" name="category">
                    <option value="">全部分类</option>
                    <option value="1">蔬菜类</option>
                    <option value="2">肉类</option>
                </select>
            </div>
            <div class="form-field select-input">
                <label>状态</label>
                <select class="form-control" name="status">
                    <option value="">全部状态</option>
                    <option value="1">启用</option>
                    <option value="0">禁用</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i>
                搜索
            </button>
            <a href="#" class="btn btn-secondary">
                <i class="fas fa-refresh"></i>
                重置
            </a>
        </form>
    </div>

    <div style="margin-top: 20px; padding: 15px; background: white; border-radius: 8px;">
        <p><strong>说明：</strong>上方的搜索栏应该在一行内显示所有元素，包括标签、输入框、下拉框和按钮。</p>
        <p>如果仍然显示多行，请检查浏览器开发者工具中的CSS样式是否被正确应用。</p>
    </div>
</body>
</html>