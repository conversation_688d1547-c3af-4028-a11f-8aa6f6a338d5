<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分析test.xlsx格式</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        .data-table th { background: #f5f5f5; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
    </style>
</head>
<body>
    <h1>分析test.xlsx格式 - 创建标准模板</h1>
    
    <?php
    require_once '../includes/ExcelReader.php';
    
    try {
        $testFile = '../test.xlsx';
        
        if (!file_exists($testFile)) {
            echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
            exit;
        }
        
        echo "<div class='test-section'>";
        echo "<h2>步骤1: 读取test.xlsx文件</h2>";
        
        $reader = new ExcelReader();
        $data = $reader->read($testFile, 'test.xlsx');
        
        echo "<p class='success'>✅ 文件读取成功，总行数: " . count($data) . "</p>";
        echo "<p class='info'>总列数: " . (empty($data) ? 0 : count($data[0])) . "</p>";
        echo "</div>";
        
        if (!empty($data)) {
            echo "<div class='test-section'>";
            echo "<h2>步骤2: 完整数据结构分析</h2>";
            
            echo "<div class='step'>";
            echo "<h4>📋 完整数据内容（所有行列）:</h4>";
            echo "<table class='data-table'>";
            
            // 表头
            echo "<tr><th>行号</th>";
            $maxCols = 0;
            foreach ($data as $row) {
                $maxCols = max($maxCols, count($row));
            }
            
            for ($i = 0; $i < $maxCols; $i++) {
                echo "<th>列" . ($i + 1) . "</th>";
            }
            echo "</tr>";
            
            // 数据行
            foreach ($data as $rowIndex => $row) {
                echo "<tr>";
                echo "<td><strong>" . ($rowIndex + 1) . "</strong></td>";
                
                for ($colIndex = 0; $colIndex < $maxCols; $colIndex++) {
                    $value = isset($row[$colIndex]) ? $row[$colIndex] : '';
                    $displayValue = htmlspecialchars($value);
                    
                    // 如果值太长，截断显示
                    if (strlen($displayValue) > 30) {
                        $displayValue = substr($displayValue, 0, 30) . '...';
                    }
                    
                    // 如果是空值，显示为灰色
                    if (empty(trim($value))) {
                        echo "<td style='color: #ccc; font-style: italic;'>(空)</td>";
                    } else {
                        echo "<td>'{$displayValue}'</td>";
                    }
                }
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
            
            echo "<div class='step'>";
            echo "<h4>🔍 格式分析:</h4>";
            
            // 分析格式类型
            $formatType = 'unknown';
            $analysis = [];
            
            if (count($data) >= 4) {
                $firstRow = $data[0] ?? [];
                $secondRow = $data[1] ?? [];
                
                // 检查是否是订货单格式
                if (count($firstRow) > 4 && count($secondRow) > 11) {
                    $orderNumber = trim($firstRow[1] ?? '');
                    if (!empty($orderNumber) && (strlen($orderNumber) > 10 || preg_match('/^[A-Z0-9]+/', $orderNumber))) {
                        $formatType = 'order_form';
                        $analysis[] = "✅ 检测为订货单格式";
                        $analysis[] = "订单号位置: 第1行第2列 = '{$orderNumber}'";
                        $analysis[] = "订单日期位置: 第1行第5列 = '" . ($firstRow[4] ?? '') . "'";
                        $analysis[] = "联系人位置: 第2行第12列 = '" . ($secondRow[11] ?? '') . "'";
                        $analysis[] = "送货地址位置: 第3行第2列 = '" . ($data[2][1] ?? '') . "'";
                        $analysis[] = "联系电话位置: 第3行第12列 = '" . ($data[2][11] ?? '') . "'";
                        $analysis[] = "订单金额位置: 第4行第2列 = '" . ($data[3][1] ?? '') . "'";
                        $analysis[] = "实际金额位置: 第4行第6列 = '" . ($data[3][5] ?? '') . "'";
                        $analysis[] = "预期交货日期位置: 第4行第12列 = '" . ($data[3][11] ?? '') . "'";
                        $analysis[] = "明细数据从第7行开始";
                    } else {
                        $formatType = 'simple_list';
                        $analysis[] = "⚠️ 不符合订货单格式，检测为简单列表格式";
                    }
                } else {
                    $formatType = 'simple_list';
                    $analysis[] = "⚠️ 行列数不足，检测为简单列表格式";
                }
            } else {
                $formatType = 'simple_list';
                $analysis[] = "⚠️ 数据行数不足，检测为简单列表格式";
            }
            
            echo "<p class='info'><strong>检测到的格式类型: {$formatType}</strong></p>";
            echo "<div class='code-block'>";
            foreach ($analysis as $item) {
                echo $item . "\n";
            }
            echo "</div>";
            echo "</div>";
            
            // 根据检测到的格式提供相应的模板结构
            echo "<div class='step'>";
            echo "<h4>📝 基于test.xlsx的模板结构:</h4>";
            
            if ($formatType === 'order_form') {
                echo "<p class='success'>✅ 将创建订货单格式模板</p>";
                echo "<div class='code-block'>";
                echo "订货单格式结构:\n";
                echo "第1行: [标签] [订单号] [空] [空] [订单日期] ...\n";
                echo "第2行: [空] [空] ... [联系人] ...\n";
                echo "第3行: [送货地址] [地址内容] ... [联系电话] ...\n";
                echo "第4行: [订单金额] [金额] ... [实际金额] [金额] ... [预期交货日期] ...\n";
                echo "第5行: 空行\n";
                echo "第6行: 明细标题行\n";
                echo "第7行开始: 明细数据\n";
                echo "</div>";
                
                // 分析明细部分
                if (count($data) > 6) {
                    echo "<h5>明细数据分析:</h5>";
                    echo "<div class='code-block'>";
                    for ($i = 6; $i < min(count($data), 12); $i++) {
                        $row = $data[$i];
                        echo "第" . ($i + 1) . "行: ";
                        for ($j = 0; $j < min(count($row), 17); $j++) {
                            $value = trim($row[$j] ?? '');
                            if (!empty($value)) {
                                echo "[{$j}]='{$value}' ";
                            }
                        }
                        echo "\n";
                    }
                    echo "</div>";
                }
                
            } else {
                echo "<p class='info'>ℹ️ 将创建简单列表格式模板</p>";
                echo "<div class='code-block'>";
                echo "简单列表格式结构:\n";
                echo "第1行: 标题行\n";
                echo "第2行开始: 数据行\n";
                echo "列结构: 供应商名称 | 订单日期 | 食材名称 | 数量 | 单价 | 备注\n";
                echo "</div>";
                
                // 分析数据行
                if (count($data) > 1) {
                    echo "<h5>数据行分析:</h5>";
                    echo "<div class='code-block'>";
                    for ($i = 0; $i < min(count($data), 6); $i++) {
                        $row = $data[$i];
                        echo "第" . ($i + 1) . "行: ";
                        for ($j = 0; $j < min(count($row), 6); $j++) {
                            $value = trim($row[$j] ?? '');
                            echo "[{$j}]='{$value}' ";
                        }
                        echo "\n";
                    }
                    echo "</div>";
                }
            }
            echo "</div>";
            echo "</div>";
            
            // 生成模板代码
            echo "<div class='test-section'>";
            echo "<h2>步骤3: 生成模板代码</h2>";
            
            echo "<div class='step'>";
            echo "<h4>🔧 基于test.xlsx的模板数据结构:</h4>";
            
            echo "<div class='code-block'>";
            echo "// 基于test.xlsx的模板数据\n";
            echo "\$templateData = [\n";
            
            // 输出前几行作为模板结构
            for ($i = 0; $i < min(count($data), 10); $i++) {
                $row = $data[$i];
                echo "    [";
                
                for ($j = 0; $j < count($row); $j++) {
                    $value = $row[$j] ?? '';
                    
                    // 如果是明显的数据字段，替换为示例数据
                    if ($i > 0 && !empty(trim($value))) {
                        if (is_numeric($value)) {
                            echo "'{$value}'";
                        } else {
                            // 根据位置和内容推测字段类型
                            if (strpos($value, '供应商') !== false || strpos($value, '公司') !== false) {
                                echo "'示例供应商'";
                            } elseif (preg_match('/\d{4}-\d{2}-\d{2}/', $value)) {
                                echo "'" . date('Y-m-d') . "'";
                            } elseif (strpos($value, '白菜') !== false || strpos($value, '蔬菜') !== false) {
                                echo "'示例食材'";
                            } else {
                                echo "'{$value}'";
                            }
                        }
                    } else {
                        echo "'{$value}'";
                    }
                    
                    if ($j < count($row) - 1) {
                        echo ", ";
                    }
                }
                
                echo "]";
                if ($i < min(count($data), 10) - 1) {
                    echo ",";
                }
                echo " // 第" . ($i + 1) . "行\n";
            }
            
            if (count($data) > 10) {
                echo "    // ... 还有 " . (count($data) - 10) . " 行数据\n";
            }
            
            echo "];\n";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 分析失败: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div class="test-section">
        <h2>步骤4: 创建标准模板</h2>
        <div class="step">
            <p class="info">基于上面的分析，我将创建一个完全符合您test.xlsx格式的模板。</p>
            <p><strong>下一步操作：</strong></p>
            <ol>
                <li>根据分析结果更新模板生成器</li>
                <li>确保新模板与test.xlsx格式完全一致</li>
                <li>测试新模板的导入功能</li>
            </ol>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
