<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分析参考提取逻辑</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .mapping-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .mapping-table th, .mapping-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .mapping-table th { background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>分析参考提取逻辑</h1>
    
    <div class="test-section">
        <h2>📋 参考逻辑分析</h2>
        <div class="step">
            <h4>🔍 关键信息提取位置：</h4>
            <table class="mapping-table">
                <tr>
                    <th>字段</th>
                    <th>位置</th>
                    <th>代码</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>订单号</td>
                    <td>第1行第2列</td>
                    <td>$data[0][1]</td>
                    <td>订货单编号</td>
                </tr>
                <tr>
                    <td>订单日期</td>
                    <td>第1行第5列</td>
                    <td>$data[0][4]</td>
                    <td>需要strtotime转换</td>
                </tr>
                <tr>
                    <td>预期交货日期</td>
                    <td>第4行第12列</td>
                    <td>$data[3][11]</td>
                    <td>需要strtotime转换</td>
                </tr>
                <tr>
                    <td>联系人</td>
                    <td>第2行第12列</td>
                    <td>$data[1][11]</td>
                    <td>联系人姓名</td>
                </tr>
                <tr>
                    <td>送货地址</td>
                    <td>第3行第2列</td>
                    <td>$data[2][1]</td>
                    <td>配送地址</td>
                </tr>
                <tr>
                    <td>联系电话</td>
                    <td>第3行第12列</td>
                    <td>$data[2][11]</td>
                    <td>联系方式</td>
                </tr>
                <tr>
                    <td>订单金额</td>
                    <td>第4行第2列</td>
                    <td>$data[3][1]</td>
                    <td>floatval转换</td>
                </tr>
                <tr>
                    <td>实际金额</td>
                    <td>第4行第6列</td>
                    <td>$data[3][5]</td>
                    <td>floatval转换</td>
                </tr>
            </table>
        </div>
        
        <div class="step">
            <h4>📦 明细数据提取（从第7行开始）：</h4>
            <table class="mapping-table">
                <tr>
                    <th>字段</th>
                    <th>列位置</th>
                    <th>代码</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>商品编码</td>
                    <td>第1列</td>
                    <td>$row[0]</td>
                    <td>用于判断是否为空行</td>
                </tr>
                <tr>
                    <td>单价</td>
                    <td>第8列</td>
                    <td>$row[7]</td>
                    <td>floatval转换</td>
                </tr>
                <tr>
                    <td>数量</td>
                    <td>第9列</td>
                    <td>$row[8]</td>
                    <td>floatval转换</td>
                </tr>
                <tr>
                    <td>小计</td>
                    <td>第10列</td>
                    <td>$row[9]</td>
                    <td>floatval转换</td>
                </tr>
                <tr>
                    <td>实收数量</td>
                    <td>第12列</td>
                    <td>$row[11]</td>
                    <td>floatval转换</td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td>第17列</td>
                    <td>$row[16]</td>
                    <td>可选字段</td>
                </tr>
            </table>
        </div>
        
        <div class="step">
            <h4>🎯 关键特点：</h4>
            <ul>
                <li><strong>工作表名称</strong>：'订货单'</li>
                <li><strong>头部信息</strong>：分布在前4行的特定位置</li>
                <li><strong>明细数据</strong>：从第7行（索引6）开始</li>
                <li><strong>空行判断</strong>：检查第1列（商品编码）是否为空</li>
                <li><strong>食材映射</strong>：需要根据商品编码或名称查找ingredient_id</li>
                <li><strong>默认值</strong>：supplier_id=1, canteen_id=1, created_by=1</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔧 需要改进的地方</h2>
        <div class="step">
            <h4>⚠️ 原逻辑的问题：</h4>
            <ul>
                <li><strong>硬编码映射</strong>：ingredient_id = 1000 + $i（不实用）</li>
                <li><strong>固定默认值</strong>：supplier_id, canteen_id, created_by都是固定的</li>
                <li><strong>缺少验证</strong>：没有数据验证和错误处理</li>
                <li><strong>依赖PhpSpreadsheet</strong>：需要Composer安装</li>
                <li><strong>缺少商品名称提取</strong>：没有提取商品名称字段</li>
            </ul>
            
            <h4>✅ 我们的改进方案：</h4>
            <ul>
                <li><strong>智能食材匹配</strong>：根据商品名称自动查找或创建食材</li>
                <li><strong>供应商匹配</strong>：根据实际数据匹配供应商</li>
                <li><strong>完整数据验证</strong>：验证所有必填字段</li>
                <li><strong>无依赖实现</strong>：使用我们的ExcelReader</li>
                <li><strong>错误处理</strong>：详细的错误信息和调试</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📝 推测的Excel结构</h2>
        <div class="step">
            <h4>📋 基于提取逻辑推测的Excel结构：</h4>
            <div class="code-block">
第1行: [标签] [订单号] [空] [空] [订单日期] ...
第2行: [空] [空] [空] [空] [空] [空] [空] [空] [空] [空] [空] [联系人] ...
第3行: [空] [送货地址] [空] [空] [空] [空] [空] [空] [空] [空] [空] [联系电话] ...
第4行: [空] [订单金额] [空] [空] [空] [实际金额] [空] [空] [空] [空] [空] [预期交货日期] ...
第5行: 空行
第6行: 明细标题行
第7行开始: [商品编码] [商品名称] [规格] [单位] [品牌] [产地] [保质期] [单价] [数量] [小计] [税率] [实收数量] [合格数量] [不合格数量] [损耗数量] [拒收数量] [备注]
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🚀 实施计划</h2>
        <div class="step">
            <h4>📋 更新计划：</h4>
            <ol>
                <li><strong>更新格式检测逻辑</strong>：精确匹配订货单格式</li>
                <li><strong>完善头部信息提取</strong>：按照参考逻辑的精确位置</li>
                <li><strong>改进明细数据处理</strong>：包含商品名称等完整信息</li>
                <li><strong>智能食材匹配</strong>：替换硬编码的ingredient_id</li>
                <li><strong>供应商智能匹配</strong>：根据实际数据匹配</li>
                <li><strong>创建标准模板</strong>：完全符合这个格式的模板</li>
            </ol>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
