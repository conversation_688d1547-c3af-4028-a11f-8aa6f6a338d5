---
type: "manual"
---

All test files must be placed strictly within the test/ directory at the project root:

✅ Examples: test/user_test.py, test/api/order.test.js

❌ Test files must not be scattered across source directories such as src/, lib/, or other folders.

Test file naming conventions:

For a source file like src/foo.py, the corresponding test file should be named test/test_foo.py or test/foo_test.py.

Supported languages include Python, JavaScript, TypeScript, Go, Java, and others.

All significant logic or structural changes must be documented in the README.md file:

This includes but is not limited to:

Introduction of new modules or features and their purposes

Modifications to directory structures (e.g., splitting or merging folders)

Changes to function signatures, parameters, or data structures

Updates to testing methodology or usage patterns

Documentation must be clear, concise, and understandable for both humans and AI tools.

AI code assistants performing refactors or generating code must adhere to the following constraints:

All tests must be automatically written to the test/ directory.

If any functional or testing logic is edited, the README.md must be updated automatically to reflect the changes in a clearly written change log section.