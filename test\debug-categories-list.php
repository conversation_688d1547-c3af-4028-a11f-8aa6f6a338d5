<?php
/**
 * 调试分类列表显示问题
 */

require_once '../includes/Database.php';

echo "<h2>调试分类列表显示问题</h2>";

try {
    $db = Database::getInstance();
    echo "<p>✅ 数据库连接成功</p>";
    
    // 1. 检查所有分类数据（不加任何条件）
    echo "<h3>1. 所有分类数据（原始数据）：</h3>";
    $allCategories = $db->fetchAll("SELECT * FROM ingredient_categories ORDER BY id ASC");
    
    if (empty($allCategories)) {
        echo "<p style='color: red;'>❌ 分类表为空</p>";
    } else {
        echo "<p style='color: green;'>✅ 找到 " . count($allCategories) . " 个分类</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>名称</th><th>代码</th><th>描述</th><th>排序</th><th>状态</th><th>创建时间</th></tr>";
        
        foreach ($allCategories as $category) {
            $statusText = isset($category['status']) ? ($category['status'] ? '启用(1)' : '禁用(0)') : '未设置';
            $statusColor = isset($category['status']) && $category['status'] ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($category['id']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($category['name']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($category['code'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars(substr($category['description'] ?? '', 0, 30)) . (strlen($category['description'] ?? '') > 30 ? '...' : '') . "</td>";
            echo "<td>" . htmlspecialchars($category['sort_order'] ?? '0') . "</td>";
            echo "<td style='color: $statusColor;'>" . $statusText . "</td>";
            echo "<td>" . htmlspecialchars($category['created_at'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 2. 检查启用状态的分类
    echo "<h3>2. 启用状态的分类（status = 1）：</h3>";
    $activeCategories = $db->fetchAll("SELECT * FROM ingredient_categories WHERE status = 1 ORDER BY id ASC");
    
    if (empty($activeCategories)) {
        echo "<p style='color: red;'>❌ 没有启用状态的分类</p>";
        echo "<p>这就是为什么分类列表页面为空的原因！</p>";
    } else {
        echo "<p style='color: green;'>✅ 找到 " . count($activeCategories) . " 个启用的分类</p>";
        foreach ($activeCategories as $category) {
            echo "<p>• " . htmlspecialchars($category['name']) . " (ID: {$category['id']})</p>";
        }
    }
    
    // 3. 修复建议
    echo "<h3>3. 修复建议：</h3>";
    
    if (empty($activeCategories) && !empty($allCategories)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4 style='color: #856404;'>🔧 问题诊断：分类状态问题</h4>";
        echo "<p>所有分类的状态都不是1，导致列表页面为空。</p>";
        echo "<p><strong>解决方案：</strong></p>";
        echo "<ol>";
        echo "<li><a href='?action=fix_status' style='color: #007bff;'>批量修复分类状态</a></li>";
        echo "<li>或者修改控制器查询条件</li>";
        echo "</ol>";
        echo "</div>";
    }
    
    // 4. 处理修复操作
    if (isset($_GET['action']) && $_GET['action'] === 'fix_status') {
        echo "<h3>4. 修复分类状态：</h3>";
        
        try {
            $updateCount = 0;
            foreach ($allCategories as $category) {
                if (!isset($category['status']) || $category['status'] != 1) {
                    $db->update('ingredient_categories', ['status' => 1], 'id = ?', [$category['id']]);
                    echo "<p style='color: green;'>✅ 修复分类「{$category['name']}」的状态</p>";
                    $updateCount++;
                }
            }
            
            if ($updateCount > 0) {
                echo "<p style='color: green; font-weight: bold;'>🎉 成功修复 $updateCount 个分类的状态</p>";
                echo "<p><a href='../modules/categories/index.php' style='color: #007bff;'>查看分类列表</a></p>";
            } else {
                echo "<p style='color: blue;'>ℹ️ 所有分类状态都正常，无需修复</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 修复失败: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}


    // 5. 检查ingredients表
    echo "<h3>5. 检查ingredients表：</h3>";
    try {
        $ingredientCount = $db->fetchOne("SELECT COUNT(*) as count FROM ingredients");
        echo "<p>📊 食材表中有 " . $ingredientCount['count'] . " 条记录</p>";

        if ($ingredientCount['count'] > 0) {
            $ingredientSample = $db->fetchAll("SELECT name, category_id, status FROM ingredients LIMIT 5");
            echo "<p>📋 食材样本：</p>";
            echo "<ul>";
            foreach ($ingredientSample as $ingredient) {
                echo "<li>" . htmlspecialchars($ingredient['name']) . " (分类ID: {$ingredient['category_id']}, 状态: {$ingredient['status']})</li>";
            }
            echo "</ul>";
        }
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ ingredients表检查失败: " . $e->getMessage() . "</p>";
        echo "<p>这可能是因为表不存在或字段不匹配</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../modules/categories/index.php'>分类列表</a> | <a href='../modules/categories/index.php?action=create'>添加分类</a> | <a href='../index.php'>返回首页</a></p>";
?>
