<?php
/**
 * 测试横向卡片布局
 */

echo "=== 横向卡片布局测试 ===\n\n";

echo "1. 检查CSS布局设置:\n";
if (file_exists('modules/purchase/style.css')) {
    $css_content = file_get_contents('modules/purchase/style.css');
    
    // 检查信息区域的横向布局
    if (strpos($css_content, 'flex-direction: row') !== false) {
        echo "   ✅ 信息区域设置为横向布局\n";
    } else {
        echo "   ❌ 信息区域未设置为横向布局\n";
    }
    
    // 检查flex-wrap设置
    if (strpos($css_content, 'flex-wrap: wrap') !== false) {
        echo "   ✅ 设置了flex-wrap换行\n";
    } else {
        echo "   ❌ 未设置flex-wrap换行\n";
    }
    
    // 检查卡片的flex设置
    if (strpos($css_content, 'flex: 1') !== false) {
        echo "   ✅ 卡片设置为等宽分布\n";
    } else {
        echo "   ❌ 卡片未设置为等宽分布\n";
    }
    
    // 检查最小宽度
    if (strpos($css_content, 'min-width: 300px') !== false) {
        echo "   ✅ 卡片设置了最小宽度 300px\n";
    } else {
        echo "   ❌ 卡片未设置合适的最小宽度\n";
    }
}

echo "\n2. 检查容器布局:\n";
if (file_exists('modules/purchase/style.css')) {
    $css_content = file_get_contents('modules/purchase/style.css');
    
    // 检查主容器设置
    if (strpos($css_content, 'grid-template-columns: 1fr') !== false) {
        echo "   ✅ 主容器设置为单列布局\n";
    } else {
        echo "   ❌ 主容器未设置为单列布局\n";
    }
    
    // 检查最大宽度
    if (strpos($css_content, 'max-width: 1400px') !== false) {
        echo "   ✅ 容器最大宽度设置为 1400px\n";
    } else {
        echo "   ❌ 容器最大宽度设置不正确\n";
    }
}

echo "\n3. 检查响应式设计:\n";
if (file_exists('modules/purchase/style.css')) {
    $css_content = file_get_contents('modules/purchase/style.css');
    
    // 检查移动端布局
    if (strpos($css_content, 'flex-direction: column') !== false) {
        echo "   ✅ 移动端设置为纵向布局\n";
    } else {
        echo "   ❌ 移动端未设置为纵向布局\n";
    }
    
    // 检查移动端最小宽度重置
    if (strpos($css_content, 'min-width: auto') !== false) {
        echo "   ✅ 移动端重置了最小宽度\n";
    } else {
        echo "   ❌ 移动端未重置最小宽度\n";
    }
}

echo "\n4. 检查模板结构:\n";
if (file_exists('modules/purchase/import-template.php')) {
    $template_content = file_get_contents('modules/purchase/import-template.php');
    
    // 检查信息区域结构
    if (strpos($template_content, 'info-section') !== false) {
        echo "   ✅ 信息区域容器存在\n";
    } else {
        echo "   ❌ 信息区域容器不存在\n";
    }
    
    // 统计信息卡片数量
    $card_count = substr_count($template_content, 'info-card');
    echo "   ✅ 信息卡片数量: {$card_count} 个\n";
    
    // 检查卡片类型
    $card_types = [
        '格式要求' => '格式要求',
        '注意事项' => '注意事项', 
        '示例数据' => '示例数据'
    ];
    
    echo "   信息卡片类型:\n";
    foreach ($card_types as $type => $description) {
        if (strpos($template_content, $type) !== false) {
            echo "     ✅ {$description}\n";
        } else {
            echo "     ❌ {$description} - 未找到\n";
        }
    }
}

echo "\n5. 布局效果预期:\n";
echo "   📱 桌面端: 三个卡片横向排列，等宽分布\n";
echo "   📱 平板端: 卡片可能换行，保持横向优先\n";
echo "   📱 手机端: 卡片纵向排列，全宽显示\n";

echo "\n6. 访问链接:\n";
echo "   横向布局页面: http://localhost:8000/modules/purchase/index.php?action=import\n";

echo "\n=== 横向布局测试完成 ===\n";
echo "✨ 信息卡片已设置为横向排列！\n";
echo "🎯 桌面端三个卡片并排显示\n";
echo "📱 移动端自动切换为纵向布局\n";
echo "🔄 支持响应式换行和适配\n";
?>
