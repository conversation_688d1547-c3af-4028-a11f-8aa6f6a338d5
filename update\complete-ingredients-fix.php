<?php
/**
 * 完整修复ingredients表结构
 */

require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>完整修复ingredients表结构</h1>";
    
    // 检查当前表结构
    echo "<h2>1. 检查当前表结构</h2>";
    
    $columns = $db->fetchAll("DESCRIBE ingredients");
    $columnNames = array_column($columns, 'Field');
    
    echo "<p>当前字段: " . implode(', ', $columnNames) . "</p>";
    
    // 完整的字段定义
    $requiredColumns = [
        'id' => "INT AUTO_INCREMENT PRIMARY KEY",
        'code' => "VARCHAR(50) UNIQUE COMMENT '食材编码'",
        'name' => "VARCHAR(100) NOT NULL COMMENT '食材名称'",
        'specification' => "VARCHAR(100) DEFAULT '' COMMENT '规格'",
        'unit' => "VARCHAR(20) DEFAULT '个' COMMENT '单位'",
        'category_id' => "INT DEFAULT 1 COMMENT '分类ID'",
        'brand' => "VARCHAR(50) DEFAULT '' COMMENT '品牌'",
        'origin' => "VARCHAR(50) DEFAULT '' COMMENT '产地'",
        'shelf_life' => "VARCHAR(50) DEFAULT '' COMMENT '保质期'",
        'shelf_life_days' => "INT DEFAULT 0 COMMENT '保质期天数'",
        'purchase_price' => "DECIMAL(10,2) DEFAULT 0 COMMENT '采购价'",
        'created_by' => "INT DEFAULT 1 COMMENT '创建者ID'",
        'status' => "TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用'",
        'created_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
        'updated_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ];
    
    echo "<h2>2. 添加缺少的字段</h2>";
    
    foreach ($requiredColumns as $column => $definition) {
        if (!in_array($column, $columnNames)) {
            try {
                if ($column === 'id') {
                    // 跳过主键，应该已经存在
                    continue;
                }
                
                $sql = "ALTER TABLE ingredients ADD COLUMN {$column} {$definition}";
                $db->query($sql);
                echo "<p style='color: green;'>✅ 添加字段 {$column} 成功</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ 添加字段 {$column} 失败: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ 字段 {$column} 已存在</p>";
        }
    }
    
    echo "<h2>3. 修改字段默认值（如果需要）</h2>";
    
    // 确保关键字段有默认值
    $fieldsToModify = [
        'brand' => "VARCHAR(50) DEFAULT ''",
        'origin' => "VARCHAR(50) DEFAULT ''",
        'shelf_life' => "VARCHAR(50) DEFAULT ''",
        'shelf_life_days' => "INT DEFAULT 0",
        'created_by' => "INT DEFAULT 1",
        'specification' => "VARCHAR(100) DEFAULT ''",
        'unit' => "VARCHAR(20) DEFAULT '个'",
        'category_id' => "INT DEFAULT 1",
        'purchase_price' => "DECIMAL(10,2) DEFAULT 0",
        'status' => "TINYINT DEFAULT 1"
    ];
    
    foreach ($fieldsToModify as $column => $definition) {
        if (in_array($column, $columnNames)) {
            try {
                $sql = "ALTER TABLE ingredients MODIFY COLUMN {$column} {$definition}";
                $db->query($sql);
                echo "<p style='color: green;'>✅ 修改字段 {$column} 默认值成功</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ 修改字段 {$column} 默认值失败: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>4. 验证修复结果</h2>";
    
    $newColumns = $db->fetchAll("DESCRIBE ingredients");
    $newColumnNames = array_column($newColumns, 'Field');
    
    echo "<p>修复后字段: " . implode(', ', $newColumnNames) . "</p>";
    
    // 显示字段详情
    echo "<h4>字段详情：</h4>";
    echo "<table style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>字段名</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>类型</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>允许NULL</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>默认值</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>注释</th>";
    echo "</tr>";
    
    foreach ($newColumns as $column) {
        $bgColor = '';
        if (in_array($column['Field'], ['brand', 'origin', 'shelf_life', 'shelf_life_days', 'created_by'])) {
            $bgColor = 'background: #e8f5e8;'; // 高亮新增字段
        }
        
        echo "<tr style='{$bgColor}'>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$column['Field']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$column['Type']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$column['Null']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>" . ($column['Comment'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>5. 测试插入数据</h2>";
    
    // 测试插入一条完整数据
    try {
        $testData = [
            'code' => 'TEST_COMPLETE_' . time(),
            'name' => '完整测试食材_' . date('H:i:s'),
            'specification' => '测试规格',
            'unit' => '个',
            'category_id' => 1,
            'brand' => '测试品牌',
            'origin' => '测试产地',
            'shelf_life' => '30天',
            'shelf_life_days' => 30,
            'purchase_price' => 10.50,
            'created_by' => 1,
            'status' => 1
        ];
        
        echo "<p>测试数据: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "</p>";
        
        $testId = $db->insert('ingredients', $testData);
        
        if ($testId) {
            echo "<p style='color: green;'>✅ 测试插入成功: ID={$testId}</p>";
            
            // 查询插入的数据
            $insertedData = $db->fetchOne("SELECT * FROM ingredients WHERE id = ?", [$testId]);
            echo "<p>插入的数据: " . json_encode($insertedData, JSON_UNESCAPED_UNICODE) . "</p>";
            
            // 删除测试数据
            $db->query("DELETE FROM ingredients WHERE id = ?", [$testId]);
            echo "<p style='color: blue;'>ℹ️ 测试数据已清理</p>";
        } else {
            echo "<p style='color: red;'>❌ 测试插入失败</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 测试插入异常: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>✅ 修复完成！</h2>";
    echo "<p><a href='../test/test-auto-create-ingredients.php'>重新测试自动创建食材功能</a></p>";
    echo "<p><a href='../modules/purchase/index.php?action=import'>重新测试Excel导入</a></p>";
    echo "<p><a href='../test/view-import-log.php?clear_log=1'>清空日志重新测试</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ 修复失败</h2>";
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
