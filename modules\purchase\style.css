/* 采购管理模块样式 */

/* 表单行布局 */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
}

/* 四列布局 */
.form-row-4 {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-row-4 .form-group {
    flex: 1;
    min-width: 0; /* 防止内容溢出 */
}

/* 响应式设计 - 在小屏幕上改为两列 */
@media (max-width: 1200px) {
    .form-row-4 {
        flex-wrap: wrap;
    }

    .form-row-4 .form-group {
        flex: 0 0 calc(50% - 7.5px);
    }
}

/* 响应式设计 - 在更小屏幕上改为单列 */
@media (max-width: 768px) {
    .form-row-4 .form-group {
        flex: 0 0 100%;
    }
}

/* 商品明细表格样式 */
.items-container {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    width: 100%;
    min-width: 1200px; /* 减小最小宽度 */
}

.items-header {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    display: grid;
    grid-template-columns: 120px 120px 150px 60px 80px 80px 80px 100px 60px; /* 大幅缩小各列宽度 */
    gap: 1px;
    font-weight: 600;
    color: #374151;
}

.item-row {
    display: grid;
    grid-template-columns: 120px 120px 150px 60px 80px 80px 80px 100px 60px; /* 与表头保持一致 */
    gap: 1px;
    border-bottom: 1px solid #f1f5f9;
    background: white;
}

.item-row:hover {
    background: #f8fafc;
}

.item-col {
    padding: 8px 4px;
    display: flex;
    align-items: center;
    font-size: 14px;
}

/* 各列特定样式 */
.item-col-category {
    background: #fef7f0;
    border-right: 1px solid #fed7aa;
}

.item-col-subcategory {
    background: #f0f9ff;
    border-right: 1px solid #bae6fd;
}

.item-col-ingredient {
    background: #f0fdf4;
    border-right: 1px solid #bbf7d0;
}

.item-col-unit {
    background: #fafafa;
    border-right: 1px solid #e5e5e5;
    justify-content: center;
}

.item-col-quantity,
.item-col-price {
    background: #fffbeb;
    border-right: 1px solid #fde68a;
    justify-content: center;
}

.item-col-total {
    background: #f0f9ff;
    border-right: 1px solid #bae6fd;
    justify-content: center;
    font-weight: 600;
    color: #1e40af;
}

.item-col-purpose {
    background: #f3e8ff;
    border-right: 1px solid #d8b4fe;
    justify-content: center;
}

.item-col-action {
    background: #fef2f2;
    justify-content: center;
}

/* 表单控件样式 */
.item-col .form-control {
    width: 100%;
    padding: 4px 6px;
    border: 1px solid #d1d5db;
    border-radius: 3px;
    font-size: 14px;
    background: white;
}

.item-col .form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
}

/* 按钮样式 */
.item-col .btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
}

/* 总计显示 */
.item-total {
    font-weight: 600;
    color: #1e40af;
}

/* 商品明细底部 */
.items-footer {
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-width: 1200px;
}

.total-amount {
    font-size: 16px;
    font-weight: 600;
    color: #1e40af;
}

.total-amount span {
    color: #374151;
    margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .items-container {
        overflow-x: auto;
    }
}

@media (max-width: 768px) {
    .items-header,
    .item-row {
        grid-template-columns: 100px 100px 120px 50px 60px 60px 60px 80px 50px;
        font-size: 12px;
    }

    .item-col {
        padding: 6px 3px;
    }

    .item-col .form-control {
        font-size: 11px;
        padding: 3px 5px;
    }
}

/* 导入页面样式 */

/* 上传区域样式 */
.upload-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

.section-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.section-header h3 {
    margin: 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.upload-area {
    border: 3px dashed #dee2e6;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 1.5rem;
}

.upload-area:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.upload-area.dragover {
    border-color: #28a745;
    background: #e8f5e8;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.upload-area:hover .upload-icon {
    color: #007bff;
}

.upload-area.dragover .upload-icon {
    color: #28a745;
}

.upload-text h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.upload-text p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* 文件信息显示 */
.file-info {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.file-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.file-meta {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.file-size {
    color: #6c757d;
    font-size: 0.875rem;
}

.btn-remove {
    width: 32px;
    height: 32px;
    border: none;
    background: #dc3545;
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-remove:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* 文件预览 */
.file-preview-content h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.preview-lines {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

.preview-line {
    display: flex;
    margin-bottom: 0.5rem;
}

.preview-line.header {
    font-weight: bold;
    color: #007bff;
}

.line-number {
    width: 30px;
    color: #6c757d;
    margin-right: 1rem;
}

.line-content {
    flex: 1;
    word-break: break-all;
}

/* 导入选项 */
.import-options {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.checkbox-label:last-child {
    margin-bottom: 0;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    margin-right: 0.75rem;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #007bff;
    border-color: #007bff;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 12px;
    font-weight: bold;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

/* 信息区域 */
.info-section {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: flex-start;
}

.info-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    flex: 1;
    min-width: 280px;
}

.card-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: between;
    align-items: center;
}

.card-header h4 {
    margin: 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
}

.card-content {
    padding: 1.5rem;
}

.format-list {
    display: grid;
    gap: 0.5rem;
    margin: 1rem 0;
}

.format-item {
    padding: 0.5rem 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #dee2e6;
    font-size: 0.9rem;
}

.format-item.required {
    border-left-color: #dc3545;
    background: #fff5f5;
}

.required-mark {
    color: #dc3545;
    font-weight: bold;
}

.note {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 1rem;
}

.notes-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.notes-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f1f1;
    color: #495057;
    font-size: 0.9rem;
}

.notes-list li:last-child {
    border-bottom: none;
}

.notes-list li::before {
    content: '•';
    color: #007bff;
    font-weight: bold;
    margin-right: 0.5rem;
}

/* 示例表格 */
.example-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.example-table th,
.example-table td {
    padding: 0.75rem 0.5rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.example-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.example-table tbody tr:hover {
    background: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .upload-section {
        padding: 1.5rem;
    }

    .info-section {
        flex-direction: column;
        gap: 1.5rem;
    }

    .info-card {
        min-width: auto;
    }

    .upload-area {
        padding: 2rem 1rem;
    }

    .upload-icon {
        font-size: 3rem;
    }

    .file-details {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }

    .card-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .example-table {
        font-size: 0.8rem;
    }

    .example-table th,
    .example-table td {
        padding: 0.5rem 0.25rem;
    }
}

@media (max-width: 480px) {
    .upload-section {
        padding: 1rem;
    }

    .upload-area {
        padding: 1.5rem 0.75rem;
    }

    .upload-icon {
        font-size: 2.5rem;
    }

    .upload-text h4 {
        font-size: 1.1rem;
    }

    .file-icon {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }

    .card-content {
        padding: 1rem;
    }

    .format-list {
        gap: 0.25rem;
    }

    .format-item {
        padding: 0.4rem 0.6rem;
        font-size: 0.85rem;
    }
}

/* 动画和过渡效果 */
.upload-area,
.info-card,
.file-info {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* 焦点状态 */
.btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.upload-area:focus-within {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* 加载状态 */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

/* 成功状态 */
.upload-area.success {
    border-color: #28a745;
    background: #e8f5e8;
}

.upload-area.success .upload-icon {
    color: #28a745;
}





.tip-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.tip-content h5 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.tip-content p {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* 上传区域样式 */
.upload-section {
    margin-bottom: 2rem;
}

.upload-area {
    border: 3px dashed #dee2e6;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 1.5rem;
}

.upload-area:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.upload-area.dragover {
    border-color: #28a745;
    background: #e8f5e8;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.upload-area:hover .upload-icon {
    color: #007bff;
}

.upload-area.dragover .upload-icon {
    color: #28a745;
}

.upload-text h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.upload-text p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* 文件信息显示 */
.file-info {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.file-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.file-meta {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.file-size {
    color: #6c757d;
    font-size: 0.875rem;
}

.btn-remove {
    width: 32px;
    height: 32px;
    border: none;
    background: #dc3545;
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-remove:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* 文件预览 */
.file-preview-content h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.preview-lines {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

.preview-line {
    display: flex;
    margin-bottom: 0.5rem;
}

.preview-line.header {
    font-weight: bold;
    color: #007bff;
}

.line-number {
    width: 30px;
    color: #6c757d;
    margin-right: 1rem;
}

.line-content {
    flex: 1;
    word-break: break-all;
}

/* 导入选项 */
.import-options {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.option-group {
    margin-bottom: 1rem;
}

.option-group:last-child {
    margin-bottom: 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    color: #2c3e50;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    margin-right: 0.75rem;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #007bff;
    border-color: #007bff;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 12px;
    font-weight: bold;
}

/* 示例数据部分 */
.example-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 2rem;
}

.example-section .section-header {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: #fff;
    padding: 1.5rem 2rem;
    margin: 0;
}

.example-section .section-header h3 {
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.example-section .section-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

/* 标签页样式 */
.example-tabs {
    padding: 2rem;
}

.tab-buttons {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 1.5rem;
}

.tab-btn {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    color: #6c757d;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tab-btn:hover {
    color: #007bff;
    background: #f8f9fa;
}

.tab-btn.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: #f8f9fa;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

/* 示例表格样式 */
.example-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.example-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.example-table th {
    background: #f8f9fa;
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #e9ecef;
    white-space: nowrap;
}

.example-table th .required {
    color: #dc3545;
    font-weight: bold;
}

.example-table td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: top;
}

.example-table .example-row:hover {
    background: #f8f9fa;
}

.cell-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.cell-value {
    font-weight: 500;
    color: #2c3e50;
}

.cell-note {
    color: #6c757d;
    font-size: 0.8rem;
    font-style: italic;
}

.empty-cell {
    color: #dc3545;
    font-style: italic;
    font-size: 0.875rem;
}

/* CSV格式视图 */
.csv-example {
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
}

.csv-header {
    background: #2c3e50;
    color: #fff;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.csv-header:hover {
    background: #34495e;
}

.csv-content {
    margin: 0;
    padding: 1.5rem;
    background: #fff;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    overflow-x: auto;
    cursor: pointer;
    transition: all 0.3s ease;
}

.csv-content:hover {
    background: #f8f9fa;
}

/* 错误提示 */
.error-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #dc3545;
    color: #fff;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(220, 53, 69, 0.3);
    z-index: 1000;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 进度条 */
.import-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.progress-content {
    background: #fff;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    min-width: 300px;
}

.progress-content h4 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    width: 0;
    animation: progressAnimation 2s ease-in-out infinite;
}

@keyframes progressAnimation {
    0% { width: 0; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.progress-content p {
    color: #6c757d;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .wizard-steps {
        flex-direction: column;
    }

    .step:not(:last-child)::after {
        display: none;
    }

    .step {
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .step:last-child {
        border-bottom: none;
    }

    .wizard-content {
        padding: 1rem;
    }

    .format-cards {
        grid-template-columns: 1fr;
    }

    .requirements-grid {
        grid-template-columns: 1fr;
    }

    .tips-grid {
        grid-template-columns: 1fr;
    }

    .step-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .step-actions .btn {
        width: 100%;
    }

    .tab-buttons {
        flex-direction: column;
    }

    .tab-btn {
        border-bottom: 1px solid #e9ecef;
        border-radius: 0;
    }

    .tab-btn.active {
        border-bottom-color: #e9ecef;
        background: #007bff;
        color: #fff;
    }

    .example-table-container {
        font-size: 0.8rem;
    }

    .example-table th,
    .example-table td {
        padding: 0.5rem 0.25rem;
    }

    .upload-area {
        padding: 2rem 1rem;
    }

    .upload-icon {
        font-size: 3rem;
    }

    .file-details {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .error-toast {
        left: 10px;
        right: 10px;
        top: 10px;
    }

    .progress-content {
        margin: 1rem;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .step-number {
        width: 28px;
        height: 28px;
        font-size: 0.875rem;
    }

    .step-title {
        font-size: 0.875rem;
    }

    .format-card {
        padding: 1rem;
    }

    .card-icon {
        font-size: 2.5rem;
    }

    .template-card {
        padding: 1.5rem;
    }

    .template-icon {
        font-size: 2.5rem;
    }

    .tip-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .upload-area {
        padding: 1.5rem 0.75rem;
    }

    .upload-icon {
        font-size: 2.5rem;
    }

    .upload-text h4 {
        font-size: 1.1rem;
    }

    .file-icon {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }
}

/* 动画增强 */
.format-card,
.tip-item,
.template-card {
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.format-card:hover,
.tip-item:hover,
.template-card:hover {
    transform: translateY(-4px);
}

/* 滚动条美化 */
.preview-lines::-webkit-scrollbar,
.csv-content::-webkit-scrollbar,
.example-table-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.preview-lines::-webkit-scrollbar-track,
.csv-content::-webkit-scrollbar-track,
.example-table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.preview-lines::-webkit-scrollbar-thumb,
.csv-content::-webkit-scrollbar-thumb,
.example-table-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.preview-lines::-webkit-scrollbar-thumb:hover,
.csv-content::-webkit-scrollbar-thumb:hover,
.example-table-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 焦点状态优化 */
.btn:focus,
.tab-btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.upload-area:focus-within {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* 加载状态 */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

/* 成功状态 */
.upload-area.success {
    border-color: #28a745;
    background: #e8f5e8;
}

.upload-area.success .upload-icon {
    color: #28a745;
}
.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.info-column h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.info-column ul, .info-column ol {
    margin-bottom: 1.5rem;
}

.info-column li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.required {
    color: #e74c3c;
    font-weight: bold;
}

.note {
    color: #7f8c8d;
    font-style: italic;
}

.template-download {
    margin-top: 1.5rem;
}

.import-form {
    max-width: 600px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input[type="file"] {
    width: 100%;
    padding: 0.75rem;
    border: 2px dashed #bdc3c7;
    border-radius: 8px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.form-group input[type="file"]:hover {
    border-color: #3498db;
    background: #e3f2fd;
}

.form-group input[type="file"]:focus {
    outline: none;
    border-color: #2980b9;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-help {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #7f8c8d;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }
}

/* 新建采购单按钮美化 */
.btn-create-purchase {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
    border: none !important;
    border-radius: 10px !important;
    padding: 8px 18px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.25) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn-create-purchase::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-create-purchase:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(66, 153, 225, 0.35) !important;
    background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%) !important;
}

.btn-create-purchase:hover::before {
    left: 100%;
}

.btn-create-purchase:active {
    transform: translateY(-1px) !important;
}

.btn-create-purchase i {
    font-size: 14px;
    margin-right: 6px;
}

/* 空状态下的新建按钮 */
.empty-state .btn-create-purchase {
    margin-top: 25px;
    padding: 16px 40px !important;
    font-size: 18px !important;
    font-weight: 700 !important;
    border-radius: 16px !important;
    box-shadow: 0 8px 24px rgba(66, 153, 225, 0.3) !important;
    transform: scale(1.05);
    letter-spacing: 0.5px;
    min-width: 200px;
    position: relative;
    overflow: hidden;
}

.empty-state .btn-create-purchase::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
}

.empty-state .btn-create-purchase:hover {
    transform: scale(1.08) translateY(-3px) !important;
    box-shadow: 0 12px 32px rgba(66, 153, 225, 0.4) !important;
    background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%) !important;
}

.empty-state .btn-create-purchase:hover::before {
    left: 100%;
}

.empty-state .btn-create-purchase:active {
    transform: scale(1.03) translateY(-1px) !important;
}

.empty-state .btn-create-purchase i {
    font-size: 20px;
    margin-right: 10px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 空状态区域美化 */
.empty-state {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border: 2px dashed #cbd5e0;
    border-radius: 20px;
    padding: 60px 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.empty-state::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(66, 153, 225, 0.05) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
    pointer-events: none;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state:hover {
    border-color: #4299e1;
    background: linear-gradient(135deg, #ebf8ff 0%, #bee3f8 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 153, 225, 0.1);
}

.empty-state i {
    font-size: 64px;
    color: #4299e1;
    margin-bottom: 20px;
    display: block;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.empty-state h5 {
    color: #2d3748;
    font-size: 24px;
    font-weight: 700;
    margin: 20px 0 10px 0;
    letter-spacing: 0.5px;
}

.empty-state p {
    color: #718096;
    font-size: 16px;
    margin: 0 0 25px 0;
    line-height: 1.6;
}

/* 移动端空状态优化 */
@media (max-width: 768px) {
    .empty-state {
        padding: 40px 20px;
        border-radius: 16px;
    }

    .empty-state i {
        font-size: 48px;
        margin-bottom: 15px;
    }

    .empty-state h5 {
        font-size: 20px;
        margin: 15px 0 8px 0;
    }

    .empty-state p {
        font-size: 14px;
        margin: 0 0 20px 0;
    }

    .empty-state .btn-create-purchase {
        padding: 14px 32px !important;
        font-size: 16px !important;
        min-width: 180px;
        transform: scale(1);
    }

    .empty-state .btn-create-purchase:hover {
        transform: scale(1.02) translateY(-2px) !important;
    }

    .empty-state .btn-create-purchase i {
        font-size: 18px;
        margin-right: 8px;
    }
}

@media (max-width: 480px) {
    .empty-state {
        padding: 30px 15px;
        border-radius: 12px;
    }

    .empty-state .btn-create-purchase {
        padding: 12px 28px !important;
        font-size: 15px !important;
        min-width: 160px;
    }
}

/* 采购单创建表单样式 */
.form-container {
    max-width: 2500px;
    margin: 0 auto;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    padding: 30px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.form-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.form-title h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
}

.form-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

.form-grid {
    padding: 30px;
}

.form-section {
    margin-bottom: 30px;
    padding-bottom: 25px;
    border-bottom: 1px solid #e2e8f0;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: #4299e1;
    font-size: 16px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-label.required::after {
    content: ' *';
    color: #e53e3e;
}

.form-control {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-control:focus {
    outline: none;
    border-color: #4299e1;
    background: white;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

/* 商品明细样式 */
.items-container {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e2e8f0;
}

.items-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 80px;
    gap: 15px;
    padding: 12px 0;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #2d3748;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.item-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 80px;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #e2e8f0;
    align-items: center;
}

.item-row:last-child {
    border-bottom: none;
}

.item-col {
    display: flex;
    align-items: center;
}

.item-total {
    font-weight: 600;
    color: #2d3748;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.items-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 2px solid #e2e8f0;
    margin-top: 20px;
}

.total-amount {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
}

.total-amount strong {
    color: #4299e1;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 20px;
}

.form-actions {
    padding: 25px 30px;
    background: #f7fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.btn-submit {
    min-width: 150px;
    padding: 12px 28px;
    font-size: 15px;
    font-weight: 600;
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    border: none;
    border-radius: 10px;
    color: white;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.btn-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(66, 153, 225, 0.35);
    background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
}

.btn-submit:hover::before {
    left: 100%;
}

.btn-submit:active {
    transform: translateY(-1px);
}

/* 响应式表单 */
@media (max-width: 768px) {
    .form-container {
        margin: 0 10px;
        border-radius: 12px;
    }

    .form-header {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .form-grid {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .items-header,
    .item-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .items-header {
        display: none;
    }

    .item-row {
        background: white;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        border: 1px solid #e2e8f0;
    }

    .item-col {
        margin-bottom: 10px;
    }

    .item-col:last-child {
        margin-bottom: 0;
    }

    .items-footer {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .form-actions {
        padding: 20px;
        flex-direction: column-reverse;
        gap: 10px;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

/* 订单信息 */
.order-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.order-number {
    font-family: 'Courier New', monospace;
    color: #2d3748;
    font-size: 14px;
}

.order-time {
    font-size: 12px;
    color: #718096;
}

/* 商品数量徽章 */
.item-count-badge {
    background: #e6fffa;
    color: #234e52;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 12px;
    display: inline-block;
}

/* 统计行 */
.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stat-icon.bg-primary { background: #4299e1; }
.stat-icon.bg-success { background: #38a169; }
.stat-icon.bg-info { background: #4299e1; }
.stat-icon.bg-warning { background: #d69e2e; }

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #718096;
    margin-top: 4px;
}

/* 搜索表单 */
.search-box .form-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1.5fr 1fr 1fr auto;
    gap: 15px;
    align-items: end;
}

/* 状态徽章 */
.badge {
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.badge-warning {
    background: #fed7aa;
    color: #9c4221;
}

.badge-info {
    background: #bfdbfe;
    color: #1e40af;
}

.badge-primary {
    background: #dbeafe;
    color: #1d4ed8;
}

.badge-success {
    background: #d1fae5;
    color: #065f46;
}

.badge-danger {
    background: #fecaca;
    color: #991b1b;
}

.badge-secondary {
    background: #f1f5f9;
    color: #475569;
}

/* 下拉菜单 */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    border: none;
    background: transparent;
    padding: 6px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.dropdown-toggle:hover {
    background: #f7fafc;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 160px;
    z-index: 9999;
    display: none;
    padding: 8px 0;
}

.dropdown.active .dropdown-menu {
    display: block;
}

.dropdown-item {
    display: block;
    padding: 8px 16px;
    color: #2d3748;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: #f7fafc;
    text-decoration: none;
}

.dropdown-item.text-danger {
    color: #e53e3e;
}

.dropdown-item.text-danger:hover {
    background: #fed7d7;
}

.dropdown-item i {
    width: 16px;
    margin-right: 8px;
}

.dropdown-divider {
    height: 1px;
    background: #e2e8f0;
    margin: 8px 0;
}

/* 确保下拉菜单不被表格容器裁剪 */
.table-responsive {
    overflow: visible !important;
}

.table-container {
    position: relative;
    overflow-y: visible;
}

/* 操作列的下拉菜单特殊处理 */
.table td:last-child {
    position: relative;
    overflow: visible;
}

.table td:last-child .dropdown {
    position: static;
}

.table td:last-child .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 9999;
}

/* 表格样式增强 */
.table th {
    background: #f8f9fa;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #2d3748;
    padding: 12px 8px;
    font-size: 13px;
}

.table td {
    padding: 12px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #e2e8f0;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* 按钮组 */
.btn-group {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    align-items: center;
}

/* 操作按钮优化 */
.btn-group .btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    min-width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* 操作列样式 */
.table td:last-child {
    white-space: nowrap;
    vertical-align: middle;
}

/* 按钮颜色优化 */
.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-sm {
    padding: 6px 10px;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .search-box .form-row {
        grid-template-columns: 2fr 1fr 1fr;
        gap: 10px;
    }
    
    .search-box .form-group:nth-child(4),
    .search-box .form-group:nth-child(5),
    .search-box .form-group:nth-child(6) {
        grid-column: span 3;
    }
}

@media (max-width: 768px) {
    .stats-row {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .search-box .form-row {
        grid-template-columns: 1fr;
    }
    
    .search-box .form-group {
        grid-column: span 1 !important;
    }
    
    /* 表格在移动端的处理 */
    .table-container {
        overflow-x: auto;
        overflow-y: visible;
    }

    .table {
        min-width: 1000px;
    }

    /* 移动端按钮组优化 */
    .btn-group {
        flex-direction: column;
        gap: 2px;
        align-items: stretch;
    }

    .btn-group .btn-sm {
        width: 100%;
        justify-content: flex-start;
        padding: 6px 8px;
        font-size: 11px;
    }

    .btn-group .btn-sm i {
        margin-right: 4px;
        width: 12px;
    }

    /* 操作列在移动端的特殊处理 */
    .table td:last-child {
        min-width: 120px;
        white-space: normal;
    }
}

@media (max-width: 480px) {
    .stats-row {
        grid-template-columns: 1fr;
    }
}

/* 动画效果 */
.stat-item {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
    color: #4299e1;
}

.empty-state h5 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #2d3748;
}

.empty-state p {
    font-size: 16px;
    margin-bottom: 20px;
}

/* 模板下载按钮组 */
.template-downloads {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.template-downloads .btn {
    font-size: 12px;
    padding: 6px 12px;
}

/* 标签页样式 */
.format-tabs {
    margin-top: 15px;
}

.tab-buttons {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
    gap: 5px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: #007bff;
    background: #f8f9fa;
}

.tab-btn.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: #f8f9fa;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.format-description {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.format-description .highlight {
    background: #e3f2fd;
    padding: 10px;
    border-left: 4px solid #2196f3;
    margin: 10px 0;
    font-weight: 500;
}

.format-section {
    margin: 15px 0;
}

.format-section h5 {
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;
}

/* 供应商选择区域样式 */
.supplier-selection {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    transition: all 0.3s ease;
}

.supplier-selection .section-header {
    margin-bottom: 15px;
}

.supplier-selection .section-header h4 {
    color: #495057;
    font-size: 16px;
    margin: 0 0 5px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.supplier-selection .section-header h4 i {
    color: #007cba;
}

.supplier-selection .section-description {
    color: #6c757d;
    font-size: 14px;
    margin: 0;
}

.supplier-selection .form-group {
    margin-bottom: 0;
}

.supplier-selection .form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.supplier-selection .required {
    color: #dc3545;
}

.supplier-selection .form-control {
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.supplier-selection .form-control:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 0.2rem rgba(0, 124, 186, 0.25);
    outline: 0;
}

.supplier-selection .form-help {
    margin-top: 6px;
    font-size: 12px;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 5px;
}

.supplier-selection .form-help i {
    color: #17a2b8;
}

/* 提交按钮状态样式 */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: none;
}
