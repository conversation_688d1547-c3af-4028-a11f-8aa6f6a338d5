<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试出库列表选择功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .feature-banner {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-link {
            display: inline-block;
            margin: 10px 15px 10px 0;
            padding: 15px 30px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-link:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .test-link.success {
            background: #28a745;
        }
        
        .test-link.success:hover {
            background: #1e7e34;
        }
        
        .test-link.info {
            background: #17a2b8;
        }
        
        .test-link.info:hover {
            background: #138496;
        }
        
        .test-link.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .test-link.warning:hover {
            background: #e0a800;
        }
        
        h1, h2 {
            color: #333;
            border-bottom: 3px solid #007bff;
            padding-bottom: 15px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
            transition: transform 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-card h3 {
            margin-top: 0;
            color: #007bff;
            font-size: 20px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:before {
            content: "✅";
            color: #28a745;
            font-size: 16px;
            margin-right: 12px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-new {
            background: #28a745;
        }
        
        .status-improved {
            background: #007bff;
        }
        
        .status-existing {
            background: #6c757d;
        }
    </style>
</head>
<body>
    <div class="feature-banner">
        <h1 style="margin: 0; font-size: 32px;">🎉 新功能：出库列表选择</h1>
        <p style="margin: 15px 0 0 0; font-size: 18px;">现在可以通过列表方式批量选择食材进行出库操作</p>
    </div>
    
    <!-- 功能对比 -->
    <div class="test-section">
        <h2>📊 出库方式对比</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>出库方式</th>
                    <th>适用场景</th>
                    <th>操作特点</th>
                    <th>状态</th>
                    <th>测试链接</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>单个出库</strong></td>
                    <td>单种食材出库</td>
                    <td>简单快速，适合少量出库</td>
                    <td><span class="status-indicator status-existing"></span>原有功能</td>
                    <td><a href="modules/outbound/index.php?action=create" class="test-link" target="_blank">测试</a></td>
                </tr>
                <tr>
                    <td><strong>列表选择出库</strong></td>
                    <td>多种食材同时出库</td>
                    <td>可视化选择，批量操作</td>
                    <td><span class="status-indicator status-new"></span>新功能</td>
                    <td><a href="modules/outbound/index.php?action=create&list=1" class="test-link success" target="_blank">测试</a></td>
                </tr>
                <tr>
                    <td><strong>批量出库</strong></td>
                    <td>大量食材批量处理</td>
                    <td>表格形式，适合大批量</td>
                    <td><span class="status-indicator status-existing"></span>原有功能</td>
                    <td><a href="modules/outbound/index.php?action=batch_create" class="test-link info" target="_blank">测试</a></td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- 新功能特点 -->
    <div class="test-section">
        <h2>🌟 列表选择出库功能特点</h2>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h3>🎯 可视化选择</h3>
                <ul class="feature-list">
                    <li>按分类展示所有食材</li>
                    <li>显示实时库存信息</li>
                    <li>低库存食材特殊标识</li>
                    <li>复选框方式选择食材</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🔍 智能搜索</h3>
                <ul class="feature-list">
                    <li>实时搜索食材名称</li>
                    <li>自动隐藏不匹配项目</li>
                    <li>分类智能折叠</li>
                    <li>快速定位目标食材</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>⚡ 批量操作</h3>
                <ul class="feature-list">
                    <li>一键全选所有食材</li>
                    <li>一键清空所有选择</li>
                    <li>批量设置出库数量</li>
                    <li>实时统计选择数量</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🛡️ 安全验证</h3>
                <ul class="feature-list">
                    <li>自动检查库存充足性</li>
                    <li>防止超量出库</li>
                    <li>必填字段验证</li>
                    <li>数据完整性检查</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- 使用流程 -->
    <div class="test-section">
        <h2>📋 使用流程</h2>
        
        <div style="background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #0066cc;">操作步骤：</h3>
            <ol style="font-size: 16px; line-height: 1.8;">
                <li><strong>填写基本信息</strong> - 选择餐次类型、用餐日期、用途等</li>
                <li><strong>搜索定位食材</strong> - 使用搜索框快速找到需要的食材</li>
                <li><strong>选择出库食材</strong> - 勾选需要出库的食材复选框</li>
                <li><strong>填写出库数量</strong> - 为每个选中的食材填写出库数量</li>
                <li><strong>查看选择汇总</strong> - 确认已选择的食材和数量</li>
                <li><strong>提交出库申请</strong> - 点击保存按钮完成出库操作</li>
            </ol>
        </div>
    </div>
    
    <!-- 测试链接 -->
    <div class="test-section">
        <h2>🧪 功能测试</h2>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h3>主要功能测试</h3>
                <a href="modules/outbound/index.php" class="test-link" target="_blank">
                    📋 出库管理首页
                </a>
                <a href="modules/outbound/index.php?action=create&list=1" class="test-link success" target="_blank">
                    🆕 列表选择出库
                </a>
            </div>
            
            <div class="feature-card">
                <h3>对比测试</h3>
                <a href="modules/outbound/index.php?action=create" class="test-link info" target="_blank">
                    📝 单个出库
                </a>
                <a href="modules/outbound/index.php?action=batch_create" class="test-link warning" target="_blank">
                    📊 批量出库
                </a>
            </div>
            
            <div class="feature-card">
                <h3>系统管理</h3>
                <a href="modules/outbound/orders.php" class="test-link info" target="_blank">
                    🏢 出库单管理
                </a>
                <a href="modules/inventory/index.php" class="test-link warning" target="_blank">
                    📦 库存管理
                </a>
            </div>
            
            <div class="feature-card">
                <h3>移动端</h3>
                <a href="mobile/outbound.php" class="test-link success" target="_blank">
                    📱 移动端出库
                </a>
                <a href="mobile/index.php" class="test-link info" target="_blank">
                    📱 移动端首页
                </a>
            </div>
        </div>
    </div>
    
    <!-- 技术说明 -->
    <div class="test-section">
        <h2>⚙️ 技术实现</h2>
        
        <div style="background: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3>实现特点：</h3>
            <ul class="feature-list">
                <li><strong>响应式设计</strong> - 适配桌面端和移动端</li>
                <li><strong>实时交互</strong> - JavaScript实现动态交互</li>
                <li><strong>数据验证</strong> - 前端和后端双重验证</li>
                <li><strong>批量处理</strong> - 支持多食材同时出库</li>
                <li><strong>库存检查</strong> - 自动检查库存充足性</li>
                <li><strong>事务处理</strong> - 确保数据一致性</li>
            </ul>
        </div>
    </div>
    
    <div class="feature-banner">
        <h2 style="margin: 0;">🎯 开始测试</h2>
        <p style="margin: 15px 0 0 0;">点击上面的测试链接体验新的列表选择出库功能</p>
    </div>
</body>
</html>
