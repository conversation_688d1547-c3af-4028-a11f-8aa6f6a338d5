<?php

require_once dirname(__DIR__, 2) . '/includes/BaseController.php';
require_once dirname(__DIR__, 2) . '/includes/helpers.php';

class ReportsController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    public function handleRequest()
    {
        switch ($this->request['action']) {
            case 'index':
            default:
                return $this->index();
        }
    }

    public function index()
    {
        $action = $_GET['action'] ?? 'overview';
        
        switch ($action) {
            case 'purchase':
                $this->purchaseReport();
                break;
            case 'inventory':
                $this->inventoryReport();
                break;
            case 'supplier':
                $this->supplierReport();
                break;
            case 'financial':
                $this->financialReport();
                break;
            default:
                $this->overview();
                break;
        }
    }

    /**
     * 报表概览
     */
    private function overview()
    {
        $data = [
            'page_title' => '数据报表 - 学校食堂管理系统',
            'current_tab' => 'overview',
            'date_range' => [
                'start' => $_GET['start_date'] ?? date('Y-m-01'),
                'end' => $_GET['end_date'] ?? date('Y-m-d')
            ]
        ];

        // 获取报表数据
        $data['summary'] = $this->getSummaryData($data['date_range']);
        $data['charts'] = $this->getChartData($data['date_range']);

        $this->setTemplateData(['data' => $data]);
        $this->render('template.php');
    }

    /**
     * 采购报表
     */
    private function purchaseReport()
    {
        $data = [
            'page_title' => '采购报表 - 学校食堂管理系统',
            'current_tab' => 'purchase',
            'date_range' => [
                'start' => $_GET['start_date'] ?? date('Y-m-01'),
                'end' => $_GET['end_date'] ?? date('Y-m-d')
            ]
        ];

        $data['purchase_data'] = $this->getPurchaseData($data['date_range']);
        $data['supplier_ranking'] = $this->getSupplierRanking($data['date_range']);

        $this->setTemplateData(['data' => $data]);
        $this->render('template.php');
    }

    /**
     * 库存报表
     */
    private function inventoryReport()
    {
        $data = [
            'page_title' => '库存报表 - 学校食堂管理系统',
            'current_tab' => 'inventory',
            'date_range' => [
                'start' => $_GET['start_date'] ?? date('Y-m-01'),
                'end' => $_GET['end_date'] ?? date('Y-m-d')
            ]
        ];

        $data['inventory_data'] = $this->getInventoryData();
        $data['turnover_data'] = $this->getTurnoverData($data['date_range']);

        $this->setTemplateData(['data' => $data]);
        $this->render('template.php');
    }

    /**
     * 供应商报表
     */
    private function supplierReport()
    {
        $data = [
            'page_title' => '供应商报表 - 学校食堂管理系统',
            'current_tab' => 'supplier',
            'date_range' => [
                'start' => $_GET['start_date'] ?? date('Y-m-01'),
                'end' => $_GET['end_date'] ?? date('Y-m-d')
            ]
        ];

        $data['supplier_performance'] = $this->getSupplierPerformance($data['date_range']);
        $data['supplier_comparison'] = $this->getSupplierComparison($data['date_range']);

        $this->setTemplateData(['data' => $data]);
        $this->render('template.php');
    }

    /**
     * 财务报表
     */
    private function financialReport()
    {
        $data = [
            'page_title' => '财务报表 - 学校食堂管理系统',
            'current_tab' => 'financial',
            'date_range' => [
                'start' => $_GET['start_date'] ?? date('Y-m-01'),
                'end' => $_GET['end_date'] ?? date('Y-m-d')
            ]
        ];

        $data['financial_summary'] = $this->getFinancialSummary($data['date_range']);
        $data['cost_analysis'] = $this->getCostAnalysis($data['date_range']);

        $this->setTemplateData(['data' => $data]);
        $this->render('template.php');
    }

    /**
     * 获取汇总数据
     */
    private function getSummaryData($dateRange)
    {
        // 模拟数据，实际应从数据库获取
        return [
            'total_purchase' => 125680.50,
            'total_orders' => 156,
            'total_suppliers' => 12,
            'avg_order_value' => 805.64,
            'inventory_value' => 45230.80,
            'low_stock_items' => 8,
            'monthly_growth' => 12.5,
            'cost_savings' => 8960.30
        ];
    }

    /**
     * 获取图表数据
     */
    private function getChartData($dateRange)
    {
        return [
            'monthly_purchase' => [
                'labels' => ['1月', '2月', '3月', '4月', '5月', '6月'],
                'data' => [85000, 92000, 78000, 105000, 118000, 125680]
            ],
            'category_distribution' => [
                'labels' => ['蔬菜类', '肉类', '水产', '调料', '粮油', '其他'],
                'data' => [35, 25, 15, 10, 10, 5]
            ],
            'supplier_ranking' => [
                'labels' => ['绿色蔬菜', '优质肉类', '新鲜水产', '调料专家', '粮油批发'],
                'data' => [45000, 38000, 25000, 12000, 8000]
            ]
        ];
    }

    /**
     * 获取采购数据
     */
    private function getPurchaseData($dateRange)
    {
        return [
            'daily_purchase' => [
                ['date' => '2024-12-01', 'amount' => 2500.00, 'orders' => 3],
                ['date' => '2024-12-02', 'amount' => 3200.00, 'orders' => 4],
                ['date' => '2024-12-03', 'amount' => 1800.00, 'orders' => 2],
                ['date' => '2024-12-04', 'amount' => 4100.00, 'orders' => 5],
                ['date' => '2024-12-05', 'amount' => 2900.00, 'orders' => 3]
            ],
            'category_breakdown' => [
                ['category' => '蔬菜类', 'amount' => 45000, 'percentage' => 35.8],
                ['category' => '肉类', 'amount' => 32000, 'percentage' => 25.4],
                ['category' => '水产', 'amount' => 18000, 'percentage' => 14.3],
                ['category' => '调料', 'amount' => 12000, 'percentage' => 9.5],
                ['category' => '粮油', 'amount' => 10000, 'percentage' => 7.9],
                ['category' => '其他', 'amount' => 8680, 'percentage' => 6.9]
            ]
        ];
    }

    /**
     * 获取供应商排名
     */
    private function getSupplierRanking($dateRange)
    {
        return [
            ['name' => '绿色蔬菜供应商', 'amount' => 45000, 'orders' => 25, 'avg_delivery_time' => 1.2],
            ['name' => '优质肉类供应商', 'amount' => 38000, 'orders' => 18, 'avg_delivery_time' => 1.5],
            ['name' => '新鲜水产供应商', 'amount' => 25000, 'orders' => 15, 'avg_delivery_time' => 0.8],
            ['name' => '调料专家供应商', 'amount' => 12000, 'orders' => 12, 'avg_delivery_time' => 2.1],
            ['name' => '粮油批发供应商', 'amount' => 8000, 'orders' => 8, 'avg_delivery_time' => 1.8]
        ];
    }

    /**
     * 获取库存数据
     */
    private function getInventoryData()
    {
        return [
            'current_stock' => [
                ['category' => '蔬菜类', 'items' => 25, 'value' => 15000, 'turnover' => 8.5],
                ['category' => '肉类', 'items' => 18, 'value' => 22000, 'turnover' => 12.3],
                ['category' => '水产', 'items' => 12, 'value' => 8000, 'turnover' => 15.2],
                ['category' => '调料', 'items' => 35, 'value' => 5000, 'turnover' => 4.2],
                ['category' => '粮油', 'items' => 15, 'value' => 12000, 'turnover' => 6.8]
            ],
            'low_stock_alerts' => [
                ['name' => '西红柿', 'current' => 50, 'minimum' => 100, 'unit' => 'kg'],
                ['name' => '猪肉', 'current' => 25, 'minimum' => 50, 'unit' => 'kg'],
                ['name' => '带鱼', 'current' => 15, 'minimum' => 30, 'unit' => 'kg']
            ]
        ];
    }

    /**
     * 获取周转率数据
     */
    private function getTurnoverData($dateRange)
    {
        return [
            'monthly_turnover' => [
                'labels' => ['1月', '2月', '3月', '4月', '5月', '6月'],
                'data' => [8.2, 9.1, 7.8, 10.5, 11.2, 9.8]
            ]
        ];
    }

    /**
     * 获取供应商绩效
     */
    private function getSupplierPerformance($dateRange)
    {
        return [
            ['name' => '绿色蔬菜供应商', 'quality_score' => 95, 'delivery_rate' => 98, 'price_competitiveness' => 85],
            ['name' => '优质肉类供应商', 'quality_score' => 92, 'delivery_rate' => 95, 'price_competitiveness' => 88],
            ['name' => '新鲜水产供应商', 'quality_score' => 98, 'delivery_rate' => 100, 'price_competitiveness' => 82],
            ['name' => '调料专家供应商', 'quality_score' => 88, 'delivery_rate' => 92, 'price_competitiveness' => 90],
            ['name' => '粮油批发供应商', 'quality_score' => 85, 'delivery_rate' => 88, 'price_competitiveness' => 95]
        ];
    }

    /**
     * 获取供应商对比
     */
    private function getSupplierComparison($dateRange)
    {
        return [
            'quality_comparison' => [
                'labels' => ['绿色蔬菜', '优质肉类', '新鲜水产', '调料专家', '粮油批发'],
                'data' => [95, 92, 98, 88, 85]
            ],
            'delivery_comparison' => [
                'labels' => ['绿色蔬菜', '优质肉类', '新鲜水产', '调料专家', '粮油批发'],
                'data' => [98, 95, 100, 92, 88]
            ]
        ];
    }

    /**
     * 获取财务汇总
     */
    private function getFinancialSummary($dateRange)
    {
        return [
            'total_expenditure' => 125680.50,
            'budget_utilization' => 78.5,
            'cost_per_meal' => 12.50,
            'monthly_savings' => 8960.30,
            'budget_remaining' => 34319.50,
            'projected_monthly' => 135000.00
        ];
    }

    /**
     * 获取成本分析
     */
    private function getCostAnalysis($dateRange)
    {
        return [
            'cost_breakdown' => [
                ['category' => '食材采购', 'amount' => 98500, 'percentage' => 78.4],
                ['category' => '运输费用', 'amount' => 12680, 'percentage' => 10.1],
                ['category' => '储存成本', 'amount' => 8900, 'percentage' => 7.1],
                ['category' => '损耗费用', 'amount' => 5600, 'percentage' => 4.4]
            ],
            'monthly_trend' => [
                'labels' => ['1月', '2月', '3月', '4月', '5月', '6月'],
                'data' => [11.80, 12.20, 11.95, 12.45, 12.80, 12.50]
            ]
        ];
    }
}
