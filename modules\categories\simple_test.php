<!DOCTYPE html>
<html>
<head>
    <title>简单文件选择测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; border: none; }
        .btn-success { background: #28a745; color: white; border: none; }
        .btn-warning { background: #ffc107; color: black; border: none; }
        input[type="file"] { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>文件选择功能测试</h1>
    
    <div class="test-section">
        <h3>方法1: 直接显示的文件输入</h3>
        <input type="file" id="file1" accept=".csv,.xlsx,.xls">
        <p>这个应该直接可见并可以点击</p>
    </div>
    
    <div class="test-section">
        <h3>方法2: 隐藏文件输入 + JavaScript触发</h3>
        <input type="file" id="file2" accept=".csv,.xlsx,.xls" style="display: none;">
        <button class="btn-primary" onclick="document.getElementById('file2').click()">
            点击选择文件 (内联JS)
        </button>
        <button class="btn-primary" id="btn2">
            点击选择文件 (事件监听器)
        </button>
    </div>
    
    <div class="test-section">
        <h3>方法3: Label元素关联</h3>
        <input type="file" id="file3" accept=".csv,.xlsx,.xls" style="display: none;">
        <label for="file3" class="btn-success" style="cursor: pointer; display: inline-block;">
            点击选择文件 (Label)
        </label>
    </div>
    
    <div class="test-section">
        <h3>方法4: 表单内的文件输入</h3>
        <form>
            <input type="file" id="file4" accept=".csv,.xlsx,.xls" style="display: none;">
            <button type="button" class="btn-warning" onclick="document.getElementById('file4').click()">
                表单内选择文件
            </button>
        </form>
    </div>
    
    <div class="test-section">
        <h3>测试结果</h3>
        <div id="results"></div>
    </div>

    <script>
        console.log('页面加载完成');
        
        function logResult(message) {
            const results = document.getElementById('results');
            results.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
            console.log(message);
        }
        
        // 为所有文件输入添加change事件
        ['file1', 'file2', 'file3', 'file4'].forEach(function(id) {
            const input = document.getElementById(id);
            input.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    logResult('✅ ' + id + ' 选择了文件: ' + e.target.files[0].name);
                }
            });
        });
        
        // 方法2的事件监听器
        document.getElementById('btn2').addEventListener('click', function() {
            logResult('🔘 btn2 被点击');
            try {
                document.getElementById('file2').click();
                logResult('✅ file2.click() 执行成功');
            } catch (error) {
                logResult('❌ file2.click() 失败: ' + error.message);
            }
        });
        
        logResult('🎉 所有事件监听器已设置');
        
        // 测试浏览器信息
        logResult('🌐 浏览器: ' + navigator.userAgent);
        logResult('📱 平台: ' + navigator.platform);
    </script>
</body>
</html>
