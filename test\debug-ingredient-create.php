<?php
/**
 * 调试食材创建页面的分类数据
 */

require_once '../includes/Database.php';
require_once '../includes/BaseController.php';
require_once '../modules/ingredients/IngredientsController.php';

echo "<h2>调试食材创建页面</h2>";

try {
    // 创建控制器实例
    $controller = new IngredientsController();
    echo "<p>✅ IngredientsController实例化成功</p>";
    
    // 模拟请求
    $_GET['action'] = 'create';
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    // 获取分类数据（模拟控制器逻辑）
    $db = Database::getInstance();
    
    echo "<h3>分类数据获取测试：</h3>";
    
    // 测试1：获取所有分类
    try {
        $allCategories = $db->fetchAll("SELECT * FROM ingredient_categories ORDER BY name ASC");
        echo "<p>✅ 所有分类查询成功，共 " . count($allCategories) . " 个分类</p>";
        
        if (!empty($allCategories)) {
            echo "<h4>分类列表：</h4>";
            echo "<ul>";
            foreach ($allCategories as $category) {
                $statusText = isset($category['status']) ? ($category['status'] ? '启用' : '禁用') : '未知';
                echo "<li>ID: {$category['id']}, 名称: " . htmlspecialchars($category['name']) . ", 状态: $statusText</li>";
            }
            echo "</ul>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 分类查询失败: " . $e->getMessage() . "</p>";
    }
    
    // 测试2：获取启用的分类
    try {
        $activeCategories = $db->fetchAll("SELECT * FROM ingredient_categories WHERE status = 1 ORDER BY name ASC");
        echo "<p>✅ 启用分类查询成功，共 " . count($activeCategories) . " 个启用分类</p>";
        
        if (empty($activeCategories)) {
            echo "<p style='color: orange;'>⚠️ 没有启用的分类，这会导致食材创建页面的分类下拉框为空</p>";
            echo "<p>建议：<a href='debug-categories-list.php?action=fix_status'>修复分类状态</a></p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 启用分类查询失败: " . $e->getMessage() . "</p>";
    }
    
    // 测试3：检查ingredients表
    echo "<h3>ingredients表检查：</h3>";
    try {
        $ingredientsTableExists = $db->fetchOne("SHOW TABLES LIKE 'ingredients'");
        if ($ingredientsTableExists) {
            echo "<p>✅ ingredients表存在</p>";
            
            // 检查表结构
            $columns = $db->fetchAll("DESCRIBE ingredients");
            echo "<h4>表结构：</h4>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // 检查数据
            $count = $db->fetchOne("SELECT COUNT(*) as count FROM ingredients");
            echo "<p>现有食材数量: " . $count['count'] . "</p>";
            
        } else {
            echo "<p style='color: red;'>❌ ingredients表不存在</p>";
            echo "<p>建议运行: <a href='../create-tables.php'>create-tables.php</a></p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ ingredients表检查失败: " . $e->getMessage() . "</p>";
    }
    
    // 测试4：模拟食材创建表单数据
    echo "<h3>模拟表单数据测试：</h3>";
    
    if (!empty($activeCategories)) {
        $testData = [
            'name' => '测试食材_' . date('YmdHis'),
            'category_id' => $activeCategories[0]['id'],
            'unit' => '公斤',
            'unit_price' => 10.50,
            'min_stock' => 5,
            'max_stock' => 100,
            'current_stock' => 50,
            'description' => '这是一个测试食材',
            'status' => 1
        ];
        
        echo "<h4>测试数据：</h4>";
        echo "<pre>" . print_r($testData, true) . "</pre>";
        
        // 尝试插入测试数据
        if (isset($_GET['test_insert']) && $_GET['test_insert'] === 'yes') {
            try {
                $id = $db->insert('ingredients', $testData);
                echo "<p style='color: green;'>✅ 测试食材插入成功，ID: $id</p>";
                
                // 立即删除测试数据
                $db->delete('ingredients', 'id = ?', [$id]);
                echo "<p style='color: blue;'>ℹ️ 测试数据已清理</p>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ 测试插入失败: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p><a href='?test_insert=yes'>点击测试插入</a></p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ 无法进行插入测试，因为没有可用的分类</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 控制器测试失败: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='../modules/ingredients/index.php?action=create'>访问食材创建页面</a> | <a href='check-categories.php'>检查分类数据</a> | <a href='../index.php'>返回首页</a></p>";
?>
