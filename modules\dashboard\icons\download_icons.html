<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘图标管理 - 阿里iconfont</title>
    <!-- 引入阿里iconfont -->
    <link rel="stylesheet" href="//at.alicdn.com/t/c/font_8d5l8fzk5b87iudi.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .icon-name {
            font-weight: 600;
            margin-bottom: 10px;
            color: #1f2937;
        }
        .icon-desc {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 15px;
        }
        .download-links {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .download-link {
            display: inline-block;
            padding: 8px 12px;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 12px;
            transition: background 0.2s;
        }
        .download-link:hover {
            background: #2563eb;
        }
        .instructions {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .warning {
            background: #fef3cd;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .icon-preview {
            font-size: 32px;
            color: #6b7280;
            margin-bottom: 10px;
        }
        .current-icons {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🎨 仪表盘图标管理 - 阿里iconfont</h1>

    <div class="current-icons">
        <h3>✅ 当前使用的阿里iconfont图标</h3>
        <p>系统已配置使用阿里巴巴iconfont图标库，以下是当前使用的图标：</p>

        <div class="icon-grid">
            <div class="icon-item">
                <div class="icon-preview"><i class="iconfont icon-shucai"></i></div>
                <div class="icon-name">icon-shucai</div>
                <div class="icon-desc">食材种类</div>
            </div>
            <div class="icon-item">
                <div class="icon-preview"><i class="iconfont icon-huoche"></i></div>
                <div class="icon-name">icon-huoche</div>
                <div class="icon-desc">合作供应商</div>
            </div>
            <div class="icon-item">
                <div class="icon-preview"><i class="iconfont icon-gouwuche"></i></div>
                <div class="icon-name">icon-gouwuche</div>
                <div class="icon-desc">本月订单</div>
            </div>
            <div class="icon-item">
                <div class="icon-preview"><i class="iconfont icon-qian"></i></div>
                <div class="icon-name">icon-qian</div>
                <div class="icon-desc">本月采购额</div>
            </div>
            <div class="icon-item">
                <div class="icon-preview"><i class="iconfont icon-cangku"></i></div>
                <div class="icon-name">icon-cangku</div>
                <div class="icon-desc">库存总值</div>
            </div>
            <div class="icon-item">
                <div class="icon-preview"><i class="iconfont icon-jinggao"></i></div>
                <div class="icon-name">icon-jinggao</div>
                <div class="icon-desc">低库存预警</div>
            </div>
            <div class="icon-item">
                <div class="icon-preview"><i class="iconfont icon-shizhong"></i></div>
                <div class="icon-name">icon-shizhong</div>
                <div class="icon-desc">待处理订单</div>
            </div>
            <div class="icon-item">
                <div class="icon-preview"><i class="iconfont icon-xiangxia"></i></div>
                <div class="icon-name">icon-xiangxia</div>
                <div class="icon-desc">今日入库</div>
            </div>
        </div>
    </div>

    <div class="warning">
        <strong>💡 提示：</strong>当前系统使用阿里iconfont图标库，图标丰富且专为中文场景优化。
    </div>

    <div class="instructions">
        <h3>🔧 如何更换图标</h3>
        <ol>
            <li><strong>访问iconfont.cn</strong> - 打开阿里巴巴图标库官网</li>
            <li><strong>搜索图标</strong> - 使用关键词搜索合适的图标</li>
            <li><strong>添加到项目</strong> - 将选中的图标添加到项目中</li>
            <li><strong>生成链接</strong> - 在项目中生成新的CSS链接</li>
            <li><strong>更新代码</strong> - 替换template.php中的CSS链接和图标类名</li>
        </ol>

        <h4>🌐 快速链接</h4>
        <ul>
            <li><a href="https://www.iconfont.cn/" target="_blank">iconfont官网</a></li>
            <li><a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4798234" target="_blank">当前项目</a></li>
            <li><a href="https://www.iconfont.cn/help/detail?helptype=code" target="_blank">使用帮助</a></li>
        </ul>
    </div>

    <div class="instructions">
        <h3>🌟 阿里iconfont的优势</h3>
        <ul>
            <li><strong>丰富的中文图标</strong> - 专为中文场景设计的图标</li>
            <li><strong>矢量图标</strong> - 支持任意缩放不失真</li>
            <li><strong>CDN加速</strong> - 快速加载，稳定可靠</li>
            <li><strong>免费使用</strong> - 大部分图标免费商用</li>
            <li><strong>持续更新</strong> - 图标库持续扩充新内容</li>
            <li><strong>统一管理</strong> - 项目化管理，便于维护</li>
        </ul>
    </div>
</body>
</html>
