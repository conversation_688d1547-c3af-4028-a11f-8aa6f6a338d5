<?php
/**
 * 下载分类导入模板
 */

// 设置 CSV 文件头
header('Content-Type: text/csv; charset=UTF-8');
header('Content-Disposition: attachment; filename="食材分类导入模板.csv"');
header('Cache-Control: no-cache, must-revalidate');

// 输出 BOM 以支持中文
echo "\xEF\xBB\xBF";

// 创建文件句柄
$output = fopen('php://output', 'w');

// 写入标题行
$headers = [
    '分类名称',
    '父分类名称',
    '分类代码',
    '描述',
    '排序'
];
fputcsv($output, $headers);

// 写入示例数据
$sampleData = [
    ['蔬菜类', '', 'VEG', '各种新鲜蔬菜', '1'],
    ['叶菜类', '蔬菜类', 'VEG_LEAF', '包括白菜、菠菜、生菜等叶类蔬菜', '1'],
    ['根茎类', '蔬菜类', 'VEG_ROOT', '包括萝卜、土豆、胡萝卜等根茎类蔬菜', '2'],
    ['瓜果类', '蔬菜类', 'VEG_FRUIT', '包括黄瓜、茄子、西红柿等瓜果类蔬菜', '3'],
    ['肉类', '', 'MEAT', '各种肉类食材', '2'],
    ['猪肉类', '肉类', 'MEAT_PORK', '各种猪肉制品', '1'],
    ['牛肉类', '肉类', 'MEAT_BEEF', '各种牛肉制品', '2'],
    ['鸡肉类', '肉类', 'MEAT_CHICKEN', '各种鸡肉制品', '3'],
    ['水产类', '', 'SEAFOOD', '鱼类、虾类等水产品', '3'],
    ['淡水鱼', '水产类', 'SEAFOOD_FRESH', '淡水鱼类', '1'],
    ['海水鱼', '水产类', 'SEAFOOD_SEA', '海水鱼类', '2'],
    ['虾蟹类', '水产类', 'SEAFOOD_SHRIMP', '虾、蟹等甲壳类', '3'],
    ['粮食类', '', 'GRAIN', '大米、面粉等主食', '4'],
    ['大米类', '粮食类', 'GRAIN_RICE', '各种大米', '1'],
    ['面粉类', '粮食类', 'GRAIN_FLOUR', '各种面粉', '2'],
    ['杂粮类', '粮食类', 'GRAIN_MISC', '小米、玉米等杂粮', '3'],
    ['调料类', '', 'SEASONING', '盐、糖、酱油等调味品', '5'],
    ['基础调料', '调料类', 'SEASONING_BASIC', '盐、糖、醋等基础调料', '1'],
    ['香料类', '调料类', 'SEASONING_SPICE', '各种香料和调味品', '2'],
    ['酱料类', '调料类', 'SEASONING_SAUCE', '酱油、豆瓣酱等酱料', '3']
];

foreach ($sampleData as $row) {
    fputcsv($output, $row);
}

// 关闭文件句柄
fclose($output);
?>
