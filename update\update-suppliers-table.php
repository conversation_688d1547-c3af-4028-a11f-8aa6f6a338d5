<?php
/**
 * 更新suppliers表结构，添加email和notes字段
 */

require_once 'config/database.php';

echo "<h2>更新suppliers表结构</h2>";

try {
    // 创建数据库连接
    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if ($mysqli->connect_error) {
        throw new Exception("数据库连接失败: " . $mysqli->connect_error);
    }
    
    echo "<p>✅ 数据库连接成功</p>";
    
    // 检查email字段是否存在
    $result = $mysqli->query("SHOW COLUMNS FROM suppliers LIKE 'email'");
    if ($result->num_rows == 0) {
        echo "<p>🔧 添加email字段...</p>";
        $sql = "ALTER TABLE suppliers ADD COLUMN email varchar(100) DEFAULT NULL AFTER phone";
        if ($mysqli->query($sql)) {
            echo "<p style='color: green;'>✅ email字段添加成功</p>";
        } else {
            echo "<p style='color: red;'>❌ email字段添加失败: " . $mysqli->error . "</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ email字段已存在</p>";
    }
    
    // 检查notes字段是否存在
    $result = $mysqli->query("SHOW COLUMNS FROM suppliers LIKE 'notes'");
    if ($result->num_rows == 0) {
        echo "<p>🔧 添加notes字段...</p>";
        $sql = "ALTER TABLE suppliers ADD COLUMN notes text DEFAULT NULL AFTER license_number";
        if ($mysqli->query($sql)) {
            echo "<p style='color: green;'>✅ notes字段添加成功</p>";
        } else {
            echo "<p style='color: red;'>❌ notes字段添加失败: " . $mysqli->error . "</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ notes字段已存在</p>";
    }
    
    // 显示更新后的表结构
    echo "<h3>更新后的suppliers表结构：</h3>";
    $result = $mysqli->query("DESCRIBE suppliers");
    if ($result) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>字段名</th><th>类型</th><th>允许NULL</th><th>键</th><th>默认值</th><th>额外</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    $mysqli->close();
    echo "<p style='color: green; font-weight: bold;'>🎉 suppliers表结构更新完成！</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='modules/suppliers/index.php'>测试供应商管理</a> | <a href='index.php'>返回首页</a></p>";
?>
