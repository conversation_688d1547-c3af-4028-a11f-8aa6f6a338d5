<?php
/**
 * 调试供应商插入问题
 */

require_once '../includes/Database.php';

echo "<h2>调试供应商插入问题</h2>";

try {
    // 创建数据库实例
    $db = Database::getInstance();
    echo "<p>✅ Database类实例化成功</p>";
    
    // 检查表结构
    echo "<h3>检查suppliers表结构：</h3>";
    $columns = $db->fetchAll("DESCRIBE suppliers");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 测试各种插入场景
    echo "<h3>测试插入场景：</h3>";
    
    // 场景1：最小数据插入
    echo "<h4>场景1：最小数据插入</h4>";
    $minData = [
        'name' => '测试供应商_最小数据_' . date('His'),
        'status' => 1
    ];
    
    try {
        $id1 = $db->insert('suppliers', $minData);
        echo "<p style='color: green;'>✅ 最小数据插入成功，ID: $id1</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 最小数据插入失败: " . $e->getMessage() . "</p>";
    }
    
    // 场景2：完整数据插入
    echo "<h4>场景2：完整数据插入</h4>";
    $fullData = [
        'name' => '测试供应商_完整数据_' . date('His'),
        'contact_person' => '测试联系人',
        'phone' => '***********',
        'email' => '<EMAIL>',
        'address' => '测试地址',
        'business_license' => 'TEST123456',
        'tax_number' => 'TAX123456',
        'bank_account' => '**********',
        'bank_name' => '测试银行',
        'credit_rating' => 'A',
        'status' => 1
    ];
    
    try {
        $id2 = $db->insert('suppliers', $fullData);
        echo "<p style='color: green;'>✅ 完整数据插入成功，ID: $id2</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 完整数据插入失败: " . $e->getMessage() . "</p>";
    }
    
    // 场景3：包含特殊字符的数据
    echo "<h4>场景3：特殊字符数据插入</h4>";
    $specialData = [
        'name' => '测试供应商_特殊字符_' . date('His') . ' & Co. "Ltd"',
        'contact_person' => "张三'李四",
        'phone' => '138-0013-8000',
        'address' => '北京市朝阳区"测试"路123号（A座）',
        'status' => 1
    ];
    
    try {
        $id3 = $db->insert('suppliers', $specialData);
        echo "<p style='color: green;'>✅ 特殊字符数据插入成功，ID: $id3</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 特殊字符数据插入失败: " . $e->getMessage() . "</p>";
    }
    
    // 显示插入的数据
    echo "<h3>插入的测试数据：</h3>";
    $testSuppliers = $db->fetchAll("SELECT * FROM suppliers WHERE name LIKE '测试供应商_%' ORDER BY id DESC LIMIT 10");
    
    if (!empty($testSuppliers)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>名称</th><th>联系人</th><th>电话</th><th>邮箱</th><th>状态</th><th>创建时间</th>";
        echo "</tr>";
        
        foreach ($testSuppliers as $supplier) {
            echo "<tr>";
            echo "<td>" . $supplier['id'] . "</td>";
            echo "<td>" . htmlspecialchars($supplier['name']) . "</td>";
            echo "<td>" . htmlspecialchars($supplier['contact_person'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($supplier['phone'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($supplier['email'] ?? '') . "</td>";
            echo "<td>" . ($supplier['status'] ? '启用' : '禁用') . "</td>";
            echo "<td>" . $supplier['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 清理测试数据
        echo "<h3>清理测试数据：</h3>";
        if (isset($_GET['cleanup']) && $_GET['cleanup'] === 'yes') {
            $deleteCount = $db->query("DELETE FROM suppliers WHERE name LIKE '测试供应商_%'")->rowCount();
            echo "<p style='color: green;'>✅ 已清理 $deleteCount 条测试数据</p>";
        } else {
            echo "<p><a href='?cleanup=yes' style='color: #dc3545;'>点击清理测试数据</a></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 操作失败: " . $e->getMessage() . "</p>";
    echo "<p>错误详情：</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='../modules/suppliers/index.php?action=create'>测试添加供应商</a> | <a href='../modules/suppliers/index.php'>供应商列表</a> | <a href='../index.php'>返回首页</a></p>";
?>
