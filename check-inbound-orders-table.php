<?php
/**
 * 检查入库单表结构
 */

require_once 'includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>检查入库单表结构</h2>";
    
    // 检查入库单表结构
    echo "<h3>inbound_orders 表字段</h3>";
    $columns = $db->query("SHOW COLUMNS FROM inbound_orders")->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>备注</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td><strong>{$col['Field']}</strong></td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "<td>{$col['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 检查是否有操作员相关字段
    echo "<h3>操作员相关字段检查</h3>";
    $operator_fields = [];
    foreach ($columns as $col) {
        if (strpos(strtolower($col['Field']), 'operator') !== false || 
            strpos(strtolower($col['Field']), 'created_by') !== false ||
            strpos(strtolower($col['Field']), 'user') !== false) {
            $operator_fields[] = $col['Field'];
        }
    }
    
    if (!empty($operator_fields)) {
        echo "<p style='color: green;'>✅ 找到操作员相关字段：" . implode(', ', $operator_fields) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ 没有找到操作员相关字段</p>";
    }
    
    // 检查表中的实际数据
    echo "<h3>表中的实际数据示例</h3>";
    $sample_data = $db->fetchAll("SELECT * FROM inbound_orders LIMIT 3");
    
    if (!empty($sample_data)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr>";
        foreach (array_keys($sample_data[0]) as $field) {
            echo "<th>$field</th>";
        }
        echo "</tr>";
        
        foreach ($sample_data as $row) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ 表中没有数据</p>";
    }
    
    // 测试不同的查询方案
    echo "<h3>测试查询方案</h3>";
    
    $queries = [
        'operator_id' => "SELECT io.*, u.name as operator_name FROM inbound_orders io LEFT JOIN users u ON io.operator_id = u.id LIMIT 1",
        'created_by' => "SELECT io.*, u.name as operator_name FROM inbound_orders io LEFT JOIN users u ON io.created_by = u.id LIMIT 1",
        'no_join' => "SELECT io.* FROM inbound_orders io LIMIT 1"
    ];
    
    foreach ($queries as $name => $query) {
        try {
            $result = $db->fetchOne($query);
            if ($result) {
                echo "<p style='color: green;'>✅ 查询方案 '$name' 成功</p>";
                echo "<pre>" . htmlspecialchars($query) . "</pre>";
            } else {
                echo "<p style='color: orange;'>⚠️ 查询方案 '$name' 返回空结果</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 查询方案 '$name' 失败: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 检查失败：" . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    background: white;
    margin: 15px 0;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
}

th {
    background: #007bff;
    color: white;
    padding: 10px;
}

td {
    padding: 8px 10px;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    margin: 10px 0;
}

p {
    margin: 10px 0;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}
</style>
