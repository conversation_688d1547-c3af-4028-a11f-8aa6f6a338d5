/**
 * 采购管理模块 JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

function initializePage() {
    initializeSearch();
    initializeDropdowns();
    initializeAnimations();
}

function initializeSearch() {
    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            showLoading();
        });
    }
}

function initializeDropdowns() {
    const dropdownToggles = document.querySelectorAll('[data-toggle="dropdown"], .dropdown-toggle');

    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 关闭其他下拉菜单
            document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                if (dropdown !== this.parentElement) {
                    dropdown.classList.remove('active');
                }
            });

            // 切换当前下拉菜单
            this.parentElement.classList.toggle('active');
        });
    });

    // 点击外部关闭下拉菜单
    document.addEventListener('click', function(e) {
        // 如果点击的不是下拉菜单内的元素，则关闭所有下拉菜单
        if (!e.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                dropdown.classList.remove('active');
            });
        }
    });
}

function initializeAnimations() {
    // 统计项动画
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach((item, index) => {
        item.style.animationDelay = (index * 0.1) + 's';
    });
    
    // 数字计数动画
    animateNumbers();
}

function animateNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(element => {
        const finalValue = element.textContent.replace(/[^\d.]/g, '');
        if (finalValue && !isNaN(finalValue)) {
            animateNumber(element, 0, parseFloat(finalValue), 1000);
        }
    });
}

function animateNumber(element, start, end, duration) {
    const startTime = Date.now();
    const isDecimal = element.textContent.includes('¥');
    
    function update() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = start + (end - start) * easeOutQuart(progress);
        
        if (isDecimal) {
            element.textContent = '¥' + current.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        } else {
            element.textContent = Math.round(current).toLocaleString();
        }
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

function easeOutQuart(t) {
    return 1 - (--t) * t * t * t;
}

function showLoading() {
    const form = document.getElementById('searchForm');
    if (form) {
        form.classList.add('loading');
    }
}

function updateStatus(orderId, status) {
    const statusNames = {
        1: '待确认',
        2: '已确认',
        3: '已发货',
        4: '已完成',
        5: '已取消'
    };
    
    const statusName = statusNames[status];
    if (confirm(`确定要将订单状态更新为"${statusName}"吗？`)) {
        document.getElementById('statusOrderId').value = orderId;
        document.getElementById('statusValue').value = status;
        document.getElementById('statusForm').submit();
    }
}

function exportData() {
    // 收集当前显示的订单数据
    const orders = [];
    const tableRows = document.querySelectorAll('.table tbody tr');
    
    tableRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 0) {
            orders.push({
                orderNumber: cells[0].querySelector('.order-number').textContent,
                supplier: cells[1].textContent,
                orderDate: cells[2].textContent,
                itemCount: cells[3].textContent,
                amount: cells[4].textContent,
                status: cells[5].textContent,
                creator: cells[6].textContent
            });
        }
    });
    
    // 生成CSV内容
    const csvContent = generateCSV(orders);
    
    // 下载文件
    downloadCSV(csvContent, '采购订单列表.csv');
}

function generateCSV(data) {
    const headers = ['订单号', '供应商', '订单日期', '商品数量', '订单金额', '状态', '创建人'];
    const csvRows = [headers.join(',')];
    
    data.forEach(row => {
        const values = [
            row.orderNumber,
            row.supplier,
            row.orderDate,
            row.itemCount,
            row.amount,
            row.status,
            row.creator
        ];
        csvRows.push(values.join(','));
    });
    
    return csvRows.join('\n');
}

function downloadCSV(content, filename) {
    const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
});
