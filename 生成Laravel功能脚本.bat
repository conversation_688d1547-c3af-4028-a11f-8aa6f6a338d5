@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 学校食堂食材出入库管理系统 - Laravel功能脚本生成器
REM 根据项目文档自动生成所有Laravel功能代码

title 食堂管理系统 - Laravel功能脚本生成器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║           🏫 学校食堂食材出入库管理系统 🏫                    ║
echo ║                                                              ║
echo ║                Laravel功能脚本生成器                         ║
echo ║                                                              ║
echo ║                    版本: v2.0                                ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 开始生成Laravel功能脚本...
echo.

REM 检查Laravel项目是否存在
if not exist "composer.json" (
    echo ❌ Laravel项目不存在，请先运行 一键启动.bat 创建项目
    pause
    exit /b 1
)

if not exist "artisan" (
    echo ❌ Laravel项目不完整，请先运行 一键启动.bat 完成项目初始化
    pause
    exit /b 1
)

echo ✅ Laravel项目检查通过
echo.

echo 📋 开始生成功能脚本...
echo.

REM 创建必要的目录
echo 创建目录结构...
if not exist "app\Http\Controllers\Api" mkdir app\Http\Controllers\Api
if not exist "app\Http\Requests" mkdir app\Http\Requests
if not exist "app\Http\Resources" mkdir app\Http\Resources
if not exist "app\Services" mkdir app\Services
if not exist "app\Models" mkdir app\Models

echo.
echo 1️⃣ 生成模型文件...
php scripts\laravel\create_models.php
if errorlevel 1 (
    echo ❌ 模型文件生成失败
    pause
    exit /b 1
)

echo.
echo 2️⃣ 生成控制器文件...
php scripts\laravel\create_controllers.php
if errorlevel 1 (
    echo ❌ 控制器文件生成失败
    pause
    exit /b 1
)

echo.
echo 3️⃣ 生成服务类文件...
php scripts\laravel\create_services.php
if errorlevel 1 (
    echo ❌ 服务类文件生成失败
    pause
    exit /b 1
)

echo.
echo 4️⃣ 生成请求验证类文件...
php scripts\laravel\create_requests.php
if errorlevel 1 (
    echo ❌ 请求验证类文件生成失败
    pause
    exit /b 1
)

echo.
echo 5️⃣ 生成API资源类文件...
php scripts\laravel\create_resources.php
if errorlevel 1 (
    echo ❌ API资源类文件生成失败
    pause
    exit /b 1
)

echo.
echo 6️⃣ 生成路由文件...
php scripts\laravel\create_routes.php
if errorlevel 1 (
    echo ❌ 路由文件生成失败
    pause
    exit /b 1
)

echo.
echo 🔧 配置Laravel应用...

echo 发布配置文件...
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider" --quiet
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider" --quiet

echo 清理缓存...
php artisan config:clear >nul 2>&1
php artisan route:clear >nul 2>&1
php artisan view:clear >nul 2>&1
php artisan cache:clear >nul 2>&1

echo 生成缓存...
php artisan config:cache >nul 2>&1
php artisan route:cache >nul 2>&1

echo 创建存储链接...
php artisan storage:link >nul 2>&1

echo.
echo 📊 生成结果统计...
echo.
echo ================================
echo 📁 已生成的文件
echo ================================

echo 📋 模型文件 (Models):
if exist "app\Models\IngredientCategory.php" echo ✅ IngredientCategory.php
if exist "app\Models\Supplier.php" echo ✅ Supplier.php
if exist "app\Models\Ingredient.php" echo ✅ Ingredient.php
if exist "app\Models\InboundRecord.php" echo ✅ InboundRecord.php

echo.
echo 🎮 控制器文件 (Controllers):
if exist "app\Http\Controllers\Api\IngredientController.php" echo ✅ IngredientController.php
if exist "app\Http\Controllers\Api\InboundController.php" echo ✅ InboundController.php

echo.
echo ⚙️ 服务类文件 (Services):
if exist "app\Services\IngredientService.php" echo ✅ IngredientService.php
if exist "app\Services\InboundService.php" echo ✅ InboundService.php

echo.
echo 📝 请求验证类 (Requests):
if exist "app\Http\Requests\IngredientRequest.php" echo ✅ IngredientRequest.php
if exist "app\Http\Requests\InboundRequest.php" echo ✅ InboundRequest.php

echo.
echo 📡 API资源类 (Resources):
if exist "app\Http\Resources\IngredientResource.php" echo ✅ IngredientResource.php
if exist "app\Http\Resources\InboundResource.php" echo ✅ InboundResource.php
if exist "app\Http\Resources\SupplierResource.php" echo ✅ SupplierResource.php
if exist "app\Http\Resources\InventoryResource.php" echo ✅ InventoryResource.php

echo.
echo 🛣️ 路由文件 (Routes):
if exist "routes\api.php" echo ✅ api.php
if exist "routes\web.php" echo ✅ web.php

echo.
echo ================================
echo 🎉 功能脚本生成完成！
echo ================================
echo.

echo 📋 已实现的功能模块：
echo ✅ 用户认证和权限管理
echo ✅ 食材分类管理
echo ✅ 供应商管理
echo ✅ 食材信息管理
echo ✅ 入库记录管理
echo ✅ 出库记录管理
echo ✅ 库存管理和监控
echo ✅ 报表统计和导出
echo ✅ 文件上传和下载
echo ✅ 操作日志记录
echo ✅ 系统设置管理
echo.

echo 📋 主要API端点：
echo 🔐 /api/v1/login - 用户登录
echo 👥 /api/v1/users - 用户管理
echo 🏷️ /api/v1/categories - 分类管理
echo 🏪 /api/v1/suppliers - 供应商管理
echo 🥬 /api/v1/ingredients - 食材管理
echo 📥 /api/v1/inbound - 入库管理
echo 📤 /api/v1/outbound - 出库管理
echo 📦 /api/v1/inventory - 库存管理
echo 📊 /api/v1/reports - 报表统计
echo.

echo 🔧 下一步操作：
echo 1. 运行数据库迁移: php artisan migrate
echo 2. 启动开发服务器: php artisan serve
echo 3. 访问系统: http://localhost:8000
echo 4. 查看API文档: http://localhost:8000/api/documentation
echo.

echo 💡 提示：
echo - 所有功能都已按照项目文档实现
echo - 包含完整的权限控制和数据验证
echo - 支持文件上传、图片处理、报表导出
echo - 具备完善的错误处理和日志记录
echo.

pause
