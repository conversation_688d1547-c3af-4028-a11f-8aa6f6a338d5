<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试46个商品入库</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .test-button { padding: 10px 20px; margin: 10px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #005a87; }
        #result { margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 5px; white-space: pre-wrap; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>测试46个商品的采购单入库</h1>
    
    <div class="test-section">
        <h2>测试修复后的46个商品数据传输</h2>
        <p>采购单号: DD250622131033389315 (46个商品)</p>
        <button class="test-button" onclick="test46ItemsInbound()">测试46个商品入库</button>
    </div>
    
    <div id="result"></div>

    <script>
    function showResult(content, isError = false) {
        const resultDiv = document.getElementById('result');
        resultDiv.textContent = content;
        resultDiv.className = isError ? 'error' : 'success';
    }

    function test46ItemsInbound() {
        showResult('正在测试46个商品入库...');
        
        const testData = new FormData();
        testData.append('batch_type', 'batch');
        testData.append('supplier_id', '2');
        testData.append('batch_number', 'TEST_46_FIXED_' + Date.now());
        testData.append('inbound_date', new Date().toISOString().split('T')[0]);
        testData.append('operator_name', '测试操作员46');
        testData.append('notes', '修复后的46个商品测试');
        testData.append('order_id', '16');
        testData.append('is_new_order', '0');
        
        // 添加46个商品，使用连续的数组索引
        for (let i = 0; i < 46; i++) {
            testData.append(`items[${i}][ingredient_id]`, 141 + i);
            testData.append(`items[${i}][actual_quantity]`, (35.0 + i * 0.5).toFixed(1));
            testData.append(`items[${i}][unit_price]`, (1.11 + i * 0.01).toFixed(2));
            testData.append(`items[${i}][order_quantity]`, '40.00');
        }

        console.log('FormData内容检查:');
        let itemCount = 0;
        for (let [key, value] of testData.entries()) {
            if (key.includes('[ingredient_id]')) {
                itemCount++;
            }
            console.log(`${key}: ${value}`);
        }
        console.log(`商品总数: ${itemCount}`);

        fetch('/mobile/inbound.php', {
            method: 'POST',
            body: testData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json().catch(() => response.text());
        })
        .then(data => {
            console.log('服务器响应:', data);
            if (typeof data === 'object') {
                if (data.success) {
                    showResult(`✅ 测试成功！\n消息: ${data.message}\n记录数: ${data.data?.record_count}\n总金额: ¥${data.data?.total_amount}`);
                } else {
                    showResult(`❌ 测试失败！\n错误: ${data.message}`, true);
                }
            } else {
                if (data.includes('成功') || data.includes('success')) {
                    showResult('✅ 测试成功（HTML响应）！\n' + data.substring(0, 200) + '...');
                } else {
                    showResult('❌ 测试失败（未知响应格式）！\n' + data.substring(0, 500), true);
                }
            }
        })
        .catch(error => {
            console.error('请求错误:', error);
            showResult(`❌ 网络错误: ${error.message}`, true);
        });
    }
    </script>
</body>
</html>