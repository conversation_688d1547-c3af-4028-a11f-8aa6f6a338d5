<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详细调试导入问题</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        .data-table th { background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>详细调试导入问题</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../includes/ExcelReader.php';
    
    try {
        $db = Database::getInstance();
        echo "<p class='success'>✅ 数据库连接成功</p>";
        
        $testFile = '../test.xlsx';
        
        echo "<div class='test-section'>";
        echo "<h2>步骤1: 完整模拟导入过程</h2>";
        
        if (!file_exists($testFile)) {
            echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
            exit;
        }
        
        echo "<div class='step'>";
        echo "<h4>🔍 读取Excel文件</h4>";
        
        $reader = new ExcelReader();
        $data = $reader->read($testFile, 'test.xlsx');
        
        echo "<p class='success'>✅ 文件读取成功，总行数: " . count($data) . "</p>";
        
        if (empty($data)) {
            echo "<p class='error'>❌ Excel文件为空</p>";
            exit;
        }
        
        // 显示原始数据
        echo "<h4>📋 原始数据（前10行）:</h4>";
        echo "<table class='data-table'>";
        echo "<tr><th>行号</th><th>列1</th><th>列2</th><th>列3</th><th>列4</th><th>列5</th><th>列6</th><th>其他列</th></tr>";
        
        for ($i = 0; $i < min(10, count($data)); $i++) {
            $row = $data[$i];
            echo "<tr>";
            echo "<td>" . ($i + 1) . "</td>";
            for ($j = 0; $j < 6; $j++) {
                $value = isset($row[$j]) ? htmlspecialchars($row[$j]) : '';
                if (strlen($value) > 20) {
                    $value = substr($value, 0, 20) . '...';
                }
                echo "<td>'{$value}'</td>";
            }
            $otherCols = count($row) > 6 ? '...' . (count($row) - 6) . '列' : '';
            echo "<td>{$otherCols}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h4>🎯 格式检测</h4>";
        
        // 模拟格式检测
        $importType = 'simple_list';
        
        if (count($data) >= 4) {
            $firstRow = $data[0] ?? [];
            $secondRow = $data[1] ?? [];
            
            if (count($firstRow) > 4 && count($secondRow) > 11) {
                $orderNumber = trim($firstRow[1] ?? '');
                if (!empty($orderNumber) && (strlen($orderNumber) > 10 || preg_match('/^[A-Z0-9]+/', $orderNumber))) {
                    $importType = 'order_form';
                }
            }
        }
        
        echo "<p class='info'>检测到的格式: <strong>{$importType}</strong></p>";
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h4>📊 数据处理模拟</h4>";
        
        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        $processLog = [];
        
        if ($importType === 'simple_list') {
            echo "<p class='info'>使用简单列表格式处理</p>";
            
            // 跳过标题行
            $rows = array_slice($data, 1);
            echo "<p class='info'>跳过标题行后，剩余 " . count($rows) . " 行数据</p>";
            
            foreach ($rows as $index => $row) {
                $rowNumber = $index + 2; // 实际行号
                $processLog[] = "处理第{$rowNumber}行...";
                
                try {
                    // 使用修复后的空行检测
                    $filteredRow = array_filter($row, function($cell) {
                        return !empty(trim($cell));
                    });
                    
                    if (empty($filteredRow)) {
                        $processLog[] = "  → 跳过：完全空行";
                        continue;
                    }
                    
                    // 检查关键字段
                    $supplier = trim($row[0] ?? '');
                    $date = trim($row[1] ?? '');
                    $ingredient = trim($row[2] ?? '');
                    $quantity = trim($row[3] ?? '');
                    $price = trim($row[4] ?? '');
                    $notes = trim($row[5] ?? '');
                    
                    $processLog[] = "  → 字段值: 供应商='{$supplier}', 日期='{$date}', 食材='{$ingredient}', 数量='{$quantity}', 单价='{$price}'";
                    
                    if (empty($supplier) && empty($ingredient) && empty($quantity) && empty($price)) {
                        $processLog[] = "  → 跳过：关键字段都为空";
                        continue;
                    }
                    
                    // 验证必填字段（模拟importPurchaseRow的验证）
                    if (empty($supplier)) {
                        throw new Exception('供应商名称不能为空');
                    }
                    if (empty($date)) {
                        throw new Exception('订单日期不能为空');
                    }
                    if (empty($ingredient)) {
                        throw new Exception('食材名称不能为空');
                    }
                    if (empty($quantity) || !is_numeric($quantity)) {
                        throw new Exception('数量必须是有效数字');
                    }
                    if (empty($price) || !is_numeric($price)) {
                        throw new Exception('单价必须是有效数字');
                    }
                    
                    $processLog[] = "  → ✅ 验证通过，可以导入";
                    $successCount++;
                    
                } catch (Exception $e) {
                    $errorCount++;
                    $errorMsg = "第{$rowNumber}行: " . $e->getMessage();
                    $errors[] = $errorMsg;
                    $processLog[] = "  → ❌ 验证失败: " . $e->getMessage();
                }
            }
            
        } else {
            echo "<p class='info'>使用订货单格式处理</p>";
            // 订货单格式处理逻辑...
            $processLog[] = "订货单格式处理（暂未详细实现）";
        }
        
        echo "<h4>📝 处理日志:</h4>";
        echo "<div class='code-block'>";
        foreach ($processLog as $log) {
            echo $log . "\n";
        }
        echo "</div>";
        
        echo "<h4>📊 最终结果:</h4>";
        echo "<p class='info'>成功: {$successCount} 条</p>";
        echo "<p class='info'>失败: {$errorCount} 条</p>";
        
        if (!empty($errors)) {
            echo "<h4>❌ 错误详情:</h4>";
            echo "<div class='code-block'>";
            foreach ($errors as $error) {
                echo $error . "\n";
            }
            echo "</div>";
        }
        
        if ($successCount === 0) {
            echo "<p class='error'>🚨 这就是导入0条记录的原因！</p>";
        }
        echo "</div>";
        echo "</div>";
        
        // 检查数据库相关问题
        echo "<div class='test-section'>";
        echo "<h2>步骤2: 检查数据库相关问题</h2>";
        
        echo "<div class='step'>";
        echo "<h4>🗄️ 检查必要的数据表</h4>";
        
        $tables = ['suppliers', 'ingredients', 'purchase_orders', 'purchase_order_items'];
        foreach ($tables as $table) {
            try {
                $result = $db->fetchOne("SELECT COUNT(*) as count FROM {$table}");
                echo "<p class='success'>✅ 表 {$table}: " . $result['count'] . " 条记录</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ 表 {$table}: " . $e->getMessage() . "</p>";
            }
        }
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h4>🔍 检查供应商数据</h4>";
        
        try {
            $suppliers = $db->fetchAll("SELECT id, name FROM suppliers LIMIT 5");
            if (empty($suppliers)) {
                echo "<p class='warning'>⚠️ 供应商表为空，这可能导致导入失败</p>";
                echo "<p class='info'>建议先添加一些供应商数据</p>";
            } else {
                echo "<p class='success'>✅ 找到 " . count($suppliers) . " 个供应商:</p>";
                echo "<div class='code-block'>";
                foreach ($suppliers as $supplier) {
                    echo "ID: {$supplier['id']}, 名称: {$supplier['name']}\n";
                }
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ 查询供应商失败: " . $e->getMessage() . "</p>";
        }
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h4>🔍 检查食材数据</h4>";
        
        try {
            $ingredients = $db->fetchAll("SELECT id, name FROM ingredients LIMIT 5");
            if (empty($ingredients)) {
                echo "<p class='warning'>⚠️ 食材表为空，这可能导致导入失败</p>";
                echo "<p class='info'>建议先添加一些食材数据</p>";
            } else {
                echo "<p class='success'>✅ 找到 " . count($ingredients) . " 个食材:</p>";
                echo "<div class='code-block'>";
                foreach ($ingredients as $ingredient) {
                    echo "ID: {$ingredient['id']}, 名称: {$ingredient['name']}\n";
                }
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ 查询食材失败: " . $e->getMessage() . "</p>";
        }
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 调试过程出错: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>
    
    <div class="test-section">
        <h2>步骤3: 创建测试数据</h2>
        <div class="step">
            <h4>🛠️ 如果数据库表为空，请先创建测试数据：</h4>
            
            <p><strong>创建供应商数据：</strong></p>
            <div class="code-block">INSERT INTO suppliers (name, contact_person, phone, address, status, created_at) VALUES
('新鲜蔬菜供应商', '张三', '13800138001', '蔬菜批发市场', 1, NOW()),
('肉类批发商', '李四', '13800138002', '肉类批发市场', 1, NOW()),
('水产供应商', '王五', '13800138003', '水产批发市场', 1, NOW());</div>
            
            <p><strong>创建食材数据：</strong></p>
            <div class="code-block">INSERT INTO ingredients (name, code, category_id, unit, status, created_at) VALUES
('白菜', 'VEG001', 1, '斤', 1, NOW()),
('猪肉', 'MEAT001', 2, '斤', 1, NOW()),
('鲫鱼', 'FISH001', 3, '条', 1, NOW());</div>
            
            <p><strong>或者访问管理页面添加：</strong></p>
            <ul>
                <li><a href="../modules/suppliers/index.php" target="_blank">供应商管理</a></li>
                <li><a href="../modules/ingredients/index.php" target="_blank">食材管理</a></li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>步骤4: 重新测试</h2>
        <div class="step">
            <p>完成数据准备后，请：</p>
            <ol>
                <li><a href="../modules/purchase/download_excel_template.php" target="_blank">下载新的Excel模板</a></li>
                <li>添加与数据库中匹配的供应商和食材名称</li>
                <li><a href="../modules/purchase/index.php?action=import" target="_blank">重新测试导入</a></li>
            </ol>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
