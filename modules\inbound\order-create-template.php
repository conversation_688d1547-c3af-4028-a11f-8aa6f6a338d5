<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - 创建入库单</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <?php include 'sidebar.php'; ?>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <div class="content-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1><i class="fas fa-plus"></i> 创建入库单</h1>
                        <div>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 错误提示 -->
                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <?= htmlspecialchars($error_message) ?>
                </div>
                <?php endif; ?>

                <!-- 创建表单 -->
                <form method="POST" id="createOrderForm">
                    <div class="row">
                        <!-- 基本信息 -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-info-circle"></i> 基本信息
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="supplier_id" class="form-label">供应商 *</label>
                                        <select id="supplier_id" name="supplier_id" class="form-select" required>
                                            <option value="">请选择供应商</option>
                                            <?php foreach ($suppliers as $supplier): ?>
                                            <option value="<?= $supplier['id'] ?>">
                                                <?= htmlspecialchars($supplier['name']) ?>
                                                <?php if ($supplier['contact_person']): ?>
                                                - <?= htmlspecialchars($supplier['contact_person']) ?>
                                                <?php endif; ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="inbound_date" class="form-label">入库日期 *</label>
                                        <input type="date" id="inbound_date" name="inbound_date" 
                                               class="form-control" value="<?= date('Y-m-d') ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="notes" class="form-label">备注</label>
                                        <textarea id="notes" name="notes" class="form-control" rows="3"
                                                  placeholder="请输入备注信息..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 入库项目 -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-list"></i> 入库项目
                                        <button type="button" class="btn btn-sm btn-primary float-end" onclick="addItem()">
                                            <i class="fas fa-plus"></i> 添加项目
                                        </button>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div id="itemsList">
                                        <!-- 项目列表将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <span class="text-muted">总项目数：</span>
                                    <span id="totalItems" class="badge bg-info">0</span>
                                    <span class="text-muted ms-3">总金额：</span>
                                    <span id="totalAmount" class="badge bg-success">¥0.00</span>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-secondary" onclick="history.back()">
                                        <i class="fas fa-times"></i> 取消
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 创建入库单
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let itemIndex = 0;
        const ingredients = <?= json_encode($ingredients) ?>;
        
        // 添加项目
        function addItem() {
            const itemsContainer = document.getElementById('itemsList');
            const itemDiv = document.createElement('div');
            itemDiv.className = 'item-row mb-3 p-3 border rounded';
            itemDiv.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">食材</label>
                        <select name="items[${itemIndex}][ingredient_id]" class="form-select" required onchange="updateItemInfo(this, ${itemIndex})">
                            <option value="">请选择食材</option>
                            ${ingredients.map(ing => `<option value="${ing.id}" data-unit="${ing.unit}">${ing.name} (${ing.category_name || '未分类'})</option>`).join('')}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">数量</label>
                        <input type="number" name="items[${itemIndex}][actual_quantity]" class="form-control" 
                               step="0.01" min="0" required onchange="calculateItemTotal(${itemIndex})">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">单价</label>
                        <input type="number" name="items[${itemIndex}][unit_price]" class="form-control" 
                               step="0.01" min="0" required onchange="calculateItemTotal(${itemIndex})">
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-3">
                        <label class="form-label">单位</label>
                        <input type="text" id="unit_${itemIndex}" class="form-control" readonly>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">小计</label>
                        <input type="text" id="total_${itemIndex}" class="form-control" readonly>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">备注</label>
                        <input type="text" name="items[${itemIndex}][notes]" class="form-control" placeholder="备注">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-danger btn-sm d-block" onclick="removeItem(this)">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
            `;
            
            itemsContainer.appendChild(itemDiv);
            itemIndex++;
            updateTotals();
        }
        
        // 删除项目
        function removeItem(button) {
            button.closest('.item-row').remove();
            updateTotals();
        }
        
        // 更新项目信息
        function updateItemInfo(select, index) {
            const selectedOption = select.options[select.selectedIndex];
            const unit = selectedOption.getAttribute('data-unit') || '';
            document.getElementById(`unit_${index}`).value = unit;
            calculateItemTotal(index);
        }
        
        // 计算项目小计
        function calculateItemTotal(index) {
            const quantityInput = document.querySelector(`input[name="items[${index}][actual_quantity]"]`);
            const priceInput = document.querySelector(`input[name="items[${index}][unit_price]"]`);
            const totalInput = document.getElementById(`total_${index}`);
            
            const quantity = parseFloat(quantityInput.value) || 0;
            const price = parseFloat(priceInput.value) || 0;
            const total = quantity * price;
            
            totalInput.value = '¥' + total.toFixed(2);
            updateTotals();
        }
        
        // 更新总计
        function updateTotals() {
            const itemRows = document.querySelectorAll('.item-row');
            let totalItems = itemRows.length;
            let totalAmount = 0;
            
            itemRows.forEach((row, index) => {
                const quantityInput = row.querySelector('input[name*="[actual_quantity]"]');
                const priceInput = row.querySelector('input[name*="[unit_price]"]');
                
                const quantity = parseFloat(quantityInput.value) || 0;
                const price = parseFloat(priceInput.value) || 0;
                totalAmount += quantity * price;
            });
            
            document.getElementById('totalItems').textContent = totalItems;
            document.getElementById('totalAmount').textContent = '¥' + totalAmount.toFixed(2);
        }
        
        // 表单提交验证
        document.getElementById('createOrderForm').addEventListener('submit', function(e) {
            const itemRows = document.querySelectorAll('.item-row');
            if (itemRows.length === 0) {
                e.preventDefault();
                alert('请至少添加一个入库项目');
                return false;
            }
            
            // 验证每个项目都已填写完整
            let hasError = false;
            itemRows.forEach(row => {
                const ingredientSelect = row.querySelector('select[name*="[ingredient_id]"]');
                const quantityInput = row.querySelector('input[name*="[actual_quantity]"]');
                const priceInput = row.querySelector('input[name*="[unit_price]"]');
                
                if (!ingredientSelect.value || !quantityInput.value || !priceInput.value) {
                    hasError = true;
                }
            });
            
            if (hasError) {
                e.preventDefault();
                alert('请完整填写所有入库项目的信息');
                return false;
            }
        });
        
        // 页面加载时添加一个默认项目
        document.addEventListener('DOMContentLoaded', function() {
            addItem();
        });
    </script>
</body>
</html>

<style>
.item-row {
    background: #f8f9fa;
    border: 1px solid #dee2e6 !important;
}

.item-row:hover {
    background: #e9ecef;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.content-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
}

#totalItems, #totalAmount {
    font-size: 14px;
    padding: 6px 12px;
}
</style>
