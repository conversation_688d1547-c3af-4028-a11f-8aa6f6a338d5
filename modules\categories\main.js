/**
 * 食材分类管理模块 JavaScript
 */

// DOM 加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    initializeDropdowns();
    initializeSearch();
    initializeCards();
    initializeAnimations();
}

/**
 * 初始化下拉菜单
 */
function initializeDropdowns() {
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 关闭其他下拉菜单
            document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                if (dropdown !== this.parentElement) {
                    dropdown.classList.remove('active');
                }
            });
            
            // 切换当前下拉菜单
            this.parentElement.classList.toggle('active');
        });
    });
    
    // 点击外部关闭下拉菜单
    document.addEventListener('click', function() {
        document.querySelectorAll('.dropdown.active').forEach(dropdown => {
            dropdown.classList.remove('active');
        });
    });
}

/**
 * 初始化搜索功能
 */
function initializeSearch() {
    const searchForm = document.querySelector('.search-box form');
    const searchInput = document.querySelector('input[name="search"]');
    
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            showLoading();
        });
    }
    
    // 实时搜索（防抖）
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2 || this.value.length === 0) {
                    filterCategories(this.value);
                }
            }, 300);
        });
    }
}

/**
 * 过滤分类卡片
 */
function filterCategories(searchTerm) {
    const categoryCards = document.querySelectorAll('.category-card');
    const searchLower = searchTerm.toLowerCase();
    
    categoryCards.forEach(card => {
        const categoryName = card.querySelector('.category-name').textContent.toLowerCase();
        const categoryDesc = card.querySelector('.category-description').textContent.toLowerCase();
        
        if (categoryName.includes(searchLower) || categoryDesc.includes(searchLower)) {
            card.style.display = 'block';
            card.classList.add('fade-in');
        } else {
            card.style.display = 'none';
            card.classList.remove('fade-in');
        }
    });
    
    // 检查是否有可见的卡片
    const visibleCards = document.querySelectorAll('.category-card[style*="block"]');
    const emptyState = document.querySelector('.empty-state');
    
    if (visibleCards.length === 0 && searchTerm) {
        if (!emptyState) {
            showNoResults();
        }
    } else {
        hideNoResults();
    }
}

/**
 * 显示无结果状态
 */
function showNoResults() {
    const grid = document.querySelector('.categories-grid');
    const noResults = document.createElement('div');
    noResults.className = 'empty-state no-results';
    noResults.innerHTML = `
        <i class="fas fa-search"></i>
        <h5>未找到匹配的分类</h5>
        <p>请尝试其他关键词</p>
    `;
    grid.appendChild(noResults);
}

/**
 * 隐藏无结果状态
 */
function hideNoResults() {
    const noResults = document.querySelector('.no-results');
    if (noResults) {
        noResults.remove();
    }
}

/**
 * 初始化卡片交互
 */
function initializeCards() {
    const categoryCards = document.querySelectorAll('.category-card');
    
    categoryCards.forEach(card => {
        // 卡片点击效果
        card.addEventListener('click', function(e) {
            // 如果点击的是按钮或下拉菜单，不执行卡片点击
            if (e.target.closest('.btn') || e.target.closest('.dropdown')) {
                return;
            }
            
            // 添加点击效果
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
        
        // 删除确认
        const deleteLink = card.querySelector('a[href*="action=delete"]');
        if (deleteLink) {
            deleteLink.addEventListener('click', function(e) {
                e.preventDefault();
                
                const categoryName = card.querySelector('.category-name').textContent;
                const ingredientCount = card.querySelector('.stat-value').textContent;
                
                let message = `确定要删除分类"${categoryName}"吗？`;
                if (parseInt(ingredientCount) > 0) {
                    message += `\n\n注意：该分类下有 ${ingredientCount} 种食材，删除前请先处理这些食材。`;
                }
                
                if (confirm(message)) {
                    showCardLoading(card);
                    window.location.href = this.href;
                }
            });
        }
    });
}

/**
 * 显示卡片加载状态
 */
function showCardLoading(card) {
    card.classList.add('loading');
}

/**
 * 隐藏卡片加载状态
 */
function hideCardLoading(card) {
    card.classList.remove('loading');
}

/**
 * 初始化动画
 */
function initializeAnimations() {
    // 统计项动画
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach((item, index) => {
        item.style.animationDelay = (index * 0.1) + 's';
        item.classList.add('slide-in-up');
    });
    
    // 分类卡片动画
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + 's';
    });
    
    // 数字计数动画
    animateNumbers();
}

/**
 * 数字计数动画
 */
function animateNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(element => {
        const finalValue = element.textContent.replace(/[^\d.]/g, '');
        if (finalValue && !isNaN(finalValue)) {
            animateNumber(element, 0, parseFloat(finalValue), 1000);
        }
    });
}

/**
 * 单个数字动画
 */
function animateNumber(element, start, end, duration) {
    const startTime = Date.now();
    const isDecimal = element.textContent.includes('¥');
    
    function update() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = start + (end - start) * easeOutQuart(progress);
        
        if (isDecimal) {
            element.textContent = '¥' + current.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        } else {
            element.textContent = Math.round(current).toLocaleString();
        }
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

/**
 * 缓动函数
 */
function easeOutQuart(t) {
    return 1 - (--t) * t * t * t;
}

/**
 * 显示加载状态
 */
function showLoading() {
    const form = document.querySelector('.search-box form');
    if (form) {
        form.classList.add('loading');
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const form = document.querySelector('.search-box form');
    if (form) {
        form.classList.remove('loading');
    }
}

/**
 * 刷新页面数据
 */
function refreshData() {
    showLoading();
    
    // 模拟数据刷新
    setTimeout(() => {
        location.reload();
    }, 500);
}

/**
 * 导出数据
 */
function exportData() {
    // 模拟导出功能
    if (typeof App !== 'undefined' && App.ui) {
        App.ui.showNotification('导出功能开发中', 'info');
    } else {
        alert('导出功能开发中');
    }
}

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
    if (typeof App !== 'undefined' && App.ui) {
        App.ui.showNotification('页面出现错误，请刷新重试', 'danger');
    }
});

// 页面卸载前的清理
window.addEventListener('beforeunload', function() {
    // 清理定时器和事件监听器
    const loadingElements = document.querySelectorAll('.loading');
    loadingElements.forEach(element => {
        element.classList.remove('loading');
    });
});
