<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分析正确的字段位置</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .mapping-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .mapping-table th, .mapping-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .mapping-table th { background: #f5f5f5; }
        .highlight { background: #ffeb3b; padding: 2px 4px; font-weight: bold; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>🔍 分析正确的字段位置</h1>
    
    <?php
    require_once '../includes/ExcelReader.php';
    
    $testFile = '../test.xlsx';
    
    if (!file_exists($testFile)) {
        echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
        exit;
    }
    
    $reader = new ExcelReader();
    $data = $reader->read($testFile, 'test.xlsx');
    
    if (empty($data)) {
        echo "<p class='error'>❌ Excel文件为空</p>";
        exit;
    }
    
    echo "<div class='test-section'>";
    echo "<h2>📊 详细行列分析</h2>";
    
    echo "<div class='step'>";
    echo "<h4>前10行的详细内容：</h4>";
    
    for ($i = 0; $i < min(10, count($data)); $i++) {
        $row = $data[$i];
        echo "<h5>第" . ($i + 1) . "行：</h5>";
        echo "<div class='code-block'>";
        for ($j = 0; $j < min(15, count($row)); $j++) {
            $value = trim($row[$j] ?? '');
            if (!empty($value)) {
                echo "[{$j}] = '{$value}'\n";
            }
        }
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🎯 正确的字段位置分析</h2>";
    
    echo "<div class='step'>";
    echo "<h4>基于调试结果，正确的位置应该是：</h4>";
    
    // 根据调试结果分析正确位置
    echo "<table class='mapping-table'>";
    echo "<tr><th>字段</th><th>标签位置</th><th>值位置</th><th>实际值</th></tr>";
    
    $fieldMappings = [
        ['订单号', '第2行第1列 [1][0]', '第2行第2列 [1][1]', $data[1][1] ?? ''],
        ['下单日期', '第2行第4列 [1][3]', '第2行第5列 [1][4]', $data[1][4] ?? ''],
        ['预交货日期', '第2行第8列 [1][7]', '第2行第9列 [1][8]', $data[1][8] ?? ''],
        ['客户名称', '第3行第1列 [2][0]', '第3行第1列 [2][0]', $data[2][0] ?? ''],
        ['联系人', '第3行第8列 [2][7]', '第3行第9列 [2][8]', $data[2][8] ?? ''],
        ['收货地址', '第4行第1列 [3][0]', '第4行第1列 [3][0]', $data[3][0] ?? ''],
        ['联系电话', '第4行第10列 [3][9]', '第4行第11列 [3][10]', $data[3][10] ?? ''],
        ['下单金额', '第5行第1列 [4][0]', '第5行第1列 [4][0]', $data[4][0] ?? ''],
        ['实际金额', '第5行第2列 [4][1]', '第5行第3列 [4][2]', $data[4][2] ?? ''],
    ];
    
    foreach ($fieldMappings as $mapping) {
        $value = trim($mapping[3]);
        $status = empty($value) ? '❌空值' : '✅有值';
        $displayValue = empty($value) ? '(空)' : htmlspecialchars($value);
        
        echo "<tr>";
        echo "<td>{$mapping[0]}</td>";
        echo "<td>{$mapping[1]}</td>";
        echo "<td class='highlight'>{$mapping[2]}</td>";
        echo "<td>{$displayValue} {$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🔧 修正后的提取逻辑</h2>";
    
    echo "<div class='step'>";
    echo "<h4>应该使用的PHP代码：</h4>";
    echo "<div class='code-block'>";
    echo "// 修正后的头部信息提取\n";
    echo "\$orderNumber = trim(\$data[1][1] ?? '');        // 第2行第2列：订单号值\n";
    echo "\$orderDateStr = trim(\$data[1][4] ?? '');       // 第2行第5列：下单日期值\n";
    echo "\$expectedDateStr = trim(\$data[1][8] ?? '');    // 第2行第9列：预交货日期值\n";
    echo "\$customerName = trim(\$data[2][0] ?? '');       // 第3行第1列：客户名称\n";
    echo "\$contactPerson = trim(\$data[2][8] ?? '');      // 第3行第9列：联系人值\n";
    echo "\$deliveryAddress = trim(\$data[3][0] ?? '');    // 第4行第1列：收货地址\n";
    echo "\$contactPhone = trim(\$data[3][10] ?? '');      // 第4行第11列：联系电话值\n";
    echo "\$orderAmount = floatval(\$data[4][0] ?? 0);     // 第5行第1列：下单金额\n";
    echo "\$actualAmount = floatval(\$data[4][2] ?? 0);    // 第5行第3列：实际金额值\n";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>✅ 验证修正结果</h2>";
    
    echo "<div class='step'>";
    echo "<h4>使用修正后的逻辑提取：</h4>";
    
    $correctedInfo = [
        '订单号' => trim($data[1][1] ?? ''),
        '下单日期' => trim($data[1][4] ?? ''),
        '预交货日期' => trim($data[1][8] ?? ''),
        '客户名称' => trim($data[2][0] ?? ''),
        '联系人' => trim($data[2][8] ?? ''),
        '收货地址' => trim($data[3][0] ?? ''),
        '联系电话' => trim($data[3][10] ?? ''),
        '下单金额' => trim($data[4][0] ?? ''),
        '实际金额' => trim($data[4][2] ?? ''),
    ];
    
    echo "<table class='mapping-table'>";
    echo "<tr><th>字段</th><th>修正后提取值</th><th>状态</th></tr>";
    
    foreach ($correctedInfo as $field => $value) {
        $status = empty($value) ? '❌空值' : '✅有值';
        $displayValue = empty($value) ? '(空)' : htmlspecialchars($value);
        echo "<tr><td>{$field}</td><td>{$displayValue}</td><td>{$status}</td></tr>";
    }
    echo "</table>";
    
    // 检查关键字段
    $orderNumber = $correctedInfo['订单号'];
    if (!empty($orderNumber) && strlen($orderNumber) > 10) {
        echo "<p class='success'>✅ 订单号验证通过: {$orderNumber} (长度: " . strlen($orderNumber) . ")</p>";
    } else {
        echo "<p class='error'>❌ 订单号验证失败: '{$orderNumber}' (长度: " . strlen($orderNumber) . ")</p>";
    }
    echo "</div>";
    echo "</div>";
    ?>
    
    <div class="test-section">
        <h2>🚀 下一步操作</h2>
        <div class="step">
            <p><strong>现在需要：</strong></p>
            <ol>
                <li>更新PurchaseController.php中的extractOrderInfo方法</li>
                <li>使用修正后的字段位置</li>
                <li>重新测试导入功能</li>
            </ol>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
