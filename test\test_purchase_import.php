<?php
/**
 * 测试采购单批量导入功能
 */

echo "=== 采购单批量导入功能测试 ===\n\n";

// 检查导入相关文件是否存在
$files_to_check = [
    'modules/purchase/PurchaseController.php',
    'modules/purchase/import-template.php',
    'modules/purchase/download_template.php',
    'test/purchase_import_test.csv'
];

echo "1. 检查文件是否存在:\n";
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "   ✅ {$file}\n";
    } else {
        echo "   ❌ {$file} - 文件不存在\n";
    }
}

echo "\n2. 检查CSV测试文件内容:\n";
if (file_exists('test/purchase_import_test.csv')) {
    $content = file_get_contents('test/purchase_import_test.csv');
    echo "   文件大小: " . strlen($content) . " 字节\n";
    echo "   行数: " . (substr_count($content, "\n") + 1) . " 行\n";
    echo "   前3行内容:\n";
    $lines = explode("\n", $content);
    for ($i = 0; $i < min(3, count($lines)); $i++) {
        echo "     " . ($i + 1) . ": " . $lines[$i] . "\n";
    }
}

echo "\n3. 检查导入功能相关方法:\n";
require_once 'modules/purchase/PurchaseController.php';

$reflection = new ReflectionClass('PurchaseController');
$methods = ['import', 'importFromCSV', 'importOrderRow'];

foreach ($methods as $method) {
    if ($reflection->hasMethod($method)) {
        echo "   ✅ {$method}() 方法存在\n";
    } else {
        echo "   ❌ {$method}() 方法不存在\n";
    }
}

echo "\n4. 测试CSV解析:\n";
if (file_exists('test/purchase_import_test.csv')) {
    if (($handle = fopen('test/purchase_import_test.csv', "r")) !== FALSE) {
        $header = fgetcsv($handle, 1000, ",");
        echo "   标题行: " . implode(', ', $header) . "\n";
        
        $row_count = 0;
        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE && $row_count < 3) {
            $row_count++;
            echo "   数据行 {$row_count}: " . implode(', ', $data) . "\n";
        }
        fclose($handle);
        echo "   ✅ CSV文件解析成功\n";
    } else {
        echo "   ❌ 无法打开CSV文件\n";
    }
}

echo "\n5. 访问链接测试:\n";
echo "   主页面: http://localhost:8000/modules/purchase/index.php\n";
echo "   导入页面: http://localhost:8000/modules/purchase/index.php?action=import\n";
echo "   模板下载: http://localhost:8000/modules/purchase/download_template.php\n";

echo "\n=== 测试完成 ===\n";
echo "如果所有检查都通过，说明批量导入功能已正确实现。\n";
echo "可以通过浏览器访问导入页面进行实际测试。\n";
?>
