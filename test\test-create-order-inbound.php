<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试自建采购单入库功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn-success { background: #28a745; }
        .feature-box { background: #e6f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 15px 0; border-radius: 5px; }
        .step-box { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; }
        .workflow-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; border-radius: 5px; }
        .code-box { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>✅ 自建采购单入库功能已完成！</h1>
    
    <div class="test-section">
        <h2>🎯 功能概述</h2>
        
        <div class="feature-box">
            <h4>🆕 新增功能：自建采购单</h4>
            <p>如果没有现成的采购单，用户可以直接在入库页面创建新的采购单，然后立即进行入库操作。</p>
            
            <h5>主要特性：</h5>
            <ul>
                <li>📝 <strong>快速创建</strong>：在入库页面直接创建采购单</li>
                <li>🏢 <strong>供应商选择</strong>：从现有供应商中选择</li>
                <li>📦 <strong>商品添加</strong>：动态添加多个商品项目</li>
                <li>💰 <strong>自动计算</strong>：自动计算小计和总金额</li>
                <li>🔄 <strong>无缝衔接</strong>：创建后直接进入入库流程</li>
                <li>💾 <strong>数据保存</strong>：采购单数据自动保存到数据库</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔄 完整工作流程</h2>
        
        <div class="workflow-box">
            <h4>📋 自建采购单 + 入库的完整流程</h4>
            <ol>
                <li><strong>选择"创建新采购单"</strong> → 显示采购单创建表单</li>
                <li><strong>填写基本信息</strong> → 供应商、采购日期、备注</li>
                <li><strong>添加商品项目</strong> → 选择食材、输入数量、单价、用途</li>
                <li><strong>确认采购单</strong> → 系统自动创建采购单记录</li>
                <li><strong>进入入库流程</strong> → 自动加载刚创建的采购单</li>
                <li><strong>填写入库信息</strong> → 日期、拍照、备注、实际数量</li>
                <li><strong>完成入库</strong> → 保存入库记录，更新库存</li>
            </ol>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🧪 测试步骤</h2>
        
        <div class="step-box">
            <h4>步骤1：访问入库页面</h4>
            <p><a href="../modules/inbound/index.php?action=create" class="btn btn-success">🚀 打开入库页面</a></p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>页面正常加载</li>
                <li>采购单下拉列表中有"+ 创建新采购单并入库"选项</li>
                <li>该选项有特殊的蓝色背景样式</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h4>步骤2：选择创建新采购单</h4>
            <p>从采购单下拉列表中选择"+ 创建新采购单并入库"</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>显示自建采购单表单</li>
                <li>自动生成采购单号（格式：PO + 日期时间）</li>
                <li>供应商下拉列表正确加载</li>
                <li>食材下拉列表正确加载</li>
                <li>"确认采购单并继续入库"按钮处于禁用状态</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h4>步骤3：填写采购单基本信息</h4>
            <p>选择供应商，设置采购日期，填写备注（可选）</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>供应商选择正常</li>
                <li>日期默认为今天</li>
                <li>采购单号自动生成且不可编辑</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h4>步骤4：添加商品项目</h4>
            <p>选择食材，输入数量、单价、用途，点击"添加"按钮</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>商品成功添加到列表</li>
                <li>自动计算小计金额</li>
                <li>更新总计金额</li>
                <li>输入框自动清空</li>
                <li>不能重复添加同一食材</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h4>步骤5：确认采购单</h4>
            <p>添加至少一个商品后，点击"确认采购单并继续入库"</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>采购单创建成功</li>
                <li>自动进入入库流程</li>
                <li>显示日期信息、拍照记录、备注信息部分</li>
                <li>显示商品入库列表</li>
                <li>商品列表中包含刚添加的所有商品</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h4>步骤6：完成入库操作</h4>
            <p>填写入库信息，调整实际数量，提交入库记录</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>入库记录保存成功</li>
                <li>库存数据更新</li>
                <li>采购单状态更新</li>
                <li>显示成功提示信息</li>
            </ul>
        </div>
    </div>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>📊 数据检查</h2>";
        
        // 检查供应商数据
        $suppliers = $db->fetchAll("SELECT id, name, status FROM suppliers ORDER BY status DESC, name ASC");
        echo "<p class='info'>供应商总数: <strong>" . count($suppliers) . "</strong></p>";
        
        $enabledSuppliers = array_filter($suppliers, function($s) { return $s['status'] == 1; });
        echo "<p class='success'>启用的供应商: <strong>" . count($enabledSuppliers) . "</strong> 个</p>";
        
        // 检查食材数据
        $ingredients = $db->fetchAll("SELECT id, name, unit, status FROM ingredients ORDER BY status DESC, name ASC");
        echo "<p class='info'>食材总数: <strong>" . count($ingredients) . "</strong></p>";
        
        $enabledIngredients = array_filter($ingredients, function($i) { return $i['status'] == 1; });
        echo "<p class='success'>启用的食材: <strong>" . count($enabledIngredients) . "</strong> 个</p>";
        
        // 检查采购单表结构
        $purchaseOrderColumns = $db->fetchAll("DESCRIBE purchase_orders");
        echo "<p class='info'>采购单表字段数: <strong>" . count($purchaseOrderColumns) . "</strong></p>";
        
        $purchaseOrderItemColumns = $db->fetchAll("DESCRIBE purchase_order_items");
        echo "<p class='info'>采购单明细表字段数: <strong>" . count($purchaseOrderItemColumns) . "</strong></p>";
        
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-section'>";
        echo "<h2 class='error'>❌ 数据库连接失败</h2>";
        echo "<p class='error'>错误信息: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
    
    <div class="test-section">
        <h2>🔧 技术实现详情</h2>
        
        <h4>前端实现：</h4>
        <div class="code-box">
// 采购单选择处理<br>
if (selectedValue === 'create_new') {<br>
&nbsp;&nbsp;&nbsp;&nbsp;showCreateOrderSection();<br>
} else if (selectedValue && selectedValue !== '') {<br>
&nbsp;&nbsp;&nbsp;&nbsp;loadPurchaseOrderItems(orderGroup);<br>
}<br><br>
// 商品添加验证<br>
if (!ingredientId || quantity <= 0 || unitPrice <= 0) {<br>
&nbsp;&nbsp;&nbsp;&nbsp;showNotification('请填写完整信息', 'error');<br>
&nbsp;&nbsp;&nbsp;&nbsp;return;<br>
}<br><br>
// 确认新采购单<br>
const orderGroup = {<br>
&nbsp;&nbsp;&nbsp;&nbsp;order_info: { /* 采购单信息 */ },<br>
&nbsp;&nbsp;&nbsp;&nbsp;items: [ /* 商品列表 */ ]<br>
};
        </div>
        
        <h4>后端实现：</h4>
        <div class="code-box">
// 检查是否为新建采购单<br>
$isNewOrder = intval($this->request['post']['is_new_order'] ?? 0);<br>
if ($isNewOrder && !empty($newOrderData)) {<br>
&nbsp;&nbsp;&nbsp;&nbsp;$orderId = $this->createPurchaseOrder($orderData);<br>
}<br><br>
// 创建采购单<br>
private function createPurchaseOrder($orderData) {<br>
&nbsp;&nbsp;&nbsp;&nbsp;// 插入采购单主表<br>
&nbsp;&nbsp;&nbsp;&nbsp;$orderId = $this->db->insert('purchase_orders', $data);<br>
&nbsp;&nbsp;&nbsp;&nbsp;// 插入商品明细<br>
&nbsp;&nbsp;&nbsp;&nbsp;$this->db->insert('purchase_order_items', $itemData);<br>
}
        </div>
        
        <h4>数据流转：</h4>
        <div class="code-box">
1. 用户创建采购单 → JavaScript收集数据<br>
2. 数据传递到后端 → PHP处理创建逻辑<br>
3. 保存到数据库 → purchase_orders + purchase_order_items<br>
4. 返回采购单ID → 继续入库流程<br>
5. 入库数据保存 → inbound_records + 库存更新
        </div>
    </div>
    
    <div class="test-section">
        <h2>✅ 功能验证清单</h2>
        
        <h4>界面功能：</h4>
        <ul>
            <li>□ "创建新采购单"选项正确显示</li>
            <li>□ 自建采购单表单正确显示</li>
            <li>□ 供应商和食材下拉列表正确加载</li>
            <li>□ 商品添加和删除功能正常</li>
            <li>□ 金额计算正确</li>
        </ul>
        
        <h4>数据处理：</h4>
        <ul>
            <li>□ 采购单创建成功</li>
            <li>□ 商品明细保存正确</li>
            <li>□ 入库记录关联正确</li>
            <li>□ 库存更新正常</li>
        </ul>
        
        <h4>用户体验：</h4>
        <ul>
            <li>□ 操作流程顺畅</li>
            <li>□ 错误提示清晰</li>
            <li>□ 界面响应快速</li>
            <li>□ 数据验证完善</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🚀 开始测试</h2>
        
        <p>自建采购单入库功能已完成，现在可以开始测试：</p>
        
        <p style="text-align: center;">
            <a href="../modules/inbound/index.php?action=create" class="btn btn-success" style="font-size: 1.1rem; padding: 12px 24px;">
                🎯 测试自建采购单入库功能
            </a>
        </p>
        
        <h4>测试建议：</h4>
        <ol>
            <li>确保数据库中有供应商和食材数据</li>
            <li>按照上述测试步骤逐一验证</li>
            <li>测试各种边界情况和错误处理</li>
            <li>验证数据库中的数据是否正确保存</li>
        </ol>
        
        <h4>测试场景：</h4>
        <ul>
            <li><strong>正常流程</strong>：完整的创建采购单并入库</li>
            <li><strong>数据验证</strong>：测试必填字段和数据格式验证</li>
            <li><strong>重复添加</strong>：尝试添加重复的食材</li>
            <li><strong>删除商品</strong>：测试商品删除功能</li>
            <li><strong>金额计算</strong>：验证小计和总计计算是否正确</li>
        </ul>
    </div>
</body>
</html>
