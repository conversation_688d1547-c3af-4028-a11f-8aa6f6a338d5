<?php
/**
 * 移动端主页面
 */
require_once '../includes/Database.php';

// 获取统计数据
try {
    $db = Database::getInstance();
    
    // 今日入库数量
    $todayInbound = $db->fetchOne("
        SELECT COUNT(*) as count 
        FROM inbound_records 
        WHERE DATE(created_at) = CURDATE()
    ")['count'] ?? 0;
    
    // 待处理采购单
    $pendingOrders = $db->fetchOne("
        SELECT COUNT(*) as count 
        FROM purchase_orders 
        WHERE status = 1
    ")['count'] ?? 0;
    
    // 库存预警
    $lowStock = $db->fetchOne("
        SELECT COUNT(*) as count 
        FROM ingredients 
        WHERE current_stock <= min_stock AND status = 1
    ")['count'] ?? 0;
    
    // 供应商数量
    $suppliers = $db->fetchOne("
        SELECT COUNT(*) as count 
        FROM suppliers 
        WHERE status = 1
    ")['count'] ?? 0;
    
} catch (Exception $e) {
    $todayInbound = 0;
    $pendingOrders = 0;
    $lowStock = 0;
    $suppliers = 0;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学校食堂食材出入库管理系统</title>

    <!-- PWA 支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="食材管理">
    <link rel="apple-touch-icon" href="icons/icon-192x192.png">

    <!-- 预加载关键资源 -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style">
    <link rel="preload" href="style.css" as="style">
    <link rel="preload" href="main.js" as="script">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header" id="mobileHeader">
        <div class="header-content">
            <div class="logo">
                <img src="../logo.png" alt="系统Logo" class="logo-img">
                <span>学校食堂管理系统</span>
            </div>
            <div class="header-actions">
                <button class="notification-btn touch-feedback" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <?php if ($lowStock > 0): ?>
                    <span class="badge"><?= $lowStock ?></span>
                    <?php endif; ?>
                </button>
                <button class="profile-btn touch-feedback" onclick="showProfile()">
                    <i class="fas fa-user-circle"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="mobile-main">
        <!-- 欢迎区域 -->
        <section class="welcome-section touch-feedback" onclick="refreshData()">
            <div class="welcome-content">
                <h1>欢迎回来</h1>
                <p>今天是 <?= date('Y年m月d日') ?> <?php
                    $weekdays = ['Sunday' => '星期日', 'Monday' => '星期一', 'Tuesday' => '星期二', 'Wednesday' => '星期三', 'Thursday' => '星期四', 'Friday' => '星期五', 'Saturday' => '星期六'];
                    echo $weekdays[date('l')];
                ?></p>
                <small style="opacity: 0.7; font-size: 12px;">点击刷新数据</small>
            </div>
            <div class="welcome-illustration">
                <i class="fas fa-warehouse"></i>
            </div>
        </section>

        <!-- 统计卡片 -->
        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card primary touch-feedback" onclick="navigateTo('inbound.php')">
                    <div class="stat-icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="todayInbound"><?= $todayInbound ?></div>
                        <div class="stat-label">今日入库</div>
                    </div>
                </div>

                <div class="stat-card warning touch-feedback" onclick="navigateTo('purchase.php')">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="pendingOrders"><?= $pendingOrders ?></div>
                        <div class="stat-label">待处理订单</div>
                    </div>
                </div>

                <div class="stat-card danger touch-feedback" onclick="showLowStockAlert()">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="lowStock"><?= $lowStock ?></div>
                        <div class="stat-label">库存预警</div>
                    </div>
                </div>

                <div class="stat-card success touch-feedback" onclick="navigateTo('suppliers.php')">
                    <div class="stat-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="suppliers"><?= $suppliers ?></div>
                        <div class="stat-label">合作供应商</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 快捷操作 -->
        <section class="quick-actions">
            <h2>快捷操作</h2>
            <div class="actions-grid">
                <a href="inbound.php" class="action-card primary touch-feedback">
                    <div class="action-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="action-content">
                        <h3>食材入库</h3>
                        <p>扫码入库，拍照记录</p>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>

                <a href="inventory.php" class="action-card success touch-feedback">
                    <div class="action-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="action-content">
                        <h3>库存查询</h3>
                        <p>实时库存，快速查找</p>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>

                <a href="purchase.php" class="action-card info touch-feedback">
                    <div class="action-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="action-content">
                        <h3>采购管理</h3>
                        <p>订单跟踪，供应商管理</p>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>

                <a href="reports.php" class="action-card warning touch-feedback">
                    <div class="action-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="action-content">
                        <h3>数据报表</h3>
                        <p>统计分析，趋势预测</p>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>
            </div>
        </section>

        <!-- 最近活动 -->
        <section class="recent-activity">
            <div class="section-header">
                <h2>最近活动</h2>
                <a href="activity.php" class="view-all">查看全部</a>
            </div>
            <div class="activity-list">
                <?php
                try {
                    $activities = $db->fetchAll("
                        SELECT 
                            ir.created_at,
                            i.name as ingredient_name,
                            ir.quantity,
                            s.name as supplier_name
                        FROM inbound_records ir
                        LEFT JOIN ingredients i ON ir.ingredient_id = i.id
                        LEFT JOIN suppliers s ON ir.supplier_id = s.id
                        ORDER BY ir.created_at DESC
                        LIMIT 5
                    ");
                    
                    if (!empty($activities)) {
                        foreach ($activities as $activity) {
                            echo '<div class="activity-item">';
                            echo '<div class="activity-icon"><i class="fas fa-arrow-down"></i></div>';
                            echo '<div class="activity-content">';
                            echo '<div class="activity-title">食材入库</div>';
                            echo '<div class="activity-description">' . htmlspecialchars($activity['ingredient_name']) . ' - ' . $activity['quantity'] . '</div>';
                            echo '<div class="activity-meta">';
                            echo '<span class="supplier">' . htmlspecialchars($activity['supplier_name']) . '</span>';
                            echo '<span class="time">' . date('H:i', strtotime($activity['created_at'])) . '</span>';
                            echo '</div>';
                            echo '</div>';
                            echo '</div>';
                        }
                    } else {
                        echo '<div class="empty-state">';
                        echo '<i class="fas fa-inbox"></i>';
                        echo '<p>暂无最近活动</p>';
                        echo '</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="empty-state">';
                    echo '<i class="fas fa-exclamation-circle"></i>';
                    echo '<p>加载失败</p>';
                    echo '</div>';
                }
                ?>
            </div>
        </section>

        <!-- 底部间距 -->
        <div class="bottom-spacer"></div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="index.php" class="nav-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="inbound.php" class="nav-item">
            <i class="fas fa-box"></i>
            <span>入库</span>
        </a>
        <a href="inventory.php" class="nav-item">
            <i class="fas fa-warehouse"></i>
            <span>库存</span>
        </a>
        <a href="purchase.php" class="nav-item">
            <i class="fas fa-shopping-cart"></i>
            <span>采购</span>
        </a>
        <a href="reports.php" class="nav-item">
            <i class="fas fa-chart-bar"></i>
            <span>报表</span>
        </a>
        <a href="profile.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </nav>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">加载中...</div>
        </div>
    </div>

    <script src="main.js"></script>
    <script>
        // 移动端优化脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 头部滚动效果
            let lastScrollTop = 0;
            const header = document.getElementById('mobileHeader');

            window.addEventListener('scroll', function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }

                lastScrollTop = scrollTop;
            }, { passive: true });

            // 触摸反馈优化
            document.querySelectorAll('.touch-feedback').forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                }, { passive: true });

                element.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                }, { passive: true });
            });

            // 防止双击缩放
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function(event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);

            // 页面可见性变化时刷新数据
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    refreshData();
                }
            });
        });

        // 导航函数
        function navigateTo(url) {
            showLoading();
            setTimeout(() => {
                window.location.href = url;
            }, 300);
        }

        // 显示加载遮罩
        function showLoading(text = '加载中...') {
            const overlay = document.getElementById('loadingOverlay');
            const loadingText = overlay.querySelector('.loading-text');
            loadingText.textContent = text;
            overlay.classList.add('show');
        }

        // 隐藏加载遮罩
        function hideLoading() {
            const overlay = document.getElementById('loadingOverlay');
            overlay.classList.remove('show');
        }

        // 刷新数据
        function refreshData() {
            showLoading('刷新数据中...');

            fetch(window.location.href, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                // 解析返回的HTML并更新统计数据
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');

                // 更新统计数字
                const stats = ['todayInbound', 'pendingOrders', 'lowStock', 'suppliers'];
                stats.forEach(stat => {
                    const element = document.getElementById(stat);
                    const newElement = doc.getElementById(stat);
                    if (element && newElement) {
                        element.textContent = newElement.textContent;
                        element.style.animation = 'bounce 0.6s ease-out';
                    }
                });

                hideLoading();

                // 显示刷新成功提示
                showToast('数据已更新', 'success');
            })
            .catch(error => {
                console.error('刷新失败:', error);
                hideLoading();
                showToast('刷新失败，请重试', 'error');
            });
        }

        // 显示通知
        function showNotifications() {
            showToast('通知功能开发中...', 'info');
        }

        // 显示个人资料
        function showProfile() {
            navigateTo('profile.php');
        }

        // 显示库存预警详情
        function showLowStockAlert() {
            const lowStockCount = document.getElementById('lowStock').textContent;
            if (parseInt(lowStockCount) > 0) {
                navigateTo('inventory.php?filter=low_stock');
            } else {
                showToast('当前没有库存预警', 'success');
            }
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <div class="toast-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            // 添加toast样式
            toast.style.cssText = `
                position: fixed;
                top: 100px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? 'var(--success-gradient)' : type === 'error' ? 'var(--danger-gradient)' : 'var(--info-gradient)'};
                color: white;
                padding: 12px 20px;
                border-radius: 25px;
                box-shadow: var(--shadow-lg);
                z-index: 10000;
                animation: slideDown 0.3s ease-out;
                font-weight: 500;
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideUp 0.3s ease-out forwards';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }

        // 添加toast动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
            }

            .toast-content {
                display: flex;
                align-items: center;
                gap: 8px;
            }
        `;
        document.head.appendChild(style);

        // PWA 安装提示
        let deferredPrompt;

        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA 安装提示事件触发');
            e.preventDefault();
            deferredPrompt = e;
            showInstallPrompt();
        });

        function showInstallPrompt() {
            const installBanner = document.createElement('div');
            installBanner.className = 'install-banner';
            installBanner.innerHTML = `
                <div class="install-content">
                    <div class="install-icon">📱</div>
                    <div class="install-text">
                        <div class="install-title">安装应用</div>
                        <div class="install-desc">添加到主屏幕，获得更好的体验</div>
                    </div>
                    <div class="install-actions">
                        <button class="install-btn" onclick="installPWA()">安装</button>
                        <button class="install-close" onclick="closeInstallPrompt()">×</button>
                    </div>
                </div>
            `;

            installBanner.style.cssText = `
                position: fixed;
                bottom: 80px;
                left: 16px;
                right: 16px;
                background: white;
                border-radius: 16px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
                z-index: 1000;
                animation: slideUp 0.3s ease-out;
                border: 1px solid rgba(102, 126, 234, 0.1);
            `;

            document.body.appendChild(installBanner);

            // 添加样式
            const installStyle = document.createElement('style');
            installStyle.textContent = `
                .install-content {
                    display: flex;
                    align-items: center;
                    padding: 16px;
                    gap: 12px;
                }

                .install-icon {
                    font-size: 32px;
                    flex-shrink: 0;
                }

                .install-text {
                    flex: 1;
                }

                .install-title {
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 4px;
                }

                .install-desc {
                    font-size: 14px;
                    color: #666;
                }

                .install-actions {
                    display: flex;
                    gap: 8px;
                    align-items: center;
                }

                .install-btn {
                    background: var(--primary-gradient);
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }

                .install-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                }

                .install-close {
                    background: none;
                    border: none;
                    font-size: 20px;
                    color: #999;
                    cursor: pointer;
                    padding: 4px;
                    border-radius: 50%;
                    width: 32px;
                    height: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .install-close:hover {
                    background: #f5f5f5;
                }

                @keyframes slideUp {
                    from {
                        transform: translateY(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateY(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(installStyle);
        }

        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('用户接受了安装提示');
                        showToast('应用安装成功！', 'success');
                    } else {
                        console.log('用户拒绝了安装提示');
                    }
                    deferredPrompt = null;
                    closeInstallPrompt();
                });
            }
        }

        function closeInstallPrompt() {
            const banner = document.querySelector('.install-banner');
            if (banner) {
                banner.style.animation = 'slideDown 0.3s ease-out forwards';
                setTimeout(() => {
                    banner.remove();
                }, 300);
            }
        }

        // 注册 Service Worker (开发环境暂时禁用)
        if ('serviceWorker' in navigator && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('sw.js')
                    .then((registration) => {
                        console.log('Service Worker 注册成功:', registration.scope);

                        // 检查更新
                        registration.addEventListener('updatefound', () => {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    showUpdatePrompt();
                                }
                            });
                        });
                    })
                    .catch((error) => {
                        console.log('Service Worker 注册失败:', error);
                    });
            });
        } else if ('serviceWorker' in navigator) {
            // 开发环境下注销现有的Service Worker
            navigator.serviceWorker.getRegistrations().then(function(registrations) {
                for(let registration of registrations) {
                    registration.unregister();
                    console.log('Service Worker 已注销 (开发环境)');
                }
            });
        }

        function showUpdatePrompt() {
            const updateBanner = document.createElement('div');
            updateBanner.className = 'update-banner';
            updateBanner.innerHTML = `
                <div class="update-content">
                    <div class="update-icon">🔄</div>
                    <div class="update-text">
                        <div class="update-title">发现新版本</div>
                        <div class="update-desc">点击更新获得最新功能</div>
                    </div>
                    <button class="update-btn" onclick="updateApp()">更新</button>
                </div>
            `;

            updateBanner.style.cssText = `
                position: fixed;
                top: 20px;
                left: 16px;
                right: 16px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 16px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
                z-index: 1000;
                animation: slideDown 0.3s ease-out;
            `;

            document.body.appendChild(updateBanner);
        }

        function updateApp() {
            window.location.reload();
        }
    </script>
</body>
</html>
