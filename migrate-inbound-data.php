<?php
/**
 * 迁移入库记录数据到入库单系统
 */

require_once 'includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>迁移入库记录数据到入库单系统</h2>";
    
    // 开始事务
    $db->beginTransaction();
    
    // 1. 获取所有入库记录，按供应商和日期分组
    echo "<h3>1. 分析现有入库记录</h3>";
    
    $records = $db->fetchAll("
        SELECT ir.*, i.name as ingredient_name, s.name as supplier_name
        FROM inbound_records ir
        LEFT JOIN ingredients i ON ir.ingredient_id = i.id
        LEFT JOIN suppliers s ON ir.supplier_id = s.id
        WHERE ir.inbound_order_id IS NULL
        ORDER BY ir.supplier_id, DATE(ir.created_at), ir.created_at
    ");
    
    if (empty($records)) {
        echo "<p style='color: green;'>✅ 没有需要迁移的数据</p>";
        $db->rollback();
        exit;
    }
    
    echo "<p>找到 " . count($records) . " 条需要迁移的入库记录</p>";
    
    // 2. 按供应商和日期分组创建入库单
    echo "<h3>2. 创建入库单</h3>";
    
    $groups = [];
    foreach ($records as $record) {
        $date = date('Y-m-d', strtotime($record['created_at']));
        $key = $record['supplier_id'] . '_' . $date;
        
        if (!isset($groups[$key])) {
            $groups[$key] = [
                'supplier_id' => $record['supplier_id'],
                'supplier_name' => $record['supplier_name'],
                'date' => $date,
                'records' => []
            ];
        }
        
        $groups[$key]['records'][] = $record;
    }
    
    echo "<p>将创建 " . count($groups) . " 个入库单</p>";
    
    $created_orders = 0;
    $migrated_records = 0;
    
    foreach ($groups as $group) {
        try {
            // 生成入库单号
            $orderNumber = 'IN' . date('Ymd') . str_pad($created_orders + 1, 4, '0', STR_PAD_LEFT);
            
            // 计算总金额和项目数
            $totalAmount = 0;
            $totalItems = count($group['records']);
            
            foreach ($group['records'] as $record) {
                $totalAmount += $record['quantity'] * $record['unit_price'];
            }
            
            // 创建入库单
            $orderData = [
                'order_number' => $orderNumber,
                'supplier_id' => $group['supplier_id'],
                'operator_id' => 1, // 默认操作员
                'total_amount' => $totalAmount,
                'total_items' => $totalItems,
                'status' => 'completed',
                'notes' => '数据迁移自动生成',
                'inbound_date' => $group['date'],
                'created_by' => 1,
                'created_at' => $group['date'] . ' 00:00:00',
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $orderId = $db->insert('inbound_orders', $orderData);
            
            echo "<p>✅ 创建入库单: {$orderNumber} (供应商: {$group['supplier_name']}, 日期: {$group['date']}, 项目: {$totalItems}, 金额: ¥" . number_format($totalAmount, 2) . ")</p>";
            
            // 创建入库单明细并更新入库记录
            foreach ($group['records'] as $index => $record) {
                // 创建入库单明细
                $itemData = [
                    'order_id' => $orderId,
                    'ingredient_id' => $record['ingredient_id'],
                    'batch_number' => $record['batch_number'],
                    'planned_quantity' => $record['quantity'],
                    'actual_quantity' => $record['quantity'],
                    'unit_price' => $record['unit_price'],
                    'total_price' => $record['quantity'] * $record['unit_price'],
                    'production_date' => $record['production_date'],
                    'expired_at' => $record['expired_at'],
                    'acceptance_status' => 'accepted',
                    'quality_check' => $record['quality_check'],
                    'notes' => $record['notes'] ?? ''
                ];
                
                $db->insert('inbound_order_items', $itemData);
                
                // 更新入库记录，关联入库单ID
                $db->update('inbound_records', [
                    'inbound_order_id' => $orderId,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$record['id']]);
                
                $migrated_records++;
            }
            
            $created_orders++;
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 创建入库单失败: " . $e->getMessage() . "</p>";
            throw $e;
        }
    }
    
    // 3. 更新单据序列
    echo "<h3>3. 更新单据序列</h3>";
    
    try {
        $db->update('document_sequences', [
            'current_number' => $created_orders + 1,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'doc_type = ?', ['inbound_order']);
        
        echo "<p>✅ 更新单据序列成功</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ 更新单据序列失败，但不影响迁移: " . $e->getMessage() . "</p>";
    }
    
    // 提交事务
    $db->commit();
    
    echo "<h3>4. 迁移完成</h3>";
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 数据迁移成功！</p>";
    echo "<ul>";
    echo "<li>✅ 创建了 <strong>{$created_orders}</strong> 个入库单</li>";
    echo "<li>✅ 迁移了 <strong>{$migrated_records}</strong> 条入库记录</li>";
    echo "<li>✅ 所有入库记录已关联到对应的入库单</li>";
    echo "</ul>";
    
    echo "<h4>下一步操作：</h4>";
    echo "<p><a href='modules/inbound/index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>查看入库单列表</a></p>";
    echo "<p><a href='debug-inbound-data.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>重新检查数据</a></p>";
    
} catch (Exception $e) {
    $db->rollback();
    echo "<p style='color: red;'>❌ 迁移失败：" . $e->getMessage() . "</p>";
    echo "<p>事务已回滚，数据未发生变化。</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3, h4 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

p {
    margin: 10px 0;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

ul {
    background: white;
    padding: 15px 30px;
    border-radius: 4px;
    margin: 15px 0;
}

li {
    margin: 5px 0;
}

a {
    display: inline-block;
    margin: 5px 10px 5px 0;
}
</style>
