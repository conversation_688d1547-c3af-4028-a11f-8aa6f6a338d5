<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入页面诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .diagnostic { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .test-area { margin: 20px 0; padding: 20px; border: 2px solid #007bff; border-radius: 8px; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; font-family: monospace; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔍 导入页面诊断工具</h1>
    
    <div id="diagnostics"></div>
    
    <div class="test-area">
        <h3>🧪 实时测试</h3>
        <input type="file" id="testFile" style="display: none;" accept=".csv,.xlsx,.xls">
        <button onclick="testFileSelection()">📁 测试文件选择</button>
        <button onclick="checkElements()">🔍 检查页面元素</button>
        <button onclick="checkStyles()">🎨 检查CSS样式</button>
        <button onclick="checkEvents()">⚡ 检查事件</button>
        <div id="testResults" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('testResults');
            const time = new Date().toLocaleTimeString();
            results.innerHTML += `<div style="color: ${getColor(type)}">[${time}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
            console.log(message);
        }
        
        function getColor(type) {
            switch(type) {
                case 'success': return 'green';
                case 'error': return 'red';
                case 'warning': return 'orange';
                default: return 'black';
            }
        }
        
        function addDiagnostic(title, message, type = 'info') {
            const diagnostics = document.getElementById('diagnostics');
            const div = document.createElement('div');
            div.className = `diagnostic ${type}`;
            div.innerHTML = `<h4>${title}</h4><p>${message}</p>`;
            diagnostics.appendChild(div);
        }
        
        function testFileSelection() {
            log('🧪 开始文件选择测试', 'info');
            try {
                const fileInput = document.getElementById('testFile');
                fileInput.click();
                log('✅ 文件选择触发成功', 'success');
            } catch (error) {
                log('❌ 文件选择失败: ' + error.message, 'error');
            }
        }
        
        function checkElements() {
            log('🔍 检查页面元素...', 'info');
            
            // 检查常见元素
            const elements = [
                'import_file',
                'uploadArea', 
                'fileInfo',
                'submitBtn',
                'importForm'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    log(`✅ 找到元素: ${id}`, 'success');
                    log(`   标签: ${element.tagName}, 类型: ${element.type || 'N/A'}`, 'info');
                    log(`   显示状态: ${getComputedStyle(element).display}`, 'info');
                } else {
                    log(`❌ 未找到元素: ${id}`, 'error');
                }
            });
        }
        
        function checkStyles() {
            log('🎨 检查CSS样式...', 'info');
            
            const testFile = document.getElementById('testFile');
            if (testFile) {
                const styles = getComputedStyle(testFile);
                log(`文件输入样式:`, 'info');
                log(`  display: ${styles.display}`, 'info');
                log(`  visibility: ${styles.visibility}`, 'info');
                log(`  position: ${styles.position}`, 'info');
                log(`  z-index: ${styles.zIndex}`, 'info');
                log(`  pointer-events: ${styles.pointerEvents}`, 'info');
            }
            
            // 检查是否有遮挡元素
            const rect = testFile ? testFile.getBoundingClientRect() : null;
            if (rect) {
                log(`文件输入位置: x=${rect.x}, y=${rect.y}, w=${rect.width}, h=${rect.height}`, 'info');
            }
        }
        
        function checkEvents() {
            log('⚡ 检查事件绑定...', 'info');
            
            const testFile = document.getElementById('testFile');
            if (testFile) {
                // 添加测试事件
                testFile.addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        log(`✅ 文件选择事件触发: ${e.target.files[0].name}`, 'success');
                    }
                });
                log('✅ 测试事件监听器已添加', 'success');
            }
            
            // 检查全局函数
            const globalFunctions = ['triggerFileSelect', 'testFileInput'];
            globalFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log(`✅ 全局函数存在: ${funcName}`, 'success');
                } else {
                    log(`❌ 全局函数不存在: ${funcName}`, 'error');
                }
            });
        }
        
        // 页面加载时的自动诊断
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 诊断工具加载完成', 'info');
            
            // 浏览器信息
            addDiagnostic('🌐 浏览器信息', 
                `用户代理: ${navigator.userAgent}<br>
                 平台: ${navigator.platform}<br>
                 语言: ${navigator.language}`, 'info');
            
            // 文件API支持
            if (window.File && window.FileReader && window.FileList && window.Blob) {
                addDiagnostic('✅ 文件API支持', '浏览器完全支持文件API', 'success');
            } else {
                addDiagnostic('❌ 文件API支持', '浏览器不支持文件API', 'error');
            }
            
            // 检查jQuery
            if (typeof jQuery !== 'undefined') {
                addDiagnostic('📚 jQuery检测', `检测到 jQuery ${jQuery.fn.jquery}`, 'info');
            } else {
                addDiagnostic('📚 jQuery检测', '未检测到 jQuery', 'info');
            }
            
            // 检查常见的冲突
            const potentialConflicts = ['$', 'Prototype', 'MooTools'];
            potentialConflicts.forEach(name => {
                if (typeof window[name] !== 'undefined') {
                    addDiagnostic('⚠️ 潜在冲突', `检测到 ${name}，可能与文件选择功能冲突`, 'warning');
                }
            });
            
            // 检查控制台错误
            const originalError = console.error;
            console.error = function(...args) {
                addDiagnostic('❌ 控制台错误', args.join(' '), 'error');
                originalError.apply(console, args);
            };
            
            // 检查网络错误
            window.addEventListener('error', function(e) {
                addDiagnostic('❌ JavaScript错误', 
                    `文件: ${e.filename}<br>行号: ${e.lineno}<br>错误: ${e.message}`, 'error');
            });
            
            log('🎉 自动诊断完成', 'success');
        });
        
        // 模拟导入页面的问题
        function simulateImportPageIssue() {
            log('🎭 模拟导入页面问题...', 'warning');
            
            // 检查是否在iframe中
            if (window !== window.top) {
                log('⚠️ 页面在iframe中，可能影响文件选择', 'warning');
            }
            
            // 检查页面是否完全加载
            if (document.readyState !== 'complete') {
                log('⚠️ 页面未完全加载', 'warning');
            }
            
            // 检查是否有阻止默认行为的事件
            document.addEventListener('click', function(e) {
                log(`🔘 点击事件: 目标=${e.target.tagName}, 类=${e.target.className}`, 'info');
            });
        }
        
        // 延迟执行模拟测试
        setTimeout(simulateImportPageIssue, 1000);
    </script>
</body>
</html>
