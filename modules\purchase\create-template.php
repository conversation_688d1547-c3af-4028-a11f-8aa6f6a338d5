<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-plus-circle"></i>
                新建采购单
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>
        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <!-- 新建采购单表单 -->
        <div class="form-container">
            <div class="form-header">
                <div class="form-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="form-title">
                    <h2>新建采购单</h2>
                    <p>请填写采购单的基本信息和商品明细</p>
                </div>
            </div>

            <form method="POST" class="purchase-form" id="purchaseForm">
                <div class="form-grid">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-info-circle"></i> 基本信息</h3>

                        <div class="form-row-4">
                            <div class="form-group">
                                <label class="form-label">订单号</label>
                                <input type="text" name="order_number" class="form-control" readonly
                                       value="<?= htmlspecialchars($_POST['order_number'] ?? 'PO' . date('YmdHis')) ?>"
                                       style="background-color: #f8f9fa;">
                                <small class="form-text">系统自动生成</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label required">供应商</label>
                                <select name="supplier_id" class="form-control" required>
                                    <option value="">请选择供应商</option>
                                    <?php foreach ($suppliers as $supplier): ?>
                                    <option value="<?= $supplier['id'] ?>"
                                            <?= (($_POST['supplier_id'] ?? '') == $supplier['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($supplier['name']) ?>
                                        <?php if ($supplier['contact_person']): ?>
                                        - <?= htmlspecialchars($supplier['contact_person']) ?>
                                        <?php endif; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label required">订货日期</label>
                                <input type="date" name="order_date" class="form-control" required
                                       value="<?= htmlspecialchars($_POST['order_date'] ?? date('Y-m-d')) ?>">
                            </div>
                            <div class="form-group">
                                <label class="form-label">期望交货日期</label>
                                <input type="date" name="delivery_date" class="form-control"
                                       value="<?= htmlspecialchars($_POST['delivery_date'] ?? '') ?>">
                            </div>
                        </div>

                        <div class="form-row-4">
                            <div class="form-group">
                                <label class="form-label required">收货食堂</label>
                                <select name="canteen_id" class="form-control" required>
                                    <option value="">请选择收货食堂</option>
                                    <option value="1" <?= (($_POST['canteen_id'] ?? '') == '1') ? 'selected' : '' ?>>第一食堂</option>
                                    <option value="2" <?= (($_POST['canteen_id'] ?? '') == '2') ? 'selected' : '' ?>>第二食堂</option>
                                    <option value="3" <?= (($_POST['canteen_id'] ?? '') == '3') ? 'selected' : '' ?>>第三食堂</option>
                                    <option value="4" <?= (($_POST['canteen_id'] ?? '') == '4') ? 'selected' : '' ?>>教工食堂</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label required">联系人</label>
                                <input type="text" name="contact_person" class="form-control" required
                                       placeholder="请输入联系人姓名" value="<?= htmlspecialchars($_POST['contact_person'] ?? '') ?>">
                            </div>
                            <div class="form-group">
                                <label class="form-label required">收货地址</label>
                                <input type="text" name="delivery_address" class="form-control" required
                                       placeholder="请输入详细收货地址" value="<?= htmlspecialchars($_POST['delivery_address'] ?? '') ?>">
                            </div>
                            <div class="form-group">
                                <label class="form-label required">联系电话</label>
                                <input type="tel" name="contact_phone" class="form-control" required
                                       placeholder="请输入联系电话" value="<?= htmlspecialchars($_POST['contact_phone'] ?? '') ?>">
                            </div>
                        </div>

                        <div class="form-row-4">
                            <div class="form-group">
                                <label class="form-label">下单金额</label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" name="order_amount" class="form-control" step="0.01" min="0" readonly
                                           placeholder="系统自动计算" value="<?= htmlspecialchars($_POST['order_amount'] ?? '0.00') ?>"
                                           style="background-color: #f8f9fa;">
                                    <span class="input-group-text">元</span>
                                </div>
                                <small class="form-text">根据商品明细自动计算</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">实际金额</label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" name="actual_amount" class="form-control" step="0.01" min="0"
                                           placeholder="请输入实际金额" value="<?= htmlspecialchars($_POST['actual_amount'] ?? '') ?>">
                                    <span class="input-group-text">元</span>
                                </div>
                                <small class="form-text">实际支付金额（可与下单金额不同）</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label required">付款状态</label>
                                <select name="payment_status" class="form-control" required>
                                    <option value="">请选择付款状态</option>
                                    <option value="unpaid" <?= (($_POST['payment_status'] ?? '') == 'unpaid') ? 'selected' : '' ?>>未付款</option>
                                    <option value="partial" <?= (($_POST['payment_status'] ?? '') == 'partial') ? 'selected' : '' ?>>部分付款</option>
                                    <option value="paid" <?= (($_POST['payment_status'] ?? '') == 'paid') ? 'selected' : '' ?>>已付款</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">备注说明</label>
                                <input type="text" name="notes" class="form-control"
                                       placeholder="请输入备注信息" value="<?= htmlspecialchars($_POST['notes'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <!-- 商品明细 -->
                    <div class="form-section">
                        <h3><i class="fas fa-list"></i> 商品明细</h3>



                        <div class="items-container">
                            <div class="items-header">
                                <div class="item-col item-col-category">一级分类</div>
                                <div class="item-col item-col-subcategory">二级分类</div>
                                <div class="item-col item-col-ingredient">食材名称</div>
                                <div class="item-col item-col-unit">单位</div>
                                <div class="item-col item-col-quantity">数量</div>
                                <div class="item-col item-col-price">单价</div>
                                <div class="item-col item-col-total">小计</div>
                                <div class="item-col item-col-purpose">用途</div>
                                <div class="item-col item-col-action">操作</div>
                            </div>
                            
                            <div id="itemsList">
                                <!-- 商品项目将通过JavaScript动态添加 -->
                            </div>
                            
                            <div class="items-footer">
                                <button type="button" class="btn btn-outline-primary" onclick="addItem()">
                                    <i class="fas fa-plus"></i> 添加商品
                                </button>
                                <div class="total-amount">
                                    <span>总金额：</span>
                                    <strong id="totalAmount">¥0.00</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="form-actions">
                    <button type="button" onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="submit" class="btn btn-primary btn-submit">
                        <i class="fas fa-save"></i> 保存采购单
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../../assets/js/common.js"></script>
<script>
// 食材数据
const ingredients = <?= json_encode($ingredients) ?>;
const categories = <?= json_encode($categories ?? []) ?>;
const subcategories = <?= json_encode($subcategories ?? []) ?>;
let itemIndex = 0;

// 添加商品项目
function addItem() {
    const itemsContainer = document.getElementById('itemsList');
    const itemDiv = document.createElement('div');
    itemDiv.className = 'item-row';
    itemDiv.innerHTML = `
        <div class="item-col item-col-category">
            <select class="form-control item-category-select" onchange="updateSubcategoriesForItem(this, ${itemIndex})" data-index="${itemIndex}">
                <option value="">请选择一级分类</option>
                ${generatePrimaryCategoryOptions()}
            </select>
        </div>
        <div class="item-col item-col-subcategory">
            <select class="form-control item-subcategory-select" onchange="updateIngredientsForItem(this, ${itemIndex})" data-index="${itemIndex}" disabled>
                <option value="">请先选择一级分类</option>
            </select>
        </div>
        <div class="item-col item-col-ingredient">
            <select name="items[${itemIndex}][ingredient_id]" class="form-control item-select" onchange="updateItemInfo(this, ${itemIndex})" required data-index="${itemIndex}">
                <option value="">请先选择分类</option>
            </select>
        </div>
        <div class="item-col item-col-unit">
            <input type="text" class="form-control item-unit" readonly>
        </div>
        <div class="item-col item-col-quantity">
            <input type="number" name="items[${itemIndex}][quantity]" class="form-control item-quantity"
                   step="0.01" min="0" onchange="calculateItemTotal(${itemIndex})" required>
        </div>
        <div class="item-col item-col-price">
            <input type="number" name="items[${itemIndex}][unit_price]" class="form-control item-price"
                   step="0.01" min="0" onchange="calculateItemTotal(${itemIndex})" required>
        </div>
        <div class="item-col item-col-total">
            <span class="item-total">¥0.00</span>
        </div>
        <div class="item-col item-col-purpose">
            <select name="items[${itemIndex}][purpose]" class="form-control item-purpose" required>
                <option value="">请选择用途</option>
                <option value="breakfast">早餐</option>
                <option value="lunch">中餐</option>
                <option value="dinner">晚餐</option>
                <option value="all_day">全天</option>
            </select>
        </div>
        <div class="item-col item-col-action">
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    
    itemsContainer.appendChild(itemDiv);
    itemIndex++;
}

// 生成一级分类选项HTML
function generatePrimaryCategoryOptions() {
    return categories.map(category =>
        `<option value="${category.id}">${category.name}</option>`
    ).join('');
}

// 生成食材选项HTML
function generateIngredientOptions(categoryId = null, subcategoryId = null) {
    let ingredientsToShow = ingredients;

    if (subcategoryId) {
        ingredientsToShow = ingredients.filter(ingredient => ingredient.subcategory_id == subcategoryId);
    } else if (categoryId) {
        ingredientsToShow = ingredients.filter(ingredient => ingredient.category_id == categoryId);
    }

    return ingredientsToShow.map(ingredient =>
        `<option value="${ingredient.id}" data-unit="${ingredient.unit}" data-price="${ingredient.unit_price}" data-category="${ingredient.category_id}" data-subcategory="${ingredient.subcategory_id || ''}">
            ${ingredient.name}
        </option>`
    ).join('');
}

// 更新单个商品行的二级分类
function updateSubcategoriesForItem(categorySelect, itemIndex) {
    const categoryId = categorySelect.value;
    const row = categorySelect.closest('.item-row');
    const subcategorySelect = row.querySelector('.item-subcategory-select');
    const ingredientSelect = row.querySelector('.item-select');

    // 重置二级分类和食材选择
    subcategorySelect.innerHTML = '<option value="">全部二级分类</option>';
    ingredientSelect.innerHTML = '<option value="">请选择食材</option>';

    if (categoryId) {
        // 加载对应的二级分类
        const relatedSubcategories = subcategories.filter(sub => sub.parent_id == categoryId);
        relatedSubcategories.forEach(sub => {
            const option = document.createElement('option');
            option.value = sub.id;
            option.textContent = sub.name;
            subcategorySelect.appendChild(option);
        });
        subcategorySelect.disabled = false;

        // 加载该分类下的所有食材
        ingredientSelect.innerHTML = '<option value="">请选择食材</option>' + generateIngredientOptions(categoryId);
    } else {
        subcategorySelect.disabled = true;
    }
}

// 更新单个商品行的食材列表
function updateIngredientsForItem(subcategorySelect, itemIndex) {
    const subcategoryId = subcategorySelect.value;
    const row = subcategorySelect.closest('.item-row');
    const categorySelect = row.querySelector('.item-category-select');
    const ingredientSelect = row.querySelector('.item-select');
    const categoryId = categorySelect.value;

    if (subcategoryId) {
        // 显示指定二级分类的食材
        ingredientSelect.innerHTML = '<option value="">请选择食材</option>' + generateIngredientOptions(categoryId, subcategoryId);
    } else if (categoryId) {
        // 显示一级分类下的所有食材
        ingredientSelect.innerHTML = '<option value="">请选择食材</option>' + generateIngredientOptions(categoryId);
    }
}



// 更新商品信息
function updateItemInfo(select, index) {
    const option = select.selectedOptions[0];
    const row = select.closest('.item-row');
    
    if (option.value) {
        const unit = option.dataset.unit;
        const price = option.dataset.price;
        
        row.querySelector('.item-unit').value = unit;
        row.querySelector('.item-price').value = price;
        
        calculateItemTotal(index);
    } else {
        row.querySelector('.item-unit').value = '';
        row.querySelector('.item-price').value = '';
        row.querySelector('.item-total').textContent = '¥0.00';
    }
    
    calculateTotal();
}

// 计算单项小计
function calculateItemTotal(index) {
    const row = document.querySelector(`select[name="items[${index}][ingredient_id]"]`).closest('.item-row');
    const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
    const price = parseFloat(row.querySelector('.item-price').value) || 0;
    const total = quantity * price;
    
    row.querySelector('.item-total').textContent = '¥' + total.toFixed(2);
    calculateTotal();
}

// 计算总金额
function calculateTotal() {
    let total = 0;
    document.querySelectorAll('.item-total').forEach(element => {
        const amount = parseFloat(element.textContent.replace('¥', '')) || 0;
        total += amount;
    });

    // 更新页面显示的总金额
    const totalAmountElement = document.getElementById('totalAmount');
    if (totalAmountElement) {
        totalAmountElement.textContent = '¥' + total.toFixed(2);
    }

    // 更新下单金额字段
    const orderAmountInput = document.querySelector('input[name="order_amount"]');
    if (orderAmountInput) {
        orderAmountInput.value = total.toFixed(2);
    }
}

// 删除商品项目
function removeItem(button) {
    button.closest('.item-row').remove();
    calculateTotal();
}

// 表单验证
document.getElementById('purchaseForm').addEventListener('submit', function(e) {
    // 验证联系电话格式
    const contactPhone = document.querySelector('input[name="contact_phone"]').value;
    if (contactPhone && !/^1[3-9]\d{9}$/.test(contactPhone) && !/^0\d{2,3}-?\d{7,8}$/.test(contactPhone)) {
        e.preventDefault();
        alert('请输入正确的联系电话格式');
        return false;
    }

    const items = document.querySelectorAll('.item-row');
    if (items.length === 0) {
        e.preventDefault();
        alert('请至少添加一个商品');
        return false;
    }

    let hasValidItem = false;
    items.forEach(item => {
        const select = item.querySelector('.item-select');
        const quantity = item.querySelector('.item-quantity');
        if (select.value && quantity.value && parseFloat(quantity.value) > 0) {
            hasValidItem = true;
        }
    });

    if (!hasValidItem) {
        e.preventDefault();
        alert('请确保至少有一个有效的商品项目');
        return false;
    }
});

// 页面加载时添加一个空项目
document.addEventListener('DOMContentLoaded', function() {
    addItem();
});
</script>

<style>
/* 表单容器样式 - 与其他页面保持一致 */
.form-container {
    max-width: 2600px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px 30px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.form-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    flex-shrink: 0;
}

.form-title h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
}

.form-title p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
