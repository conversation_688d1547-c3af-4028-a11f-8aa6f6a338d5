<?php
$db = new PDO('mysql:host=************;dbname=sc;charset=utf8mb4', 'sc', 'pw5K4SsM7kZsjdxy');

// 查找这个采购单
$orderNumber = 'DD250622131033389315';
echo "查找采购单: $orderNumber\n\n";

$stmt = $db->prepare("SELECT * FROM purchase_orders WHERE order_number = ?");
$stmt->execute([$orderNumber]);
$order = $stmt->fetch(PDO::FETCH_ASSOC);

if ($order) {
    echo "采购单信息:\n";
    print_r($order);
    
    echo "\n采购单商品:\n";
    $stmt = $db->prepare("SELECT poi.*, i.name as ingredient_name, i.unit FROM purchase_order_items poi LEFT JOIN ingredients i ON poi.ingredient_id = i.id WHERE poi.order_id = ?");
    $stmt->execute([$order['id']]);
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    print_r($items);
} else {
    echo "未找到采购单\n";
}

// 检查最近的错误日志
echo "\n检查最近的数据库错误:\n";
try {
    $stmt = $db->query("SHOW GLOBAL STATUS LIKE 'Last_SQL_Error'");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        print_r($row);
    }
} catch (Exception $e) {
    echo "无法获取错误信息: " . $e->getMessage() . "\n";
}
?>