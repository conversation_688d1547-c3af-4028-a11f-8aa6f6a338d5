<?php
// 更新数据库表结构 - 添加采购管理相关表

// 数据库配置
$host = '************';
$port = 3306;
$dbname = 'sc';
$username = 'sc';
$password = 'pw5K4SsM7kZsjdxy';

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>数据库更新 - 采购管理</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .success { color: #155724; background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #721c24; background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; border: 1px solid #bee5eb; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 数据库更新 - 采购管理模块</h1>";

// 检查MySQLi扩展
if (!extension_loaded('mysqli')) {
    echo "<div class='error'>❌ MySQLi扩展未安装，无法连接数据库</div>";
    echo "<p>请安装PHP MySQL扩展或使用XAMPP/WAMP</p>";
    echo "</div></body></html>";
    exit;
}

// 连接数据库
$mysqli = new mysqli($host, $username, $password, $dbname, $port);
if ($mysqli->connect_error) {
    echo "<div class='error'>❌ 数据库连接失败: " . $mysqli->connect_error . "</div>";
    echo "</div></body></html>";
    exit;
}

$mysqli->set_charset("utf8");

echo "<div class='success'>✅ 数据库连接成功</div>";
echo "<div class='info'>数据库: {$host}:{$port}/{$dbname}</div>";

// 检查并添加缺失的字段
echo "<h2>检查并更新表结构</h2>";

// 检查 purchase_orders 表是否存在以及字段
$table_check = $mysqli->query("SHOW TABLES LIKE 'purchase_orders'");
if ($table_check && $table_check->num_rows > 0) {
    echo "<div class='info'>📋 检查 purchase_orders 表字段...</div>";

    // 获取现有字段
    $columns_result = $mysqli->query("SHOW COLUMNS FROM purchase_orders");
    $existing_columns = [];
    while ($row = $columns_result->fetch_assoc()) {
        $existing_columns[] = $row['Field'];
    }

    // 需要添加的字段
    $fields_to_add = [
        'canteen_id' => "ADD COLUMN `canteen_id` INT NULL COMMENT '收货食堂ID' AFTER `expected_delivery_date`",
        'contact_person' => "ADD COLUMN `contact_person` VARCHAR(50) NULL COMMENT '联系人' AFTER `canteen_id`",
        'delivery_address' => "ADD COLUMN `delivery_address` VARCHAR(255) NULL COMMENT '送货地址' AFTER `contact_person`",
        'contact_phone' => "ADD COLUMN `contact_phone` VARCHAR(20) NULL COMMENT '联系电话' AFTER `delivery_address`",
        'order_amount' => "ADD COLUMN `order_amount` DECIMAL(12,2) DEFAULT 0 COMMENT '订单金额' AFTER `contact_phone`",
        'actual_amount' => "ADD COLUMN `actual_amount` DECIMAL(12,2) DEFAULT 0 COMMENT '实际金额' AFTER `order_amount`",
        'payment_status' => "ADD COLUMN `payment_status` VARCHAR(20) DEFAULT 'unpaid' COMMENT '付款状态' AFTER `actual_amount`"
    ];

    // 添加缺失字段
    foreach ($fields_to_add as $field => $sql) {
        if (!in_array($field, $existing_columns)) {
            echo "<div class='info'>🔧 添加字段 {$field}...</div>";
            if ($mysqli->query("ALTER TABLE `purchase_orders` {$sql}")) {
                echo "<div class='success'>✅ 字段 {$field} 添加成功</div>";
            } else {
                echo "<div class='error'>❌ 字段 {$field} 添加失败: " . $mysqli->error . "</div>";
            }
        } else {
            echo "<div class='info'>ℹ️ 字段 {$field} 已存在</div>";
        }
    }

    // 添加索引
    $indexes_to_add = [
        'idx_canteen' => "ADD INDEX `idx_canteen` (`canteen_id`)"
    ];

    // 检查现有索引
    $indexes_result = $mysqli->query("SHOW INDEX FROM purchase_orders");
    $existing_indexes = [];
    while ($row = $indexes_result->fetch_assoc()) {
        $existing_indexes[] = $row['Key_name'];
    }

    foreach ($indexes_to_add as $index => $sql) {
        if (!in_array($index, $existing_indexes)) {
            echo "<div class='info'>🔧 添加索引 {$index}...</div>";
            if ($mysqli->query("ALTER TABLE `purchase_orders` {$sql}")) {
                echo "<div class='success'>✅ 索引 {$index} 添加成功</div>";
            } else {
                echo "<div class='error'>❌ 索引 {$index} 添加失败: " . $mysqli->error . "</div>";
            }
        } else {
            echo "<div class='info'>ℹ️ 索引 {$index} 已存在</div>";
        }
    }
}

echo "<h2>创建或更新表结构</h2>";

// 要创建的表结构
$tables = [
    'purchase_orders' => "
    CREATE TABLE IF NOT EXISTS `purchase_orders` (
        `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
        `order_number` VARCHAR(64) NOT NULL COMMENT '订货单号',
        `supplier_id` BIGINT NOT NULL COMMENT '供应商ID',
        `order_date` DATE NOT NULL COMMENT '订货日期',
        `expected_delivery_date` DATE NULL COMMENT '预期交货日期',
        `canteen_id` INT NULL COMMENT '收货食堂ID',
        `contact_person` VARCHAR(50) NULL COMMENT '联系人',
        `delivery_address` VARCHAR(255) NULL COMMENT '送货地址',
        `contact_phone` VARCHAR(20) NULL COMMENT '联系电话',
        `order_amount` DECIMAL(12,2) DEFAULT 0 COMMENT '订单金额',
        `actual_amount` DECIMAL(12,2) DEFAULT 0 COMMENT '实际金额',
        `payment_status` VARCHAR(20) DEFAULT 'unpaid' COMMENT '付款状态',
        `total_amount` DECIMAL(12,2) DEFAULT 0 COMMENT '订单总金额',
        `status` TINYINT DEFAULT 1 COMMENT '状态(1待确认2已确认3部分到货4已完成5已取消)',
        `notes` TEXT NULL COMMENT '备注',
        `created_by` BIGINT NOT NULL COMMENT '创建人ID',
        `approved_by` BIGINT NULL COMMENT '审批人ID',
        `approved_at` TIMESTAMP NULL COMMENT '审批时间',
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        UNIQUE KEY `unique_order_number` (`order_number`),
        KEY `idx_supplier` (`supplier_id`),
        KEY `idx_order_date` (`order_date`),
        KEY `idx_status` (`status`),
        KEY `idx_canteen` (`canteen_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='采购订货单表'",
    
    'purchase_order_items' => "
    CREATE TABLE IF NOT EXISTS `purchase_order_items` (
        `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
        `order_id` BIGINT NOT NULL COMMENT '订货单ID',
        `ingredient_id` BIGINT NOT NULL COMMENT '食材ID',
        `quantity` DECIMAL(10,2) NOT NULL COMMENT '订货数量',
        `unit_price` DECIMAL(10,2) NOT NULL COMMENT '单价',
        `total_price` DECIMAL(10,2) NOT NULL COMMENT '小计',
        `received_quantity` DECIMAL(10,2) DEFAULT 0 COMMENT '已收货数量',
        `notes` TEXT NULL COMMENT '备注',
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        KEY `idx_order` (`order_id`),
        KEY `idx_ingredient` (`ingredient_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='采购订货单明细表'"
];

// 创建表
foreach ($tables as $table_name => $sql) {
    echo "<h3>创建表: {$table_name}</h3>";
    
    if ($mysqli->query($sql)) {
        echo "<div class='success'>✅ 表 {$table_name} 创建成功</div>";
    } else {
        echo "<div class='error'>❌ 表 {$table_name} 创建失败: " . $mysqli->error . "</div>";
    }
}

// 检查是否需要创建uploads目录
$upload_dir = 'uploads/';
if (!is_dir($upload_dir)) {
    if (mkdir($upload_dir, 0755, true)) {
        echo "<div class='success'>✅ 创建上传目录: {$upload_dir}</div>";
    } else {
        echo "<div class='error'>❌ 创建上传目录失败: {$upload_dir}</div>";
    }
} else {
    echo "<div class='info'>📁 上传目录已存在: {$upload_dir}</div>";
}

// 插入一些测试数据
echo "<h3>插入测试数据</h3>";

// 检查是否已有测试数据
$check_result = $mysqli->query("SELECT COUNT(*) as count FROM purchase_orders");
if ($check_result) {
    $count = $check_result->fetch_assoc()['count'];
    if ($count == 0) {
        // 插入测试采购订单
        $test_orders = [
            [
                'order_number' => 'PO20240101001',
                'supplier_id' => 1,
                'order_date' => '2024-01-15',
                'expected_delivery_date' => '2024-01-20',
                'total_amount' => 1500.00,
                'status' => 2,
                'notes' => '测试采购订单',
                'created_by' => 1
            ]
        ];
        
        foreach ($test_orders as $order) {
            $stmt = $mysqli->prepare("INSERT INTO purchase_orders (order_number, supplier_id, order_date, expected_delivery_date, total_amount, status, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("sissdisi", 
                $order['order_number'], 
                $order['supplier_id'], 
                $order['order_date'], 
                $order['expected_delivery_date'], 
                $order['total_amount'], 
                $order['status'], 
                $order['notes'], 
                $order['created_by']
            );
            
            if ($stmt->execute()) {
                $order_id = $mysqli->insert_id;
                echo "<div class='success'>✅ 测试订单创建成功: {$order['order_number']}</div>";
                
                // 插入订单明细
                $test_items = [
                    ['ingredient_id' => 1, 'quantity' => 100, 'unit_price' => 3.50, 'notes' => '新鲜土豆'],
                    ['ingredient_id' => 2, 'quantity' => 50, 'unit_price' => 2.00, 'notes' => '优质白菜'],
                    ['ingredient_id' => 3, 'quantity' => 30, 'unit_price' => 15.00, 'notes' => '新鲜猪肉']
                ];
                
                $item_stmt = $mysqli->prepare("INSERT INTO purchase_order_items (order_id, ingredient_id, quantity, unit_price, total_price, notes) VALUES (?, ?, ?, ?, ?, ?)");
                
                foreach ($test_items as $item) {
                    $total_price = $item['quantity'] * $item['unit_price'];
                    $item_stmt->bind_param("iiddds", $order_id, $item['ingredient_id'], $item['quantity'], $item['unit_price'], $total_price, $item['notes']);
                    $item_stmt->execute();
                }
                
                echo "<div class='info'>📦 订单明细添加完成 (" . count($test_items) . " 项)</div>";
            } else {
                echo "<div class='error'>❌ 测试订单创建失败: " . $stmt->error . "</div>";
            }
        }
    } else {
        echo "<div class='info'>📊 已存在 {$count} 个采购订单，跳过测试数据插入</div>";
    }
}

// 显示表结构信息
echo "<h3>数据库表结构</h3>";
$tables_info = $mysqli->query("SHOW TABLES LIKE 'purchase_%'");
if ($tables_info && $tables_info->num_rows > 0) {
    echo "<div class='info'>";
    echo "<strong>采购管理相关表：</strong><br>";
    while ($table = $tables_info->fetch_array()) {
        $table_name = $table[0];
        $count_result = $mysqli->query("SELECT COUNT(*) as count FROM {$table_name}");
        $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
        echo "• {$table_name} ({$count} 条记录)<br>";
    }
    echo "</div>";
}

$mysqli->close();

echo "<h3>✅ 数据库更新完成</h3>";
echo "<div class='success'>采购管理模块的数据库表已成功创建并初始化！</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='purchase.php' class='btn'>🛒 进入采购管理</a>";
echo "<a href='dashboard.php' class='btn'>🏠 返回首页</a>";
echo "</div>";

echo "</div></body></html>";
?>
