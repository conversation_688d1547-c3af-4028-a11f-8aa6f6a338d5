<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试拍照按钮优化</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-mobile { background: #17a2b8; }
        .btn-primary { background: #667eea; }
        .feature-box { background: #e6f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 15px 0; border-radius: 5px; }
        .step-box { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; }
        .mobile-demo { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; }
        .mobile-frame { width: 320px; height: 640px; border: 8px solid #333; border-radius: 25px; margin: 0 auto; background: white; position: relative; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .mobile-screen { width: 100%; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; display: flex; flex-direction: column; }
        .mobile-header { background: rgba(255,255,255,0.95); color: #667eea; padding: 15px; display: flex; align-items: center; justify-content: center; font-weight: bold; }
        .mobile-content { flex: 1; padding: 20px; display: flex; flex-direction: column; gap: 8px; overflow-y: auto; }
        
        /* 表格布局样式 */
        .mobile-table-header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 8px; 
            border-radius: 6px; 
            display: grid; 
            grid-template-columns: 2fr 1fr 1.2fr 0.8fr 0.6fr; 
            gap: 6px; 
            font-size: 10px; 
            font-weight: 600; 
            text-align: center; 
        }
        .mobile-table-row { 
            background: white; 
            padding: 8px; 
            color: #333; 
            border-left: 4px solid #667eea; 
            display: grid; 
            grid-template-columns: 2fr 1fr 1.2fr 0.8fr 0.6fr; 
            gap: 6px; 
            align-items: center; 
            min-height: 40px; 
            margin-bottom: 1px; 
        }
        
        /* 拍照按钮样式 */
        .photo-btn-demo {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .photo-btn-demo.camera {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .photo-btn-demo.success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }
        
        .photo-btn-demo.view {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .comparison-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        .comparison-table th { background: #f5f5f5; }
        .before-col { background: #fff3cd; }
        .after-col { background: #d4edda; }
        .code-box { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>✅ 拍照按钮已优化！</h1>
    
    <div class="test-section">
        <h2>🎯 优化目标</h2>
        
        <div class="feature-box">
            <h4>📷 拍照按钮布局问题</h4>
            <p>原来的拍照按钮包含文字，在表格布局中容易造成错位。现已优化为只显示图标，确保布局整齐。</p>

            <h5>主要改进：</h5>
            <ul>
                <li>📷 <strong>只显示图标</strong>：去掉所有按钮文字，只保留图标</li>
                <li>📐 <strong>固定尺寸</strong>：统一按钮尺寸，避免错位</li>
                <li>🎨 <strong>颜色区分</strong>：不同状态使用不同颜色</li>
                <li>💡 <strong>简化提示</strong>：使用简短的title属性（拍照、查看、重新拍摄）</li>
                <li>📱 <strong>响应式适配</strong>：小屏幕下自动调整尺寸</li>
                <li>🔧 <strong>全面优化</strong>：包括手动添加模式、表格模式、模态框等所有场景</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 按钮状态对比</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>按钮状态</th>
                    <th>图标</th>
                    <th>颜色</th>
                    <th>功能说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>未拍照</strong></td>
                    <td>📷 (fa-camera)</td>
                    <td class="info">蓝紫色渐变</td>
                    <td>悬停提示：拍照</td>
                </tr>
                <tr>
                    <td><strong>已拍照</strong></td>
                    <td>✅ (fa-check)</td>
                    <td class="after-col">绿色渐变</td>
                    <td>悬停提示：重新拍摄</td>
                </tr>
                <tr>
                    <td><strong>查看照片</strong></td>
                    <td>👁️ (fa-eye)</td>
                    <td class="info">蓝色渐变</td>
                    <td>悬停提示：查看</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <h2>📱 界面预览</h2>
        
        <div class="mobile-demo">
            <h4>表格布局中的拍照按钮</h4>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <div class="mobile-header">
                        📷 拍照按钮优化
                    </div>
                    <div class="mobile-content">
                        <!-- 表头 -->
                        <div class="mobile-table-header">
                            <div style="text-align: left; padding-left: 4px;">商品名称</div>
                            <div>采购数量</div>
                            <div>实际重量</div>
                            <div>拍照</div>
                            <div>状态</div>
                        </div>
                        
                        <!-- 未拍照商品 -->
                        <div class="mobile-table-row">
                            <div style="font-weight: 600; font-size: 12px; text-align: left; padding-left: 4px;">白菜</div>
                            <div style="font-size: 11px; text-align: center;">50斤</div>
                            <div style="display: flex; justify-content: center;">
                                <div style="display: flex; border: 1px solid #ddd; border-radius: 3px; overflow: hidden; width: 60px; height: 24px;">
                                    <input style="width: 40px; padding: 2px; border: none; font-size: 10px; text-align: center;" placeholder="0.00">
                                    <div style="padding: 2px 4px; background: #f0f0f0; font-size: 9px; line-height: 1.8;">斤</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: center;">
                                <button class="photo-btn-demo camera" title="拍照">
                                    📷
                                </button>
                            </div>
                            <div style="display: flex; justify-content: center; font-size: 12px; color: #f39c12;">⏰</div>
                        </div>
                        
                        <!-- 已拍照商品 -->
                        <div class="mobile-table-row">
                            <div style="font-weight: 600; font-size: 12px; text-align: left; padding-left: 4px;">猪肉</div>
                            <div style="font-size: 11px; text-align: center;">20斤</div>
                            <div style="display: flex; justify-content: center;">
                                <div style="display: flex; border: 1px solid #ddd; border-radius: 3px; overflow: hidden; width: 60px; height: 24px;">
                                    <input style="width: 40px; padding: 2px; border: none; font-size: 10px; text-align: center;" value="19.5">
                                    <div style="padding: 2px 4px; background: #f0f0f0; font-size: 9px; line-height: 1.8;">斤</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: center; gap: 2px;">
                                <button class="photo-btn-demo success" title="重新拍摄">
                                    ✅
                                </button>
                                <button class="photo-btn-demo view" title="查看照片">
                                    👁️
                                </button>
                            </div>
                            <div style="display: flex; justify-content: center; font-size: 12px; color: #27ae60;">✅</div>
                        </div>
                        
                        <!-- 部分完成商品 -->
                        <div class="mobile-table-row">
                            <div style="font-weight: 600; font-size: 12px; text-align: left; padding-left: 4px;">胡萝卜</div>
                            <div style="font-size: 11px; text-align: center;">30斤</div>
                            <div style="display: flex; justify-content: center;">
                                <div style="display: flex; border: 1px solid #ddd; border-radius: 3px; overflow: hidden; width: 60px; height: 24px;">
                                    <input style="width: 40px; padding: 2px; border: none; font-size: 10px; text-align: center;" value="28.5">
                                    <div style="padding: 2px 4px; background: #f0f0f0; font-size: 9px; line-height: 1.8;">斤</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: center;">
                                <button class="photo-btn-demo camera" title="拍照">
                                    📷
                                </button>
                            </div>
                            <div style="display: flex; justify-content: center; font-size: 12px; color: #3498db;">📷</div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 20px; font-size: 12px; color: rgba(255,255,255,0.8);">
                            📷 只显示图标，布局整齐<br>
                            悬停显示功能说明
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
