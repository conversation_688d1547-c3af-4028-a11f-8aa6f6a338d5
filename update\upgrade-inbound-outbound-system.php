<?php
/**
 * 升级入库出库系统 - 添加入库单和出库单功能
 */

require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    echo "<h2>升级入库出库系统数据库</h2>";
    
    // 1. 检查并创建入库单表
    $checkInboundOrders = $db->query("SHOW TABLES LIKE 'inbound_orders'")->fetch();
    if (!$checkInboundOrders) {
        echo "<p>创建入库单表...</p>";
        $db->query("
        CREATE TABLE `inbound_orders` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `order_number` varchar(50) NOT NULL COMMENT '入库单号',
            `supplier_id` bigint(20) NOT NULL COMMENT '供应商ID',
            `operator_id` bigint(20) NOT NULL COMMENT '操作员ID',
            `delivery_photo` varchar(255) DEFAULT NULL COMMENT '送货单照片',
            `total_amount` decimal(12,2) DEFAULT 0.00 COMMENT '总金额',
            `total_items` int(11) DEFAULT 0 COMMENT '总项目数',
            `status` enum('pending','completed','cancelled') DEFAULT 'pending' COMMENT '状态',
            `notes` text DEFAULT NULL COMMENT '备注',
            `inbound_date` date NOT NULL COMMENT '入库日期',
            `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
            `approved_by` bigint(20) DEFAULT NULL COMMENT '审核人ID',
            `approved_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `order_number` (`order_number`),
            KEY `supplier_id` (`supplier_id`),
            KEY `operator_id` (`operator_id`),
            KEY `inbound_date` (`inbound_date`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='入库单表'
        ");
        echo "<p style='color: green;'>✅ 入库单表创建成功</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ 入库单表已存在</p>";
    }
    
    // 2. 检查并创建入库单明细表
    $checkInboundOrderItems = $db->query("SHOW TABLES LIKE 'inbound_order_items'")->fetch();
    if (!$checkInboundOrderItems) {
        echo "<p>创建入库单明细表...</p>";
        $db->query("
        CREATE TABLE `inbound_order_items` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `order_id` bigint(20) NOT NULL COMMENT '入库单ID',
            `ingredient_id` bigint(20) NOT NULL COMMENT '食材ID',
            `batch_number` varchar(64) NOT NULL COMMENT '批次号',
            `planned_quantity` decimal(10,2) NOT NULL COMMENT '计划数量',
            `actual_quantity` decimal(10,2) NOT NULL COMMENT '实际数量',
            `unit_price` decimal(10,2) NOT NULL COMMENT '单价',
            `total_price` decimal(12,2) NOT NULL COMMENT '总价',
            `production_date` date DEFAULT NULL COMMENT '生产日期',
            `expired_at` date NOT NULL COMMENT '过期日期',
            `acceptance_status` enum('pending','accepted','rejected') DEFAULT 'pending' COMMENT '验收状态',
            `quality_check` tinyint(1) DEFAULT 1 COMMENT '质检状态',
            `weight_photo` varchar(255) DEFAULT NULL COMMENT '称重照片',
            `notes` text DEFAULT NULL COMMENT '备注',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `order_id` (`order_id`),
            KEY `ingredient_id` (`ingredient_id`),
            KEY `batch_number` (`batch_number`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='入库单明细表'
        ");
        echo "<p style='color: green;'>✅ 入库单明细表创建成功</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ 入库单明细表已存在</p>";
    }
    
    // 3. 检查并创建出库单表
    $checkOutboundOrders = $db->query("SHOW TABLES LIKE 'outbound_orders'")->fetch();
    if (!$checkOutboundOrders) {
        echo "<p>创建出库单表...</p>";
        $db->query("
        CREATE TABLE `outbound_orders` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `order_number` varchar(50) NOT NULL COMMENT '出库单号',
            `meal_type` enum('breakfast','lunch','dinner','other') NOT NULL COMMENT '餐次类型',
            `meal_date` date NOT NULL COMMENT '用餐日期',
            `operator_id` bigint(20) NOT NULL COMMENT '操作员ID',
            `total_amount` decimal(12,2) DEFAULT 0.00 COMMENT '总金额',
            `total_items` int(11) DEFAULT 0 COMMENT '总项目数',
            `status` enum('pending','completed','cancelled') DEFAULT 'pending' COMMENT '状态',
            `purpose` varchar(200) DEFAULT NULL COMMENT '用途说明',
            `notes` text DEFAULT NULL COMMENT '备注',
            `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
            `approved_by` bigint(20) DEFAULT NULL COMMENT '审核人ID',
            `approved_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `order_number` (`order_number`),
            KEY `meal_type` (`meal_type`),
            KEY `meal_date` (`meal_date`),
            KEY `operator_id` (`operator_id`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='出库单表'
        ");
        echo "<p style='color: green;'>✅ 出库单表创建成功</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ 出库单表已存在</p>";
    }
    
    // 4. 检查并创建出库单明细表
    $checkOutboundOrderItems = $db->query("SHOW TABLES LIKE 'outbound_order_items'")->fetch();
    if (!$checkOutboundOrderItems) {
        echo "<p>创建出库单明细表...</p>";
        $db->query("
        CREATE TABLE `outbound_order_items` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `order_id` bigint(20) NOT NULL COMMENT '出库单ID',
            `ingredient_id` bigint(20) NOT NULL COMMENT '食材ID',
            `batch_number` varchar(64) NOT NULL COMMENT '批次号',
            `quantity` decimal(10,2) NOT NULL COMMENT '出库数量',
            `unit_price` decimal(10,2) NOT NULL COMMENT '单价',
            `total_price` decimal(12,2) NOT NULL COMMENT '总价',
            `purpose` varchar(200) DEFAULT NULL COMMENT '用途',
            `notes` text DEFAULT NULL COMMENT '备注',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `order_id` (`order_id`),
            KEY `ingredient_id` (`ingredient_id`),
            KEY `batch_number` (`batch_number`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='出库单明细表'
        ");
        echo "<p style='color: green;'>✅ 出库单明细表创建成功</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ 出库单明细表已存在</p>";
    }
    
    // 5. 为现有的入库记录表添加入库单关联字段
    $checkInboundOrderIdColumn = $db->query("SHOW COLUMNS FROM inbound_records LIKE 'inbound_order_id'")->fetch();
    if (!$checkInboundOrderIdColumn) {
        echo "<p>为入库记录表添加入库单关联字段...</p>";
        $db->query("ALTER TABLE inbound_records ADD COLUMN inbound_order_id bigint(20) DEFAULT NULL COMMENT '入库单ID' AFTER id");
        $db->query("ALTER TABLE inbound_records ADD KEY idx_inbound_order_id (inbound_order_id)");
        echo "<p style='color: green;'>✅ 入库记录表字段添加成功</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ 入库记录表字段已存在</p>";
    }
    
    // 6. 为现有的出库记录表添加出库单关联字段
    $checkOutboundOrderIdColumn = $db->query("SHOW COLUMNS FROM outbound_records LIKE 'outbound_order_id'")->fetch();
    if (!$checkOutboundOrderIdColumn) {
        echo "<p>为出库记录表添加出库单关联字段...</p>";
        $db->query("ALTER TABLE outbound_records ADD COLUMN outbound_order_id bigint(20) DEFAULT NULL COMMENT '出库单ID' AFTER id");
        $db->query("ALTER TABLE outbound_records ADD KEY idx_outbound_order_id (outbound_order_id)");
        echo "<p style='color: green;'>✅ 出库记录表字段添加成功</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ 出库记录表字段已存在</p>";
    }
    
    // 7. 创建单据编号序列表
    $checkSequences = $db->query("SHOW TABLES LIKE 'document_sequences'")->fetch();
    if (!$checkSequences) {
        echo "<p>创建单据编号序列表...</p>";
        $db->query("
        CREATE TABLE `document_sequences` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `doc_type` varchar(20) NOT NULL COMMENT '单据类型',
            `prefix` varchar(10) NOT NULL COMMENT '前缀',
            `current_number` int(11) NOT NULL DEFAULT 1 COMMENT '当前编号',
            `date_format` varchar(20) DEFAULT 'Ymd' COMMENT '日期格式',
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `doc_type` (`doc_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='单据编号序列表'
        ");
        
        // 插入初始数据
        $db->query("
        INSERT INTO document_sequences (doc_type, prefix, current_number, date_format) VALUES
        ('inbound_order', 'IN', 1, 'Ymd'),
        ('outbound_order', 'OUT', 1, 'Ymd')
        ");
        echo "<p style='color: green;'>✅ 单据编号序列表创建成功</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ 单据编号序列表已存在</p>";
    }
    
    echo "<h3 style='color: green;'>🎉 数据库升级完成！</h3>";
    echo "<p>现在系统支持：</p>";
    echo "<ul>";
    echo "<li>✅ 入库单管理（包含多个入库项目）</li>";
    echo "<li>✅ 出库单管理（包含多个出库项目）</li>";
    echo "<li>✅ 单据编号自动生成</li>";
    echo "<li>✅ 入库记录与入库单关联</li>";
    echo "<li>✅ 出库记录与出库单关联</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 升级失败：" . $e->getMessage() . "</p>";
    echo "<p>请检查数据库连接和权限设置。</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

p {
    margin: 10px 0;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

ul {
    background: white;
    padding: 15px 30px;
    border-radius: 4px;
    margin: 15px 0;
}

li {
    margin: 5px 0;
}
</style>
