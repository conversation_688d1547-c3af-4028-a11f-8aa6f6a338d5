<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试修正后的提取逻辑</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #005a8b; }
        .btn-success { background: #28a745; }
        .mapping-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .mapping-table th, .mapping-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .mapping-table th { background: #f5f5f5; }
        .highlight { background: #ffeb3b; padding: 2px 4px; font-weight: bold; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🔧 测试修正后的提取逻辑</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../includes/ExcelReader.php';
    
    try {
        $db = Database::getInstance();
        $testFile = '../test.xlsx';
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 完整模拟导入过程（使用修正后的逻辑）</h2>";
        
        if (!file_exists($testFile)) {
            echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
            exit;
        }
        
        echo "<div class='debug-log'>";
        
        function debugLog($message, $type = 'info') {
            $icons = ['info' => 'ℹ️', 'success' => '✅', 'error' => '❌', 'warning' => '⚠️'];
            $icon = $icons[$type] ?? 'ℹ️';
            echo "[" . date('H:i:s') . "] {$icon} {$message}\n";
        }
        
        debugLog("开始读取Excel文件", 'info');
        
        $reader = new ExcelReader();
        $data = $reader->read($testFile, 'test.xlsx');
        
        if (empty($data)) {
            debugLog("Excel文件为空", 'error');
            exit;
        }
        
        debugLog("文件读取成功，总行数: " . count($data), 'success');
        
        // 格式检测
        debugLog("开始格式检测", 'info');
        
        $firstRow = $data[0] ?? [];
        $secondRow = $data[1] ?? [];
        
        $firstRowText = implode('', $firstRow);
        $hasOrderTitle = strpos($firstRowText, '订货单') !== false;
        
        if ($hasOrderTitle) {
            debugLog("检测到订货单标题", 'success');
            $importType = 'order_form';
        } else {
            $orderNumber = trim($secondRow[1] ?? ''); // 修正：检查第2列而不是第1列
            if (!empty($orderNumber) && strlen($orderNumber) > 10) {
                debugLog("通过订单号检测为订货单格式", 'success');
                $importType = 'order_form';
            } else {
                debugLog("检测为简单列表格式", 'warning');
                $importType = 'simple_list';
            }
        }
        
        debugLog("格式检测结果: {$importType}", $importType === 'order_form' ? 'success' : 'warning');
        
        if ($importType === 'order_form') {
            debugLog("开始提取头部信息（使用修正后的位置）", 'info');
            
            // 使用修正后的位置提取头部信息
            $orderNumber = trim($data[1][1] ?? '');        // 第2行第2列：订单号值
            $orderDateStr = trim($data[1][4] ?? '');       // 第2行第5列：下单日期值
            $expectedDateStr = trim($data[1][8] ?? '');    // 第2行第9列：预交货日期值
            $customerName = trim($data[2][0] ?? '');       // 第3行第1列：客户名称
            $contactPerson = trim($data[2][8] ?? '');      // 第3行第9列：联系人值
            $deliveryAddress = trim($data[3][0] ?? '');    // 第4行第1列：收货地址
            $contactPhone = trim($data[3][10] ?? '');      // 第4行第11列：联系电话值
            $orderAmount = floatval($data[4][0] ?? 0);     // 第5行第1列：下单金额
            $actualAmount = floatval($data[4][2] ?? 0);    // 第5行第3列：实际金额值
            
            debugLog("订单号: '{$orderNumber}'", !empty($orderNumber) ? 'success' : 'error');
            debugLog("下单日期: '{$orderDateStr}'", !empty($orderDateStr) ? 'success' : 'warning');
            debugLog("预交货日期: '{$expectedDateStr}'", !empty($expectedDateStr) ? 'success' : 'warning');
            debugLog("客户名称: '{$customerName}'", !empty($customerName) ? 'success' : 'warning');
            debugLog("联系人: '{$contactPerson}'", !empty($contactPerson) ? 'success' : 'warning');
            debugLog("收货地址: '{$deliveryAddress}'", !empty($deliveryAddress) ? 'success' : 'warning');
            debugLog("联系电话: '{$contactPhone}'", !empty($contactPhone) ? 'success' : 'warning');
            debugLog("下单金额: {$orderAmount}", $orderAmount > 0 ? 'success' : 'warning');
            debugLog("实际金额: {$actualAmount}", $actualAmount > 0 ? 'success' : 'warning');
            
            // 验证必填字段
            if (empty($orderNumber)) {
                debugLog("订单号为空，无法继续", 'error');
            } else {
                debugLog("订单号验证通过", 'success');
                
                // 处理日期
                $orderDate = date('Y-m-d');
                if (!empty($orderDateStr)) {
                    $timestamp = strtotime($orderDateStr);
                    if ($timestamp !== false) {
                        $orderDate = date('Y-m-d', $timestamp);
                        debugLog("订单日期解析成功: {$orderDate}", 'success');
                    } else {
                        debugLog("订单日期解析失败，使用当前日期", 'warning');
                    }
                }
                
                $expectedDate = date('Y-m-d', strtotime('+1 day'));
                if (!empty($expectedDateStr)) {
                    $timestamp = strtotime($expectedDateStr);
                    if ($timestamp !== false) {
                        $expectedDate = date('Y-m-d', $timestamp);
                        debugLog("预交货日期解析成功: {$expectedDate}", 'success');
                    } else {
                        debugLog("预交货日期解析失败，使用明天", 'warning');
                    }
                }
                
                debugLog("头部信息提取完成，准备创建订单", 'success');
                
                // 查找明细数据开始行
                debugLog("查找明细数据开始行", 'info');
                
                $detailStartRow = -1;
                for ($i = 5; $i < count($data); $i++) {
                    $row = $data[$i];
                    $firstCell = trim($row[0] ?? '');
                    
                    // 查找包含"序号"或数字的行作为明细开始
                    if (strpos($firstCell, '序号') !== false || 
                        is_numeric($firstCell) ||
                        !empty($firstCell)) {
                        $detailStartRow = $i;
                        debugLog("找到明细数据开始行: 第" . ($i + 1) . "行", 'success');
                        break;
                    }
                }
                
                if ($detailStartRow === -1) {
                    debugLog("未找到明细数据", 'error');
                } else {
                    // 处理明细数据
                    $validItems = 0;
                    $totalItems = 0;
                    
                    debugLog("开始处理明细数据", 'info');
                    
                    for ($i = $detailStartRow; $i < count($data); $i++) {
                        $row = $data[$i];
                        $rowNum = $i + 1;
                        $totalItems++;
                        
                        // 检查是否为空行
                        $filteredRow = array_filter($row, function($cell) {
                            return !empty(trim($cell));
                        });
                        
                        if (empty($filteredRow)) {
                            debugLog("第{$rowNum}行: 空行，跳过", 'info');
                            continue;
                        }
                        
                        // 检查是否为标题行
                        $firstCell = trim($row[0] ?? '');
                        if (strpos($firstCell, '序号') !== false) {
                            debugLog("第{$rowNum}行: 标题行，跳过", 'info');
                            continue;
                        }
                        
                        // 检查是否为有效的明细行
                        if (is_numeric($firstCell) || !empty($row[1])) {
                            $itemCode = trim($row[1] ?? '');
                            $itemName = trim($row[2] ?? '');
                            $quantity = trim($row[9] ?? '');
                            $unitPrice = trim($row[8] ?? '');
                            
                            debugLog("第{$rowNum}行: 编码='{$itemCode}', 名称='{$itemName}', 数量='{$quantity}', 单价='{$unitPrice}'", 'info');
                            
                            if (!empty($itemCode) && is_numeric($quantity) && floatval($quantity) > 0) {
                                $validItems++;
                                debugLog("  ✅ 有效明细", 'success');
                            } else {
                                debugLog("  ❌ 无效明细（编码为空或数量无效）", 'error');
                            }
                        } else {
                            debugLog("第{$rowNum}行: 不是有效的明细行", 'warning');
                        }
                        
                        if ($totalItems >= 20) {
                            debugLog("（只检查前20行明细）", 'info');
                            break;
                        }
                    }
                    
                    debugLog("明细数据处理完成", 'info');
                    debugLog("总检查行数: {$totalItems}", 'info');
                    debugLog("有效明细数量: {$validItems}", $validItems > 0 ? 'success' : 'error');
                    
                    if ($validItems === 0) {
                        debugLog("没有有效的明细数据，这就是导入0条记录的原因！", 'error');
                    } else {
                        debugLog("预计可以导入 {$validItems} 条明细记录", 'success');
                    }
                }
            }
        } else {
            debugLog("文件未被识别为订货单格式", 'error');
        }
        
        debugLog("调试完成", 'info');
        
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 调试过程出错: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div class="test-section">
        <h2>🚀 下一步测试</h2>
        <div class="step">
            <p><strong>基于修正后的逻辑：</strong></p>
            <ol>
                <li><strong>如果头部信息提取正确</strong>：
                    <a href="../modules/purchase/index.php?action=import" class="btn btn-success">立即测试导入</a>
                </li>
                <li><strong>如果还有问题</strong>：
                    <a href="analyze-correct-positions.php" class="btn">查看详细位置分析</a>
                </li>
                <li><strong>查看实际导入结果</strong>：
                    <a href="../modules/purchase/index.php" class="btn">查看采购订单列表</a>
                </li>
            </ol>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
