<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试订货单格式导入</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 4px; text-align: left; }
        .data-table th { background: #f5f5f5; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .highlight { background: #ffeb3b; padding: 2px 4px; }
    </style>
</head>
<body>
    <h1>🔍 调试订货单格式导入功能</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../includes/ExcelReader.php';
    
    try {
        $db = Database::getInstance();
        echo "<p class='success'>✅ 数据库连接成功</p>";
        
        $testFile = '../test.xlsx';
        
        echo "<div class='test-section'>";
        echo "<h2>步骤1: 读取和分析Excel文件</h2>";
        
        if (!file_exists($testFile)) {
            echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
            echo "<p class='info'>请确保文件位于项目根目录下</p>";
            exit;
        }
        
        echo "<div class='step'>";
        echo "<h4>📁 文件信息</h4>";
        echo "<p class='info'>文件路径: " . realpath($testFile) . "</p>";
        echo "<p class='info'>文件大小: " . number_format(filesize($testFile)) . " 字节</p>";
        echo "<p class='info'>最后修改: " . date('Y-m-d H:i:s', filemtime($testFile)) . "</p>";
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h4>📊 读取Excel数据</h4>";
        
        $reader = new ExcelReader();
        $data = $reader->read($testFile, 'test.xlsx');
        
        echo "<p class='success'>✅ Excel文件读取成功</p>";
        echo "<p class='info'>总行数: " . count($data) . "</p>";
        echo "<p class='info'>总列数: " . (empty($data) ? 0 : count($data[0])) . "</p>";
        echo "</div>";
        echo "</div>";
        
        if (!empty($data)) {
            echo "<div class='test-section'>";
            echo "<h2>步骤2: 格式检测和验证</h2>";
            
            echo "<div class='step'>";
            echo "<h4>🔍 格式检测逻辑</h4>";
            
            // 模拟格式检测
            $formatType = 'simple_list';
            
            if (count($data) >= 4) {
                $firstRow = $data[0] ?? [];
                $secondRow = $data[1] ?? [];
                
                echo "<p class='info'>第1行列数: " . count($firstRow) . "</p>";
                echo "<p class='info'>第2行列数: " . count($secondRow) . "</p>";
                
                if (count($firstRow) > 4 && count($secondRow) > 11) {
                    $orderNumber = trim($firstRow[1] ?? '');
                    echo "<p class='info'>第1行第2列内容: '<span class='highlight'>" . htmlspecialchars($orderNumber) . "</span>'</p>";
                    echo "<p class='info'>订单号长度: " . strlen($orderNumber) . "</p>";
                    
                    if (!empty($orderNumber) && (strlen($orderNumber) > 10 || preg_match('/^[A-Z0-9]+/', $orderNumber))) {
                        $formatType = 'order_form';
                        echo "<p class='success'>✅ 检测为订货单格式</p>";
                    } else {
                        echo "<p class='warning'>⚠️ 订单号不符合条件（长度<=10且不匹配模式）</p>";
                        echo "<p class='info'>条件1: 长度>10 → " . (strlen($orderNumber) > 10 ? '✅' : '❌') . "</p>";
                        echo "<p class='info'>条件2: 匹配A-Z0-9开头 → " . (preg_match('/^[A-Z0-9]+/', $orderNumber) ? '✅' : '❌') . "</p>";
                    }
                } else {
                    echo "<p class='warning'>⚠️ 列数不足（需要第1行>4列，第2行>11列）</p>";
                }
            } else {
                echo "<p class='warning'>⚠️ 数据行数不足（需要>=4行）</p>";
            }
            
            echo "<p class='info'><strong>最终检测结果: <span class='highlight'>{$formatType}</span></strong></p>";
            echo "</div>";
            echo "</div>";
            
            echo "<div class='test-section'>";
            echo "<h2>步骤3: 详细数据分析</h2>";
            
            echo "<div class='step'>";
            echo "<h4>📋 关键位置数据检查（按参考逻辑）</h4>";
            
            if ($formatType === 'order_form') {
                echo "<h5>🔍 头部信息提取：</h5>";
                echo "<table class='data-table'>";
                echo "<tr><th>字段</th><th>位置</th><th>值</th><th>状态</th></tr>";
                
                $headerChecks = [
                    ['订单号', '第1行第2列 [0][1]', $data[0][1] ?? '', ''],
                    ['订单日期', '第1行第5列 [0][4]', $data[0][4] ?? '', ''],
                    ['联系人', '第2行第12列 [1][11]', $data[1][11] ?? '', ''],
                    ['送货地址', '第3行第2列 [2][1]', $data[2][1] ?? '', ''],
                    ['联系电话', '第3行第12列 [2][11]', $data[2][11] ?? '', ''],
                    ['订单金额', '第4行第2列 [3][1]', $data[3][1] ?? '', ''],
                    ['实际金额', '第4行第6列 [3][5]', $data[3][5] ?? '', ''],
                    ['预期交货日期', '第4行第12列 [3][11]', $data[3][11] ?? '', '']
                ];
                
                foreach ($headerChecks as $check) {
                    $value = trim($check[2]);
                    $status = empty($value) ? '❌ 空值' : '✅ 有值';
                    $displayValue = empty($value) ? '(空)' : htmlspecialchars($value);
                    echo "<tr><td>{$check[0]}</td><td>{$check[1]}</td><td>{$displayValue}</td><td>{$status}</td></tr>";
                }
                echo "</table>";
                
                echo "<h5>📦 明细数据检查（从第7行开始）：</h5>";
                $itemCount = 0;
                $validItems = 0;
                
                echo "<table class='data-table'>";
                echo "<tr><th>行号</th><th>商品编码[0]</th><th>商品名称[1]</th><th>单价[7]</th><th>数量[8]</th><th>小计[9]</th><th>实收[11]</th><th>备注[16]</th><th>状态</th></tr>";
                
                for ($i = 6; $i < min(count($data), 15); $i++) {
                    $row = $data[$i];
                    $itemCount++;
                    
                    $code = trim($row[0] ?? '');
                    $name = trim($row[1] ?? '');
                    $price = trim($row[7] ?? '');
                    $quantity = trim($row[8] ?? '');
                    $total = trim($row[9] ?? '');
                    $received = trim($row[11] ?? '');
                    $notes = trim($row[16] ?? '');
                    
                    $status = '❌ 空行';
                    if (!empty($code)) {
                        if (is_numeric($quantity) && floatval($quantity) > 0 && is_numeric($price) && floatval($price) >= 0) {
                            $status = '✅ 有效';
                            $validItems++;
                        } else {
                            $status = '⚠️ 数据问题';
                        }
                    }
                    
                    echo "<tr>";
                    echo "<td>" . ($i + 1) . "</td>";
                    echo "<td>" . (empty($code) ? '(空)' : htmlspecialchars($code)) . "</td>";
                    echo "<td>" . (empty($name) ? '(空)' : htmlspecialchars($name)) . "</td>";
                    echo "<td>" . (empty($price) ? '(空)' : htmlspecialchars($price)) . "</td>";
                    echo "<td>" . (empty($quantity) ? '(空)' : htmlspecialchars($quantity)) . "</td>";
                    echo "<td>" . (empty($total) ? '(空)' : htmlspecialchars($total)) . "</td>";
                    echo "<td>" . (empty($received) ? '(空)' : htmlspecialchars($received)) . "</td>";
                    echo "<td>" . (empty($notes) ? '(空)' : htmlspecialchars($notes)) . "</td>";
                    echo "<td>{$status}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                echo "<p class='info'>检查的明细行数: {$itemCount}</p>";
                echo "<p class='info'>有效明细数量: <span class='highlight'>{$validItems}</span></p>";
                
                if ($validItems === 0) {
                    echo "<p class='error'>🚨 这就是导入0条记录的原因！没有有效的明细数据</p>";
                }
                
            } else {
                echo "<p class='warning'>⚠️ 检测为简单列表格式，但您要求使用订货单格式</p>";
                echo "<p class='info'>请检查您的Excel文件是否符合订货单格式要求</p>";
            }
            echo "</div>";
            echo "</div>";
            
            echo "<div class='test-section'>";
            echo "<h2>步骤4: 数据库依赖检查</h2>";
            
            echo "<div class='step'>";
            echo "<h4>🗄️ 检查必要的基础数据</h4>";
            
            // 检查供应商
            $suppliers = $db->fetchAll("SELECT id, name FROM suppliers LIMIT 5");
            echo "<p class='info'>供应商数量: " . count($suppliers) . "</p>";
            if (empty($suppliers)) {
                echo "<p class='error'>❌ 供应商表为空，这可能导致导入失败</p>";
            } else {
                echo "<p class='success'>✅ 供应商数据存在</p>";
                echo "<div class='code-block'>";
                foreach ($suppliers as $supplier) {
                    echo "ID: {$supplier['id']}, 名称: {$supplier['name']}\n";
                }
                echo "</div>";
            }
            
            // 检查食材
            $ingredients = $db->fetchAll("SELECT id, name, code FROM ingredients LIMIT 5");
            echo "<p class='info'>食材数量: " . count($ingredients) . "</p>";
            if (empty($ingredients)) {
                echo "<p class='warning'>⚠️ 食材表为空，将自动创建新食材</p>";
            } else {
                echo "<p class='success'>✅ 食材数据存在</p>";
                echo "<div class='code-block'>";
                foreach ($ingredients as $ingredient) {
                    echo "ID: {$ingredient['id']}, 编码: {$ingredient['code']}, 名称: {$ingredient['name']}\n";
                }
                echo "</div>";
            }
            echo "</div>";
            echo "</div>";
            
            echo "<div class='test-section'>";
            echo "<h2>步骤5: 问题诊断和解决方案</h2>";
            
            echo "<div class='step'>";
            echo "<h4>🔍 可能的问题原因：</h4>";
            
            if ($formatType !== 'order_form') {
                echo "<p class='error'>❌ <strong>主要问题：格式检测失败</strong></p>";
                echo "<ul>";
                echo "<li>您的Excel文件没有被识别为订货单格式</li>";
                echo "<li>可能是订单号位置不正确或格式不符合要求</li>";
                echo "<li>可能是行列数不足</li>";
                echo "</ul>";
                
                echo "<h4>💡 解决方案：</h4>";
                echo "<ol>";
                echo "<li><strong>检查订单号</strong>：确保第1行第2列有订单号，且长度>10或以字母数字开头</li>";
                echo "<li><strong>检查列数</strong>：确保第1行有至少5列，第2行有至少12列</li>";
                echo "<li><strong>使用标准模板</strong>：<a href='../modules/purchase/download_template_based_on_test.php' class='btn'>下载标准模板</a></li>";
                echo "</ol>";
                
            } elseif (isset($validItems) && $validItems === 0) {
                echo "<p class='error'>❌ <strong>主要问题：明细数据无效</strong></p>";
                echo "<ul>";
                echo "<li>虽然格式检测通过，但明细数据都是空的或无效的</li>";
                echo "<li>商品编码（第1列）为空</li>";
                echo "<li>数量或单价不是有效数字</li>";
                echo "</ul>";
                
                echo "<h4>💡 解决方案：</h4>";
                echo "<ol>";
                echo "<li><strong>填写明细数据</strong>：从第7行开始填写商品明细</li>";
                echo "<li><strong>确保商品编码不为空</strong>：第1列必须有商品编码</li>";
                echo "<li><strong>确保数量和单价是数字</strong>：第8列（数量）和第9列（单价）必须是有效数字</li>";
                echo "</ol>";
                
            } else {
                echo "<p class='success'>✅ 格式检测和数据验证都通过了</p>";
                echo "<p class='warning'>⚠️ 可能是其他原因导致导入失败，建议检查：</p>";
                echo "<ul>";
                echo "<li>数据库连接是否正常</li>";
                echo "<li>是否有权限插入数据</li>";
                echo "<li>是否有其他验证逻辑阻止了导入</li>";
                echo "</ul>";
            }
            echo "</div>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 调试过程出错: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>
    
    <div class="test-section">
        <h2>步骤6: 快速修复</h2>
        <div class="step">
            <h4>🚀 立即行动：</h4>
            <ol>
                <li><strong>下载标准模板</strong>：
                    <a href="../modules/purchase/download_template_based_on_test.php" class="btn">下载订货单模板</a>
                </li>
                <li><strong>对比您的文件</strong>：将您的test.xlsx与下载的模板进行对比</li>
                <li><strong>修正格式</strong>：按照模板格式调整您的文件</li>
                <li><strong>重新测试</strong>：
                    <a href="../modules/purchase/index.php?action=import" class="btn">重新导入测试</a>
                </li>
            </ol>
            
            <h4>📞 如果还有问题：</h4>
            <p>请将上面的调试信息截图，特别是：</p>
            <ul>
                <li>格式检测结果</li>
                <li>关键位置数据检查结果</li>
                <li>明细数据检查结果</li>
            </ul>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
