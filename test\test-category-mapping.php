<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试分类映射</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        .data-table th { background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>🔍 测试分类映射功能</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../modules/purchase/PurchaseController.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>📊 当前分类统计</h2>";
        
        // 检查ingredient_categories表是否存在
        $tables = $db->fetchAll("SHOW TABLES LIKE 'ingredient_categories'");
        if (empty($tables)) {
            echo "<p class='warning'>⚠️ ingredient_categories表不存在，需要先创建</p>";
            
            // 创建分类表
            $createTableSql = "
            CREATE TABLE ingredient_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL COMMENT '分类名称',
                parent_id INT DEFAULT 0 COMMENT '父分类ID',
                description TEXT COMMENT '描述',
                status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='食材分类表'";
            
            $db->query($createTableSql);
            echo "<p class='success'>✅ ingredient_categories表创建成功</p>";
            
            // 插入默认分类
            $defaultCategories = [
                ['name' => '默认分类', 'parent_id' => 0, 'description' => '默认分类'],
                ['name' => '蔬菜类', 'parent_id' => 0, 'description' => '蔬菜类食材'],
                ['name' => '肉类', 'parent_id' => 0, 'description' => '肉类食材'],
                ['name' => '豆制品', 'parent_id' => 0, 'description' => '豆制品类食材'],
                ['name' => '调料', 'parent_id' => 0, 'description' => '调料类食材'],
            ];
            
            foreach ($defaultCategories as $category) {
                $db->insert('ingredient_categories', $category);
            }
            echo "<p class='success'>✅ 默认分类创建成功</p>";
        } else {
            echo "<p class='success'>✅ ingredient_categories表已存在</p>";
        }
        
        $categoryCount = $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories")['count'];
        echo "<p class='info'>当前分类总数: <strong>{$categoryCount}</strong></p>";
        
        // 显示现有分类
        $categories = $db->fetchAll("SELECT * FROM ingredient_categories ORDER BY id");
        
        if (!empty($categories)) {
            echo "<h4>现有分类：</h4>";
            echo "<table class='data-table'>";
            echo "<tr><th>ID</th><th>名称</th><th>父分类ID</th><th>描述</th><th>状态</th><th>创建时间</th></tr>";
            
            foreach ($categories as $category) {
                echo "<tr>";
                echo "<td>{$category['id']}</td>";
                echo "<td>{$category['name']}</td>";
                echo "<td>{$category['parent_id']}</td>";
                echo "<td>{$category['description']}</td>";
                echo "<td>" . ($category['status'] ? '启用' : '禁用') . "</td>";
                echo "<td>{$category['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🧪 测试分类查找和创建</h2>";
        
        // 测试分类名称
        $testCategories = [
            '蔬菜类',      // 应该找到现有的
            '新分类测试',   // 应该自动创建
            '',           // 空名称，应该返回默认分类
            '肉类',        // 应该找到现有的
        ];
        
        $controller = new PurchaseController();
        
        echo "<div class='debug-log'>";
        
        foreach ($testCategories as $index => $categoryName) {
            echo "=== 测试 " . ($index + 1) . " ===\n";
            echo "分类名称: '{$categoryName}'\n";
            
            try {
                // 使用反射调用私有方法
                $reflection = new ReflectionClass($controller);
                $method = $reflection->getMethod('findOrCreateCategoryByName');
                $method->setAccessible(true);
                
                $categoryId = $method->invoke($controller, $categoryName);
                
                echo "✅ 成功: 分类ID = {$categoryId}\n";
                
                // 验证返回的分类
                $foundCategory = $db->fetchOne("SELECT * FROM ingredient_categories WHERE id = ?", [$categoryId]);
                if ($foundCategory) {
                    echo "   分类信息: {$foundCategory['name']} (ID: {$foundCategory['id']})\n";
                }
                
            } catch (Exception $e) {
                echo "❌ 失败: " . $e->getMessage() . "\n";
            }
            
            echo "\n";
        }
        
        echo "</div>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>📈 测试后统计</h2>";
        
        $newCategoryCount = $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories")['count'];
        $addedCount = $newCategoryCount - $categoryCount;
        
        echo "<p class='info'>测试后分类总数: <strong>{$newCategoryCount}</strong></p>";
        echo "<p class='success'>新增分类数量: <strong>{$addedCount}</strong></p>";
        
        if ($addedCount > 0) {
            echo "<h4>新增的分类：</h4>";
            $newCategories = $db->fetchAll("SELECT * FROM ingredient_categories ORDER BY created_at DESC LIMIT ?", [$addedCount]);
            
            echo "<table class='data-table'>";
            echo "<tr><th>ID</th><th>名称</th><th>父分类ID</th><th>描述</th><th>创建时间</th></tr>";
            
            foreach ($newCategories as $category) {
                echo "<tr style='background: #e8f5e8;'>";
                echo "<td>{$category['id']}</td>";
                echo "<td>{$category['name']}</td>";
                echo "<td>{$category['parent_id']}</td>";
                echo "<td>{$category['description']}</td>";
                echo "<td>{$category['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        echo "</div>";
        
        // 显示导入日志
        $logFile = '../logs/import_debug.log';
        if (file_exists($logFile)) {
            echo "<div class='test-section'>";
            echo "<h2>📋 分类处理日志（最后15行）</h2>";
            
            $logContent = file_get_contents($logFile);
            $lines = explode("\n", $logContent);
            $lastLines = array_slice($lines, -15);
            
            echo "<div class='debug-log'>";
            foreach ($lastLines as $line) {
                if (!empty(trim($line)) && (strpos($line, '分类') !== false || strpos($line, '查找') !== false)) {
                    $class = '';
                    if (strpos($line, '✅') !== false) $class = 'success';
                    elseif (strpos($line, '❌') !== false) $class = 'error';
                    elseif (strpos($line, '⚠️') !== false) $class = 'warning';
                    
                    echo "<div class='{$class}'>" . htmlspecialchars($line) . "</div>";
                }
            }
            echo "</div>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 测试过程出错: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>
    
    <div class="test-section">
        <h2>🎯 下一步操作</h2>
        <div class="step">
            <p>
                <a href="../test/view-import-log.php?clear_log=1" class="btn">🗑️ 清空日志</a>
                <a href="../modules/purchase/index.php?action=import" class="btn">🔄 测试Excel导入</a>
                <a href="../test/view-import-log.php" class="btn">📋 查看导入日志</a>
                <a href="../modules/ingredients/index.php" class="btn">📊 查看食材管理</a>
            </p>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
