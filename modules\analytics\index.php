<?php
/**
 * 趋势分析模块
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // 引入控制器
    require_once 'AnalyticsController.php';

    // 创建控制器实例并处理请求
    $controller = new AnalyticsController();
    $controller->handleRequest();
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; color: #721c24; padding: 20px; margin: 20px; border-radius: 5px;">';
    echo '<h3>错误信息:</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    echo '</div>';
}
