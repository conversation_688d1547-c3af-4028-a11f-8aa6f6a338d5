<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试修复后的查询</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        .data-table th { background: #f5f5f5; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .query-box { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
        .fix-box { background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>✅ 测试修复后的查询</h1>
    
    <div class="test-section">
        <h2>🔧 问题修复</h2>
        
        <div class="fix-box">
            <h4>❌ 原始问题</h4>
            <p><strong>错误信息：</strong>SQLSTATE[42S22]: Column not found: 1054 Unknown column 'poi.purpose' in 'field list'</p>
            
            <h4>✅ 修复方案</h4>
            <p><strong>原查询：</strong><code>COALESCE(poi.purpose, poi.notes, '') as purpose</code></p>
            <p><strong>修复后：</strong><code>COALESCE(poi.notes, '') as purpose</code></p>
            
            <h4>🔍 修复原因</h4>
            <p>purchase_order_items表中没有'purpose'字段，只有'notes'字段用于存储用途信息</p>
        </div>
    </div>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>🧪 测试修复后的查询</h2>";
        
        echo "<h4>修复后的完整查询：</h4>";
        echo "<div class='query-box'>";
        echo "SELECT <br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.id,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.order_number,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.supplier_id,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;s.name as supplier_name,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.order_date,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.order_amount,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.ingredient_id,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;i.name as ingredient_name,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;i.unit,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.quantity,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.unit_price,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;<strong>COALESCE(poi.notes, '') as purpose</strong><br>";
        echo "FROM purchase_orders po<br>";
        echo "INNER JOIN purchase_order_items poi ON po.id = poi.order_id<br>";
        echo "LEFT JOIN suppliers s ON po.supplier_id = s.id<br>";
        echo "LEFT JOIN ingredients i ON poi.ingredient_id = i.id<br>";
        echo "WHERE po.status IN (1, 2)<br>";
        echo "AND poi.ingredient_id IS NOT NULL<br>";
        echo "AND i.id IS NOT NULL<br>";
        echo "ORDER BY po.order_date DESC, po.order_number ASC";
        echo "</div>";
        
        // 执行修复后的查询
        $purchaseOrders = $db->fetchAll("
            SELECT 
                po.id,
                po.order_number,
                po.supplier_id,
                s.name as supplier_name,
                po.order_date,
                po.order_amount,
                poi.ingredient_id,
                i.name as ingredient_name,
                i.unit,
                poi.quantity,
                poi.unit_price,
                COALESCE(poi.notes, '') as purpose
            FROM purchase_orders po
            INNER JOIN purchase_order_items poi ON po.id = poi.order_id
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN ingredients i ON poi.ingredient_id = i.id
            WHERE po.status IN (1, 2) 
            AND poi.ingredient_id IS NOT NULL
            AND i.id IS NOT NULL
            ORDER BY po.order_date DESC, po.order_number ASC
        ");
        
        echo "<p class='success'>✅ 查询执行成功！</p>";
        echo "<p class='info'>查询结果: <strong>" . count($purchaseOrders) . "</strong> 条记录</p>";
        
        if (!empty($purchaseOrders)) {
            echo "<h4>查询结果详情：</h4>";
            echo "<table class='data-table'>";
            echo "<tr><th>订单号</th><th>供应商</th><th>食材</th><th>单位</th><th>数量</th><th>单价</th><th>用途</th></tr>";
            foreach (array_slice($purchaseOrders, 0, 10) as $order) {
                echo "<tr>";
                echo "<td>{$order['order_number']}</td>";
                echo "<td>{$order['supplier_name']}</td>";
                echo "<td>{$order['ingredient_name']}</td>";
                echo "<td>{$order['unit']}</td>";
                echo "<td>{$order['quantity']}</td>";
                echo "<td>" . number_format($order['unit_price'], 2) . "</td>";
                echo "<td>" . ($order['purpose'] ?: '无') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // 按采购单分组
            $groupedOrders = [];
            foreach ($purchaseOrders as $order) {
                $key = $order['id'] . '_' . $order['order_number'];
                if (!isset($groupedOrders[$key])) {
                    $groupedOrders[$key] = [
                        'order_info' => $order,
                        'items' => []
                    ];
                }
                $groupedOrders[$key]['items'][] = $order;
            }
            
            echo "<h4>按采购单分组（共" . count($groupedOrders) . "个采购单）：</h4>";
            foreach ($groupedOrders as $key => $group) {
                $orderInfo = $group['order_info'];
                echo "<div style='background: #e6f3ff; padding: 15px; margin: 10px 0; border-radius: 8px;'>";
                echo "<h5><strong>{$orderInfo['order_number']}</strong> - {$orderInfo['supplier_name']}</h5>";
                echo "<p>订单日期: {$orderInfo['order_date']} | 商品数量: " . count($group['items']) . " 种</p>";
                echo "<ul>";
                foreach ($group['items'] as $item) {
                    $purpose = $item['purpose'] ? " ({$item['purpose']})" : '';
                    echo "<li>{$item['ingredient_name']} - {$item['quantity']}{$item['unit']} - ¥{$item['unit_price']}/{$item['unit']}{$purpose}</li>";
                }
                echo "</ul>";
                echo "</div>";
            }
            
        } else {
            echo "<p class='warning'>⚠️ 查询结果为空</p>";
            echo "<h4>可能的原因：</h4>";
            echo "<ul>";
            echo "<li>数据库中没有状态为1或2的采购单</li>";
            echo "<li>采购单没有关联的商品项目</li>";
            echo "<li>食材或供应商数据缺失</li>";
            echo "</ul>";
            
            // 分步检查
            echo "<h4>分步检查：</h4>";
            
            $step1 = $db->fetchAll("SELECT COUNT(*) as count FROM purchase_orders WHERE status IN (1, 2)");
            echo "<p><strong>步骤1</strong> - 状态为1或2的采购单: " . $step1[0]['count'] . " 个</p>";
            
            $step2 = $db->fetchAll("SELECT COUNT(*) as count FROM purchase_order_items");
            echo "<p><strong>步骤2</strong> - 采购单商品项目总数: " . $step2[0]['count'] . " 个</p>";
            
            $step3 = $db->fetchAll("SELECT COUNT(*) as count FROM ingredients");
            echo "<p><strong>步骤3</strong> - 食材总数: " . $step3[0]['count'] . " 个</p>";
            
            $step4 = $db->fetchAll("SELECT COUNT(*) as count FROM suppliers WHERE status = 1");
            echo "<p><strong>步骤4</strong> - 启用的供应商: " . $step4[0]['count'] . " 个</p>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-section'>";
        echo "<h2 class='error'>❌ 查询执行失败</h2>";
        echo "<p class='error'>错误信息: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
    
    <div class="test-section">
        <h2>🎯 下一步操作</h2>
        
        <h4>如果查询成功且有数据：</h4>
        <p><a href="../modules/inbound/index.php?action=create" class="btn">🚀 测试入库页面</a></p>
        <p>入库页面的采购单选择下拉框应该能正常显示采购单了</p>
        
        <h4>如果查询成功但没有数据：</h4>
        <ol>
            <li><a href="../modules/purchase/index.php?action=create" class="btn">创建新采购单</a></li>
            <li>确保采购单状态为1（待确认）或2（已确认）</li>
            <li>为采购单添加商品项目</li>
            <li>确保相关的供应商和食材数据存在</li>
        </ol>
        
        <h4>如果仍然有问题：</h4>
        <p><a href="fix-purchase-order-items.php" class="btn">运行修复脚本</a> - 自动为采购单添加商品项目</p>
    </div>
    
    <div class="test-section">
        <h2>📋 修复总结</h2>
        
        <h4>✅ 已修复的问题：</h4>
        <ul>
            <li><strong>字段名错误</strong>：将不存在的'purpose'字段改为'notes'字段</li>
            <li><strong>查询语法</strong>：使用COALESCE处理空值情况</li>
            <li><strong>数据兼容</strong>：确保查询与实际表结构匹配</li>
        </ul>
        
        <h4>🔧 技术细节：</h4>
        <ul>
            <li><strong>原查询</strong>：COALESCE(poi.purpose, poi.notes, '') as purpose</li>
            <li><strong>修复后</strong>：COALESCE(poi.notes, '') as purpose</li>
            <li><strong>效果</strong>：使用notes字段作为用途信息，如果为空则显示空字符串</li>
        </ul>
    </div>
</body>
</html>
