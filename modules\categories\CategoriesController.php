<?php
/**
 * 食材分类管理控制器
 */
require_once dirname(__DIR__, 2) . '/includes/BaseController.php';
require_once dirname(__DIR__, 2) . '/includes/helpers.php';

class CategoriesController extends BaseController
{
    protected function init()
    {
        $this->setTemplateData([
            'page_title' => '食材分类管理 - ' . $this->config['name'],
            'current_module' => 'categories'
        ]);
    }

    public function handleRequest()
    {
        switch ($this->request['action']) {
            case 'create':
                return $this->create();
            case 'edit':
                return $this->edit();
            case 'delete':
                return $this->delete();
            case 'import':
                return $this->import();
            case 'toggle_status':
                return $this->toggleStatus();
            case 'manage_subcategories':
                return $this->manageSubcategories();
            case 'index':
            default:
                return $this->index();
        }
    }

    /**
     * 分类列表页面
     */
    private function index()
    {
        try {
            // 获取搜索参数
            $search = $this->request['get']['search'] ?? '';
            $level = $this->request['get']['level'] ?? '1';  // 默认显示一级分类
            $parent_id = $this->request['get']['parent_id'] ?? '';

            // 构建查询条件 - 默认只显示一级分类和有效分类
            $where = ['c.status = 1'];  // 只显示有效的分类
            $params = [];

            if ($search) {
                $where[] = 'c.name LIKE ?';
                $params[] = '%' . $search . '%';
            }

            // 检查数据库是否已升级
            $hasLevelField = $this->checkLevelFieldExists();

            if ($level && $hasLevelField) {
                $where[] = 'c.level = ?';
                $params[] = intval($level);
            } elseif ($hasLevelField) {
                // 如果数据库已升级但没有指定级别，默认显示一级分类
                $where[] = 'c.level = 1';
            }

            if ($parent_id) {
                $where[] = 'c.parent_id = ?';
                $params[] = intval($parent_id);
            }

            $whereClause = 'WHERE ' . implode(' AND ', $where);

            // 检查数据库是否已升级
            $hasLevelField = $this->checkLevelFieldExists();
            $hasParentIdField = $this->checkParentIdFieldExists();

            if ($hasLevelField && $hasParentIdField) {
                // 数据库已升级，使用完整查询
                $categories = $this->db->fetchAll("
                    SELECT c.*,
                           p.name as parent_name,
                           p.code as parent_code,
                           COUNT(i.id) as ingredient_count,
                           COALESCE(SUM(CASE WHEN i.unit_price IS NOT NULL AND i.current_stock IS NOT NULL
                                             THEN i.current_stock * i.unit_price
                                             ELSE 0 END), 0) as total_value,
                           (SELECT COUNT(*) FROM ingredient_categories sc WHERE sc.parent_id = c.id AND sc.status = 1) as subcategory_count
                    FROM ingredient_categories c
                    LEFT JOIN ingredient_categories p ON c.parent_id = p.id
                    LEFT JOIN ingredients i ON c.id = i.category_id AND i.status = 1
                    $whereClause
                    GROUP BY c.id
                    ORDER BY
                        COALESCE(c.parent_id, c.id),
                        c.level,
                        c.sort_order ASC,
                        c.name ASC
                ", $params);
            } else {
                // 数据库未升级，使用简化查询
                $categories = $this->db->fetchAll("
                    SELECT c.*,
                           COUNT(i.id) as ingredient_count,
                           COALESCE(SUM(CASE WHEN i.unit_price IS NOT NULL AND i.current_stock IS NOT NULL
                                             THEN i.current_stock * i.unit_price
                                             ELSE 0 END), 0) as total_value
                    FROM ingredient_categories c
                    LEFT JOIN ingredients i ON c.id = i.category_id AND i.status = 1
                    $whereClause
                    GROUP BY c.id
                    ORDER BY c.sort_order ASC, c.name ASC
                ", $params);

                // 为兼容性添加默认字段
                foreach ($categories as &$category) {
                    $category['level'] = 1;
                    $category['parent_id'] = null;
                    $category['parent_name'] = null;
                    $category['parent_code'] = null;
                    $category['subcategory_count'] = 0;
                }
            }

            // 获取一级分类列表（用于筛选）
            if ($hasLevelField) {
                $parentCategories = $this->db->fetchAll("
                    SELECT id, name
                    FROM ingredient_categories
                    WHERE level = 1 AND status = 1
                    ORDER BY sort_order ASC, name ASC
                ");
            } else {
                // 数据库未升级，获取所有分类作为潜在的父分类
                $parentCategories = $this->db->fetchAll("
                    SELECT id, name
                    FROM ingredient_categories
                    WHERE status = 1
                    ORDER BY sort_order ASC, name ASC
                ");
            }

            $this->setTemplateData([
                'categories' => $categories,
                'parent_categories' => $parentCategories,
                'search' => $search,
                'level' => $level,
                'parent_id' => $parent_id,
                'total_count' => count($categories)
            ]);

        } catch (Exception $e) {
            // 使用模拟数据
            $this->setTemplateData([
                'categories' => $this->getMockCategories(),
                'parent_categories' => [],
                'search' => $search ?? '',
                'level' => $level,
                'parent_id' => $parent_id,
                'total_count' => 0
            ]);
        }

        $this->render('template.php');
    }

    /**
     * 创建分类
     */
    private function create()
    {
        if ($this->request['method'] === 'POST') {
            try {
                $parent_id = intval($this->request['post']['parent_id'] ?? 0);
                $level = $parent_id > 0 ? 2 : 1;

                // 检查数据库字段
                $hasLevelField = $this->checkLevelFieldExists();
                $hasParentIdField = $this->checkParentIdFieldExists();

                // 构建基础数据
                $data = [
                    'name' => trim($this->request['post']['name']),
                    'code' => $this->generateCategoryCode(trim($this->request['post']['name'])),
                    'description' => trim($this->request['post']['description'] ?? ''),
                    'sort_order' => intval($this->request['post']['sort_order'] ?? 0),
                    'status' => 1
                ];

                // 根据数据库状态添加字段
                if ($hasParentIdField) {
                    $data['parent_id'] = $parent_id > 0 ? $parent_id : null;
                }
                if ($hasLevelField) {
                    $data['level'] = $level;
                }

                $errors = $this->validateRequired($data, ['name']);
                if (!empty($errors)) {
                    throw new Exception(implode(', ', $errors));
                }

                // 如果是二级分类，验证父分类是否存在
                if ($level == 2) {
                    // 检查数据库是否有level字段
                    $hasLevelField = $this->checkLevelFieldExists();

                    if ($hasLevelField) {
                        $parentCategory = $this->db->fetchOne("SELECT id, level FROM ingredient_categories WHERE id = ? AND status = 1", [$parent_id]);
                        if (!$parentCategory) {
                            throw new Exception("选择的父分类不存在或已停用");
                        }
                        if ($parentCategory['level'] != 1) {
                            throw new Exception("只能在一级分类下创建二级分类");
                        }
                    } else {
                        // 数据库未升级，只检查父分类是否存在
                        $parentCategory = $this->db->fetchOne("SELECT id FROM ingredient_categories WHERE id = ?", [$parent_id]);
                        if (!$parentCategory) {
                            throw new Exception("选择的父分类不存在");
                        }
                    }
                }

                // 检查分类名称是否已存在（在同一父分类下）
                $nameCheckSql = "SELECT id FROM ingredient_categories WHERE name = ?";
                $nameCheckParams = [$data['name']];

                // 检查数据库是否有相关字段
                $hasLevelField = $this->checkLevelFieldExists();
                $hasParentIdField = $this->checkParentIdFieldExists();

                if ($level == 2 && $hasParentIdField) {
                    $nameCheckSql .= " AND parent_id = ?";
                    $nameCheckParams[] = $parent_id;
                } elseif ($level == 1 && $hasLevelField) {
                    $nameCheckSql .= " AND level = 1";
                }

                $existing = $this->db->fetchOne($nameCheckSql, $nameCheckParams);
                if ($existing) {
                    $scope = $level == 2 ? "在该父分类下" : "";
                    throw new Exception("分类名称「{$data['name']}」{$scope}已存在，请使用其他名称");
                }

                // 检查分类代码是否已存在
                $existingCode = $this->db->fetchOne("SELECT id FROM ingredient_categories WHERE code = ?", [$data['code']]);
                if ($existingCode) {
                    $data['code'] = $this->generateUniqueCode($data['name']);
                }

                $this->db->insert('ingredient_categories', $data);

                // 如果是从子分类管理页面跳转来的，返回到子分类管理页面
                $redirectUrl = 'index.php';
                if ($level == 2 && $parent_id > 0) {
                    $redirectUrl = "index.php?action=manage_subcategories&parent_id={$parent_id}";
                }

                $this->redirect($redirectUrl, '分类添加成功', 'success');

            } catch (Exception $e) {
                $this->setTemplateData('error_message', $e->getMessage());
            }
        }

        // 获取一级分类列表用于选择父分类
        try {
            $hasLevelField = $this->checkLevelFieldExists();
            if ($hasLevelField) {
                $parentCategories = $this->db->fetchAll("
                    SELECT id, name
                    FROM ingredient_categories
                    WHERE level = 1 AND status = 1
                    ORDER BY sort_order ASC, name ASC
                ");
            } else {
                // 数据库未升级，获取所有分类
                $parentCategories = $this->db->fetchAll("
                    SELECT id, name
                    FROM ingredient_categories
                    WHERE status = 1
                    ORDER BY sort_order ASC, name ASC
                ");
            }
        } catch (Exception $e) {
            $parentCategories = [];
        }

        // 获取默认父分类ID（从URL参数）
        $defaultParentId = intval($_GET['parent_id'] ?? 0);
        $defaultParentCategory = null;

        // 如果指定了父分类ID，获取父分类信息
        if ($defaultParentId > 0) {
            try {
                $defaultParentCategory = $this->db->fetchOne("SELECT * FROM ingredient_categories WHERE id = ? AND status = 1", [$defaultParentId]);
            } catch (Exception $e) {
                // 如果获取失败，忽略默认父分类
                $defaultParentId = 0;
            }
        }

        $this->setTemplateData([
            'parent_categories' => $parentCategories,
            'default_parent_id' => $defaultParentId,
            'default_parent_category' => $defaultParentCategory
        ]);

        $this->render('create-template.php');
    }

    /**
     * 编辑分类
     */
    private function edit()
    {
        $id = intval($this->request['get']['id'] ?? 0);
        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        // 获取当前分类信息
        try {
            $category = $this->db->fetchOne("SELECT * FROM ingredient_categories WHERE id = ?", [$id]);
            if (!$category) {
                $this->redirect('index.php', '分类不存在', 'error');
                return;
            }
        } catch (Exception $e) {
            $this->redirect('index.php', '获取分类信息失败', 'error');
            return;
        }

        if ($this->request['method'] === 'POST') {
            try {
                $parent_id = intval($this->request['post']['parent_id'] ?? 0);
                $level = $parent_id > 0 ? 2 : 1;

                // 检查数据库字段
                $hasLevelField = $this->checkLevelFieldExists();
                $hasParentIdField = $this->checkParentIdFieldExists();

                // 构建基础数据
                $data = [
                    'name' => trim($this->request['post']['name']),
                    'description' => trim($this->request['post']['description'] ?? ''),
                    'sort_order' => intval($this->request['post']['sort_order'] ?? 0)
                ];

                // 根据数据库状态添加字段
                if ($hasParentIdField) {
                    $data['parent_id'] = $parent_id > 0 ? $parent_id : null;
                }
                if ($hasLevelField) {
                    $data['level'] = $level;
                }

                // 如果是二级分类，验证父分类
                if ($level == 2) {
                    if ($hasLevelField) {
                        $parentCategory = $this->db->fetchOne("SELECT id, level FROM ingredient_categories WHERE id = ? AND status = 1", [$parent_id]);
                        if (!$parentCategory) {
                            throw new Exception("选择的父分类不存在或已停用");
                        }
                        if ($parentCategory['level'] != 1) {
                            throw new Exception("只能选择一级分类作为父分类");
                        }
                    } else {
                        // 数据库未升级，只检查父分类是否存在
                        $parentCategory = $this->db->fetchOne("SELECT id FROM ingredient_categories WHERE id = ?", [$parent_id]);
                        if (!$parentCategory) {
                            throw new Exception("选择的父分类不存在");
                        }
                    }
                }

                // 检查是否有子分类（如果要改为二级分类）
                if ($category['level'] == 1 && $level == 2) {
                    $hasChildren = $this->db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories WHERE parent_id = ?", [$id]);
                    if ($hasChildren['count'] > 0) {
                        throw new Exception("该分类下有子分类，不能改为二级分类");
                    }
                }

                // 检查分类名称是否已被其他分类使用（在同一父分类下）
                $nameCheckSql = "SELECT id FROM ingredient_categories WHERE name = ? AND id != ?";
                $nameCheckParams = [$data['name'], $id];

                // 检查数据库是否有相关字段
                $hasLevelField = $this->checkLevelFieldExists();
                $hasParentIdField = $this->checkParentIdFieldExists();

                if ($level == 2 && $hasParentIdField) {
                    $nameCheckSql .= " AND parent_id = ?";
                    $nameCheckParams[] = $parent_id;
                } elseif ($level == 1 && $hasLevelField) {
                    $nameCheckSql .= " AND level = 1";
                }

                $existing = $this->db->fetchOne($nameCheckSql, $nameCheckParams);
                if ($existing) {
                    $scope = $level == 2 ? "在该父分类下" : "";
                    throw new Exception("分类名称「{$data['name']}」{$scope}已被其他分类使用，请使用其他名称");
                }

                $this->db->update('ingredient_categories', $data, 'id = ?', [$id]);
                $this->redirect('index.php', '分类更新成功', 'success');

            } catch (Exception $e) {
                $this->setTemplateData('error_message', $e->getMessage());
            }
        }

        // 获取一级分类列表用于选择父分类（排除自己）
        try {
            $hasLevelField = $this->checkLevelFieldExists();
            if ($hasLevelField) {
                $parentCategories = $this->db->fetchAll("
                    SELECT id, name
                    FROM ingredient_categories
                    WHERE level = 1 AND status = 1 AND id != ?
                    ORDER BY sort_order ASC, name ASC
                ", [$id]);
            } else {
                // 数据库未升级，获取除自己外的所有分类
                $parentCategories = $this->db->fetchAll("
                    SELECT id, name
                    FROM ingredient_categories
                    WHERE status = 1 AND id != ?
                    ORDER BY sort_order ASC, name ASC
                ", [$id]);
            }
        } catch (Exception $e) {
            $parentCategories = [];
        }

        $this->setTemplateData([
            'category' => $category,
            'parent_categories' => $parentCategories
        ]);
        $this->render('edit-template.php');
    }

    /**
     * 导入分类
     */
    private function import()
    {
        if ($this->request['method'] === 'POST') {
            // 设置JSON响应头
            header('Content-Type: application/json');

            try {
                // 检查是否有上传文件
                if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
                    throw new Exception('请选择要导入的文件');
                }

                $file = $_FILES['import_file'];
                $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

                if (!in_array($fileExtension, ['csv', 'xlsx', 'xls'])) {
                    throw new Exception('只支持 CSV、Excel 格式的文件');
                }

                // 处理 CSV 文件
                if ($fileExtension === 'csv') {
                    $result = $this->importFromCSV($file['tmp_name']);
                } else {
                    // 处理 Excel 文件，传递原始文件名用于扩展名检测
                    $result = $this->importFromExcel($file['tmp_name'], $file['name']);
                }

                // 返回成功的JSON响应
                echo json_encode([
                    'success' => true,
                    'total' => $result['total'],
                    'imported' => $result['success'],
                    'errors' => $result['errors']
                ]);
                exit;

            } catch (Exception $e) {
                // 返回错误的JSON响应
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
                exit;
            }
        }

        // GET请求渲染模板
        $this->render('import-template.php');
    }

    /**
     * 从 CSV 文件导入数据
     */
    private function importFromCSV($filePath)
    {
        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        if (($handle = fopen($filePath, "r")) !== FALSE) {
            // 跳过标题行
            $header = fgetcsv($handle, 1000, ",");

            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                try {
                    $this->importCategoryRow($data);
                    $successCount++;
                } catch (Exception $e) {
                    $errorCount++;
                    $errors[] = "第" . ($successCount + $errorCount + 1) . "行: " . $e->getMessage();
                }
            }
            fclose($handle);
        }

        return [
            'total' => $successCount + $errorCount,
            'success' => $successCount,
            'error' => $errorCount,
            'errors' => $errors
        ];
    }

    /**
     * 从 Excel 文件导入数据
     */
    private function importFromExcel($filePath, $originalFileName = null)
    {
        require_once '../../includes/ExcelReader.php';

        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        try {
            $reader = new ExcelReader();
            $data = $reader->read($filePath, $originalFileName);

            if (empty($data)) {
                throw new Exception('Excel文件为空或无法读取');
            }

            // 跳过标题行
            $rows = array_slice($data, 1);

            foreach ($rows as $index => $row) {
                try {
                    // 过滤空行
                    if (empty(array_filter($row))) {
                        continue;
                    }

                    $this->importCategoryRow($row);
                    $successCount++;
                } catch (Exception $e) {
                    $errorCount++;
                    $errors[] = "第" . ($index + 2) . "行: " . $e->getMessage();
                }
            }

        } catch (Exception $e) {
            throw new Exception('Excel文件解析失败: ' . $e->getMessage());
        }

        return [
            'total' => $successCount + $errorCount,
            'success' => $successCount,
            'error' => $errorCount,
            'errors' => $errors
        ];
    }

    /**
     * 解析Excel文件
     */
    private function parseExcelFile($filePath, $originalFileName = null)
    {
        // 检查文件是否存在
        if (!file_exists($filePath)) {
            throw new Exception('文件不存在');
        }

        // 使用原始文件名获取扩展名，如果没有则从文件路径获取
        $fileName = $originalFileName ?: $filePath;
        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        if ($fileExtension === 'xlsx') {
            return $this->parseXlsxFile($filePath);
        } elseif ($fileExtension === 'xls') {
            return $this->parseXlsFile($filePath);
        } else {
            throw new Exception('不支持的Excel文件格式');
        }
    }

    /**
     * 解析XLSX文件（简化版本）
     */
    private function parseXlsxFile($filePath)
    {

        try {
            // 创建临时目录
            $tempDir = sys_get_temp_dir() . '/xlsx_' . uniqid();

            // 解压XLSX文件
            $zip = new ZipArchive();
            if ($zip->open($filePath) !== TRUE) {
                throw new Exception('无法打开XLSX文件');
            }

            $zip->extractTo($tempDir);
            $zip->close();

            // 读取工作表数据
            $sheetFile = $tempDir . '/xl/worksheets/sheet1.xml';
            $stringsFile = $tempDir . '/xl/sharedStrings.xml';

            if (!file_exists($sheetFile)) {
                throw new Exception('XLSX文件格式错误：找不到工作表');
            }

            // 读取共享字符串
            $strings = [];
            if (file_exists($stringsFile)) {
                $stringsXml = simplexml_load_file($stringsFile);
                if ($stringsXml) {
                    foreach ($stringsXml->si as $si) {
                        $strings[] = (string)$si->t;
                    }
                }
            }

            // 读取工作表数据
            $sheetXml = simplexml_load_file($sheetFile);
            if (!$sheetXml) {
                throw new Exception('无法解析工作表数据');
            }

            $rows = [];
            foreach ($sheetXml->sheetData->row as $row) {
                $rowData = [];
                $colIndex = 0;

                foreach ($row->c as $cell) {
                    $cellValue = '';

                    if (isset($cell->v)) {
                        $value = (string)$cell->v;

                        // 检查是否是共享字符串
                        if (isset($cell['t']) && (string)$cell['t'] === 's') {
                            $cellValue = isset($strings[$value]) ? $strings[$value] : '';
                        } else {
                            $cellValue = $value;
                        }
                    }

                    $rowData[$colIndex] = $cellValue;
                    $colIndex++;
                }

                $rows[] = $rowData;
            }

            // 清理临时文件
            $this->removeDirectory($tempDir);

            return $rows;

        } catch (Exception $e) {
            // 清理临时文件
            if (isset($tempDir) && is_dir($tempDir)) {
                $this->removeDirectory($tempDir);
            }
            throw new Exception('XLSX文件解析失败: ' . $e->getMessage());
        }
    }

    /**
     * 解析XLS文件（简化版本）
     */
    private function parseXlsFile($filePath)
    {
        // 对于XLS文件，建议用户转换为XLSX或CSV格式
        throw new Exception('暂不支持.xls格式，请将文件另存为.xlsx或.csv格式');
    }

    /**
     * 递归删除目录
     */
    private function removeDirectory($dir)
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }

    /**
     * 导入单行分类数据
     */
    private function importCategoryRow($data)
    {
        // CSV 格式：分类名称,父分类名称,分类代码,描述,排序
        if (count($data) < 1) {
            throw new Exception('数据格式不正确，缺少必要字段');
        }

        $name = trim($data[0]);
        $parentName = trim($data[1] ?? '');
        $code = trim($data[2] ?? '');
        $description = trim($data[3] ?? '');
        $sortOrder = intval($data[4] ?? 0);

        // 验证必填字段
        if (empty($name)) {
            throw new Exception('分类名称不能为空');
        }

        // 检查数据库字段
        $hasLevelField = $this->checkLevelFieldExists();
        $hasParentIdField = $this->checkParentIdFieldExists();

        // 处理父分类
        $parentId = null;
        $level = 1;

        if (!empty($parentName) && $hasParentIdField) {
            $parent = $this->db->fetchOne("SELECT id FROM ingredient_categories WHERE name = ? AND status = 1", [$parentName]);
            if ($parent) {
                $parentId = $parent['id'];
                $level = 2;
            } else {
                throw new Exception("找不到父分类：{$parentName}");
            }
        }

        // 生成分类代码
        if (empty($code)) {
            $code = $this->generateCategoryCode($name);
        }

        // 检查分类名称是否已存在
        $nameCheckSql = "SELECT id FROM ingredient_categories WHERE name = ?";
        $nameCheckParams = [$name];

        if ($level == 2 && $hasParentIdField) {
            $nameCheckSql .= " AND parent_id = ?";
            $nameCheckParams[] = $parentId;
        } elseif ($level == 1 && $hasLevelField) {
            $nameCheckSql .= " AND level = 1";
        }

        $existing = $this->db->fetchOne($nameCheckSql, $nameCheckParams);
        if ($existing) {
            $scope = $level == 2 ? "在父分类「{$parentName}」下" : "";
            throw new Exception("分类名称「{$name}」{$scope}已存在");
        }

        // 检查分类代码是否已存在
        $existingCode = $this->db->fetchOne("SELECT id FROM ingredient_categories WHERE code = ?", [$code]);
        if ($existingCode) {
            $code = $this->generateUniqueCode($name);
        }

        // 构建插入数据
        $insertData = [
            'name' => $name,
            'code' => $code,
            'description' => $description,
            'sort_order' => $sortOrder,
            'status' => 1
        ];

        // 根据数据库状态添加字段
        if ($hasParentIdField) {
            $insertData['parent_id'] = $parentId;
        }
        if ($hasLevelField) {
            $insertData['level'] = $level;
        }

        $this->db->insert('ingredient_categories', $insertData);
    }

    /**
     * 删除分类
     */
    private function delete()
    {
        $id = intval($this->request['get']['id'] ?? 0);
        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            // 获取分类信息
            $category = $this->db->fetchOne("SELECT * FROM ingredient_categories WHERE id = ?", [$id]);
            if (!$category) {
                $this->redirect('index.php', '分类不存在', 'error');
                return;
            }

            // 检查是否有子分类（仅在数据库已升级时检查）
            $hasLevelField = $this->checkLevelFieldExists();
            $hasParentIdField = $this->checkParentIdFieldExists();

            if ($hasLevelField && $hasParentIdField && ($category['level'] ?? 1) == 1) {
                $childCount = $this->db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories WHERE parent_id = ? AND status = 1", [$id]);
                if ($childCount['count'] > 0) {
                    $this->redirect('index.php', '该分类下还有子分类，请先删除子分类', 'error');
                    return;
                }
            }

            // 检查是否有食材使用此分类
            $count = $this->db->fetchOne("SELECT COUNT(*) as count FROM ingredients WHERE category_id = ? AND status = 1", [$id]);
            if ($count['count'] > 0) {
                $this->redirect('index.php', '该分类下还有食材，无法删除', 'error');
                return;
            }

            // 真正删除分类记录
            $this->db->delete('ingredient_categories', 'id = ?', [$id]);
            $this->redirect('index.php', '分类删除成功', 'success');
        } catch (Exception $e) {
            $this->redirect('index.php', '删除失败：' . $e->getMessage(), 'error');
        }
    }

    /**
     * 切换分类状态
     */
    private function toggleStatus()
    {
        // 设置JSON响应头
        header('Content-Type: application/json');

        if ($this->request['method'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => '请求方法错误']);
            exit;
        }

        if (!isset($this->request['post']['id']) || !isset($this->request['post']['status'])) {
            echo json_encode(['success' => false, 'message' => '缺少必要参数']);
            exit;
        }

        try {
            $id = intval($this->request['post']['id']);
            $status = intval($this->request['post']['status']);

            // 验证状态值
            if (!in_array($status, [0, 1])) {
                throw new Exception('无效的状态值');
            }

            // 检查分类是否存在
            $category = $this->db->fetchOne("SELECT * FROM ingredient_categories WHERE id = ?", [$id]);
            if (!$category) {
                throw new Exception('分类不存在');
            }

            // 更新状态
            $result = $this->db->update('ingredient_categories', ['status' => $status], 'id = ?', [$id]);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => $status == 1 ? '分类已开启' : '分类已停用'
                ]);
            } else {
                throw new Exception('状态更新失败');
            }
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }

    /**
     * 管理二级分类
     */
    private function manageSubcategories()
    {
        $parent_id = intval($_GET['parent_id'] ?? 0);
        if (!$parent_id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            // 检查数据库是否支持二级分类
            $hasParentIdField = $this->checkParentIdFieldExists();
            if (!$hasParentIdField) {
                $this->redirect('index.php', '数据库尚未升级支持二级分类功能，请联系管理员', 'error');
                return;
            }

            // 获取父分类信息
            $parentCategory = $this->db->fetchOne("SELECT * FROM ingredient_categories WHERE id = ?", [$parent_id]);
            if (!$parentCategory) {
                $this->redirect('index.php', '父分类不存在', 'error');
                return;
            }

            // 获取子分类列表
            $subcategories = $this->db->fetchAll("
                SELECT c.*,
                       COUNT(i.id) as ingredient_count,
                       COALESCE(SUM(CASE WHEN i.unit_price IS NOT NULL AND i.current_stock IS NOT NULL
                                         THEN i.current_stock * i.unit_price
                                         ELSE 0 END), 0) as total_value
                FROM ingredient_categories c
                LEFT JOIN ingredients i ON c.id = i.category_id AND i.status = 1
                WHERE c.parent_id = ?
                GROUP BY c.id
                ORDER BY c.sort_order ASC, c.name ASC
            ", [$parent_id]);

            $this->setTemplateData([
                'parent_category' => $parentCategory,
                'subcategories' => $subcategories,
                'parent_id' => $parent_id
            ]);
            $this->render('subcategories-template.php');

        } catch (Exception $e) {
            $this->redirect('index.php', '获取子分类失败：' . $e->getMessage(), 'error');
        }
    }

    /**
     * 获取模拟分类数据
     */
    private function getMockCategories()
    {
        return [
            // 一级分类
            [
                'id' => 1,
                'name' => '蔬菜类',
                'code' => 'VEG',
                'parent_id' => null,
                'level' => 1,
                'description' => '新鲜蔬菜及蔬菜制品',
                'sort_order' => 1,
                'status' => 1,
                'created_at' => '2024-01-15 10:30:00',
                'parent_name' => null,
                'parent_code' => null,
                'ingredient_count' => 8,
                'total_value' => 1250.50,
                'subcategory_count' => 3
            ],
            [
                'id' => 2,
                'name' => '肉类',
                'code' => 'MEAT',
                'parent_id' => null,
                'level' => 1,
                'description' => '猪肉、牛肉、鸡肉等各类肉制品',
                'sort_order' => 2,
                'status' => 1,
                'created_at' => '2024-01-15 10:35:00',
                'parent_name' => null,
                'parent_code' => null,
                'ingredient_count' => 5,
                'total_value' => 3200.80,
                'subcategory_count' => 3
            ],
            [
                'id' => 3,
                'name' => '水产类',
                'code' => 'SEAFOOD',
                'parent_id' => null,
                'level' => 1,
                'description' => '鱼类、虾类、蟹类等水产品',
                'sort_order' => 3,
                'status' => 1,
                'created_at' => '2024-01-15 10:40:00',
                'parent_name' => null,
                'parent_code' => null,
                'ingredient_count' => 3,
                'total_value' => 890.00,
                'subcategory_count' => 2
            ],
            // 二级分类示例
            [
                'id' => 11,
                'name' => '叶菜类',
                'code' => 'VEG_LEAF',
                'parent_id' => 1,
                'level' => 2,
                'description' => '包括白菜、菠菜、生菜等叶类蔬菜',
                'sort_order' => 1,
                'status' => 1,
                'created_at' => '2024-01-15 10:45:00',
                'parent_name' => '蔬菜类',
                'parent_code' => 'VEG',
                'ingredient_count' => 3,
                'total_value' => 450.20,
                'subcategory_count' => 0
            ],
            [
                'id' => 12,
                'name' => '根茎类',
                'code' => 'VEG_ROOT',
                'parent_id' => 1,
                'level' => 2,
                'description' => '包括萝卜、土豆、胡萝卜等根茎类蔬菜',
                'sort_order' => 2,
                'status' => 1,
                'created_at' => '2024-01-15 10:50:00',
                'parent_name' => '蔬菜类',
                'parent_code' => 'VEG',
                'ingredient_count' => 4,
                'total_value' => 320.60,
                'subcategory_count' => 0
            ]
        ];
    }

    /**
     * 生成分类代码
     */
    private function generateCategoryCode($name)
    {
        // 简单的拼音转换映射
        $pinyinMap = [
            '蔬菜' => 'shucai',
            '肉类' => 'roulei',
            '水产' => 'shuichan',
            '粮油' => 'liangyou',
            '调料' => 'tiaoliao',
            '豆制品' => 'douzhipin',
            '水果' => 'shuiguo',
            '饮料' => 'yinliao',
            '零食' => 'lingshi',
            '冷冻' => 'lengdong'
        ];

        // 查找匹配的拼音
        foreach ($pinyinMap as $chinese => $pinyin) {
            if (strpos($name, $chinese) !== false) {
                return $pinyin;
            }
        }

        // 如果没有匹配，生成简单代码
        $code = 'cat_' . date('His') . '_' . rand(100, 999);
        return $code;
    }

    /**
     * 生成唯一的分类代码
     */
    private function generateUniqueCode($name)
    {
        $baseCode = $this->generateCategoryCode($name);
        $code = $baseCode;
        $counter = 1;

        // 检查代码是否已存在，如果存在则添加数字后缀
        while (true) {
            $existing = $this->db->fetchOne("SELECT id FROM ingredient_categories WHERE code = ?", [$code]);
            if (!$existing) {
                break;
            }
            $code = $baseCode . '_' . $counter;
            $counter++;
        }

        return $code;
    }

    /**
     * 检查数据库是否有level字段
     */
    private function checkLevelFieldExists()
    {
        try {
            $this->db->fetchOne("SELECT level FROM ingredient_categories LIMIT 1");
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 检查数据库是否有parent_id字段
     */
    private function checkParentIdFieldExists()
    {
        try {
            $this->db->fetchOne("SELECT parent_id FROM ingredient_categories LIMIT 1");
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}
?>
