<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线模式 - 学校食堂食材出入库管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            padding: 20px;
        }
        
        .offline-container {
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        
        .offline-icon {
            font-size: 80px;
            margin-bottom: 30px;
            opacity: 0.8;
            animation: pulse 2s infinite;
        }
        
        .offline-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .offline-message {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.5);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }
        
        .network-status {
            margin-top: 30px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            font-size: 14px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-offline {
            background: #ff4757;
        }
        
        .status-online {
            background: #2ed573;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.1);
                opacity: 1;
            }
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .offline-container > * {
            animation: fadeIn 0.6s ease-out;
        }
        
        .offline-container > *:nth-child(2) { animation-delay: 0.1s; }
        .offline-container > *:nth-child(3) { animation-delay: 0.2s; }
        .offline-container > *:nth-child(4) { animation-delay: 0.3s; }
        .offline-container > *:nth-child(5) { animation-delay: 0.4s; }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            📡
        </div>
        
        <h1 class="offline-title">网络连接中断</h1>
        
        <p class="offline-message">
            抱歉，当前无法连接到服务器。请检查您的网络连接，然后重试。
        </p>
        
        <div class="offline-actions">
            <button class="btn btn-primary" onclick="retryConnection()">
                <span>🔄</span>
                重新连接
            </button>
            
            <a href="/mobile/index.php" class="btn btn-secondary">
                <span>🏠</span>
                返回首页
            </a>
        </div>
        
        <div class="network-status">
            <span class="status-indicator status-offline" id="statusIndicator"></span>
            <span id="statusText">网络连接已断开</span>
        </div>
    </div>

    <script>
        // 检查网络状态
        function updateNetworkStatus() {
            const indicator = document.getElementById('statusIndicator');
            const text = document.getElementById('statusText');
            
            if (navigator.onLine) {
                indicator.className = 'status-indicator status-online';
                text.textContent = '网络连接正常';
                
                // 网络恢复后自动跳转
                setTimeout(() => {
                    window.location.href = '/mobile/index.php';
                }, 1000);
            } else {
                indicator.className = 'status-indicator status-offline';
                text.textContent = '网络连接已断开';
            }
        }
        
        // 重试连接
        function retryConnection() {
            const btn = event.target.closest('.btn');
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<span>⏳</span> 连接中...';
            btn.disabled = true;
            
            // 尝试连接
            fetch('/mobile/index.php', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(response => {
                if (response.ok) {
                    btn.innerHTML = '<span>✅</span> 连接成功';
                    setTimeout(() => {
                        window.location.href = '/mobile/index.php';
                    }, 500);
                } else {
                    throw new Error('连接失败');
                }
            })
            .catch(error => {
                btn.innerHTML = '<span>❌</span> 连接失败';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 2000);
            });
        }
        
        // 监听网络状态变化
        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);
        
        // 初始化
        updateNetworkStatus();
        
        // 定期检查网络状态
        setInterval(updateNetworkStatus, 5000);
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'r' || e.key === 'R') {
                retryConnection();
            } else if (e.key === 'h' || e.key === 'H') {
                window.location.href = '/mobile/index.php';
            }
        });
    </script>
</body>
</html>
