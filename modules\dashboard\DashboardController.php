<?php
/**
 * 仪表板控制器
 */
require_once dirname(__DIR__, 2) . '/includes/BaseController.php';
require_once dirname(__DIR__, 2) . '/includes/helpers.php';

class DashboardController extends BaseController
{
    protected function init()
    {
        $this->setTemplateData([
            'page_title' => '仪表板 - ' . $this->config['name'],
            'current_module' => 'dashboard'
        ]);
    }

    public function handleRequest()
    {
        switch ($this->request['action']) {
            case 'index':
            default:
                return $this->index();
        }
    }

    /**
     * 仪表板首页
     */
    private function index()
    {
        // 使用与数据报表相同的数据结构
        $summary = [
            'total_purchase' => 425600.00,
            'total_orders' => 156,
            'total_suppliers' => 28,
            'avg_order_value' => 2730.77,
            'inventory_value' => 285600.00,
            'cost_savings' => 15200.00,
            'monthly_growth' => 8.5,
            'low_stock_items' => 8
        ];

        $charts = [
            'monthly_purchase' => [
                'labels' => ['1月', '2月', '3月', '4月', '5月', '6月'],
                'data' => [120000, 135000, 148000, 162000, 155000, 178000]
            ],
            'category_distribution' => [
                'labels' => ['蔬菜类', '肉类', '粮食类', '调料类', '水产类', '其他'],
                'data' => [35.8, 28.2, 18.5, 8.3, 6.2, 3.0]
            ],
            'supplier_ranking' => [
                'labels' => ['绿源农场', '新鲜配送', '优质食材', '农家直供', '品质保证'],
                'data' => [85000, 72000, 58000, 45000, 38000]
            ]
        ];

        $this->setTemplateData([
            'data' => [
                'summary' => $summary,
                'charts' => $charts
            ]
        ]);

        $this->render('template.php');
    }

    /**
     * 获取统计数据
     */
    private function getStatistics()
    {
        $stats = [
            'total_ingredients' => 0,
            'total_suppliers' => 0,
            'monthly_orders' => 0,
            'monthly_amount' => 0,
            'inventory_value' => 0,
            'low_stock_count' => 0,
            'pending_orders' => 0,
            'today_inbound' => 0
        ];

        try {
            // 食材总数
            $result = $this->db->fetchOne("SELECT COUNT(*) as count FROM ingredients WHERE status = 1");
            $stats['total_ingredients'] = $result['count'] ?? 0;
        } catch (Exception $e) {
            // 如果表不存在，使用模拟数据
            $stats['total_ingredients'] = 25;
        }

        try {
            // 供应商总数
            $result = $this->db->fetchOne("SELECT COUNT(*) as count FROM suppliers WHERE status = 1");
            $stats['total_suppliers'] = $result['count'] ?? 0;
        } catch (Exception $e) {
            $stats['total_suppliers'] = 8;
        }

        try {
            // 本月采购订单数
            $result = $this->db->fetchOne("
                SELECT COUNT(*) as count
                FROM purchase_orders
                WHERE YEAR(created_at) = YEAR(CURDATE())
                AND MONTH(created_at) = MONTH(CURDATE())
            ");
            $stats['monthly_orders'] = $result['count'] ?? 0;
        } catch (Exception $e) {
            $stats['monthly_orders'] = 12;
        }

        try {
            // 本月采购金额
            $result = $this->db->fetchOne("
                SELECT COALESCE(SUM(total_amount), 0) as amount
                FROM purchase_orders
                WHERE YEAR(created_at) = YEAR(CURDATE())
                AND MONTH(created_at) = MONTH(CURDATE())
                AND status != 5
            ");
            $stats['monthly_amount'] = $result['amount'] ?? 0;
        } catch (Exception $e) {
            $stats['monthly_amount'] = 15680.50;
        }

        try {
            // 库存总值
            $result = $this->db->fetchOne("
                SELECT COALESCE(SUM(current_stock * unit_price), 0) as value
                FROM ingredients
                WHERE status = 1
            ");
            $stats['inventory_value'] = $result['value'] ?? 0;
        } catch (Exception $e) {
            $stats['inventory_value'] = 45230.80;
        }

        try {
            // 低库存商品数量
            $result = $this->db->fetchOne("
                SELECT COUNT(*) as count
                FROM ingredients
                WHERE status = 1
                AND current_stock <= min_stock
            ");
            $stats['low_stock_count'] = $result['count'] ?? 0;
        } catch (Exception $e) {
            $stats['low_stock_count'] = 3;
        }

        try {
            // 待确认订单数
            $result = $this->db->fetchOne("
                SELECT COUNT(*) as count
                FROM purchase_orders
                WHERE status = 1
            ");
            $stats['pending_orders'] = $result['count'] ?? 0;
        } catch (Exception $e) {
            $stats['pending_orders'] = 5;
        }

        try {
            // 今日入库数量
            $result = $this->db->fetchOne("
                SELECT COUNT(*) as count
                FROM inbound_records
                WHERE DATE(created_at) = CURDATE()
            ");
            $stats['today_inbound'] = $result['count'] ?? 0;
        } catch (Exception $e) {
            $stats['today_inbound'] = 8;
        }

        return $stats;
    }

    /**
     * 获取最近活动
     */
    private function getRecentActivities()
    {
        $activities = [];

        try {
            // 最近的采购订单
            $orders = $this->db->fetchAll("
                SELECT po.order_number, po.total_amount, po.created_at, s.name as supplier_name
                FROM purchase_orders po
                LEFT JOIN suppliers s ON po.supplier_id = s.id
                ORDER BY po.created_at DESC
                LIMIT 5
            ");

            foreach ($orders as $order) {
                $activities[] = [
                    'type' => 'purchase',
                    'icon' => 'fas fa-shopping-cart',
                    'title' => '新建采购订单',
                    'description' => "订单号：{$order['order_number']}，供应商：{$order['supplier_name']}",
                    'amount' => $order['total_amount'],
                    'time' => $order['created_at']
                ];
            }

            // 最近的入库记录
            $inbounds = $this->db->fetchAll("
                SELECT ir.quantity, ir.created_at, i.name as ingredient_name
                FROM inbound_records ir
                LEFT JOIN ingredients i ON ir.ingredient_id = i.id
                ORDER BY ir.created_at DESC
                LIMIT 5
            ");

            foreach ($inbounds as $inbound) {
                $activities[] = [
                    'type' => 'inbound',
                    'icon' => 'fas fa-arrow-down',
                    'title' => '食材入库',
                    'description' => "食材：{$inbound['ingredient_name']}，数量：{$inbound['quantity']}",
                    'amount' => null,
                    'time' => $inbound['created_at']
                ];
            }

            // 按时间排序
            usort($activities, function($a, $b) {
                return strtotime($b['time']) - strtotime($a['time']);
            });

            $activities = array_slice($activities, 0, 10);

        } catch (Exception $e) {
            // 返回模拟数据
            $activities = [
                [
                    'type' => 'purchase',
                    'icon' => 'fas fa-shopping-cart',
                    'title' => '新建采购订单',
                    'description' => '订单号：PO20241201001，供应商：绿色蔬菜供应商',
                    'amount' => 1250.00,
                    'time' => date('Y-m-d H:i:s', strtotime('-2 hours'))
                ],
                [
                    'type' => 'inbound',
                    'icon' => 'fas fa-arrow-down',
                    'title' => '食材入库',
                    'description' => '食材：白菜，数量：50斤',
                    'amount' => null,
                    'time' => date('Y-m-d H:i:s', strtotime('-4 hours'))
                ],
                [
                    'type' => 'purchase',
                    'icon' => 'fas fa-shopping-cart',
                    'title' => '新建采购订单',
                    'description' => '订单号：PO20241130002，供应商：优质肉类供应商',
                    'amount' => 2800.00,
                    'time' => date('Y-m-d H:i:s', strtotime('-1 day'))
                ]
            ];
        }

        return $activities;
    }

    /**
     * 获取低库存商品
     */
    private function getLowStockItems()
    {
        try {
            return $this->db->fetchAll("
                SELECT i.name, i.current_stock, i.min_stock, i.unit, ic.name as category_name
                FROM ingredients i
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                WHERE i.status = 1
                AND i.current_stock <= i.min_stock
                ORDER BY (i.current_stock / i.min_stock) ASC
                LIMIT 10
            ");
        } catch (Exception $e) {
            // 返回模拟数据
            return [
                [
                    'name' => '白菜',
                    'current_stock' => 15.00,
                    'min_stock' => 20.00,
                    'unit' => '斤',
                    'category_name' => '蔬菜类'
                ],
                [
                    'name' => '鸡蛋',
                    'current_stock' => 8.00,
                    'min_stock' => 15.00,
                    'unit' => '斤',
                    'category_name' => '肉类'
                ],
                [
                    'name' => '食用油',
                    'current_stock' => 1.00,
                    'min_stock' => 3.00,
                    'unit' => '桶',
                    'category_name' => '粮油类'
                ]
            ];
        }
    }

    /**
     * 获取待处理订单
     */
    private function getPendingOrders()
    {
        try {
            return $this->db->fetchAll("
                SELECT po.id, po.order_number, po.total_amount, po.order_date,
                       po.expected_delivery_date, s.name as supplier_name
                FROM purchase_orders po
                LEFT JOIN suppliers s ON po.supplier_id = s.id
                WHERE po.status IN (1, 2, 3)
                ORDER BY po.order_date ASC
                LIMIT 10
            ");
        } catch (Exception $e) {
            // 返回模拟数据
            return [
                [
                    'id' => 1,
                    'order_number' => 'PO20241201001',
                    'total_amount' => 1250.00,
                    'order_date' => date('Y-m-d'),
                    'expected_delivery_date' => date('Y-m-d', strtotime('+2 days')),
                    'supplier_name' => '绿色蔬菜供应商'
                ],
                [
                    'id' => 2,
                    'order_number' => 'PO20241201002',
                    'total_amount' => 2800.00,
                    'order_date' => date('Y-m-d'),
                    'expected_delivery_date' => date('Y-m-d', strtotime('+3 days')),
                    'supplier_name' => '优质肉类供应商'
                ],
                [
                    'id' => 3,
                    'order_number' => 'PO20241130001',
                    'total_amount' => 890.00,
                    'order_date' => date('Y-m-d', strtotime('-1 day')),
                    'expected_delivery_date' => date('Y-m-d', strtotime('+1 day')),
                    'supplier_name' => '新鲜水产供应商'
                ]
            ];
        }
    }

    /**
     * 获取图表数据
     */
    public function getChartData()
    {
        // 检查是否为AJAX请求（简化版本）
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) ||
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
            $this->redirect('index.php');
            return;
        }

        try {
            $type = $this->request['get']['type'] ?? 'monthly_purchase';
            
            switch ($type) {
                case 'monthly_purchase':
                    $data = $this->getMonthlyPurchaseData();
                    break;
                case 'category_distribution':
                    $data = $this->getCategoryDistributionData();
                    break;
                case 'supplier_ranking':
                    $data = $this->getSupplierRankingData();
                    break;
                default:
                    throw new Exception('未知的图表类型');
            }
            
            $this->json(['success' => true, 'data' => $data]);
            
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }

    /**
     * 获取月度采购数据
     */
    private function getMonthlyPurchaseData()
    {
        $data = $this->db->fetchAll("
            SELECT
                DATE_FORMAT(created_at, '%Y-%m') as month,
                COUNT(*) as order_count,
                SUM(COALESCE(actual_amount, order_amount, 0)) as total_amount
            FROM purchase_orders
            WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
            AND status != 5
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month ASC
        ");

        return $data;
    }

    /**
     * 获取分类分布数据
     */
    private function getCategoryDistributionData()
    {
        $data = $this->db->fetchAll("
            SELECT
                ic.name as category_name,
                COUNT(i.id) as ingredient_count,
                SUM(COALESCE(i.purchase_price, 0)) as total_value
            FROM ingredient_categories ic
            LEFT JOIN ingredients i ON ic.id = i.category_id AND i.status = 1
            WHERE ic.status = 1
            GROUP BY ic.id, ic.name
            HAVING ingredient_count > 0
            ORDER BY total_value DESC
        ");

        return $data;
    }

    /**
     * 获取供应商排名数据
     */
    private function getSupplierRankingData()
    {
        $data = $this->db->fetchAll("
            SELECT
                s.name as supplier_name,
                COUNT(po.id) as order_count,
                SUM(COALESCE(po.actual_amount, po.order_amount, 0)) as total_amount
            FROM suppliers s
            LEFT JOIN purchase_orders po ON s.id = po.supplier_id
                AND po.created_at >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                AND po.status != 5
            WHERE s.status = 1
            GROUP BY s.id, s.name
            HAVING order_count > 0
            ORDER BY total_amount DESC
            LIMIT 10
        ");

        return $data;
    }
}
?>
