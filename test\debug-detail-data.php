<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试明细数据</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 11px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 4px; text-align: left; }
        .data-table th { background: #f5f5f5; }
        .highlight { background: #ffeb3b; padding: 2px 4px; font-weight: bold; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>🔍 调试明细数据处理</h1>
    
    <?php
    require_once '../includes/ExcelReader.php';
    
    $testFile = '../test.xlsx';
    
    if (!file_exists($testFile)) {
        echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
        exit;
    }
    
    $reader = new ExcelReader();
    $data = $reader->read($testFile, 'test.xlsx');
    
    if (empty($data)) {
        echo "<p class='error'>❌ Excel文件为空</p>";
        exit;
    }
    
    echo "<div class='test-section'>";
    echo "<h2>📊 修正后的头部信息提取测试</h2>";
    
    echo "<div class='step'>";
    echo "<h4>使用修正后的位置提取头部信息：</h4>";
    
    $headerInfo = [
        '订单号' => trim($data[1][1] ?? ''),        // 第2行第2列
        '下单日期' => trim($data[1][4] ?? ''),       // 第2行第5列
        '预交货日期' => trim($data[1][11] ?? ''),    // 第2行第13列
        '客户名称' => trim($data[2][0] ?? ''),       // 第3行第1列
        '联系人' => trim($data[2][11] ?? ''),        // 第3行第13列
        '收货地址' => trim($data[3][1] ?? ''),       // 第4行第1列
        '联系电话' => trim($data[3][11] ?? ''),      // 第4行第13列
        '下单金额' => trim($data[4][1] ?? ''),       // 第5行第1列
        '实际金额' => trim($data[4][4] ?? ''),       // 第5行第5列
    ];
    
    echo "<table class='data-table'>";
    echo "<tr><th>字段</th><th>位置</th><th>提取值</th><th>状态</th></tr>";
    
    $positions = [
        '订单号' => '[1][1]',
        '下单日期' => '[1][4]',
        '预交货日期' => '[1][12]',
        '客户名称' => '[2][0]',
        '联系人' => '[2][12]',
        '收货地址' => '[3][0]',
        '联系电话' => '[3][12]',
        '下单金额' => '[4][0]',
        '实际金额' => '[4][4]',
    ];
    
    foreach ($headerInfo as $field => $value) {
        $status = empty($value) ? '❌空值' : '✅有值';
        $displayValue = empty($value) ? '(空)' : htmlspecialchars($value);
        $position = $positions[$field];
        echo "<tr><td>{$field}</td><td>{$position}</td><td>{$displayValue}</td><td>{$status}</td></tr>";
    }
    echo "</table>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>📦 明细数据分析</h2>";
    
    echo "<div class='step'>";
    echo "<h4>查找明细数据开始行：</h4>";
    
    // 显示第6行到第20行的内容，查找明细数据开始位置
    echo "<table class='data-table'>";
    echo "<tr><th>行号</th>";
    for ($j = 0; $j < 15; $j++) {
        echo "<th>列{$j}</th>";
    }
    echo "</tr>";
    
    $detailStartRow = -1;
    
    for ($i = 5; $i < min(20, count($data)); $i++) {
        $row = $data[$i];
        $rowNum = $i + 1;
        
        echo "<tr>";
        echo "<td><strong>{$rowNum}</strong></td>";
        
        $hasContent = false;
        for ($j = 0; $j < 15; $j++) {
            $value = trim($row[$j] ?? '');
            if (!empty($value)) {
                $hasContent = true;
                echo "<td class='highlight'>" . htmlspecialchars($value) . "</td>";
            } else {
                echo "<td>(空)</td>";
            }
        }
        echo "</tr>";
        
        // 判断是否为明细开始行
        $firstCell = trim($row[0] ?? '');
        if ($hasContent && $detailStartRow === -1) {
            if (strpos($firstCell, '序号') !== false || 
                strpos($firstCell, '商品') !== false ||
                strpos($firstCell, '编码') !== false ||
                is_numeric($firstCell)) {
                $detailStartRow = $i;
                echo "<tr><td colspan='16' class='success'>👆 检测到明细数据开始行</td></tr>";
            }
        }
    }
    echo "</table>";
    
    if ($detailStartRow === -1) {
        echo "<p class='error'>❌ 未找到明细数据开始行</p>";
    } else {
        echo "<p class='success'>✅ 明细数据从第" . ($detailStartRow + 1) . "行开始</p>";
    }
    echo "</div>";
    
    if ($detailStartRow !== -1) {
        echo "<div class='step'>";
        echo "<h4>明细数据结构分析：</h4>";
        
        // 分析明细数据的列结构
        $headerRow = $data[$detailStartRow];
        echo "<p class='info'>明细标题行内容：</p>";
        echo "<div class='code-block'>";
        for ($j = 0; $j < min(20, count($headerRow)); $j++) {
            $value = trim($headerRow[$j] ?? '');
            if (!empty($value)) {
                echo "[{$j}] = '{$value}'\n";
            }
        }
        echo "</div>";
        
        echo "<h5>明细数据样例（前5行）：</h5>";
        echo "<table class='data-table'>";
        echo "<tr><th>行号</th><th>序号[0]</th><th>编码[1]</th><th>名称[2]</th><th>规格[3]</th><th>单位[4]</th><th>单价[8]</th><th>数量[9]</th><th>小计[10]</th><th>实收[12]</th><th>备注[17]</th></tr>";
        
        $validItems = 0;
        for ($i = $detailStartRow + 1; $i < min($detailStartRow + 6, count($data)); $i++) {
            $row = $data[$i];
            $rowNum = $i + 1;
            
            // 检查是否为空行
            $filteredRow = array_filter($row, function($cell) {
                return !empty(trim($cell));
            });
            
            if (empty($filteredRow)) {
                echo "<tr><td>{$rowNum}</td><td colspan='10' class='warning'>空行</td></tr>";
                continue;
            }
            
            $seqNo = trim($row[0] ?? '');
            $itemCode = trim($row[1] ?? '');
            $itemName = trim($row[2] ?? '');
            $specification = trim($row[3] ?? '');
            $unit = trim($row[4] ?? '');
            $unitPrice = trim($row[8] ?? '');
            $quantity = trim($row[9] ?? '');
            $totalPrice = trim($row[10] ?? '');
            $receivedQuantity = trim($row[12] ?? '');
            $notes = trim($row[17] ?? '');
            
            echo "<tr>";
            echo "<td>{$rowNum}</td>";
            echo "<td>" . htmlspecialchars($seqNo) . "</td>";
            echo "<td>" . htmlspecialchars($itemCode) . "</td>";
            echo "<td>" . htmlspecialchars($itemName) . "</td>";
            echo "<td>" . htmlspecialchars($specification) . "</td>";
            echo "<td>" . htmlspecialchars($unit) . "</td>";
            echo "<td>" . htmlspecialchars($unitPrice) . "</td>";
            echo "<td>" . htmlspecialchars($quantity) . "</td>";
            echo "<td>" . htmlspecialchars($totalPrice) . "</td>";
            echo "<td>" . htmlspecialchars($receivedQuantity) . "</td>";
            echo "<td>" . htmlspecialchars($notes) . "</td>";
            echo "</tr>";
            
            // 验证是否为有效明细
            if (!empty($itemCode) && is_numeric($quantity) && floatval($quantity) > 0) {
                $validItems++;
                echo "<tr><td></td><td colspan='10' class='success'>✅ 有效明细</td></tr>";
            } else {
                echo "<tr><td></td><td colspan='10' class='error'>❌ 无效明细（编码为空或数量无效）</td></tr>";
            }
        }
        echo "</table>";
        
        echo "<p class='info'>有效明细数量: <span class='highlight'>{$validItems}</span></p>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🔧 修正建议</h2>";
    
    echo "<div class='step'>";
    if ($detailStartRow !== -1) {
        echo "<h4>需要修正的代码：</h4>";
        echo "<div class='code-block'>";
        echo "// 修正明细数据起始行\n";
        echo "for (\$i = {$detailStartRow}; \$i < count(\$data); \$i++) {\n";
        echo "    \$row = \$data[\$i];\n";
        echo "    \n";
        echo "    // 跳过标题行\n";
        echo "    if (\$i === {$detailStartRow}) {\n";
        echo "        continue;\n";
        echo "    }\n";
        echo "    \n";
        echo "    // 提取明细字段\n";
        echo "    \$itemCode = trim(\$row[1] ?? '');           // 第2列：商品编码\n";
        echo "    \$itemName = trim(\$row[2] ?? '');           // 第3列：商品名称\n";
        echo "    \$unitPrice = floatval(\$row[8] ?? 0);       // 第9列：单价\n";
        echo "    \$quantity = floatval(\$row[9] ?? 0);        // 第10列：数量\n";
        echo "    \$totalPrice = floatval(\$row[10] ?? 0);     // 第11列：小计\n";
        echo "    \$receivedQuantity = floatval(\$row[12] ?? 0); // 第13列：实收数量\n";
        echo "    \$notes = trim(\$row[17] ?? '');             // 第18列：备注\n";
        echo "    \n";
        echo "    // 验证和处理...\n";
        echo "}\n";
        echo "</div>";
    } else {
        echo "<p class='error'>❌ 无法确定明细数据开始行，请检查Excel文件格式</p>";
    }
    echo "</div>";
    echo "</div>";
    ?>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
