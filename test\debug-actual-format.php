<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试实际订货单格式</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .mapping-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .mapping-table th, .mapping-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .mapping-table th { background: #f5f5f5; }
        .highlight { background: #ffeb3b; padding: 2px 4px; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔍 调试实际订货单格式</h1>
    
    <div class="test-section">
        <h2>📋 实际格式映射</h2>
        <div class="step">
            <h4>根据您提供的实际订货单格式：</h4>
            <table class="mapping-table">
                <tr>
                    <th>字段</th>
                    <th>实际位置</th>
                    <th>原来的位置</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>订单号</td>
                    <td class="highlight">第2行第1列 [1][0]</td>
                    <td>第1行第2列 [0][1]</td>
                    <td>DD250622131033389315</td>
                </tr>
                <tr>
                    <td>下单日期</td>
                    <td class="highlight">第2行第5列 [1][4]</td>
                    <td>第1行第5列 [0][4]</td>
                    <td>2025-06-22 13:10:32</td>
                </tr>
                <tr>
                    <td>预交货日期</td>
                    <td class="highlight">第2行第9列 [1][8]</td>
                    <td>第4行第12列 [3][11]</td>
                    <td>2025-06-23</td>
                </tr>
                <tr>
                    <td>联系人</td>
                    <td class="highlight">第3行第9列 [2][8]</td>
                    <td>第2行第12列 [1][11]</td>
                    <td>杨光芳</td>
                </tr>
                <tr>
                    <td>收货地址</td>
                    <td class="highlight">第4行第1列 [3][0]</td>
                    <td>第3行第2列 [2][1]</td>
                    <td>凯里市开怀街道金山大道88号</td>
                </tr>
                <tr>
                    <td>联系电话</td>
                    <td class="highlight">第4行第11列 [3][10]</td>
                    <td>第3行第12列 [2][11]</td>
                    <td>15885815856</td>
                </tr>
                <tr>
                    <td>下单金额</td>
                    <td class="highlight">第5行第1列 [4][0]</td>
                    <td>第4行第2列 [3][1]</td>
                    <td>11660.66</td>
                </tr>
                <tr>
                    <td>实际金额</td>
                    <td class="highlight">第5行第3列 [4][2]</td>
                    <td>第4行第6列 [3][5]</td>
                    <td>11660.66</td>
                </tr>
            </table>
        </div>
    </div>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../includes/ExcelReader.php';
    
    try {
        $db = Database::getInstance();
        $testFile = '../test.xlsx';
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 测试实际格式提取</h2>";
        
        if (!file_exists($testFile)) {
            echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
            exit;
        }
        
        echo "<div class='step'>";
        echo "<h4>📊 读取Excel文件</h4>";
        
        $reader = new ExcelReader();
        $data = $reader->read($testFile, 'test.xlsx');
        
        if (empty($data)) {
            echo "<p class='error'>❌ Excel文件为空</p>";
            exit;
        }
        
        echo "<p class='success'>✅ 文件读取成功，总行数: " . count($data) . "</p>";
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h4>🔍 格式检测测试</h4>";
        
        // 测试新的格式检测逻辑
        $firstRow = $data[0] ?? [];
        $secondRow = $data[1] ?? [];
        
        echo "<p class='info'>第1行内容: " . implode(' | ', array_slice($firstRow, 0, 10)) . "</p>";
        echo "<p class='info'>第2行内容: " . implode(' | ', array_slice($secondRow, 0, 10)) . "</p>";
        
        // 方法1：检查第1行是否包含"订货单"
        $firstRowText = implode('', $firstRow);
        $hasOrderTitle = strpos($firstRowText, '订货单') !== false;
        echo "<p class='info'>第1行包含'订货单': " . ($hasOrderTitle ? '✅是' : '❌否') . "</p>";
        
        // 方法2：检查第2行第1列是否有订单号
        $orderNumber = trim($secondRow[0] ?? '');
        echo "<p class='info'>第2行第1列内容: '<span class='highlight'>{$orderNumber}</span>'</p>";
        echo "<p class='info'>订单号长度: " . strlen($orderNumber) . "</p>";
        
        $lengthCheck = strlen($orderNumber) > 10;
        $patternCheck = preg_match('/^[A-Z0-9]+/', $orderNumber);
        echo "<p class='info'>长度检查(>10): " . ($lengthCheck ? '✅通过' : '❌失败') . "</p>";
        echo "<p class='info'>模式检查(A-Z0-9开头): " . ($patternCheck ? '✅通过' : '❌失败') . "</p>";
        
        $isOrderForm = $hasOrderTitle || (!empty($orderNumber) && ($lengthCheck || $patternCheck));
        echo "<p class='info'><strong>格式检测结果: " . ($isOrderForm ? '✅订货单格式' : '❌简单列表格式') . "</strong></p>";
        echo "</div>";
        
        if ($isOrderForm) {
            echo "<div class='step'>";
            echo "<h4>📋 头部信息提取测试</h4>";
            
            // 按照实际格式提取头部信息
            $headerInfo = [
                '订单号' => trim($data[1][0] ?? ''),        // 第2行第1列
                '下单日期' => trim($data[1][4] ?? ''),       // 第2行第5列
                '预交货日期' => trim($data[1][8] ?? ''),    // 第2行第9列
                '客户名称' => trim($data[2][0] ?? ''),       // 第3行第1列
                '联系人' => trim($data[2][8] ?? ''),        // 第3行第9列
                '收货地址' => trim($data[3][0] ?? ''),      // 第4行第1列
                '联系电话' => trim($data[3][10] ?? ''),     // 第4行第11列
                '下单金额' => trim($data[4][0] ?? ''),      // 第5行第1列
                '实际金额' => trim($data[4][2] ?? ''),      // 第5行第3列
                '付款状态' => trim($data[4][6] ?? ''),      // 第5行第7列
            ];
            
            echo "<table class='mapping-table'>";
            echo "<tr><th>字段</th><th>提取值</th><th>状态</th></tr>";
            
            foreach ($headerInfo as $field => $value) {
                $status = empty($value) ? '❌空值' : '✅有值';
                $displayValue = empty($value) ? '(空)' : htmlspecialchars($value);
                echo "<tr><td>{$field}</td><td>{$displayValue}</td><td>{$status}</td></tr>";
            }
            echo "</table>";
            
            // 检查必填字段
            $requiredFields = ['订单号', '下单日期', '收货地址'];
            $missingFields = [];
            foreach ($requiredFields as $field) {
                if (empty($headerInfo[$field])) {
                    $missingFields[] = $field;
                }
            }
            
            if (empty($missingFields)) {
                echo "<p class='success'>✅ 所有必填字段都有值</p>";
            } else {
                echo "<p class='error'>❌ 缺少必填字段: " . implode(', ', $missingFields) . "</p>";
            }
            echo "</div>";
            
            echo "<div class='step'>";
            echo "<h4>📦 明细数据检查</h4>";
            
            // 查找明细数据开始行
            $detailStartRow = -1;
            for ($i = 5; $i < count($data); $i++) {
                $row = $data[$i];
                $firstCell = trim($row[0] ?? '');
                
                // 查找包含"序号"或"商品"等关键词的标题行
                if (strpos($firstCell, '序号') !== false || 
                    strpos($firstCell, '商品') !== false || 
                    strpos($firstCell, '编码') !== false ||
                    !empty($firstCell)) {
                    $detailStartRow = $i;
                    break;
                }
            }
            
            if ($detailStartRow === -1) {
                echo "<p class='warning'>⚠️ 未找到明细数据开始行</p>";
            } else {
                echo "<p class='info'>明细数据从第" . ($detailStartRow + 1) . "行开始</p>";
                
                // 显示明细标题行
                $headerRow = $data[$detailStartRow];
                echo "<p class='info'>明细标题行: " . implode(' | ', array_slice($headerRow, 0, 10)) . "</p>";
                
                // 检查明细数据
                $validItems = 0;
                $totalItems = 0;
                
                for ($i = $detailStartRow + 1; $i < min(count($data), $detailStartRow + 11); $i++) {
                    $row = $data[$i];
                    $totalItems++;
                    
                    $filteredRow = array_filter($row, function($cell) {
                        return !empty(trim($cell));
                    });
                    
                    if (!empty($filteredRow)) {
                        $validItems++;
                        echo "<p class='info'>第" . ($i + 1) . "行: " . implode(' | ', array_slice($row, 0, 6)) . "</p>";
                    } else {
                        echo "<p class='warning'>第" . ($i + 1) . "行: 空行</p>";
                    }
                }
                
                echo "<p class='info'>检查的明细行数: {$totalItems}</p>";
                echo "<p class='info'>有效明细数量: <span class='highlight'>{$validItems}</span></p>";
                
                if ($validItems === 0) {
                    echo "<p class='error'>❌ 没有有效的明细数据</p>";
                } else {
                    echo "<p class='success'>✅ 找到 {$validItems} 条有效明细</p>";
                }
            }
            echo "</div>";
            
        } else {
            echo "<div class='step'>";
            echo "<p class='error'>❌ 文件未被识别为订货单格式</p>";
            echo "<p class='info'>可能的原因：</p>";
            echo "<ul>";
            echo "<li>第1行不包含'订货单'标题</li>";
            echo "<li>第2行第1列没有有效的订单号</li>";
            echo "</ul>";
            echo "</div>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 调试过程出错: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div class="test-section">
        <h2>🛠️ 下一步操作</h2>
        <div class="step">
            <p><strong>基于实际格式的调试结果：</strong></p>
            <ol>
                <li><strong>如果格式检测通过</strong>：
                    <a href="../modules/purchase/index.php?action=import" class="btn">测试导入功能</a>
                </li>
                <li><strong>如果还有问题</strong>：
                    <a href="real-time-import-debugger.php" class="btn">运行实时调试器</a>
                </li>
                <li><strong>创建标准模板</strong>：
                    <a href="../modules/purchase/download_template_based_on_test.php" class="btn">下载模板</a>
                </li>
            </ol>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
