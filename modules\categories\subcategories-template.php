<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理二级分类 - 学校食材管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../includes/styles.css?v=<?= time() ?>" rel="stylesheet">
    <link href="../../assets/css/common.css?v=<?= time() ?>" rel="stylesheet">
    <link href="style.css?v=<?= time() ?>" rel="stylesheet">
</head>
<body>

<?php include 'sidebar.php'; ?>

<!-- 主内容区 -->
<div class="main-content">
    <!-- 顶部栏 -->
    <div class="topbar">
        <div class="topbar-left">
            <h1><i class="fas fa-sitemap"></i> 管理二级分类</h1>
            <p class="page-subtitle">
                父分类：<?= htmlspecialchars($parent_category['name']) ?>
            </p>
        </div>
        <div class="topbar-right">
            <div class="btn-group">
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> 返回分类列表
                </a>
                <a href="index.php?action=create&parent_id=<?= $parent_id ?>" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> 添加二级分类
                </a>
            </div>
        </div>
    </div>

    <div class="content">
            <!-- 子分类管理区域 -->
            <div class="subcategories-management">
                <!-- 统计信息 -->
                <div class="stats-row">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?= count($subcategories) ?></div>
                            <div class="stat-label">二级分类</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?= count(array_filter($subcategories, function($cat) { return ($cat['status'] ?? 1) == 1; })) ?></div>
                            <div class="stat-label">已启用</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?= count(array_filter($subcategories, function($cat) { return ($cat['status'] ?? 1) == 0; })) ?></div>
                            <div class="stat-label">已停用</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?= array_sum(array_column($subcategories, 'ingredient_count')) ?></div>
                            <div class="stat-label">总食材数</div>
                        </div>
                    </div>
                </div>

            <!-- 子分类列表 -->
            <?php if (!empty($subcategories)): ?>
                <div class="categories-list">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 60px;">序号</th>
                                    <th>分类名称</th>
                                    <th>描述</th>
                                    <th style="width: 100px;">食材数量</th>
                                    <th style="width: 120px;">总价值</th>
                                    <th style="width: 120px;">新增时间</th>
                                    <th style="width: 80px;">状态</th>
                                    <th style="width: 180px;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($subcategories as $index => $subcategory): ?>
                                <?php $status = $subcategory['status'] ?? 1; ?>
                                <tr class="category-row">
                                    <!-- 序号 -->
                                    <td class="text-center">
                                        <span class="row-number"><?= $index + 1 ?></span>
                                    </td>
                                    
                                    <!-- 分类名称 -->
                                    <td>
                                        <div class="category-name" style="font-weight: 600; color: #2c3e50;">
                                            <?= htmlspecialchars($subcategory['name']) ?>
                                        </div>
                                    </td>
                                    
                                    <!-- 描述 -->
                                    <td class="align-middle">
                                        <div class="category-description" style="color: #6c757d; font-size: 14px; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="<?= htmlspecialchars($subcategory['description'] ?? '暂无描述') ?>">
                                            <?= htmlspecialchars($subcategory['description'] ?? '暂无描述') ?>
                                        </div>
                                    </td>
                                    
                                    <!-- 食材数量 -->
                                    <td class="text-center">
                                        <div class="ingredient-count-badge">
                                            <img src="../../assets/images/ingredient-icon.png" alt="食材" class="badge-icon" onerror="this.style.display='none'">
                                            <span class="badge-text"><?= intval($subcategory['ingredient_count']) ?></span>
                                        </div>
                                    </td>
                                    
                                    <!-- 总价值 -->
                                    <td class="text-end">
                                        <span class="text-primary fw-bold"><?= formatCurrency($subcategory['total_value'] ?? 0) ?></span>
                                    </td>
                                    
                                    <!-- 新增时间 -->
                                    <td>
                                        <small class="text-muted"><?= date('Y-m-d H:i', strtotime($subcategory['created_at'] ?? 'now')) ?></small>
                                    </td>
                                    
                                    <!-- 状态 -->
                                    <td class="text-center">
                                        <?php if ($status == 1): ?>
                                            <div class="status-badge status-active" onclick="toggleStatus(<?= $subcategory['id'] ?>, 0)" title="点击停用">
                                                <img src="../../assets/images/status-active.png" alt="开启" class="status-icon" onerror="this.style.display='none'">
                                                <span class="status-text">开启</span>
                                            </div>
                                        <?php else: ?>
                                            <div class="status-badge status-inactive" onclick="toggleStatus(<?= $subcategory['id'] ?>, 1)" title="点击开启">
                                                <img src="../../assets/images/status-inactive.png" alt="停用" class="status-icon" onerror="this.style.display='none'">
                                                <span class="status-text">停用</span>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    
                                    <!-- 操作 -->
                                    <td>
                                        <div class="action-buttons" role="group">
                                            <a href="../ingredients/index.php?category=<?= $subcategory['id'] ?>" class="btn btn-sm btn-info" title="查看食材">
                                                <i class="fas fa-eye"></i>
                                                <span>查看</span>
                                            </a>
                                            <a href="index.php?action=edit&id=<?= $subcategory['id'] ?>" class="btn btn-sm btn-warning" title="编辑分类">
                                                <i class="fas fa-edit"></i>
                                                <span>编辑</span>
                                            </a>
                                            <a href="?action=delete&id=<?= $subcategory['id'] ?>" class="btn btn-sm btn-danger" title="删除分类" onclick="return confirm('确定要删除这个分类吗？')">
                                                <i class="fas fa-trash"></i>
                                                <span>删除</span>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php else: ?>
                <div class="categories-list">
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <h3 class="empty-title">暂无二级分类</h3>
                        <p class="empty-description">该一级分类下还没有二级分类，点击下方按钮添加第一个二级分类</p>
                        <div class="empty-actions">
                            <a href="index.php?action=create&parent_id=<?= $parent_id ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加第一个二级分类
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            </div> <!-- 关闭 subcategories-management -->
        </div>
    </div>
</div>

<script src="../../assets/js/common.js"></script>
<script src="main.js"></script>
    <script>
    // 状态切换功能
    function toggleStatus(categoryId, newStatus) {
        if (!confirm(newStatus == 1 ? '确定要开启这个分类吗？' : '确定要停用这个分类吗？')) {
            return;
        }
        
        fetch('index.php?action=toggle_status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `id=${categoryId}&status=${newStatus}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('状态更新失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('状态更新失败:', error);
            alert('状态更新失败，请稍后重试');
        });
    }
    </script>
</body>
</html>
