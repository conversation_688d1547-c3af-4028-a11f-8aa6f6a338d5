<?php
$db = new PDO('mysql:host=************;dbname=sc;charset=utf8mb4', 'sc', 'pw5K4SsM7kZsjdxy');
$stmt = $db->query('DESCRIBE inbound_records');
echo "inbound_records 表结构:\n";
$columns = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $columns[] = $row['Field'];
    echo $row['Field'] . ' - ' . $row['Type'] . ' - ' . $row['Null'] . ' - ' . $row['Default'] . "\n";
}

echo "\n字段检查:\n";
echo 'order_id exists: ' . (in_array('order_id', $columns) ? 'YES' : 'NO') . "\n";
echo 'delivery_photo exists: ' . (in_array('delivery_photo', $columns) ? 'YES' : 'NO') . "\n";
echo 'weight_photo exists: ' . (in_array('weight_photo', $columns) ? 'YES' : 'NO') . "\n";
echo 'inbound_date exists: ' . (in_array('inbound_date', $columns) ? 'YES' : 'NO') . "\n";
echo 'operator_name exists: ' . (in_array('operator_name', $columns) ? 'YES' : 'NO') . "\n";
?>