<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端入库测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .test-button { padding: 10px 20px; margin: 10px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #005a87; }
        #result { margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 5px; white-space: pre-wrap; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>移动端入库功能测试</h1>
    
    <div class="test-section">
        <h2>测试1: 使用真实采购单入库</h2>
        <p>采购单号: DD250622131033389315</p>
        <button class="test-button" onclick="testRealPurchaseOrder()">测试真实采购单入库</button>
    </div>
    
    <div class="test-section">
        <h2>测试2: 创建新采购单入库</h2>
        <p>自动创建采购单并入库</p>
        <button class="test-button" onclick="testNewPurchaseOrder()">测试新采购单入库</button>
    </div>
    
    <div class="test-section">
        <h2>测试3: 检查服务器状态</h2>
        <button class="test-button" onclick="checkServerStatus()">检查服务器状态</button>
    </div>
    
    <div id="result"></div>

    <script>
    function showResult(content, isError = false) {
        const resultDiv = document.getElementById('result');
        resultDiv.textContent = content;
        resultDiv.className = isError ? 'error' : 'success';
    }

    function testRealPurchaseOrder() {
        showResult('正在测试真实采购单入库...');
        
        const testData = new FormData();
        testData.append('batch_type', 'batch');
        testData.append('supplier_id', '2');
        testData.append('batch_number', 'TEST_REAL_' + Date.now());
        testData.append('inbound_date', new Date().toISOString().split('T')[0]);
        testData.append('operator_name', '测试操作员');
        testData.append('notes', '真实采购单测试');
        testData.append('order_id', '16');
        testData.append('is_new_order', '0');
        
        // 添加测试商品
        testData.append('items[0][ingredient_id]', '141');
        testData.append('items[0][actual_quantity]', '35.5');
        testData.append('items[0][unit_price]', '1.11');
        testData.append('items[0][order_quantity]', '40.00');
        
        testData.append('items[1][ingredient_id]', '142');
        testData.append('items[1][actual_quantity]', '38.2');
        testData.append('items[1][unit_price]', '1.07');
        testData.append('items[1][order_quantity]', '40.00');

        fetch('/mobile/inbound.php', {
            method: 'POST',
            body: testData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json().catch(() => response.text());
        })
        .then(data => {
            console.log('服务器响应:', data);
            if (typeof data === 'object') {
                if (data.success) {
                    showResult(`测试成功！\n消息: ${data.message}\n记录数: ${data.data?.record_count}\n总金额: ¥${data.data?.total_amount}`);
                } else {
                    showResult(`测试失败！\n错误: ${data.message}`, true);
                }
            } else {
                if (data.includes('成功') || data.includes('success')) {
                    showResult('测试成功（HTML响应）！\n' + data.substring(0, 200) + '...');
                } else {
                    showResult('测试失败（未知响应格式）！\n' + data.substring(0, 500), true);
                }
            }
        })
        .catch(error => {
            console.error('请求错误:', error);
            showResult(`网络错误: ${error.message}`, true);
        });
    }

    function testNewPurchaseOrder() {
        showResult('正在测试新采购单入库...');
        
        const testData = new FormData();
        testData.append('batch_type', 'batch');
        testData.append('supplier_id', '1');
        testData.append('batch_number', 'TEST_NEW_' + Date.now());
        testData.append('inbound_date', new Date().toISOString().split('T')[0]);
        testData.append('operator_name', '测试操作员');
        testData.append('notes', '新采购单测试');
        testData.append('order_id', '');
        testData.append('is_new_order', '1');
        
        // 构造新采购单数据
        const newOrderData = {
            order_info: {
                id: 'new',
                order_number: 'PO' + Date.now(),
                supplier_id: '1',
                supplier_name: '测试供应商',
                order_date: new Date().toISOString().split('T')[0],
                order_amount: 75.0,
                notes: '移动端测试采购单'
            },
            items: [
                {
                    id: 0,
                    ingredient_id: '1',
                    ingredient_name: '白菜',
                    unit: '斤',
                    quantity: 20.0,
                    unit_price: 2.5,
                    purpose: '测试'
                },
                {
                    id: 1,
                    ingredient_id: '2',
                    ingredient_name: '萝卜',
                    unit: '斤',
                    quantity: 10.0,
                    unit_price: 2.5,
                    purpose: '测试'
                }
            ]
        };
        testData.append('new_order_data', JSON.stringify(newOrderData));
        
        // 添加商品实际数量
        testData.append('items[0][ingredient_id]', '1');
        testData.append('items[0][actual_quantity]', '19.5');
        testData.append('items[0][unit_price]', '2.5');
        testData.append('items[0][order_quantity]', '20.0');
        
        testData.append('items[1][ingredient_id]', '2');
        testData.append('items[1][actual_quantity]', '9.8');
        testData.append('items[1][unit_price]', '2.5');
        testData.append('items[1][order_quantity]', '10.0');

        fetch('/mobile/inbound.php', {
            method: 'POST',
            body: testData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json().catch(() => response.text());
        })
        .then(data => {
            console.log('服务器响应:', data);
            if (typeof data === 'object') {
                if (data.success) {
                    showResult(`测试成功！\n消息: ${data.message}\n记录数: ${data.data?.record_count}\n总金额: ¥${data.data?.total_amount}`);
                } else {
                    showResult(`测试失败！\n错误: ${data.message}`, true);
                }
            } else {
                if (data.includes('成功') || data.includes('success')) {
                    showResult('测试成功（HTML响应）！\n' + data.substring(0, 200) + '...');
                } else {
                    showResult('测试失败（未知响应格式）！\n' + data.substring(0, 500), true);
                }
            }
        })
        .catch(error => {
            console.error('请求错误:', error);
            showResult(`网络错误: ${error.message}`, true);
        });
    }

    function checkServerStatus() {
        showResult('正在检查服务器状态...');
        
        fetch('/mobile/inbound.php', {
            method: 'GET'
        })
        .then(response => {
            if (response.ok) {
                showResult(`服务器状态正常\nHTTP状态: ${response.status}\n响应时间: ${Date.now()} ms`);
            } else {
                showResult(`服务器响应异常\nHTTP状态: ${response.status}`, true);
            }
        })
        .catch(error => {
            showResult(`服务器连接失败: ${error.message}`, true);
        });
    }
    </script>
</body>
</html>