<?php
/**
 * PHP环境检查脚本
 * 检查PHP版本、扩展和配置
 */

echo "<h2>PHP环境检查</h2>";

// 检查PHP版本
echo "<h3>PHP版本信息</h3>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>PHP SAPI: " . php_sapi_name() . "</p>";

// 检查必需的扩展
echo "<h3>必需扩展检查</h3>";
$required_extensions = [
    'pdo',
    'pdo_mysql',
    'mysqli',
    'json',
    'mbstring',
    'openssl',
    'curl',
    'gd'
];

foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✅' : '❌';
    $color = extension_loaded($ext) ? 'green' : 'red';
    echo "<p style='color: $color;'>$status $ext</p>";
}

// 检查重要配置
echo "<h3>重要配置检查</h3>";
$configs = [
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit'),
    'display_errors' => ini_get('display_errors') ? 'On' : 'Off',
    'error_reporting' => ini_get('error_reporting')
];

foreach ($configs as $key => $value) {
    echo "<p><strong>$key:</strong> $value</p>";
}

// 检查数据库连接
echo "<h3>数据库连接测试</h3>";
try {
    require_once '../includes/Database.php';
    $db = Database::getInstance();
    echo "<p style='color: green;'>✅ 数据库连接成功</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../index.php'>返回首页</a></p>";
?>
