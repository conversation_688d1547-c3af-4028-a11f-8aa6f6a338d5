<?php
/**
 * 基于实际订货单格式的模板
 */

// 设置响应头
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="实际订货单格式模板.xlsx"');
header('Cache-Control: max-age=0');

/**
 * 创建基于实际格式的订货单模板
 */
function createActualFormatTemplate()
{
    // 基于您提供的实际订货单格式创建模板
    $templateData = [];
    
    // 第1行：标题行
    $row1 = array_fill(0, 20, '');
    $row1[0] = '订货单';
    $templateData[] = $row1;
    
    // 第2行：订单号、下单日期、预交货日期
    $row2 = array_fill(0, 20, '');
    $row2[0] = 'DD' . date('YmdHis'); // 订单号
    $row2[1] = '';
    $row2[2] = '';
    $row2[3] = '下单日期';
    $row2[4] = date('Y-m-d H:i:s'); // 下单日期
    $row2[5] = '';
    $row2[6] = '';
    $row2[7] = '预交货日期';
    $row2[8] = date('Y-m-d', strtotime('+1 day')); // 预交货日期
    $templateData[] = $row2;
    
    // 第3行：客户名称、集团、联系人
    $row3 = array_fill(0, 20, '');
    $row3[0] = '学校食堂'; // 客户名称
    $row3[1] = '';
    $row3[2] = '';
    $row3[3] = '集团';
    $row3[4] = '教育集团'; // 集团
    $row3[5] = '';
    $row3[6] = '';
    $row3[7] = '联系人';
    $row3[8] = '张三'; // 联系人
    $templateData[] = $row3;
    
    // 第4行：收货地址、所属线路、联系电话
    $row4 = array_fill(0, 20, '');
    $row4[0] = '学校食堂一楼'; // 收货地址
    $row4[1] = '';
    $row4[2] = '';
    $row4[3] = '所属线路';
    $row4[4] = 'XL001'; // 所属线路
    $row4[5] = '';
    $row4[6] = '';
    $row4[7] = '刘锦明';
    $row4[8] = '联系电话';
    $row4[9] = '无';
    $row4[10] = '13800138000'; // 联系电话
    $templateData[] = $row4;
    
    // 第5行：下单金额、实际金额、付款状态
    $row5 = array_fill(0, 20, '');
    $row5[0] = '1500.00'; // 下单金额
    $row5[1] = '实际金额';
    $row5[2] = '1500.00'; // 实际金额
    $row5[3] = '';
    $row5[4] = '';
    $row5[5] = '付款状态';
    $row5[6] = '未付款'; // 付款状态
    $row5[7] = '订单备注';
    $templateData[] = $row5;
    
    // 第6行：空行
    $templateData[] = array_fill(0, 20, '');
    
    // 第7行：明细标题行
    $headerRow = array_fill(0, 20, '');
    $headerRow[0] = '序号';
    $headerRow[1] = '商品编码';
    $headerRow[2] = '商品名称';
    $headerRow[3] = '规格';
    $headerRow[4] = '单位';
    $headerRow[5] = '品牌';
    $headerRow[6] = '产地';
    $headerRow[7] = '保质期';
    $headerRow[8] = '单价';
    $headerRow[9] = '数量';
    $headerRow[10] = '小计';
    $headerRow[11] = '税率';
    $headerRow[12] = '实收数量';
    $headerRow[13] = '合格数量';
    $headerRow[14] = '不合格数量';
    $headerRow[15] = '损耗数量';
    $headerRow[16] = '拒收数量';
    $headerRow[17] = '备注';
    $templateData[] = $headerRow;
    
    // 第8行开始：示例明细数据
    $sampleItems = [
        ['1', 'VEG001', '白菜', '500g/包', '包', '绿源', '本地', '3天', '2.50', '20', '50.00', '0%', '20', '20', '0', '0', '0', '新鲜'],
        ['2', 'MEAT001', '猪肉', '1kg/块', 'kg', '优质', '本地', '2天', '25.00', '10', '250.00', '0%', '10', '10', '0', '0', '0', ''],
        ['3', 'FISH001', '鲫鱼', '500g/条', '条', '鲜活', '本地', '1天', '12.00', '15', '180.00', '0%', '15', '15', '0', '0', '0', '活鱼'],
        ['4', 'GRAIN001', '大米', '10kg/袋', '袋', '优质', '东北', '12个月', '45.00', '5', '225.00', '0%', '5', '5', '0', '0', '0', ''],
        ['5', 'OIL001', '食用油', '5L/桶', '桶', '金龙鱼', '本地', '18个月', '35.00', '3', '105.00', '0%', '3', '3', '0', '0', '0', ''],
        ['6', 'SEASON001', '生抽', '500ml/瓶', '瓶', '海天', '广东', '24个月', '8.50', '10', '85.00', '0%', '10', '10', '0', '0', '0', ''],
        ['7', 'VEG002', '土豆', '1kg/袋', 'kg', '新鲜', '本地', '7天', '3.00', '30', '90.00', '0%', '30', '30', '0', '0', '0', ''],
        ['8', 'MEAT002', '鸡蛋', '30个/盒', '盒', '土鸡蛋', '本地', '15天', '15.00', '8', '120.00', '0%', '8', '8', '0', '0', '0', ''],
        ['9', 'SEASON002', '老抽', '500ml/瓶', '瓶', '海天', '广东', '24个月', '9.50', '5', '47.50', '0%', '5', '5', '0', '0', '0', ''],
        ['10', 'VEG003', '萝卜', '1kg/袋', 'kg', '新鲜', '本地', '7天', '2.00', '25', '50.00', '0%', '25', '25', '0', '0', '0', '']
    ];
    
    foreach ($sampleItems as $item) {
        $itemRow = array_fill(0, 20, '');
        for ($i = 0; $i < count($item); $i++) {
            $itemRow[$i] = $item[$i];
        }
        $templateData[] = $itemRow;
    }
    
    // 填充剩余行到100行
    while (count($templateData) < 100) {
        $templateData[] = array_fill(0, 20, '');
    }
    
    return createXlsxFile($templateData, '订货单');
}

/**
 * 创建XLSX文件
 */
function createXlsxFile($data, $sheetName = 'Sheet1')
{
    $tempDir = sys_get_temp_dir() . '/xlsx_' . uniqid();
    mkdir($tempDir);
    mkdir($tempDir . '/xl');
    mkdir($tempDir . '/xl/worksheets');
    mkdir($tempDir . '/_rels');
    mkdir($tempDir . '/xl/_rels');

    // 创建基本的XLSX文件结构
    createXlsxStructure($tempDir, $sheetName);
    
    // 处理数据
    $strings = [];
    $stringMap = [];
    foreach ($data as $row) {
        foreach ($row as $cell) {
            if (is_string($cell) && !is_numeric($cell) && !empty($cell)) {
                if (!isset($stringMap[$cell])) {
                    $stringMap[$cell] = count($strings);
                    $strings[] = $cell;
                }
            }
        }
    }

    // 创建共享字符串文件
    createSharedStrings($tempDir, $strings);
    
    // 创建工作表
    createWorksheet($tempDir, $data, $stringMap);

    // 创建ZIP文件
    return createZipFile($tempDir);
}

/**
 * 创建XLSX基本结构
 */
function createXlsxStructure($tempDir, $sheetName)
{
    // [Content_Types].xml
    $contentTypes = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml"/>
    <Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>
    <Override PartName="/xl/sharedStrings.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml"/>
</Types>';
    file_put_contents($tempDir . '/[Content_Types].xml', $contentTypes);

    // _rels/.rels
    $rels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/>
</Relationships>';
    file_put_contents($tempDir . '/_rels/.rels', $rels);

    // xl/_rels/workbook.xml.rels
    $workbookRels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/>
    <Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings" Target="sharedStrings.xml"/>
</Relationships>';
    file_put_contents($tempDir . '/xl/_rels/workbook.xml.rels', $workbookRels);

    // xl/workbook.xml
    $workbook = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
    <sheets>
        <sheet name="' . htmlspecialchars($sheetName, ENT_XML1, 'UTF-8') . '" sheetId="1" r:id="rId1"/>
    </sheets>
</workbook>';
    file_put_contents($tempDir . '/xl/workbook.xml', $workbook);
}

/**
 * 创建共享字符串文件
 */
function createSharedStrings($tempDir, $strings)
{
    $sharedStrings = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="' . count($strings) . '" uniqueCount="' . count($strings) . '">';
    foreach ($strings as $string) {
        $sharedStrings .= '<si><t>' . htmlspecialchars($string, ENT_XML1, 'UTF-8') . '</t></si>';
    }
    $sharedStrings .= '</sst>';
    file_put_contents($tempDir . '/xl/sharedStrings.xml', $sharedStrings);
}

/**
 * 创建工作表
 */
function createWorksheet($tempDir, $data, $stringMap)
{
    $worksheet = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
    <sheetData>';

    foreach ($data as $rowIndex => $row) {
        $rowNum = $rowIndex + 1;
        $worksheet .= '<row r="' . $rowNum . '">';
        
        foreach ($row as $colIndex => $cell) {
            $colLetter = chr(65 + ($colIndex % 26));
            if ($colIndex >= 26) {
                $colLetter = chr(64 + intval($colIndex / 26)) . chr(65 + ($colIndex % 26));
            }
            $cellRef = $colLetter . $rowNum;
            
            if (is_string($cell) && !is_numeric($cell) && !empty($cell)) {
                $stringIndex = $stringMap[$cell];
                $worksheet .= '<c r="' . $cellRef . '" t="s"><v>' . $stringIndex . '</v></c>';
            } elseif (!empty($cell)) {
                $worksheet .= '<c r="' . $cellRef . '"><v>' . htmlspecialchars($cell, ENT_XML1, 'UTF-8') . '</v></c>';
            }
        }
        
        $worksheet .= '</row>';
    }

    $worksheet .= '</sheetData></worksheet>';
    file_put_contents($tempDir . '/xl/worksheets/sheet1.xml', $worksheet);
}

/**
 * 创建ZIP文件
 */
function createZipFile($tempDir)
{
    $zipFile = tempnam(sys_get_temp_dir(), 'xlsx') . '.xlsx';
    $zip = new ZipArchive();
    
    if ($zip->open($zipFile, ZipArchive::CREATE) !== TRUE) {
        throw new Exception('无法创建XLSX文件');
    }

    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($tempDir));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $relativePath = str_replace($tempDir . DIRECTORY_SEPARATOR, '', $file->getPathname());
            $relativePath = str_replace('\\', '/', $relativePath);
            $zip->addFile($file->getPathname(), $relativePath);
        }
    }

    $zip->close();

    // 清理临时目录
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($tempDir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    foreach ($iterator as $file) {
        if ($file->isDir()) {
            rmdir($file->getPathname());
        } else {
            unlink($file->getPathname());
        }
    }
    rmdir($tempDir);

    return $zipFile;
}

try {
    // 创建基于实际格式的模板
    $excelFile = createActualFormatTemplate();
    
    // 输出文件内容
    readfile($excelFile);
    
    // 清理临时文件
    unlink($excelFile);
    
} catch (Exception $e) {
    // 如果创建失败，返回错误信息
    header('Content-Type: text/plain; charset=utf-8');
    echo '实际格式模板生成失败: ' . $e->getMessage();
}
?>
