-- 学校食堂食材出入库管理系统数据库创建脚本 (MySQL 5.6兼容版)
-- 版本: v2.0 (MySQL 5.6 Compatible)
-- 创建日期: 2024-01-01
-- 适用于远程数据库: 82.156.63.73 数据库名: sc

-- 注意：此脚本假设已连接到目标数据库，不会创建新数据库

-- 设置SQL模式以兼容MySQL 5.6
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- 创建食材分类表
CREATE TABLE `ingredient_categories` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
    `code` VARCHAR(20) NOT NULL COMMENT '分类编码',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY `unique_name` (`name`),
    UNIQUE KEY `unique_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='食材分类表';

-- 创建供应商表
CREATE TABLE `suppliers` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL COMMENT '供应商名称',
    `contact_person` VARCHAR(50) NOT NULL COMMENT '联系人',
    `phone` VARCHAR(20) NOT NULL COMMENT '联系电话',
    `address` VARCHAR(255) NULL COMMENT '地址',
    `license_number` VARCHAR(50) NULL COMMENT '营业执照号',
    `rating` TINYINT DEFAULT 5 COMMENT '评级(1-5星)',
    `status` TINYINT DEFAULT 1 COMMENT '状态(1正常0黑名单)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='供应商表';

-- 创建食材信息表
CREATE TABLE `ingredients` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL COMMENT '食材名称',
    `category_id` INT NOT NULL COMMENT '分类ID',
    `unit` VARCHAR(20) NOT NULL COMMENT '计量单位',
    `shelf_life_days` INT NOT NULL COMMENT '保质期（天）',
    `min_stock` DECIMAL(10,2) DEFAULT 0 COMMENT '最低库存预警值',
    `image_path` VARCHAR(255) NULL COMMENT '食材图片路径',
    `description` TEXT NULL COMMENT '食材描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态(1启用0停用)',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `unique_name` (`name`),
    KEY `idx_category` (`category_id`),
    KEY `idx_status` (`status`),
    FOREIGN KEY (`category_id`) REFERENCES `ingredient_categories`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='食材信息表';

-- 创建入库记录表
CREATE TABLE `inbound_records` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `ingredient_id` BIGINT NOT NULL COMMENT '食材ID',
    `supplier_id` BIGINT NOT NULL COMMENT '供应商ID',
    `batch_number` VARCHAR(64) NOT NULL COMMENT '批次号',
    `quantity` DECIMAL(10,2) NOT NULL COMMENT '入库数量',
    `unit_price` DECIMAL(10,2) NOT NULL COMMENT '单价',
    `total_price` DECIMAL(10,2) NOT NULL COMMENT '总价',
    `production_date` DATE NULL COMMENT '生产日期',
    `expired_at` DATE NOT NULL COMMENT '过期日期',
    `weight_images` TEXT NULL COMMENT '称重图片路径(JSON格式)',
    `purchase_invoice` VARCHAR(255) NOT NULL COMMENT '采购单路径',
    `quality_check` TINYINT DEFAULT 1 COMMENT '质检状态(1合格0不合格)',
    `notes` TEXT NULL COMMENT '备注',
    `status` TINYINT DEFAULT 1 COMMENT '状态(1正常0作废)',
    `created_by` BIGINT NOT NULL COMMENT '操作人ID',
    `approved_by` BIGINT NULL COMMENT '审核人ID',
    `approved_at` TIMESTAMP NULL COMMENT '审核时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `unique_batch` (`batch_number`),
    KEY `idx_ingredient` (`ingredient_id`),
    KEY `idx_supplier` (`supplier_id`),
    KEY `idx_date` (`created_at`),
    KEY `idx_expired` (`expired_at`),
    FOREIGN KEY (`ingredient_id`) REFERENCES `ingredients`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='入库记录表';

-- 创建出库记录表
CREATE TABLE `outbound_records` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `ingredient_id` BIGINT NOT NULL COMMENT '食材ID',
    `batch_number` VARCHAR(64) NOT NULL COMMENT '批次号',
    `quantity` DECIMAL(10,2) NOT NULL COMMENT '出库数量',
    `unit_price` DECIMAL(10,2) NOT NULL COMMENT '单价',
    `total_price` DECIMAL(10,2) NOT NULL COMMENT '总价',
    `used_for` VARCHAR(100) NOT NULL COMMENT '用途',
    `meal_date` DATE NOT NULL COMMENT '用餐日期',
    `notes` TEXT NULL COMMENT '备注',
    `created_by` BIGINT NOT NULL COMMENT '操作人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    KEY `idx_ingredient` (`ingredient_id`),
    KEY `idx_batch` (`batch_number`),
    KEY `idx_date` (`created_at`),
    KEY `idx_meal_date` (`meal_date`),
    FOREIGN KEY (`ingredient_id`) REFERENCES `ingredients`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='出库记录表';

-- 创建库存表
CREATE TABLE `inventory` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `ingredient_id` BIGINT NOT NULL COMMENT '食材ID',
    `current_quantity` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '当前库存',
    `total_value` DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '库存总价值',
    `last_inbound_at` TIMESTAMP NULL COMMENT '最后入库时间',
    `last_outbound_at` TIMESTAMP NULL COMMENT '最后出库时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `unique_ingredient` (`ingredient_id`),
    FOREIGN KEY (`ingredient_id`) REFERENCES `ingredients`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='库存表';

-- 创建库存批次表
CREATE TABLE `inventory_batches` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `ingredient_id` BIGINT NOT NULL COMMENT '食材ID',
    `batch_number` VARCHAR(64) NOT NULL COMMENT '批次号',
    `remaining_quantity` DECIMAL(10,2) NOT NULL COMMENT '剩余数量',
    `unit_price` DECIMAL(10,2) NOT NULL COMMENT '单价',
    `expired_at` DATE NOT NULL COMMENT '过期日期',
    `status` TINYINT DEFAULT 1 COMMENT '状态(1正常2即将过期3已过期)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `unique_batch` (`batch_number`),
    KEY `idx_ingredient` (`ingredient_id`),
    KEY `idx_expired` (`expired_at`),
    KEY `idx_status` (`status`),
    FOREIGN KEY (`ingredient_id`) REFERENCES `ingredients`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='库存批次表';

-- 创建采购订货单表
CREATE TABLE `purchase_orders` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `order_number` VARCHAR(64) NOT NULL COMMENT '订货单号',
    `supplier_id` BIGINT NOT NULL COMMENT '供应商ID',
    `order_date` DATE NOT NULL COMMENT '订货日期',
    `expected_delivery_date` DATE NULL COMMENT '预期交货日期',
    `total_amount` DECIMAL(12,2) DEFAULT 0 COMMENT '订单总金额',
    `status` TINYINT DEFAULT 1 COMMENT '状态(1待确认2已确认3部分到货4已完成5已取消)',
    `notes` TEXT NULL COMMENT '备注',
    `created_by` BIGINT NOT NULL COMMENT '创建人ID',
    `approved_by` BIGINT NULL COMMENT '审批人ID',
    `approved_at` TIMESTAMP NULL COMMENT '审批时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `unique_order_number` (`order_number`),
    KEY `idx_supplier` (`supplier_id`),
    KEY `idx_order_date` (`order_date`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_purchase_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='采购订货单表';

-- 创建采购订货单明细表
CREATE TABLE `purchase_order_items` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `order_id` BIGINT NOT NULL COMMENT '订货单ID',
    `ingredient_id` BIGINT NOT NULL COMMENT '食材ID',
    `quantity` DECIMAL(10,2) NOT NULL COMMENT '订货数量',
    `unit_price` DECIMAL(10,2) NOT NULL COMMENT '单价',
    `total_price` DECIMAL(10,2) NOT NULL COMMENT '小计',
    `received_quantity` DECIMAL(10,2) DEFAULT 0 COMMENT '已收货数量',
    `notes` TEXT NULL COMMENT '备注',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    KEY `idx_order` (`order_id`),
    KEY `idx_ingredient` (`ingredient_id`),
    CONSTRAINT `fk_order_item_order` FOREIGN KEY (`order_id`) REFERENCES `purchase_orders` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_order_item_ingredient` FOREIGN KEY (`ingredient_id`) REFERENCES `ingredients` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='采购订货单明细表';

-- 创建用户表 (Laravel兼容，字段长度限制为191)
CREATE TABLE `users` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(191) NOT NULL COMMENT '用户名',
    `email` VARCHAR(191) NOT NULL COMMENT '邮箱',
    `email_verified_at` TIMESTAMP NULL COMMENT '邮箱验证时间',
    `password` VARCHAR(191) NOT NULL COMMENT '密码(加密)',
    `real_name` VARCHAR(50) NOT NULL COMMENT '真实姓名',
    `phone` VARCHAR(20) NULL COMMENT '手机号',
    `avatar` VARCHAR(255) NULL COMMENT '头像路径',
    `status` TINYINT DEFAULT 1 COMMENT '状态(1正常0禁用)',
    `remember_token` VARCHAR(100) NULL COMMENT '记住登录令牌',
    `last_login_at` TIMESTAMP NULL COMMENT '最后登录时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `unique_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='用户表';

-- 创建角色表
CREATE TABLE `roles` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(50) NOT NULL COMMENT '角色名称',
    `guard_name` VARCHAR(191) NOT NULL DEFAULT 'web' COMMENT '守卫名称',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `unique_name_guard` (`name`, `guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='角色表';

-- 创建权限表
CREATE TABLE `permissions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(50) NOT NULL COMMENT '权限名称',
    `guard_name` VARCHAR(191) NOT NULL DEFAULT 'web' COMMENT '守卫名称',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `unique_name_guard` (`name`, `guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='权限表';

-- 创建用户角色关联表
CREATE TABLE `model_has_roles` (
    `role_id` INT NOT NULL,
    `model_type` VARCHAR(191) NOT NULL,
    `model_id` BIGINT NOT NULL,
    PRIMARY KEY (`role_id`, `model_id`, `model_type`),
    KEY `idx_model` (`model_id`, `model_type`),
    FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='用户角色关联表';

-- 创建角色权限关联表
CREATE TABLE `role_has_permissions` (
    `permission_id` INT NOT NULL,
    `role_id` INT NOT NULL,
    PRIMARY KEY (`permission_id`, `role_id`),
    KEY `idx_role` (`role_id`),
    FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='角色权限关联表';

-- 创建操作日志表
CREATE TABLE `operation_logs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `action` VARCHAR(100) NOT NULL COMMENT '操作动作',
    `description` VARCHAR(255) NOT NULL COMMENT '操作描述',
    `ip_address` VARCHAR(45) NOT NULL COMMENT 'IP地址',
    `user_agent` TEXT NULL COMMENT '用户代理',
    `before_data` TEXT NULL COMMENT '操作前数据(JSON格式)',
    `after_data` TEXT NULL COMMENT '操作后数据(JSON格式)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    KEY `idx_user` (`user_id`),
    KEY `idx_action` (`action`),
    KEY `idx_date` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='操作日志表';

-- 插入基础数据
-- 插入食材分类数据
INSERT INTO `ingredient_categories` (`name`, `code`, `sort_order`) VALUES
('蔬菜类', 'VEG', 1),
('肉类', 'MEAT', 2),
('水产类', 'SEAFOOD', 3),
('粮食类', 'GRAIN', 4),
('调料类', 'SEASONING', 5),
('豆制品', 'BEAN', 6),
('蛋奶类', 'DAIRY', 7),
('水果类', 'FRUIT', 8),
('干货类', 'DRIED', 9),
('冷冻食品', 'FROZEN', 10);

-- 插入角色数据
INSERT INTO `roles` (`name`, `guard_name`) VALUES
('管理员', 'web'),
('审核员', 'web'),
('仓库员', 'web');

-- 插入权限数据
INSERT INTO `permissions` (`name`, `guard_name`) VALUES
-- 基础数据管理权限
('食材信息维护', 'web'),
('供应商管理', 'web'),
('分类管理', 'web'),
-- 入库管理权限
('食材入库', 'web'),
('上传称重图', 'web'),
('上传采购单', 'web'),
('入库审核', 'web'),
-- 出库管理权限
('食材出库', 'web'),
('批次选择', 'web'),
-- 库存管理权限
('库存查看', 'web'),
('库存盘点', 'web'),
-- 报表统计权限
('报表查看', 'web'),
('报表导出', 'web'),
-- 系统管理权限
('用户管理', 'web'),
('系统配置', 'web'),
('数据备份', 'web'),
('操作日志', 'web');

-- 插入角色权限关联数据
-- 管理员拥有所有权限
INSERT INTO `role_has_permissions` (`role_id`, `permission_id`)
SELECT 1, `id` FROM `permissions`;

-- 审核员权限
INSERT INTO `role_has_permissions` (`role_id`, `permission_id`)
SELECT 2, `id` FROM `permissions`
WHERE `name` IN ('库存查看', '库存盘点', '报表查看', '报表导出', '入库审核', '操作日志');

-- 仓库员权限
INSERT INTO `role_has_permissions` (`role_id`, `permission_id`)
SELECT 3, `id` FROM `permissions`
WHERE `name` IN ('食材入库', '上传称重图', '上传采购单', '食材出库', '批次选择', '库存查看', '库存盘点', '报表查看');

-- 插入默认用户
INSERT INTO `users` (`name`, `email`, `password`, `real_name`, `phone`, `status`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', '13800138000', 1),
('auditor', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '审核员', '13800138001', 1),
('warehouse', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '仓库员', '13800138002', 1);

-- 插入用户角色关联数据
INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1), -- admin -> 管理员
(2, 'App\\Models\\User', 2), -- auditor -> 审核员
(3, 'App\\Models\\User', 3); -- warehouse -> 仓库员

-- 插入供应商数据
INSERT INTO `suppliers` (`name`, `contact_person`, `phone`, `address`, `license_number`, `rating`, `status`) VALUES
('绿色农场有限公司', '张三', '13800138100', '北京市朝阳区农业园区1号', '91110000000000001X', 5, 1),
('新鲜蔬菜批发市场', '李四', '13800138101', '北京市海淀区批发市场2号', '91110000000000002X', 4, 1),
('优质肉类供应商', '王五', '13800138102', '北京市丰台区肉类加工园3号', '91110000000000003X', 5, 1),
('海鲜水产公司', '赵六', '13800138103', '北京市顺义区水产基地4号', '91110000000000004X', 4, 1),
('粮油调料批发', '钱七', '13800138104', '北京市通州区粮油市场5号', '91110000000000005X', 3, 1);

-- 插入食材数据
INSERT INTO `ingredients` (`name`, `category_id`, `unit`, `shelf_life_days`, `min_stock`, `description`, `created_by`) VALUES
-- 蔬菜类
('土豆', 1, '斤', 30, 50.00, '新鲜土豆，产地山东', 1),
('白菜', 1, '斤', 7, 30.00, '新鲜大白菜，产地河北', 1),
('萝卜', 1, '斤', 15, 20.00, '新鲜白萝卜，产地山东', 1),
('西红柿', 1, '斤', 7, 25.00, '新鲜西红柿，产地山东', 1),
('黄瓜', 1, '斤', 5, 15.00, '新鲜黄瓜，产地河北', 1),
-- 肉类
('猪肉', 2, '斤', 3, 100.00, '新鲜猪肉，检疫合格', 1),
('牛肉', 2, '斤', 3, 50.00, '新鲜牛肉，检疫合格', 1),
('鸡肉', 2, '斤', 2, 80.00, '新鲜鸡肉，检疫合格', 1),
-- 水产类
('草鱼', 3, '斤', 1, 30.00, '新鲜草鱼，活鱼现杀', 1),
('带鱼', 3, '斤', 2, 20.00, '冷冻带鱼，海捕野生', 1),
-- 粮食类
('大米', 4, '袋', 365, 10.00, '优质大米，25kg装', 1),
('面粉', 4, '袋', 180, 8.00, '高筋面粉，25kg装', 1),
-- 调料类
('食用油', 5, '桶', 365, 5.00, '大豆调和油，5L装', 1),
('食盐', 5, '袋', 730, 10.00, '精制食盐，500g装', 1),
('生抽', 5, '瓶', 365, 20.00, '生抽酱油，500ml装', 1);

-- 初始化库存数据（所有食材库存为0）
INSERT INTO `inventory` (`ingredient_id`, `current_quantity`, `total_value`)
SELECT `id`, 0, 0 FROM `ingredients`;
