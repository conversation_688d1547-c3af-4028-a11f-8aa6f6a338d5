<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-plus-circle"></i>
                新增报损
            </h1>
            <div class="header-actions">
                <a href="index.php?action=damage" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <!-- 新增报损表单 -->
        <div class="form-container">
            <div class="form-header">
                <div class="form-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="form-title">
                    <h2>食材报损记录</h2>
                    <p>请填写报损食材的详细信息</p>
                </div>
            </div>

            <form method="POST" class="damage-form" id="damageForm">
                <div class="form-grid">
                    <!-- 食材信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-carrot"></i> 食材信息</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">选择食材</label>
                                <select name="ingredient_id" class="form-control" required id="ingredientSelect">
                                    <option value="">请选择要报损的食材</option>
                                    <?php if (!empty($ingredients)): ?>
                                        <?php 
                                        $currentCategory = '';
                                        foreach ($ingredients as $ingredient): 
                                            if ($currentCategory !== $ingredient['category_name']):
                                                if ($currentCategory !== ''): echo '</optgroup>'; endif;
                                                $currentCategory = $ingredient['category_name'];
                                                echo '<optgroup label="' . htmlspecialchars($currentCategory) . '">';
                                            endif;
                                        ?>
                                            <option value="<?= $ingredient['id'] ?>" 
                                                    data-unit="<?= htmlspecialchars($ingredient['unit']) ?>"
                                                    data-stock="<?= $ingredient['current_stock'] ?>"
                                                    data-price="<?= $ingredient['unit_price'] ?>"
                                                    <?= (isset($form_data['ingredient_id']) && $form_data['ingredient_id'] == $ingredient['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($ingredient['name']) ?> 
                                                (库存: <?= number_format($ingredient['current_stock'], 1) ?><?= htmlspecialchars($ingredient['unit']) ?>)
                                            </option>
                                        <?php endforeach; ?>
                                        <?php if ($currentCategory !== ''): echo '</optgroup>'; endif; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">当前库存</label>
                                <div class="stock-display" id="stockDisplay">
                                    <span class="stock-value">-</span>
                                    <span class="stock-unit"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">报损数量</label>
                                <div class="quantity-input-group">
                                    <input type="number" name="damage_quantity" class="form-control" 
                                           step="0.1" min="0.1" required placeholder="请输入报损数量"
                                           value="<?= htmlspecialchars($form_data['damage_quantity'] ?? '') ?>" id="damageQuantity">
                                    <span class="input-unit" id="quantityUnit">-</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">单价</label>
                                <div class="price-display" id="priceDisplay">
                                    ¥<span class="price-value">-</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">报损价值</label>
                                <div class="damage-value-display" id="damageValueDisplay">
                                    ¥<span class="damage-value">0.00</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">批次号</label>
                                <input type="text" name="batch_number" class="form-control" 
                                       placeholder="选填，食材批次号" value="<?= htmlspecialchars($form_data['batch_number'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <!-- 报损信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-info-circle"></i> 报损信息</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">报损类型</label>
                                <select name="damage_type" class="form-control" required>
                                    <option value="">请选择报损类型</option>
                                    <option value="expired" <?= (isset($form_data['damage_type']) && $form_data['damage_type'] === 'expired') ? 'selected' : '' ?>>过期报损</option>
                                    <option value="damaged" <?= (isset($form_data['damage_type']) && $form_data['damage_type'] === 'damaged') ? 'selected' : '' ?>>破损报损</option>
                                    <option value="quality" <?= (isset($form_data['damage_type']) && $form_data['damage_type'] === 'quality') ? 'selected' : '' ?>>质量问题</option>
                                    <option value="other" <?= (isset($form_data['damage_type']) && $form_data['damage_type'] === 'other') ? 'selected' : '' ?>>其他原因</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label required">报损原因</label>
                            <textarea name="damage_reason" class="form-control" rows="4" required 
                                      placeholder="请详细说明报损原因，如：超过保质期、运输过程中破损、质量不符合标准等"><?= htmlspecialchars($form_data['damage_reason'] ?? '') ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="form-actions">
                    <button type="button" onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="submit" class="btn btn-danger btn-submit">
                        <i class="fas fa-save"></i> 确认报损
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* 新增报损页面特有样式 */
.form-container {
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #f56565 0%, #c53030 100%);
    color: white;
    padding: 30px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.form-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.form-title h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
}

.form-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

.form-grid {
    padding: 30px;
}

.form-section {
    margin-bottom: 30px;
    padding-bottom: 25px;
    border-bottom: 1px solid #e2e8f0;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: #f56565;
    font-size: 16px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 20px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-label.required::after {
    content: ' *';
    color: #e53e3e;
}

.form-control {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-control:focus {
    outline: none;
    border-color: #f56565;
    background: white;
    box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
}

.form-control::placeholder {
    color: #a0aec0;
}

/* 库存显示 */
.stock-display {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 12px 16px;
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 14px;
    color: #2d3748;
}

.stock-value {
    font-weight: 600;
    color: #2d3748;
}

.stock-unit {
    color: #718096;
}

/* 数量输入组 */
.quantity-input-group {
    display: flex;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    overflow: hidden;
    background: #fafafa;
}

.quantity-input-group input {
    border: none;
    background: transparent;
    flex: 1;
    padding: 12px 16px;
}

.quantity-input-group input:focus {
    outline: none;
    background: white;
}

.quantity-input-group:focus-within {
    border-color: #f56565;
    box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
}

.input-unit {
    background: #e2e8f0;
    padding: 12px 16px;
    color: #718096;
    font-size: 14px;
    min-width: 50px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 价格显示 */
.price-display {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 14px;
    color: #2d3748;
    font-weight: 600;
}

/* 报损价值显示 */
.damage-value-display {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #fed7d7;
    border: 2px solid #fc8181;
    border-radius: 10px;
    font-size: 16px;
    color: #c53030;
    font-weight: 700;
}

/* 表单操作按钮 */
.form-actions {
    padding: 25px 30px;
    background: #f7fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.btn-submit {
    min-width: 150px;
    padding: 12px 28px;
    font-size: 15px;
    font-weight: 600;
    background: linear-gradient(135deg, #f56565 0%, #c53030 100%);
    border: none;
    border-radius: 10px;
    color: white;
    box-shadow: 0 4px 12px rgba(245, 101, 101, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.btn-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(245, 101, 101, 0.35);
    background: linear-gradient(135deg, #c53030 0%, #9b2c2c 100%);
}

.btn-submit:hover::before {
    left: 100%;
}

.btn-submit:active {
    transform: translateY(-1px);
}

/* 响应式表单 */
@media (max-width: 768px) {
    .form-container {
        margin: 0 10px;
        border-radius: 12px;
    }

    .form-header {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .form-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .form-title h2 {
        font-size: 20px;
    }

    .form-grid {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-section {
        margin-bottom: 25px;
        padding-bottom: 20px;
    }

    .form-actions {
        padding: 20px;
        flex-direction: column-reverse;
        gap: 10px;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
        padding: 14px 20px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .form-header {
        padding: 15px;
    }

    .form-grid {
        padding: 15px;
    }

    .form-actions {
        padding: 15px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const ingredientSelect = document.getElementById('ingredientSelect');
    const stockDisplay = document.getElementById('stockDisplay');
    const quantityUnit = document.getElementById('quantityUnit');
    const priceDisplay = document.getElementById('priceDisplay');
    const damageQuantity = document.getElementById('damageQuantity');
    const damageValueDisplay = document.getElementById('damageValueDisplay');

    // 食材选择变化时更新显示
    ingredientSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        
        if (selectedOption.value) {
            const unit = selectedOption.dataset.unit;
            const stock = parseFloat(selectedOption.dataset.stock);
            const price = parseFloat(selectedOption.dataset.price);

            // 更新库存显示
            stockDisplay.querySelector('.stock-value').textContent = stock.toFixed(1);
            stockDisplay.querySelector('.stock-unit').textContent = unit;

            // 更新单位显示
            quantityUnit.textContent = unit;

            // 更新单价显示
            priceDisplay.querySelector('.price-value').textContent = price.toFixed(2);

            // 设置数量输入的最大值
            damageQuantity.max = stock;

            // 重新计算报损价值
            calculateDamageValue();
        } else {
            // 清空显示
            stockDisplay.querySelector('.stock-value').textContent = '-';
            stockDisplay.querySelector('.stock-unit').textContent = '';
            quantityUnit.textContent = '-';
            priceDisplay.querySelector('.price-value').textContent = '-';
            damageQuantity.max = '';
            damageValueDisplay.querySelector('.damage-value').textContent = '0.00';
        }
    });

    // 数量输入变化时计算报损价值
    damageQuantity.addEventListener('input', calculateDamageValue);

    function calculateDamageValue() {
        const selectedOption = ingredientSelect.options[ingredientSelect.selectedIndex];
        const quantity = parseFloat(damageQuantity.value) || 0;
        
        if (selectedOption.value && quantity > 0) {
            const price = parseFloat(selectedOption.dataset.price);
            const damageValue = quantity * price;
            damageValueDisplay.querySelector('.damage-value').textContent = damageValue.toFixed(2);
        } else {
            damageValueDisplay.querySelector('.damage-value').textContent = '0.00';
        }
    }

    // 表单验证
    document.getElementById('damageForm').addEventListener('submit', function(e) {
        const ingredientId = ingredientSelect.value;
        const quantity = parseFloat(damageQuantity.value) || 0;
        const damageType = document.querySelector('select[name="damage_type"]').value;
        const damageReason = document.querySelector('textarea[name="damage_reason"]').value.trim();

        if (!ingredientId) {
            e.preventDefault();
            alert('请选择要报损的食材');
            ingredientSelect.focus();
            return false;
        }

        if (quantity <= 0) {
            e.preventDefault();
            alert('报损数量必须大于0');
            damageQuantity.focus();
            return false;
        }

        const selectedOption = ingredientSelect.options[ingredientSelect.selectedIndex];
        const maxStock = parseFloat(selectedOption.dataset.stock);
        if (quantity > maxStock) {
            e.preventDefault();
            alert(`报损数量不能超过当前库存(${maxStock}${selectedOption.dataset.unit})`);
            damageQuantity.focus();
            return false;
        }

        if (!damageType) {
            e.preventDefault();
            alert('请选择报损类型');
            document.querySelector('select[name="damage_type"]').focus();
            return false;
        }

        if (!damageReason) {
            e.preventDefault();
            alert('请填写报损原因');
            document.querySelector('textarea[name="damage_reason"]').focus();
            return false;
        }

        // 确认提交
        const damageValue = quantity * parseFloat(selectedOption.dataset.price);
        const confirmMessage = `确认要报损以下食材吗？\n\n食材：${selectedOption.text.split('(')[0]}\n数量：${quantity}${selectedOption.dataset.unit}\n价值：¥${damageValue.toFixed(2)}\n类型：${document.querySelector('select[name="damage_type"] option:checked').text}\n\n此操作将减少库存，请确认信息无误。`;
        
        if (!confirm(confirmMessage)) {
            e.preventDefault();
            return false;
        }

        // 显示提交状态
        const submitBtn = this.querySelector('.btn-submit');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
    });
});
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>