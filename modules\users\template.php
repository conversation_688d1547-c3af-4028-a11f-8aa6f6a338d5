<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-users"></i>
                用户管理
            </h1>
            <div class="header-actions">
                <a href="index.php?action=create" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    新增用户
                </a>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon bg-blue">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['total_users'] ?? 0) ?></h3>
                    <p>总用户数</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-green">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['active_users'] ?? 0) ?></h3>
                    <p>活跃用户</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-orange">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['recent_login_users'] ?? 0) ?></h3>
                    <p>最近登录</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-red">
                    <i class="fas fa-user-times"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['inactive_users'] ?? 0) ?></h3>
                    <p>停用用户</p>
                </div>
            </div>
        </div>

        <!-- 角色统计 -->
        <?php if (!empty($data['stats']['role_stats'])): ?>
        <div class="role-stats">
            <h3>角色分布</h3>
            <div class="role-grid">
                <?php foreach ($data['stats']['role_stats'] as $roleStat): ?>
                    <div class="role-item">
                        <span class="role-name"><?= htmlspecialchars($roleStat['display_name']) ?></span>
                        <span class="role-count"><?= $roleStat['user_count'] ?> 人</span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- 搜索筛选 -->
        <div class="search-bar" style="padding: 15px !important;">
            <form method="GET" action="index.php" class="search-bar-form" style="display: flex !important; flex-wrap: nowrap !important; gap: 10px !important; align-items: center !important; padding: 0 !important;">
                <div class="form-field text-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">用户信息</label>
                    <input type="text" name="search" placeholder="用户名、姓名、邮箱或手机号..." 
                           value="<?= htmlspecialchars($data['search'] ?? '') ?>" class="form-control" style="height: 36px !important; width: 220px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">角色</label>
                    <select name="role" class="form-control" style="height: 36px !important; width: 120px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部角色</option>
                        <?php if (!empty($data['roles'])): ?>
                            <?php foreach ($data['roles'] as $role): ?>
                                <option value="<?= $role['id'] ?>" <?= ($data['selectedRole'] ?? '') == $role['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($role['display_name']) ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
                
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">状态</label>
                    <select name="status" class="form-control" style="height: 36px !important; width: 120px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部状态</option>
                        <option value="1" <?= ($data['selectedStatus'] ?? '') === '1' ? 'selected' : '' ?>>启用</option>
                        <option value="0" <?= ($data['selectedStatus'] ?? '') === '0' ? 'selected' : '' ?>>停用</option>
                    </select>
                </div>
                
                <button type="submit" class="btn btn-primary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #3b82f6 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                
                <a href="index.php" class="btn btn-secondary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #6b7280 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </form>
        </div>

        <!-- 用户列表 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>用户信息</th>
                        <th>角色</th>
                        <th>联系方式</th>
                        <th>状态</th>
                        <th>最后登录</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($data['users'])): ?>
                        <?php foreach ($data['users'] as $user): ?>
                            <tr>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">
                                            <?php if (!empty($user['avatar'])): ?>
                                                <img src="<?= htmlspecialchars($user['avatar']) ?>" alt="头像">
                                            <?php else: ?>
                                                <i class="fas fa-user"></i>
                                            <?php endif; ?>
                                        </div>
                                        <div class="user-details">
                                            <div class="user-name"><?= htmlspecialchars($user['real_name'] ?? $user['name']) ?></div>
                                            <div class="user-email"><?= htmlspecialchars($user['email']) ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($user['role_display_name'])): ?>
                                        <span class="role-badge"><?= htmlspecialchars($user['role_display_name']) ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">未分配</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($user['phone'])): ?>
                                        <div class="contact-info">
                                            <div><i class="fas fa-phone"></i> <?= htmlspecialchars($user['phone']) ?></div>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">未填写</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($user['status'] == 1): ?>
                                        <span class="status-badge status-active">启用</span>
                                    <?php else: ?>
                                        <span class="status-badge status-inactive">停用</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($user['last_login_at'])): ?>
                                        <small><?= date('m-d H:i', strtotime($user['last_login_at'])) ?></small>
                                    <?php else: ?>
                                        <span class="text-muted">从未登录</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?= date('Y-m-d', strtotime($user['created_at'])) ?></small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="index.php?action=view&id=<?= $user['id'] ?>"
                                           class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                            <span>查看</span>
                                        </a>
                                        <a href="index.php?action=edit&id=<?= $user['id'] ?>"
                                           class="btn btn-sm btn-warning" title="编辑用户">
                                            <i class="fas fa-edit"></i>
                                            <span>编辑</span>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-primary"
                                                onclick="resetPassword(<?= $user['id'] ?>)" title="重置密码">
                                            <i class="fas fa-key"></i>
                                            <span>重置</span>
                                        </button>
                                        <?php if ($user['status'] == 1): ?>
                                            <button type="button" class="btn btn-sm btn-secondary"
                                                    onclick="changeStatus(<?= $user['id'] ?>, 0)" title="停用用户">
                                                <i class="fas fa-ban"></i>
                                                <span>停用</span>
                                            </button>
                                        <?php else: ?>
                                            <button type="button" class="btn btn-sm btn-success"
                                                    onclick="changeStatus(<?= $user['id'] ?>, 1)" title="启用用户">
                                                <i class="fas fa-check"></i>
                                                <span>启用</span>
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-sm btn-danger"
                                                onclick="deleteUser(<?= $user['id'] ?>)" title="删除用户">
                                            <i class="fas fa-trash"></i>
                                            <span>删除</span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="7" class="text-center text-muted">
                                <i class="fas fa-inbox"></i>
                                暂无用户数据
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 重置密码弹窗 -->
<div id="resetPasswordModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>重置用户密码</h3>
            <button type="button" class="modal-close" onclick="closeModal('resetPasswordModal')">&times;</button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="newPassword">新密码</label>
                <input type="password" id="newPassword" class="form-control" placeholder="请输入新密码">
            </div>
            <div class="form-group">
                <label for="confirmPassword">确认密码</label>
                <input type="password" id="confirmPassword" class="form-control" placeholder="请再次输入新密码">
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-primary" onclick="confirmResetPassword()">确认重置</button>
            <button type="button" class="btn btn-secondary" onclick="closeModal('resetPasswordModal')">取消</button>
        </div>
    </div>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
}

.stat-icon.bg-blue { background: #4299e1; }
.stat-icon.bg-green { background: #48bb78; }
.stat-icon.bg-orange { background: #ed8936; }
.stat-icon.bg-red { background: #f56565; }

.stat-content h3 {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
    color: #2d3748;
}

.stat-content p {
    margin: 5px 0 0 0;
    color: #718096;
    font-size: 14px;
}

.role-stats {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.role-stats h3 {
    margin: 0 0 15px 0;
    color: #2d3748;
}

.role-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.role-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f7fafc;
    border-radius: 6px;
    border-left: 4px solid #4299e1;
}

.role-name {
    font-weight: 500;
    color: #2d3748;
}

.role-count {
    font-weight: bold;
    color: #4299e1;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-avatar i {
    color: #a0aec0;
    font-size: 18px;
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.user-name {
    font-weight: 500;
    color: #2d3748;
}

.user-email {
    font-size: 12px;
    color: #718096;
}

.role-badge {
    background: #bee3f8;
    color: #2b6cb0;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.contact-info {
    font-size: 12px;
    color: #4a5568;
}

.contact-info i {
    width: 12px;
    color: #a0aec0;
    margin-right: 4px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-active {
    background: #c6f6d5;
    color: #22543d;
}

.status-inactive {
    background: #fed7d7;
    color: #c53030;
}

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.table {
    margin: 0;
}

.table th {
    background: #f7fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #4a5568;
}

.table td {
    vertical-align: middle;
}

.table tbody tr:hover {
    background: #f7fafc;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #a0aec0;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 800px;
    }
}
</style>

<script>
let currentUserId = null;

function resetPassword(userId) {
    currentUserId = userId;
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmPassword').value = '';
    document.getElementById('resetPasswordModal').style.display = 'flex';
}

function confirmResetPassword() {
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (!newPassword) {
        alert('请输入新密码');
        return;
    }
    
    if (newPassword.length < 6) {
        alert('密码长度不能少于6位');
        return;
    }
    
    if (newPassword !== confirmPassword) {
        alert('两次输入的密码不一致');
        return;
    }
    
    fetch('index.php?action=reset_password', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${currentUserId}&new_password=${encodeURIComponent(newPassword)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('resetPasswordModal');
            showMessage(data.message, 'success');
        } else {
            alert('操作失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('操作失败: ' + error.message);
    });
}

function changeStatus(userId, status) {
    const action = status == 1 ? '启用' : '停用';
    if (confirm(`确定要${action}此用户吗？`)) {
        fetch('index.php?action=change_status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `id=${userId}&status=${status}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('操作失败: ' + error.message);
        });
    }
}

function deleteUser(userId) {
    if (confirm('确定要删除此用户吗？此操作不可恢复。')) {
        fetch('index.php?action=delete&id=' + userId, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('操作失败: ' + error.message);
        });
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    currentUserId = null;
}

// 点击模态框背景关闭
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
        currentUserId = null;
    }
});
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>