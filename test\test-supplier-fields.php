<?php
/**
 * 测试供应商字段
 */

require_once '../includes/Database.php';

echo "<h2>测试供应商字段</h2>";

try {
    $db = Database::getInstance();
    echo "<p>✅ 数据库连接成功</p>";
    
    // 检查suppliers表结构
    echo "<h3>suppliers表结构：</h3>";
    try {
        $columns = $db->fetchAll("DESCRIBE suppliers");
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>字段名</th><th>数据类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($column['Field']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 检查数据
        echo "<h3>数据检查：</h3>";
        $count = $db->fetchOne("SELECT COUNT(*) as count FROM suppliers");
        echo "<p>总记录数: " . $count['count'] . "</p>";
        
        if ($count['count'] > 0) {
            $suppliers = $db->fetchAll("SELECT * FROM suppliers LIMIT 5");
            echo "<h4>前5条记录：</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            foreach (array_keys($suppliers[0]) as $field) {
                echo "<th>" . htmlspecialchars($field) . "</th>";
            }
            echo "</tr>";
            
            foreach ($suppliers as $supplier) {
                echo "<tr>";
                foreach ($supplier as $value) {
                    echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // 测试插入操作
        echo "<h3>测试插入操作：</h3>";
        $testData = [
            'name' => '测试供应商_' . date('YmdHis'),
            'contact_person' => '测试联系人',
            'phone' => '13800138000',
            'address' => '测试地址',
            'status' => 1
        ];
        
        try {
            $id = $db->insert('suppliers', $testData);
            echo "<p style='color: green;'>✅ 测试数据插入成功，ID: $id</p>";
            
            // 立即删除测试数据
            $db->delete('suppliers', 'id = ?', [$id]);
            echo "<p style='color: blue;'>ℹ️ 测试数据已清理</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 插入测试失败: " . $e->getMessage() . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 表结构检查失败: " . $e->getMessage() . "</p>";
        
        // 提供创建表的建议
        echo "<h3>建议操作：</h3>";
        echo "<p>suppliers表可能不存在，请运行以下脚本创建：</p>";
        echo "<ul>";
        echo "<li><a href='../create-tables.php'>创建所有表</a></li>";
        echo "<li><a href='../update-suppliers-table.php'>更新suppliers表</a></li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../modules/suppliers/index.php'>供应商管理</a> | <a href='../index.php'>返回首页</a></p>";
?>
