<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-plus"></i>
                新增出库记录 - 列表选择
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <?php if (!empty($data['error_message'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <?= htmlspecialchars($data['error_message']) ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="index.php?action=create" id="outboundForm">
            <!-- 基本信息 -->
            <div class="info-card">
                <div class="card-header">
                    <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="meal_type">
                                <i class="fas fa-utensils"></i>
                                餐次 <span class="required">*</span>
                            </label>
                            <select name="meal_type" id="meal_type" class="form-control" required>
                                <option value="">请选择餐次</option>
                                <option value="breakfast">早餐</option>
                                <option value="lunch">午餐</option>
                                <option value="dinner">晚餐</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="meal_date">
                                <i class="fas fa-calendar-alt"></i>
                                用餐日期 <span class="required">*</span>
                            </label>
                            <input type="date" name="meal_date" id="meal_date" class="form-control" 
                                   value="<?= date('Y-m-d') ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="purpose">
                                <i class="fas fa-utensils"></i>
                                具体用途
                            </label>
                            <input type="text" name="purpose" id="purpose" class="form-control" 
                                   placeholder="如：炒白菜、红烧肉等">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 食材选择列表 -->
            <div class="ingredients-card">
                <div class="card-header">
                    <h3><i class="fas fa-list"></i> 选择出库食材</h3>
                    <div class="header-actions">
                        <input type="text" id="searchIngredients" class="form-control search-input" 
                               placeholder="搜索食材名称...">
                        <button type="button" class="btn btn-info" onclick="selectAll()">
                            <i class="fas fa-check-square"></i> 全选
                        </button>
                        <button type="button" class="btn btn-warning" onclick="clearAll()">
                            <i class="fas fa-square"></i> 清空
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="ingredients-grid" id="ingredientsGrid">
                        <?php if (!empty($data['ingredients'])): ?>
                            <?php 
                            $currentCategory = '';
                            foreach ($data['ingredients'] as $ingredient): 
                                if ($currentCategory !== $ingredient['category_name']):
                                    if ($currentCategory !== '') echo '</div></div>';
                                    $currentCategory = $ingredient['category_name'];
                                    echo '<div class="category-section">';
                                    echo '<h4 class="category-title">' . htmlspecialchars($currentCategory ?: '未分类') . '</h4>';
                                    echo '<div class="ingredients-row">';
                                endif;
                            ?>
                                <div class="ingredient-item" data-name="<?= htmlspecialchars(strtolower($ingredient['name'])) ?>">
                                    <div class="ingredient-card">
                                        <div class="ingredient-header">
                                            <label class="ingredient-checkbox">
                                                <input type="checkbox" name="selected_ingredients[]" 
                                                       value="<?= $ingredient['id'] ?>"
                                                       data-name="<?= htmlspecialchars($ingredient['name']) ?>"
                                                       data-unit="<?= htmlspecialchars($ingredient['unit']) ?>"
                                                       data-stock="<?= $ingredient['current_stock'] ?>"
                                                       onchange="toggleIngredient(this)">
                                                <span class="checkmark"></span>
                                                <strong><?= htmlspecialchars($ingredient['name']) ?></strong>
                                            </label>
                                        </div>
                                        <div class="ingredient-info">
                                            <div class="stock-info">
                                                <span class="stock-label">库存:</span>
                                                <span class="stock-value <?= $ingredient['current_stock'] <= 10 ? 'low-stock' : '' ?>">
                                                    <?= number_format($ingredient['current_stock'], 1) ?><?= htmlspecialchars($ingredient['unit']) ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="quantity-input" style="display: none;">
                                            <label>出库数量:</label>
                                            <div class="input-group">
                                                <input type="number" name="quantities[<?= $ingredient['id'] ?>]" 
                                                       class="form-control quantity-field" 
                                                       step="0.1" min="0.1" 
                                                       max="<?= $ingredient['current_stock'] ?>"
                                                       placeholder="数量">
                                                <span class="input-group-text"><?= htmlspecialchars($ingredient['unit']) ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            <?php if ($currentCategory !== '') echo '</div></div>'; ?>
                        <?php else: ?>
                            <div class="no-ingredients">
                                <i class="fas fa-exclamation-circle"></i>
                                <p>暂无可用食材</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- 已选择的食材汇总 -->
            <div class="selected-summary" id="selectedSummary" style="display: none;">
                <div class="card-header">
                    <h3><i class="fas fa-shopping-cart"></i> 已选择食材 (<span id="selectedCount">0</span>)</h3>
                </div>
                <div class="card-body">
                    <div id="selectedList"></div>
                </div>
            </div>

            <!-- 备注信息 -->
            <div class="notes-card">
                <div class="card-header">
                    <h3><i class="fas fa-sticky-note"></i> 备注信息</h3>
                </div>
                <div class="card-body">
                    <textarea name="notes" id="notes" class="form-control" rows="3" 
                              placeholder="请输入备注信息..."></textarea>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                    <i class="fas fa-save"></i>
                    保存出库记录 (<span id="submitCount">0</span> 项)
                </button>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    取消
                </a>
            </div>
        </form>
    </div>
</div>

<style>
.info-card, .ingredients-card, .selected-summary, .notes-card {
    background: white;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #495057;
    font-size: 18px;
}

.card-body {
    padding: 20px;
}

.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input {
    width: 200px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.category-section {
    margin-bottom: 30px;
}

.category-title {
    color: #007bff;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

.ingredients-row {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
}

.ingredient-item {
    transition: all 0.3s ease;
}

.ingredient-card {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
    background: #fff;
}

.ingredient-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.ingredient-card.selected {
    border-color: #28a745;
    background: #f8fff9;
}

.ingredient-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 10px;
}

.ingredient-checkbox input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.stock-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.stock-label {
    color: #6c757d;
    font-size: 14px;
}

.stock-value {
    font-weight: 600;
    color: #28a745;
}

.stock-value.low-stock {
    color: #dc3545;
}

.quantity-input {
    margin-top: 10px;
}

.quantity-input label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.input-group {
    display: flex;
}

.input-group .form-control {
    border-radius: 4px 0 0 4px;
}

.input-group-text {
    background: #e9ecef;
    border: 1px solid #ced4da;
    border-left: none;
    border-radius: 0 4px 4px 0;
    padding: 8px 12px;
    font-size: 14px;
}

.selected-summary {
    border: 2px solid #28a745;
}

.selected-summary .card-header {
    background: #d4edda;
    color: #155724;
}

.form-actions {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-actions .btn {
    margin: 0 10px;
    padding: 12px 30px;
    font-size: 16px;
}

.no-ingredients {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.no-ingredients i {
    font-size: 48px;
    margin-bottom: 15px;
}

.required {
    color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ingredients-row {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .header-actions {
        flex-direction: column;
        gap: 5px;
    }

    .search-input {
        width: 100%;
    }
}
</style>

<script>
let selectedIngredients = new Set();

// 切换食材选择
function toggleIngredient(checkbox) {
    const ingredientCard = checkbox.closest('.ingredient-card');
    const quantityInput = ingredientCard.querySelector('.quantity-input');
    const ingredientId = checkbox.value;

    if (checkbox.checked) {
        selectedIngredients.add(ingredientId);
        ingredientCard.classList.add('selected');
        quantityInput.style.display = 'block';
        quantityInput.querySelector('input').required = true;
        quantityInput.querySelector('input').focus();
    } else {
        selectedIngredients.delete(ingredientId);
        ingredientCard.classList.remove('selected');
        quantityInput.style.display = 'none';
        quantityInput.querySelector('input').required = false;
        quantityInput.querySelector('input').value = '';
    }

    updateSelectedSummary();
    updateSubmitButton();
}

// 全选
function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="selected_ingredients[]"]');
    checkboxes.forEach(checkbox => {
        if (!checkbox.checked) {
            checkbox.checked = true;
            toggleIngredient(checkbox);
        }
    });
}

// 清空选择
function clearAll() {
    const checkboxes = document.querySelectorAll('input[name="selected_ingredients[]"]');
    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            checkbox.checked = false;
            toggleIngredient(checkbox);
        }
    });
}

// 更新已选择食材汇总
function updateSelectedSummary() {
    const summaryDiv = document.getElementById('selectedSummary');
    const selectedList = document.getElementById('selectedList');
    const selectedCount = document.getElementById('selectedCount');

    if (selectedIngredients.size === 0) {
        summaryDiv.style.display = 'none';
        return;
    }

    summaryDiv.style.display = 'block';
    selectedCount.textContent = selectedIngredients.size;

    let html = '<div class="selected-items-grid">';

    selectedIngredients.forEach(ingredientId => {
        const checkbox = document.querySelector(`input[value="${ingredientId}"]`);
        const name = checkbox.dataset.name;
        const unit = checkbox.dataset.unit;
        const stock = checkbox.dataset.stock;
        const quantityInput = checkbox.closest('.ingredient-card').querySelector('.quantity-field');
        const quantity = quantityInput.value || '0';

        html += `
            <div class="selected-item">
                <div class="item-info">
                    <strong>${name}</strong>
                    <span class="item-details">库存: ${parseFloat(stock).toFixed(1)}${unit}</span>
                </div>
                <div class="item-quantity">
                    <span class="quantity-display">${quantity || '未填写'}${unit}</span>
                </div>
            </div>
        `;
    });

    html += '</div>';
    selectedList.innerHTML = html;
}

// 更新提交按钮状态
function updateSubmitButton() {
    const submitBtn = document.getElementById('submitBtn');
    const submitCount = document.getElementById('submitCount');

    submitCount.textContent = selectedIngredients.size;

    if (selectedIngredients.size > 0) {
        submitBtn.disabled = false;
        submitBtn.classList.remove('btn-secondary');
        submitBtn.classList.add('btn-primary');
    } else {
        submitBtn.disabled = true;
        submitBtn.classList.remove('btn-primary');
        submitBtn.classList.add('btn-secondary');
    }
}

// 搜索功能
document.getElementById('searchIngredients').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const ingredientItems = document.querySelectorAll('.ingredient-item');

    ingredientItems.forEach(item => {
        const name = item.dataset.name;
        if (name.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });

    // 隐藏空的分类
    const categories = document.querySelectorAll('.category-section');
    categories.forEach(category => {
        const visibleItems = category.querySelectorAll('.ingredient-item[style="display: block"], .ingredient-item:not([style*="display: none"])');
        if (visibleItems.length === 0) {
            category.style.display = 'none';
        } else {
            category.style.display = 'block';
        }
    });
});

// 监听数量输入变化
document.addEventListener('input', function(e) {
    if (e.target.classList.contains('quantity-field')) {
        updateSelectedSummary();
    }
});

// 表单提交验证
document.getElementById('outboundForm').addEventListener('submit', function(e) {
    if (selectedIngredients.size === 0) {
        e.preventDefault();
        alert('请至少选择一个食材进行出库！');
        return false;
    }

    // 检查所有选中的食材是否都填写了数量
    let hasEmptyQuantity = false;
    selectedIngredients.forEach(ingredientId => {
        const quantityInput = document.querySelector(`input[name="quantities[${ingredientId}]"]`);
        if (!quantityInput.value || parseFloat(quantityInput.value) <= 0) {
            hasEmptyQuantity = true;
        }
    });

    if (hasEmptyQuantity) {
        e.preventDefault();
        alert('请为所有选中的食材填写出库数量！');
        return false;
    }

    // 检查数量是否超过库存
    let hasOverStock = false;
    selectedIngredients.forEach(ingredientId => {
        const checkbox = document.querySelector(`input[value="${ingredientId}"]`);
        const stock = parseFloat(checkbox.dataset.stock);
        const quantityInput = document.querySelector(`input[name="quantities[${ingredientId}]"]`);
        const quantity = parseFloat(quantityInput.value);

        if (quantity > stock) {
            hasOverStock = true;
            alert(`${checkbox.dataset.name} 的出库数量(${quantity})超过了库存(${stock})！`);
        }
    });

    if (hasOverStock) {
        e.preventDefault();
        return false;
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updateSubmitButton();
});
</script>

<style>
.selected-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.selected-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-info strong {
    display: block;
    color: #495057;
    margin-bottom: 5px;
}

.item-details {
    font-size: 12px;
    color: #6c757d;
}

.quantity-display {
    font-weight: 600;
    color: #007bff;
    font-size: 16px;
}
</style>
