<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-plus"></i>
                新增出库记录 - 选择入库单出库
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <?php if (!empty($data['error_message'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <?= htmlspecialchars($data['error_message']) ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="index.php?action=create" id="outboundForm">
            <!-- 基本信息 -->
            <div class="info-card">
                <div class="card-header">
                    <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="meal_type">
                                <i class="fas fa-utensils"></i>
                                餐次 <span class="required">*</span>
                            </label>
                            <select name="meal_type" id="meal_type" class="form-control" required>
                                <option value="">请选择餐次</option>
                                <option value="breakfast">早餐</option>
                                <option value="lunch">午餐</option>
                                <option value="dinner">晚餐</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="meal_date">
                                <i class="fas fa-calendar-alt"></i>
                                用餐日期 <span class="required">*</span>
                            </label>
                            <input type="date" name="meal_date" id="meal_date" class="form-control" 
                                   value="<?= date('Y-m-d') ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="purpose">
                                <i class="fas fa-utensils"></i>
                                具体用途
                            </label>
                            <input type="text" name="purpose" id="purpose" class="form-control" 
                                   placeholder="如：炒白菜、红烧肉等">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 入库单选择列表 -->
            <div class="ingredients-card">
                <div class="card-header">
                    <h3><i class="fas fa-list"></i> 选择入库单</h3>
                    <div class="header-actions">
                        <input type="text" id="searchInboundOrders" class="form-control search-input"
                               placeholder="搜索入库单号或供应商...">
                        <button type="button" class="btn btn-info" onclick="refreshInboundOrders()">
                            <i class="fas fa-refresh"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="inbound-orders-grid" id="inboundOrdersGrid">
                        <?php if (!empty($data['inbound_orders'])): ?>
                            <?php foreach ($data['inbound_orders'] as $order): ?>
                                <div class="inbound-order-item" data-search="<?= htmlspecialchars(strtolower($order['order_number'] . ' ' . $order['supplier_name'])) ?>">
                                    <div class="inbound-order-card">
                                        <div class="order-header">
                                            <div class="order-info">
                                                <h4><?= htmlspecialchars($order['order_number']) ?></h4>
                                                <p class="supplier-name"><?= htmlspecialchars($order['supplier_name']) ?></p>
                                                <p class="order-date">入库日期: <?= date('Y-m-d', strtotime($order['inbound_date'])) ?></p>
                                            </div>
                                            <div class="order-stats">
                                                <span class="stat-badge">
                                                    <i class="fas fa-list"></i>
                                                    <?= $order['item_count'] ?> 项
                                                </span>
                                                <span class="stat-badge">
                                                    <i class="fas fa-money-bill"></i>
                                                    ¥<?= number_format($order['total_amount'], 2) ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="order-items">
                                            <h5>包含食材:</h5>
                                            <div class="items-list">
                                                <?php if (!empty($order['items'])): ?>
                                                    <?php foreach ($order['items'] as $item): ?>
                                                        <div class="item-row">
                                                            <label class="item-checkbox">
                                                                <input type="checkbox" name="selected_items[]"
                                                                       value="<?= $item['id'] ?>"
                                                                       data-order-id="<?= $order['id'] ?>"
                                                                       data-ingredient-id="<?= $item['ingredient_id'] ?>"
                                                                       data-ingredient-name="<?= htmlspecialchars($item['ingredient_name']) ?>"
                                                                       data-unit="<?= htmlspecialchars($item['unit']) ?>"
                                                                       data-available-quantity="<?= $item['actual_quantity'] ?>"
                                                                       data-unit-price="<?= $item['unit_price'] ?>"
                                                                       onchange="toggleOrderItem(this)">
                                                                <span class="item-name"><?= htmlspecialchars($item['ingredient_name']) ?></span>
                                                            </label>
                                                            <div class="item-details">
                                                                <span class="quantity">可用: <?= number_format($item['actual_quantity'], 1) ?><?= htmlspecialchars($item['unit']) ?></span>
                                                                <span class="price">¥<?= number_format($item['unit_price'], 2) ?>/<?= htmlspecialchars($item['unit']) ?></span>
                                                            </div>
                                                            <div class="quantity-input" style="display: none;">
                                                                <label>出库数量:</label>
                                                                <div class="input-group">
                                                                    <input type="number" name="outbound_quantities[<?= $item['id'] ?>]"
                                                                           class="form-control quantity-field"
                                                                           step="0.1" min="0.1"
                                                                           max="<?= $item['actual_quantity'] ?>"
                                                                           placeholder="数量">
                                                                    <span class="input-group-text"><?= htmlspecialchars($item['unit']) ?></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="no-orders">
                                <i class="fas fa-exclamation-circle"></i>
                                <p>暂无可用的入库单</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- 已选择的食材汇总 -->
            <div class="selected-summary" id="selectedSummary" style="display: none;">
                <div class="card-header">
                    <h3><i class="fas fa-shopping-cart"></i> 已选择食材 (<span id="selectedCount">0</span>)</h3>
                </div>
                <div class="card-body">
                    <div id="selectedList"></div>
                </div>
            </div>

            <!-- 备注信息 -->
            <div class="notes-card">
                <div class="card-header">
                    <h3><i class="fas fa-sticky-note"></i> 备注信息</h3>
                </div>
                <div class="card-body">
                    <textarea name="notes" id="notes" class="form-control" rows="3" 
                              placeholder="请输入备注信息..."></textarea>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                    <i class="fas fa-save"></i>
                    保存出库记录 (<span id="submitCount">0</span> 项)
                </button>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    取消
                </a>
            </div>
        </form>
    </div>
</div>

<style>
.info-card, .ingredients-card, .selected-summary, .notes-card {
    background: white;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #495057;
    font-size: 18px;
}

.card-body {
    padding: 20px;
}

.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input {
    width: 200px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.inbound-orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

.inbound-order-item {
    transition: all 0.3s ease;
}

.inbound-order-card {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    background: #fff;
}

.inbound-order-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.order-info h4 {
    margin: 0 0 5px 0;
    color: #007bff;
    font-size: 18px;
    font-weight: 600;
}

.supplier-name {
    margin: 0 0 5px 0;
    color: #495057;
    font-weight: 500;
}

.order-date {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

.order-stats {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-badge {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 4px;
}

.order-items h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 16px;
}

.items-list {
    max-height: 300px;
    overflow-y: auto;
}

.item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.item-row:hover {
    background: #f8f9fa;
}

.item-row.selected {
    border-color: #28a745;
    background: #f8fff9;
}

.item-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    flex: 1;
}

.item-checkbox input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.item-name {
    font-weight: 500;
    color: #495057;
}

.item-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
    font-size: 12px;
}

.quantity {
    color: #28a745;
    font-weight: 500;
}

.price {
    color: #6c757d;
}

.stock-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.stock-label {
    color: #6c757d;
    font-size: 14px;
}

.stock-value {
    font-weight: 600;
    color: #28a745;
}

.stock-value.low-stock {
    color: #dc3545;
}

.quantity-input {
    margin-top: 10px;
}

.quantity-input label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.input-group {
    display: flex;
}

.input-group .form-control {
    border-radius: 4px 0 0 4px;
}

.input-group-text {
    background: #e9ecef;
    border: 1px solid #ced4da;
    border-left: none;
    border-radius: 0 4px 4px 0;
    padding: 8px 12px;
    font-size: 14px;
}

.selected-summary {
    border: 2px solid #28a745;
}

.selected-summary .card-header {
    background: #d4edda;
    color: #155724;
}

.form-actions {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-actions .btn {
    margin: 0 10px;
    padding: 12px 30px;
    font-size: 16px;
}

.no-orders {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.no-orders i {
    font-size: 48px;
    margin-bottom: 15px;
}

.required {
    color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .inbound-orders-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .header-actions {
        flex-direction: column;
        gap: 5px;
    }

    .search-input {
        width: 100%;
    }

    .order-header {
        flex-direction: column;
        gap: 10px;
    }

    .item-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .item-details {
        align-items: flex-start;
    }
}
</style>

<script>
let selectedItems = new Set();

// 切换入库单食材选择
function toggleOrderItem(checkbox) {
    const itemRow = checkbox.closest('.item-row');
    const quantityInput = itemRow.querySelector('.quantity-input');
    const itemId = checkbox.value;

    if (checkbox.checked) {
        selectedItems.add(itemId);
        itemRow.classList.add('selected');
        quantityInput.style.display = 'block';
        quantityInput.querySelector('input').required = true;
        quantityInput.querySelector('input').focus();
    } else {
        selectedItems.delete(itemId);
        itemRow.classList.remove('selected');
        quantityInput.style.display = 'none';
        quantityInput.querySelector('input').required = false;
        quantityInput.querySelector('input').value = '';
    }

    updateSelectedSummary();
    updateSubmitButton();
}

// 刷新入库单列表
function refreshInboundOrders() {
    location.reload();
}

// 更新已选择食材汇总
function updateSelectedSummary() {
    const summaryDiv = document.getElementById('selectedSummary');
    const selectedList = document.getElementById('selectedList');
    const selectedCount = document.getElementById('selectedCount');

    if (selectedItems.size === 0) {
        summaryDiv.style.display = 'none';
        return;
    }

    summaryDiv.style.display = 'block';
    selectedCount.textContent = selectedItems.size;

    let html = '<div class="selected-items-grid">';

    selectedItems.forEach(itemId => {
        const checkbox = document.querySelector(`input[value="${itemId}"]`);
        const name = checkbox.dataset.ingredientName;
        const unit = checkbox.dataset.unit;
        const availableQuantity = checkbox.dataset.availableQuantity;
        const unitPrice = checkbox.dataset.unitPrice;
        const quantityInput = checkbox.closest('.item-row').querySelector('.quantity-field');
        const quantity = quantityInput.value || '0';

        html += `
            <div class="selected-item">
                <div class="item-info">
                    <strong>${name}</strong>
                    <span class="item-details">可用: ${parseFloat(availableQuantity).toFixed(1)}${unit} | ¥${parseFloat(unitPrice).toFixed(2)}/${unit}</span>
                </div>
                <div class="item-quantity">
                    <span class="quantity-display">${quantity || '未填写'}${unit}</span>
                </div>
            </div>
        `;
    });

    html += '</div>';
    selectedList.innerHTML = html;
}

// 更新提交按钮状态
function updateSubmitButton() {
    const submitBtn = document.getElementById('submitBtn');
    const submitCount = document.getElementById('submitCount');

    submitCount.textContent = selectedItems.size;

    if (selectedItems.size > 0) {
        submitBtn.disabled = false;
        submitBtn.classList.remove('btn-secondary');
        submitBtn.classList.add('btn-primary');
    } else {
        submitBtn.disabled = true;
        submitBtn.classList.remove('btn-primary');
        submitBtn.classList.add('btn-secondary');
    }
}

// 搜索功能
document.getElementById('searchInboundOrders').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const orderItems = document.querySelectorAll('.inbound-order-item');

    orderItems.forEach(item => {
        const searchData = item.dataset.search;
        if (searchData.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

// 监听数量输入变化
document.addEventListener('input', function(e) {
    if (e.target.classList.contains('quantity-field')) {
        updateSelectedSummary();
    }
});

// 表单提交验证
document.getElementById('outboundForm').addEventListener('submit', function(e) {
    if (selectedItems.size === 0) {
        e.preventDefault();
        alert('请至少选择一个食材进行出库！');
        return false;
    }

    // 检查所有选中的食材是否都填写了数量
    let hasEmptyQuantity = false;
    selectedItems.forEach(itemId => {
        const quantityInput = document.querySelector(`input[name="outbound_quantities[${itemId}]"]`);
        if (!quantityInput.value || parseFloat(quantityInput.value) <= 0) {
            hasEmptyQuantity = true;
        }
    });

    if (hasEmptyQuantity) {
        e.preventDefault();
        alert('请为所有选中的食材填写出库数量！');
        return false;
    }

    // 检查数量是否超过可用数量
    let hasOverStock = false;
    selectedItems.forEach(itemId => {
        const checkbox = document.querySelector(`input[value="${itemId}"]`);
        const availableQuantity = parseFloat(checkbox.dataset.availableQuantity);
        const quantityInput = document.querySelector(`input[name="outbound_quantities[${itemId}]"]`);
        const quantity = parseFloat(quantityInput.value);

        if (quantity > availableQuantity) {
            hasOverStock = true;
            alert(`${checkbox.dataset.ingredientName} 的出库数量(${quantity})超过了可用数量(${availableQuantity})！`);
        }
    });

    if (hasOverStock) {
        e.preventDefault();
        return false;
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updateSubmitButton();
});
</script>

<style>
.selected-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.selected-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-info strong {
    display: block;
    color: #495057;
    margin-bottom: 5px;
}

.item-details {
    font-size: 12px;
    color: #6c757d;
}

.quantity-display {
    font-weight: 600;
    color: #007bff;
    font-size: 16px;
}
</style>
