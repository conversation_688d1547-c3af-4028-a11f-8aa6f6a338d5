<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试统计项布局优化</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-mobile { background: #17a2b8; }
        .btn-primary { background: #667eea; }
        .feature-box { background: #e6f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 15px 0; border-radius: 5px; }
        .step-box { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; }
        .mobile-demo { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; }
        .mobile-frame { width: 320px; height: 640px; border: 8px solid #333; border-radius: 25px; margin: 0 auto; background: white; position: relative; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .mobile-screen { width: 100%; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; display: flex; flex-direction: column; }
        .mobile-header { background: rgba(255,255,255,0.95); color: #667eea; padding: 15px; display: flex; align-items: center; justify-content: center; font-weight: bold; }
        .mobile-content { flex: 1; padding: 20px; display: flex; flex-direction: column; gap: 8px; overflow-y: auto; }
        
        /* 统计项样式 */
        .items-stats {
            display: flex;
            gap: 8px;
            align-items: center;
            flex-wrap: nowrap;
            margin-top: 10px;
            overflow-x: auto;
            padding-bottom: 2px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            white-space: nowrap;
            flex-shrink: 0;
            min-width: 0;
        }

        .stat-label {
            font-weight: 500;
            opacity: 0.95;
            color: rgba(255, 255, 255, 0.9);
            font-size: 10px;
        }

        .stat-value {
            font-weight: 700;
            font-size: 12px;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            min-width: 20px;
            text-align: center;
            display: inline-block;
        }

        .stat-value.total {
            background: #3498db;
        }

        .stat-value.accepted {
            background: #27ae60;
        }

        .stat-value.pending {
            background: #e67e22;
        }

        /* 小屏幕优化 */
        @media (max-width: 360px) {
            .items-stats {
                gap: 6px;
                margin-top: 8px;
            }

            .stat-item {
                padding: 3px 6px;
                font-size: 10px;
                gap: 3px;
                border-radius: 10px;
            }

            .stat-label {
                font-size: 9px;
            }

            .stat-value {
                font-size: 11px;
                padding: 2px 6px;
                min-width: 18px;
            }
        }

        .comparison-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        .comparison-table th { background: #f5f5f5; }
        .before-col { background: #fff3cd; }
        .after-col { background: #d4edda; }
        .code-box { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
        
        .demo-container { display: flex; gap: 20px; justify-content: center; flex-wrap: wrap; }
        .demo-frame { width: 280px; height: 500px; border: 4px solid #333; border-radius: 15px; background: white; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
        .demo-screen { width: 100%; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; display: flex; flex-direction: column; }
        .demo-title { text-align: center; font-weight: bold; margin-bottom: 15px; font-size: 14px; }
    </style>
</head>
<body>
    <h1>✅ 统计项布局优化完成！</h1>
    
    <div class="test-section">
        <h2>🎯 优化目标</h2>
        
        <div class="feature-box">
            <h4>📊 统计项布局问题</h4>
            <p>确保商品数量统计（总数、验收数量、未验收数量）无论在什么屏幕大小下都保持在同一行显示。</p>
            
            <h5>主要改进：</h5>
            <ul>
                <li>🚫 <strong>禁止换行</strong>：使用 flex-wrap: nowrap 确保不换行</li>
                <li>📐 <strong>紧凑布局</strong>：减小间距和内边距，优化空间利用</li>
                <li>📱 <strong>响应式优化</strong>：小屏幕下进一步压缩尺寸</li>
                <li>🔄 <strong>横向滚动</strong>：如果内容过宽，支持横向滚动</li>
                <li>💪 <strong>防收缩</strong>：使用 flex-shrink: 0 防止项目被压缩</li>
                <li>📏 <strong>统一尺寸</strong>：设置最小宽度和居中对齐，确保标签大小一致</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 布局对比</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>属性</th>
                    <th class="before-col">优化前</th>
                    <th class="after-col">优化后</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>换行设置</strong></td>
                    <td class="before-col">flex-wrap: wrap（允许换行）</td>
                    <td class="after-col">flex-wrap: nowrap（禁止换行）</td>
                </tr>
                <tr>
                    <td><strong>间距</strong></td>
                    <td class="before-col">gap: 12px</td>
                    <td class="after-col">gap: 8px（小屏幕6px）</td>
                </tr>
                <tr>
                    <td><strong>内边距</strong></td>
                    <td class="before-col">padding: 6px 12px</td>
                    <td class="after-col">padding: 4px 8px（小屏幕3px 6px）</td>
                </tr>
                <tr>
                    <td><strong>字体大小</strong></td>
                    <td class="before-col">font-size: 12px</td>
                    <td class="after-col">font-size: 11px（小屏幕10px）</td>
                </tr>
                <tr>
                    <td><strong>收缩控制</strong></td>
                    <td class="before-col">默认可收缩</td>
                    <td class="after-col">flex-shrink: 0（不收缩）</td>
                </tr>
                <tr>
                    <td><strong>溢出处理</strong></td>
                    <td class="before-col">无处理</td>
                    <td class="after-col">overflow-x: auto（横向滚动）</td>
                </tr>
                <tr>
                    <td><strong>尺寸统一</strong></td>
                    <td class="before-col">数字位数不同导致大小不一</td>
                    <td class="after-col">min-width + text-align: center（统一大小）</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <h2>📱 不同屏幕尺寸效果</h2>
        
        <div class="demo-container">
            <!-- 正常屏幕 -->
            <div class="demo-frame">
                <div class="demo-screen">
                    <div class="demo-title">正常屏幕 (320px)</div>
                    <div class="items-stats">
                        <div class="stat-item">
                            <span class="stat-label">商品数量</span>
                            <span class="stat-value total">46</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">验收数量</span>
                            <span class="stat-value accepted">1</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">未验收数量</span>
                            <span class="stat-value pending">45</span>
                        </div>
                    </div>
                    <div style="margin-top: 20px; font-size: 12px; opacity: 0.8;">
                        ✅ 三个统计项在同一行<br>
                        ✅ 布局紧凑整齐<br>
                        ✅ 颜色区分清晰
                    </div>
                </div>
            </div>
            
            <!-- 小屏幕 -->
            <div class="demo-frame" style="width: 240px;">
                <div class="demo-screen" style="font-size: 10px;">
                    <div class="demo-title">小屏幕 (240px)</div>
                    <div class="items-stats" style="gap: 6px;">
                        <div class="stat-item" style="padding: 3px 6px; font-size: 10px; gap: 3px;">
                            <span class="stat-label" style="font-size: 9px;">商品数量</span>
                            <span class="stat-value total" style="font-size: 9px;">46</span>
                        </div>
                        <div class="stat-item" style="padding: 3px 6px; font-size: 10px; gap: 3px;">
                            <span class="stat-label" style="font-size: 9px;">验收数量</span>
                            <span class="stat-value accepted" style="font-size: 9px;">1</span>
                        </div>
                        <div class="stat-item" style="padding: 3px 6px; font-size: 10px; gap: 3px;">
                            <span class="stat-label" style="font-size: 9px;">未验收数量</span>
                            <span class="stat-value pending" style="font-size: 9px;">45</span>
                        </div>
                    </div>
                    <div style="margin-top: 20px; font-size: 11px; opacity: 0.8;">
                        ✅ 自动压缩尺寸<br>
                        ✅ 仍保持同一行<br>
                        ✅ 文字清晰可读
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
