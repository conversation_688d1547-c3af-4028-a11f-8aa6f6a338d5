<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查采购单表结构</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>检查采购单表结构</h1>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        echo "<p class='success'>✅ 数据库连接成功</p>";
        
        // 检查 purchase_orders 表结构
        echo "<h2>purchase_orders 表结构</h2>";
        $columns = $db->fetchAll("SHOW COLUMNS FROM purchase_orders");
        
        echo "<table>";
        echo "<tr><th>字段名</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th><th>注释</th></tr>";
        
        $required_fields = ['canteen_id', 'contact_person', 'delivery_address', 'contact_phone', 'order_amount', 'actual_amount', 'payment_status'];
        $found_fields = [];
        
        foreach ($columns as $column) {
            $field_name = $column['Field'];
            if (in_array($field_name, $required_fields)) {
                $found_fields[] = $field_name;
            }
            
            echo "<tr>";
            echo "<td>{$field_name}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . ($column['Comment'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 检查必需字段
        echo "<h2>必需字段检查</h2>";
        foreach ($required_fields as $field) {
            if (in_array($field, $found_fields)) {
                echo "<p class='success'>✅ 字段 {$field} 存在</p>";
            } else {
                echo "<p class='error'>❌ 字段 {$field} 缺失</p>";
            }
        }
        
        // 检查索引
        echo "<h2>索引信息</h2>";
        $indexes = $db->fetchAll("SHOW INDEX FROM purchase_orders");
        
        echo "<table>";
        echo "<tr><th>索引名</th><th>字段</th><th>唯一</th></tr>";
        foreach ($indexes as $index) {
            echo "<tr>";
            echo "<td>{$index['Key_name']}</td>";
            echo "<td>{$index['Column_name']}</td>";
            echo "<td>" . ($index['Non_unique'] == 0 ? '是' : '否') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 测试插入数据
        echo "<h2>测试数据插入</h2>";
        $test_data = [
            'order_number' => 'TEST_' . date('YmdHis'),
            'supplier_id' => 1,
            'order_date' => date('Y-m-d'),
            'canteen_id' => 1,
            'contact_person' => '测试联系人',
            'delivery_address' => '测试地址',
            'contact_phone' => '13800138000',
            'order_amount' => 100.00,
            'actual_amount' => 100.00,
            'payment_status' => 'unpaid',
            'status' => 1,
            'created_by' => 1
        ];
        
        try {
            $orderId = $db->insert('purchase_orders', $test_data);
            echo "<p class='success'>✅ 测试数据插入成功，订单ID: {$orderId}</p>";
            
            // 删除测试数据
            $db->delete('purchase_orders', 'id = ?', [$orderId]);
            echo "<p class='info'>ℹ️ 测试数据已清理</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ 测试数据插入失败: " . $e->getMessage() . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
