<?php
/**
 * 调试46个商品的表单提交问题
 */

// 模拟46个商品的数据提交
$testData = [
    'batch_type' => 'batch',
    'supplier_id' => '2',
    'batch_number' => 'TEST_46_' . microtime(true) * 10000,
    'inbound_date' => date('Y-m-d'),
    'operator_name' => '调试操作员',
    'notes' => '46个商品提交测试',
    'order_id' => '16',
    'is_new_order' => '0'
];

// 构造46个商品数据（模拟真实场景）
for ($i = 0; $i < 46; $i++) {
    $testData["items[$i][ingredient_id]"] = 141 + $i; // 从141开始递增
    $testData["items[$i][actual_quantity]"] = number_format(35.0 + ($i * 0.5), 1);
    $testData["items[$i][unit_price]"] = number_format(1.11 + ($i * 0.01), 2);
    $testData["items[$i][order_quantity]"] = '40.00';
}

echo "=== 调试46个商品的表单提交 ===\n";
echo "总字段数: " . count($testData) . "\n";
echo "PHP max_input_vars 限制: " . ini_get('max_input_vars') . "\n";
echo "POST最大大小: " . ini_get('post_max_size') . "\n\n";

// 计算预期的表单字段数量
$expectedFields = 8 + (46 * 4); // 基础字段8个 + 每个商品4个字段
echo "预期字段数: $expectedFields\n";

// 发送POST请求
$postData = http_build_query($testData);
echo "POST数据大小: " . strlen($postData) . " 字节\n\n";

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/x-www-form-urlencoded',
            'X-Requested-With: XMLHttpRequest'
        ],
        'content' => $postData
    ]
]);

echo "发送请求到移动端入库页面...\n";
$response = file_get_contents('http://localhost:8000/mobile/inbound.php', false, $context);

if ($response === false) {
    echo "❌ 请求失败\n";
    exit;
}

echo "服务器响应:\n";
echo "响应长度: " . strlen($response) . " 字符\n";

// 尝试解析JSON响应
$jsonResponse = json_decode($response, true);
if ($jsonResponse) {
    echo "✅ JSON响应解析成功\n";
    echo "成功状态: " . ($jsonResponse['success'] ? '✅ 成功' : '❌ 失败') . "\n";
    echo "消息: " . $jsonResponse['message'] . "\n";
    
    if (isset($jsonResponse['data'])) {
        echo "返回数据:\n";
        foreach ($jsonResponse['data'] as $key => $value) {
            echo "  $key: $value\n";
        }
    }
    
    if (isset($jsonResponse['error_code'])) {
        echo "错误代码: " . $jsonResponse['error_code'] . "\n";
    }
} else {
    echo "❌ 无法解析为JSON响应\n";
    echo "前500字符: " . substr($response, 0, 500) . "\n";
}

// 检查调试日志
$logFile = dirname(__DIR__) . '/logs/inbound_debug_' . date('Y-m-d') . '.log';
if (file_exists($logFile)) {
    echo "\n=== 服务器端调试日志（最后200行） ===\n";
    $logContent = file_get_contents($logFile);
    $logLines = explode("\n", $logContent);
    $lastLines = array_slice($logLines, -200);
    echo implode("\n", $lastLines);
}

echo "\n=== 调试完成 ===\n";
?>