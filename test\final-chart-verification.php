<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终图表验证</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        .data-table th { background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>✅ 最终图表验证</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../modules/dashboard/DashboardController.php';
    
    try {
        $db = Database::getInstance();
        $controller = new DashboardController();
        
        echo "<div class='test-section'>";
        echo "<h2>🔧 已修复的问题总结</h2>";
        
        echo "<h4>1. JavaScript AJAX请求修复：</h4>";
        echo "<ul>";
        echo "<li class='success'>✅ 添加了 X-Requested-With: XMLHttpRequest 请求头</li>";
        echo "<li class='success'>✅ 添加了 Content-Type: application/json 请求头</li>";
        echo "</ul>";
        
        echo "<h4>2. 数据库字段名修复：</h4>";
        echo "<ul>";
        echo "<li class='success'>✅ 月度采购：使用 status 而不是 order_status</li>";
        echo "<li class='success'>✅ 月度采购：使用 COALESCE(actual_amount, order_amount, 0)</li>";
        echo "<li class='success'>✅ 分类分布：使用 purchase_price 而不是 current_stock * unit_price</li>";
        echo "<li class='success'>✅ 供应商排名：使用 COALESCE(actual_amount, order_amount, 0)</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>📊 测试所有图表数据</h2>";
        
        // 测试月度采购数据
        echo "<h4>1. 月度采购数据测试：</h4>";
        try {
            $reflection = new ReflectionClass($controller);
            $method = $reflection->getMethod('getMonthlyPurchaseData');
            $method->setAccessible(true);
            $monthlyData = $method->invoke($controller);
            
            echo "<p class='success'>✅ 月度采购数据查询成功</p>";
            echo "<p class='info'>数据条数: " . count($monthlyData) . "</p>";
            
            if (!empty($monthlyData)) {
                echo "<table class='data-table'>";
                echo "<tr><th>月份</th><th>订单数</th><th>总金额</th></tr>";
                foreach (array_slice($monthlyData, 0, 5) as $item) {
                    echo "<tr>";
                    echo "<td>{$item['month']}</td>";
                    echo "<td>{$item['order_count']}</td>";
                    echo "<td>" . number_format($item['total_amount'], 2) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='warning'>⚠️ 暂无月度采购数据</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ 月度采购数据查询失败: " . $e->getMessage() . "</p>";
        }
        
        // 测试分类分布数据
        echo "<h4>2. 分类分布数据测试：</h4>";
        try {
            $method = $reflection->getMethod('getCategoryDistributionData');
            $method->setAccessible(true);
            $categoryData = $method->invoke($controller);
            
            echo "<p class='success'>✅ 分类分布数据查询成功</p>";
            echo "<p class='info'>数据条数: " . count($categoryData) . "</p>";
            
            if (!empty($categoryData)) {
                echo "<table class='data-table'>";
                echo "<tr><th>分类名称</th><th>食材数量</th><th>总价值</th></tr>";
                foreach (array_slice($categoryData, 0, 5) as $item) {
                    echo "<tr>";
                    echo "<td>{$item['category_name']}</td>";
                    echo "<td>{$item['ingredient_count']}</td>";
                    echo "<td>" . number_format($item['total_value'], 2) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='warning'>⚠️ 暂无分类分布数据</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ 分类分布数据查询失败: " . $e->getMessage() . "</p>";
        }
        
        // 测试供应商排名数据
        echo "<h4>3. 供应商排名数据测试：</h4>";
        try {
            $method = $reflection->getMethod('getSupplierRankingData');
            $method->setAccessible(true);
            $supplierData = $method->invoke($controller);
            
            echo "<p class='success'>✅ 供应商排名数据查询成功</p>";
            echo "<p class='info'>数据条数: " . count($supplierData) . "</p>";
            
            if (!empty($supplierData)) {
                echo "<table class='data-table'>";
                echo "<tr><th>供应商名称</th><th>订单数</th><th>总金额</th></tr>";
                foreach (array_slice($supplierData, 0, 5) as $item) {
                    echo "<tr>";
                    echo "<td>{$item['supplier_name']}</td>";
                    echo "<td>{$item['order_count']}</td>";
                    echo "<td>" . number_format($item['total_amount'], 2) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='warning'>⚠️ 暂无供应商排名数据</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ 供应商排名数据查询失败: " . $e->getMessage() . "</p>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🌐 AJAX接口测试</h2>";
        
        echo "<button onclick='testAllCharts()' class='btn'>测试所有图表接口</button>";
        echo "<div id='ajaxResults' class='debug-log' style='display: none;'></div>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>📈 数据库状态检查</h2>";
        
        // 检查关键表的数据
        $tables = [
            'purchase_orders' => '采购订单',
            'suppliers' => '供应商',
            'ingredients' => '食材',
            'ingredient_categories' => '食材分类'
        ];
        
        echo "<table class='data-table'>";
        echo "<tr><th>表名</th><th>中文名</th><th>记录数</th><th>状态</th></tr>";
        
        foreach ($tables as $table => $name) {
            try {
                $count = $db->fetchOne("SELECT COUNT(*) as count FROM {$table}")['count'];
                $status = $count > 0 ? '有数据' : '无数据';
                $statusClass = $count > 0 ? 'success' : 'warning';
                
                echo "<tr>";
                echo "<td>{$table}</td>";
                echo "<td>{$name}</td>";
                echo "<td>{$count}</td>";
                echo "<td class='{$statusClass}'>{$status}</td>";
                echo "</tr>";
            } catch (Exception $e) {
                echo "<tr>";
                echo "<td>{$table}</td>";
                echo "<td>{$name}</td>";
                echo "<td>-</td>";
                echo "<td class='error'>表不存在</td>";
                echo "</tr>";
            }
        }
        echo "</table>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 验证过程出错: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>
    
    <div class="test-section">
        <h2>🎯 最终验证</h2>
        <div class="step">
            <p>
                <a href="../modules/dashboard/index.php" class="btn">📊 访问仪表板</a>
            </p>
            
            <h4>预期结果：</h4>
            <ul>
                <li class="success">✅ 月度采购趋势图表正常显示</li>
                <li class="success">✅ 可以切换不同图表类型</li>
                <li class="success">✅ 图表有数据且显示正确</li>
                <li class="success">✅ 没有JavaScript错误</li>
                <li class="success">✅ 没有"数据加载失败"提示</li>
            </ul>
            
            <h4>如果仍有问题：</h4>
            <ul>
                <li>检查浏览器控制台是否有JavaScript错误</li>
                <li>确认数据库中有采购订单数据</li>
                <li>检查网络请求是否成功</li>
            </ul>
        </div>
    </div>
    
    <script>
    function testAllCharts() {
        const resultDiv = document.getElementById('ajaxResults');
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = '正在测试所有图表接口...';
        
        const chartTypes = [
            { type: 'monthly_purchase', name: '月度采购' },
            { type: 'category_distribution', name: '分类分布' },
            { type: 'supplier_ranking', name: '供应商排名' }
        ];
        
        let results = [];
        let completed = 0;
        
        chartTypes.forEach(chart => {
            fetch(`../modules/dashboard/chart-data.php?type=${chart.type}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                results.push({
                    name: chart.name,
                    success: data.success,
                    dataCount: data.data ? data.data.length : 0,
                    message: data.message || '成功'
                });
            })
            .catch(error => {
                results.push({
                    name: chart.name,
                    success: false,
                    dataCount: 0,
                    message: error.message
                });
            })
            .finally(() => {
                completed++;
                if (completed === chartTypes.length) {
                    displayResults(results);
                }
            });
        });
    }
    
    function displayResults(results) {
        const resultDiv = document.getElementById('ajaxResults');
        let html = '<h4>AJAX接口测试结果:</h4>';
        
        results.forEach(result => {
            const statusClass = result.success ? 'success' : 'error';
            const statusIcon = result.success ? '✅' : '❌';
            
            html += `<p class="${statusClass}">${statusIcon} ${result.name}: ${result.message}`;
            if (result.success) {
                html += ` (${result.dataCount} 条数据)`;
            }
            html += '</p>';
        });
        
        resultDiv.innerHTML = html;
    }
    </script>
    
    <p><a href="../modules/dashboard/index.php">返回仪表板</a></p>
</body>
</html>
