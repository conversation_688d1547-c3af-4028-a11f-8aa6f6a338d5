<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试移动端单行布局</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-mobile { background: #17a2b8; }
        .btn-primary { background: #667eea; }
        .feature-box { background: #e6f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 15px 0; border-radius: 5px; }
        .step-box { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; }
        .mobile-demo { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; }
        .mobile-frame { width: 320px; height: 640px; border: 8px solid #333; border-radius: 25px; margin: 0 auto; background: white; position: relative; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .mobile-screen { width: 100%; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; display: flex; flex-direction: column; }
        .mobile-header { background: rgba(255,255,255,0.95); color: #667eea; padding: 15px; display: flex; align-items: center; justify-content: center; font-weight: bold; }
        .mobile-content { flex: 1; padding: 20px; display: flex; flex-direction: column; gap: 8px; overflow-y: auto; }
        .mobile-single-row { background: white; border-radius: 8px; padding: 12px 15px; color: #333; border-left: 4px solid #667eea; display: flex; align-items: center; justify-content: space-between; min-height: 50px; }
        .comparison-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        .comparison-table th { background: #f5f5f5; }
        .before-col { background: #fff3cd; }
        .after-col { background: #d4edda; }
        .code-box { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>✅ 移动端单行布局已完成！</h1>

    <div class="test-section">
        <h2>🎯 功能概述</h2>

        <div class="feature-box">
            <h4>📏 极简单行设计</h4>
            <p>将每个商品的所有信息和操作都压缩到一行内显示，包括商品名称、采购数量、重量输入和拍照按钮，最大化屏幕利用率，提供类似表格的紧凑体验。</p>

            <h5>主要特点：</h5>
            <ul>
                <li>📏 <strong>单行布局</strong>：所有信息和操作都在一行内完成</li>
                <li>🎯 <strong>信息精简</strong>：只显示最关键的信息</li>
                <li>⚡ <strong>操作高效</strong>：重量输入和拍照按钮紧凑排列</li>
                <li>📱 <strong>空间最大化</strong>：最大化利用移动端屏幕空间</li>
                <li>🎨 <strong>视觉统一</strong>：保持一致的视觉风格</li>
                <li>👆 <strong>触摸优化</strong>：按钮大小适合手指操作</li>
                <li>📊 <strong>表格化体验</strong>：类似表格的数据展示方式</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 布局对比</h2>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>布局元素</th>
                    <th class="before-col">多行布局</th>
                    <th class="after-col">单行布局</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>商品名称</strong></td>
                    <td class="before-col">独立一行，较大字体</td>
                    <td class="after-col">左侧显示，紧凑字体</td>
                </tr>
                <tr>
                    <td><strong>采购数量</strong></td>
                    <td class="before-col">独立一行，带图标</td>
                    <td class="after-col">商品名称下方，小字体</td>
                </tr>
                <tr>
                    <td><strong>重量输入</strong></td>
                    <td class="before-col">独立区域，较大输入框</td>
                    <td class="after-col">紧凑输入框，固定宽度</td>
                </tr>
                <tr>
                    <td><strong>拍照按钮</strong></td>
                    <td class="before-col">文字按钮，较大尺寸</td>
                    <td class="after-col">图标按钮，32x32px</td>
                </tr>
                <tr>
                    <td><strong>状态指示</strong></td>
                    <td class="before-col">右上角，带文字</td>
                    <td class="after-col">最右侧，仅图标</td>
                </tr>
                <tr>
                    <td><strong>垂直空间</strong></td>
                    <td class="before-col">每个商品约80-100px</td>
                    <td class="after-col">每个商品约50px</td>
                </tr>
                <tr>
                    <td><strong>可见商品数</strong></td>
                    <td class="before-col">屏幕显示5-6个商品</td>
                    <td class="after-col">屏幕显示10-12个商品</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="test-section">
        <h2>📱 移动端界面预览</h2>

        <div class="mobile-demo">
            <h4>单行布局效果</h4>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <div class="mobile-header">
                        📏 单行商品列表
                    </div>
                    <div class="mobile-content">
                        <div class="mobile-single-row">
                            <div style="flex: 1; min-width: 0;">
                                <div style="font-weight: 600; font-size: 14px; display: flex; align-items: center; gap: 6px; margin-bottom: 4px;">
                                    🥬 白菜 <span style="background: #667eea; color: white; font-size: 8px; padding: 2px 4px; border-radius: 8px;">采购单</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">50斤</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="display: flex; border: 1px solid #e9ecef; border-radius: 6px; overflow: hidden;">
                                    <input style="width: 60px; padding: 6px 8px; border: none; font-size: 13px; text-align: center;" placeholder="0.00">
                                    <div style="padding: 6px 8px; background: #f8f9fa; font-size: 11px; border-left: 1px solid #e9ecef;">斤</div>
                                </div>
                                <button style="width: 32px; height: 32px; background: #667eea; color: white; border: none; border-radius: 6px; font-size: 12px;">📷</button>
                                <div style="width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #f39c12;">⏰</div>
                            </div>
                        </div>

                        <div class="mobile-single-row">
                            <div style="flex: 1; min-width: 0;">
                                <div style="font-weight: 600; font-size: 14px; display: flex; align-items: center; gap: 6px; margin-bottom: 4px;">
                                    🥩 猪肉 <span style="background: #667eea; color: white; font-size: 8px; padding: 2px 4px; border-radius: 8px;">采购单</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">20斤</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="display: flex; border: 1px solid #e9ecef; border-radius: 6px; overflow: hidden;">
                                    <input style="width: 60px; padding: 6px 8px; border: none; font-size: 13px; text-align: center;" value="19.5">
                                    <div style="padding: 6px 8px; background: #f8f9fa; font-size: 11px; border-left: 1px solid #e9ecef;">斤</div>
                                </div>
                                <div style="display: flex; gap: 4px;">
                                    <button style="width: 32px; height: 32px; background: #27ae60; color: white; border: none; border-radius: 6px; font-size: 12px;">✅</button>
                                    <button style="width: 32px; height: 32px; background: #4facfe; color: white; border: none; border-radius: 6px; font-size: 12px;">👁️</button>
                                </div>
                                <div style="width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #27ae60;">✅</div>
                            </div>
                        </div>

                        <div class="mobile-single-row">
                            <div style="flex: 1; min-width: 0;">
                                <div style="font-weight: 600; font-size: 14px; display: flex; align-items: center; gap: 6px; margin-bottom: 4px;">
                                    🥕 胡萝卜 <span style="background: #667eea; color: white; font-size: 8px; padding: 2px 4px; border-radius: 8px;">采购单</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">30斤</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="display: flex; border: 1px solid #e9ecef; border-radius: 6px; overflow: hidden;">
                                    <input style="width: 60px; padding: 6px 8px; border: none; font-size: 13px; text-align: center;" value="28.5">
                                    <div style="padding: 6px 8px; background: #f8f9fa; font-size: 11px; border-left: 1px solid #e9ecef;">斤</div>
                                </div>
                                <button style="width: 32px; height: 32px; background: #667eea; color: white; border: none; border-radius: 6px; font-size: 12px;">📷</button>
                                <div style="width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #3498db;">📷</div>
                            </div>
                        </div>

                        <div class="mobile-single-row">
                            <div style="flex: 1; min-width: 0;">
                                <div style="font-weight: 600; font-size: 14px; display: flex; align-items: center; gap: 6px; margin-bottom: 4px;">
                                    🧅 洋葱 <span style="background: #667eea; color: white; font-size: 8px; padding: 2px 4px; border-radius: 8px;">采购单</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">15斤</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="display: flex; border: 1px solid #e9ecef; border-radius: 6px; overflow: hidden;">
                                    <input style="width: 60px; padding: 6px 8px; border: none; font-size: 13px; text-align: center;" placeholder="0.00">
                                    <div style="padding: 6px 8px; background: #f8f9fa; font-size: 11px; border-left: 1px solid #e9ecef;">斤</div>
                                </div>
                                <button style="width: 32px; height: 32px; background: #667eea; color: white; border: none; border-radius: 6px; font-size: 12px;">📷</button>
                                <div style="width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #f39c12;">⏰</div>
                            </div>
                        </div>

                        <div class="mobile-single-row">
                            <div style="flex: 1; min-width: 0;">
                                <div style="font-weight: 600; font-size: 14px; display: flex; align-items: center; gap: 6px; margin-bottom: 4px;">
                                    🥒 黄瓜 <span style="background: #667eea; color: white; font-size: 8px; padding: 2px 4px; border-radius: 8px;">采购单</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">25斤</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="display: flex; border: 1px solid #e9ecef; border-radius: 6px; overflow: hidden;">
                                    <input style="width: 60px; padding: 6px 8px; border: none; font-size: 13px; text-align: center;" placeholder="0.00">
                                    <div style="padding: 6px 8px; background: #f8f9fa; font-size: 11px; border-left: 1px solid #e9ecef;">斤</div>
                                </div>
                                <button style="width: 32px; height: 32px; background: #667eea; color: white; border: none; border-radius: 6px; font-size: 12px;">📷</button>
                                <div style="width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #f39c12;">⏰</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🎨 设计特色</h2>

        <h4>单行布局优势：</h4>
        <div class="feature-box">
            <h5>空间效率</h5>
            <ul>
                <li><strong>垂直压缩</strong>：每个商品只占用50px高度</li>
                <li><strong>信息密度</strong>：一屏可显示10-12个商品</li>
                <li><strong>快速浏览</strong>：用户可以快速扫描所有商品</li>
                <li><strong>减少滚动</strong>：大幅减少页面滚动需求</li>
            </ul>

            <h5>操作便捷</h5>
            <ul>
                <li><strong>紧凑输入</strong>：60px宽度的重量输入框</li>
                <li><strong>图标按钮</strong>：32x32px的拍照按钮</li>
                <li><strong>状态一目了然</strong>：右侧状态图标清晰显示</li>
                <li><strong>触摸友好</strong>：按钮大小适合手指操作</li>
            </ul>
        </div>

        <h4>视觉层次：</h4>
        <div class="feature-box">
            <h5>信息组织</h5>
            <ul>
                <li><strong>左侧信息区</strong>：商品名称 + 采购数量</li>
                <li><strong>中间操作区</strong>：重量输入 + 拍照按钮</li>
                <li><strong>右侧状态区</strong>：完成状态指示器</li>
                <li><strong>蓝色左边框</strong>：采购单商品标识</li>
            </ul>

            <h5>交互反馈</h5>
            <ul>
                <li><strong>悬停效果</strong>：行悬停时轻微上浮</li>
                <li><strong>按钮反馈</strong>：按钮点击时缩放效果</li>
                <li><strong>状态变化</strong>：实时更新完成状态</li>
                <li><strong>颜色编码</strong>：不同状态使用不同颜色</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 测试步骤</h2>

        <div class="step-box">
            <h4>步骤1：访问移动端入库页面</h4>
            <p><a href="../mobile/inbound.php" class="btn btn-primary">📱 打开移动端入库页面</a></p>
            <p><strong>操作：</strong>选择一个采购单，进入第二步</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>每个商品显示在一行内</li>
                <li>左侧显示商品名称和采购数量</li>
                <li>右侧显示重量输入、拍照按钮和状态</li>
                <li>整体布局紧凑整齐</li>
            </ul>
        </div>

        <div class="step-box">
            <h4>步骤2：测试重量输入</h4>
            <p><strong>操作：</strong>在重量输入框中输入数值</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>输入框响应正常</li>
                <li>数值居中显示</li>
                <li>单位标识清晰</li>
                <li>状态实时更新</li>
            </ul>
        </div>

        <div class="step-box">
            <h4>步骤3：测试拍照功能</h4>
            <p><strong>操作：</strong>点击拍照按钮</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>按钮响应正常</li>
                <li>拍照后显示两个按钮</li>
                <li>按钮图标清晰可见</li>
                <li>查看照片功能正常</li>
            </ul>
        </div>

        <div class="step-box">
            <h4>步骤4：测试屏幕适配</h4>
            <p><strong>操作：</strong>在不同屏幕尺寸下测试</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>小屏幕下布局正常</li>
                <li>按钮大小适中</li>
                <li>文字不会被截断</li>
                <li>操作区域不重叠</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>✅ 功能验证清单</h2>

        <h4>布局效果：</h4>
        <ul>
            <li>□ 每个商品显示在一行内</li>
            <li>□ 左右布局平衡合理</li>
            <li>□ 垂直对齐正确</li>
            <li>□ 间距统一美观</li>
            <li>□ 左边框标识清晰</li>
        </ul>

        <h4>信息显示：</h4>
        <ul>
            <li>□ 商品名称显示完整</li>
            <li>□ 采购数量信息清晰</li>
            <li>□ 采购单徽章正常</li>
            <li>□ 状态图标正确</li>
            <li>□ 文字大小适中</li>
        </ul>

        <h4>操作功能：</h4>
        <ul>
            <li>□ 重量输入框正常</li>
            <li>□ 拍照按钮响应正常</li>
            <li>□ 照片查看功能正常</li>
            <li>□ 状态更新正确</li>
            <li>□ 表单提交成功</li>
        </ul>

        <h4>响应式效果：</h4>
        <ul>
            <li>□ 小屏幕适配正常</li>
            <li>□ 按钮大小合适</li>
            <li>□ 文字不被截断</li>
            <li>□ 触摸操作友好</li>
            <li>□ 整体体验流畅</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🚀 开始测试</h2>

        <p>移动端单行布局已完成，现在可以开始测试：</p>

        <div style="text-align: center; margin: 20px 0;">
            <a href="../mobile/inbound.php" class="btn btn-primary" style="font-size: 1.1rem; padding: 12px 24px;">
                📏 测试单行布局效果
            </a>
        </div>

        <h4>测试重点：</h4>
        <ol>
            <li><strong>布局紧凑性</strong>：确认单行布局效果</li>
            <li><strong>信息可读性</strong>：验证信息显示清晰</li>
            <li><strong>操作便捷性</strong>：测试重量输入和拍照</li>
            <li><strong>空间利用率</strong>：评估屏幕空间使用</li>
            <li><strong>用户体验</strong>：整体操作流畅度</li>
        </ol>

        <h4>设计优势：</h4>
        <ul>
            <li><strong>极致紧凑</strong>：最大化利用屏幕空间</li>
            <li><strong>信息密度高</strong>：一屏显示更多商品</li>
            <li><strong>操作高效</strong>：所有操作在一行完成</li>
            <li><strong>视觉统一</strong>：保持一致的设计风格</li>
        </ul>

        <div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h5>🎉 设计亮点</h5>
            <p>新的单行布局设计实现了极致的空间利用：</p>
            <ul>
                <li><strong>空间最大化</strong>：每个商品只占用50px高度</li>
                <li><strong>信息精简</strong>：只显示最关键的信息</li>
                <li><strong>操作集中</strong>：所有操作都在一行内完成</li>
                <li><strong>表格化体验</strong>：类似表格的数据展示方式</li>
            </ul>
            <p>这使得移动端入库操作更加高效，特别适合商品较多的采购单！</p>
        </div>
    </div>
</body>
</html>