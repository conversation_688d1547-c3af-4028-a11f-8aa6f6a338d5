<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-carrot"></i>
                食材管理
            </h1>
            <div class="header-actions">
                <a href="?action=create" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    添加食材
                </a>
                <a href="index.php?action=import" class="btn btn-success">
                    <i class="fas fa-upload"></i>
                    批量导入
                </a>
                <button type="button" class="btn btn-secondary" onclick="exportData()">
                    <i class="fas fa-download"></i>
                    导出
                </button>
                <button type="button" class="btn btn-secondary" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </button>
            </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="search-bar" style="padding: 15px !important;">
            <form method="GET" class="search-bar-form" style="display: flex !important; flex-wrap: nowrap !important; gap: 10px !important; align-items: center !important; padding: 0 !important;">
                <div class="form-field text-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">搜索食材</label>
                    <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="输入食材名称..." style="height: 36px !important; width: 180px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">分类</label>
                    <select class="form-control" name="category" style="height: 36px !important; width: 140px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部分类</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?= $category['id'] ?>" <?= $selected_category == $category['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">状态</label>
                    <select class="form-control" name="status" style="height: 36px !important; width: 120px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部状态</option>
                        <option value="1" <?= $status === '1' ? 'selected' : '' ?>>启用</option>
                        <option value="0" <?= $status === '0' ? 'selected' : '' ?>>禁用</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #3b82f6 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                <a href="index.php" class="btn btn-secondary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #6b7280 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </form>
        </div>

        <!-- 食材列表 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>食材信息</th>
                        <th>分类</th>
                        <th>计量单位</th>
                        <th>当前库存</th>
                        <th>最低库存</th>
                        <th>单价</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($ingredients)): ?>
                        <?php foreach ($ingredients as $ingredient): ?>
                            <tr>
                                <td>
                                    <div class="ingredient-info">
                                        <div class="ingredient-name"><?= htmlspecialchars($ingredient['name']) ?></div>
                                        <?php if (!empty($ingredient['description'])): ?>
                                            <div class="ingredient-desc"><?= htmlspecialchars($ingredient['description']) ?></div>
                                        <?php endif; ?>
                                        <?php if (!empty($ingredient['specification'])): ?>
                                            <div class="ingredient-spec">规格: <?= htmlspecialchars($ingredient['specification']) ?></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($ingredient['category_name'])): ?>
                                        <span class="category-badge"><?= htmlspecialchars($ingredient['category_name']) ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">未分类</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="unit-badge"><?= htmlspecialchars($ingredient['unit']) ?></span>
                                </td>
                                <td>
                                    <div class="stock-info">
                                        <div class="stock-value <?= ($ingredient['current_stock'] ?? 0) <= ($ingredient['min_stock'] ?? 0) ? 'stock-low' : 'stock-normal' ?>">
                                            <?= number_format($ingredient['current_stock'] ?? 0, 1) ?>
                                        </div>
                                        <div class="stock-unit"><?= htmlspecialchars($ingredient['unit']) ?></div>
                                    </div>
                                </td>
                                <td>
                                    <div class="min-stock-info"><?= number_format($ingredient['min_stock'] ?? 0, 1) ?> <?= htmlspecialchars($ingredient['unit']) ?></div>
                                </td>
                                <td>
                                    <div class="price-info">¥<?= number_format($ingredient['unit_price'] ?? 0, 2) ?></div>
                                </td>
                                <td>
                                    <label class="status-switch">
                                        <input type="checkbox" <?= $ingredient['status'] == 1 ? 'checked' : '' ?> 
                                               onchange="toggleStatus(<?= $ingredient['id'] ?>, this.checked)">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td>
                                    <div class="time-info"><?= date('m-d H:i', strtotime($ingredient['created_at'])) ?></div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="?action=view&id=<?= $ingredient['id'] ?>"
                                           class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                            <span>查看</span>
                                        </a>
                                        <a href="?action=edit&id=<?= $ingredient['id'] ?>"
                                           class="btn btn-sm btn-warning" title="编辑食材">
                                            <i class="fas fa-edit"></i>
                                            <span>编辑</span>
                                        </a>
                                        <a href="../inbound/create.php?ingredient_id=<?= $ingredient['id'] ?>"
                                           class="btn btn-sm btn-success" title="食材入库">
                                            <i class="fas fa-sign-in-alt"></i>
                                            <span>入库</span>
                                        </a>
                                        <a href="../outbound/index.php?action=create&ingredient_id=<?= $ingredient['id'] ?>"
                                           class="btn btn-sm btn-primary" title="食材出库">
                                            <i class="fas fa-sign-out-alt"></i>
                                            <span>出库</span>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="9" class="text-center text-muted">
                                <i class="fas fa-inbox"></i>
                                暂无食材数据
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
/* 食材管理模块特有样式 */
.ingredient-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.ingredient-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 15px;
}

.ingredient-desc {
    font-size: 12px;
    color: #718096;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

.ingredient-spec {
    font-size: 12px;
    color: #4a5568;
    background: #e2e8f0;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

.category-badge {
    background: #bee3f8;
    color: #2b6cb0;
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
}

.unit-badge {
    background: #e6fffa;
    color: #234e52;
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
}

.stock-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.stock-value {
    font-weight: bold;
    font-size: 16px;
}

.stock-normal {
    color: #22543d;
}

.stock-low {
    color: #c53030;
}

.stock-unit {
    font-size: 12px;
    color: #718096;
}

.min-stock-info, .price-info, .time-info {
    font-size: 14px;
    color: #4a5568;
    text-align: center;
}

.price-info {
    font-weight: 600;
    color: #2d3748;
}

.status-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.status-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.switch-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .switch-slider {
    background-color: #48bb78;
}

input:checked + .switch-slider:before {
    transform: translateX(20px);
}

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 1200px;
    }
}
</style>

<script>
// 导出数据
function exportData() {
    alert('导出功能开发中...');
}

// 刷新数据
function refreshData() {
    location.reload();
}

// 切换状态
function toggleStatus(id, checked) {
    const status = checked ? 1 : 0;

    fetch('index.php?action=toggle_status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${id}&status=${status}`
    })
    .then(response => {
        // 检查响应是否为JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回了非JSON响应，可能是页面错误');
        }
        return response.json();
    })
    .then(data => {
        if (!data.success) {
            alert('状态更新失败: ' + (data.message || '未知错误'));
            // 恢复原状态
            document.querySelector(`input[onchange*="${id}"]`).checked = !checked;
        } else {
            console.log('状态更新成功:', data.message);
        }
    })
    .catch(error => {
        console.error('状态更新错误:', error);
        alert('状态更新失败: ' + error.message);
        // 恢复原状态
        document.querySelector(`input[onchange*="${id}"]`).checked = !checked;
    });
}
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>