<?php

require_once dirname(__DIR__, 2) . '/includes/BaseController.php';
require_once dirname(__DIR__, 2) . '/includes/helpers.php';

class AnalyticsController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    public function handleRequest()
    {
        switch ($this->request['action']) {
            case 'index':
            default:
                return $this->index();
        }
    }

    public function index()
    {
        $timeRange = $_GET['range'] ?? '30d';
        $metric = $_GET['metric'] ?? 'purchase';
        
        $data = [
            'page_title' => '趋势分析 - 学校食堂管理系统',
            'time_range' => $timeRange,
            'current_metric' => $metric,
            'date_range' => $this->getDateRange($timeRange)
        ];

        // 获取趋势数据
        $data['trends'] = $this->getTrendData($timeRange, $metric);
        $data['predictions'] = $this->getPredictionData($timeRange, $metric);
        $data['insights'] = $this->getInsights($data['trends']);
        $data['comparisons'] = $this->getComparisons($timeRange);

        $this->setTemplateData(['data' => $data]);
        $this->render('template.php');
    }

    /**
     * 获取日期范围
     */
    private function getDateRange($timeRange)
    {
        $end = new DateTime();
        $start = clone $end;
        
        switch ($timeRange) {
            case '7d':
                $start->modify('-7 days');
                break;
            case '30d':
                $start->modify('-30 days');
                break;
            case '90d':
                $start->modify('-90 days');
                break;
            case '1y':
                $start->modify('-1 year');
                break;
            default:
                $start->modify('-30 days');
        }
        
        return [
            'start' => $start->format('Y-m-d'),
            'end' => $end->format('Y-m-d')
        ];
    }

    /**
     * 获取趋势数据
     */
    private function getTrendData($timeRange, $metric)
    {
        // 模拟趋势数据
        $data = [];
        
        switch ($metric) {
            case 'purchase':
                $data = $this->getPurchaseTrends($timeRange);
                break;
            case 'inventory':
                $data = $this->getInventoryTrends($timeRange);
                break;
            case 'cost':
                $data = $this->getCostTrends($timeRange);
                break;
            case 'supplier':
                $data = $this->getSupplierTrends($timeRange);
                break;
            default:
                $data = $this->getPurchaseTrends($timeRange);
        }
        
        return $data;
    }

    /**
     * 采购趋势
     */
    private function getPurchaseTrends($timeRange)
    {
        $labels = [];
        $amounts = [];
        $orders = [];
        
        // 根据时间范围生成数据
        switch ($timeRange) {
            case '7d':
                $labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                $amounts = [2500, 3200, 1800, 4100, 2900, 3500, 2800];
                $orders = [3, 4, 2, 5, 3, 4, 3];
                break;
            case '30d':
                for ($i = 29; $i >= 0; $i--) {
                    $date = date('m/d', strtotime("-{$i} days"));
                    $labels[] = $date;
                    $amounts[] = rand(1500, 4500);
                    $orders[] = rand(2, 6);
                }
                break;
            case '90d':
                for ($i = 11; $i >= 0; $i--) {
                    $date = date('Y-m', strtotime("-{$i} months"));
                    $labels[] = $date;
                    $amounts[] = rand(45000, 85000);
                    $orders[] = rand(45, 85);
                }
                break;
            case '1y':
                for ($i = 11; $i >= 0; $i--) {
                    $date = date('Y-m', strtotime("-{$i} months"));
                    $labels[] = $date;
                    $amounts[] = rand(80000, 150000);
                    $orders[] = rand(80, 150);
                }
                break;
        }
        
        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => '采购金额',
                    'data' => $amounts,
                    'type' => 'amount'
                ],
                [
                    'label' => '订单数量',
                    'data' => $orders,
                    'type' => 'count'
                ]
            ],
            'summary' => [
                'total_amount' => array_sum($amounts),
                'total_orders' => array_sum($orders),
                'avg_amount' => round(array_sum($amounts) / count($amounts), 2),
                'growth_rate' => $this->calculateGrowthRate($amounts)
            ]
        ];
    }

    /**
     * 库存趋势
     */
    private function getInventoryTrends($timeRange)
    {
        $labels = [];
        $values = [];
        $turnover = [];
        
        switch ($timeRange) {
            case '7d':
                $labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                $values = [45000, 42000, 48000, 44000, 46000, 43000, 45000];
                $turnover = [8.5, 9.2, 7.8, 8.9, 8.1, 9.5, 8.7];
                break;
            case '30d':
                for ($i = 29; $i >= 0; $i--) {
                    $date = date('m/d', strtotime("-{$i} days"));
                    $labels[] = $date;
                    $values[] = rand(40000, 50000);
                    $turnover[] = rand(70, 120) / 10;
                }
                break;
        }
        
        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => '库存价值',
                    'data' => $values,
                    'type' => 'amount'
                ],
                [
                    'label' => '周转率',
                    'data' => $turnover,
                    'type' => 'rate'
                ]
            ],
            'summary' => [
                'current_value' => end($values),
                'avg_turnover' => round(array_sum($turnover) / count($turnover), 2),
                'efficiency_score' => 85
            ]
        ];
    }

    /**
     * 成本趋势
     */
    private function getCostTrends($timeRange)
    {
        $labels = [];
        $costs = [];
        $savings = [];
        
        switch ($timeRange) {
            case '30d':
                for ($i = 29; $i >= 0; $i--) {
                    $date = date('m/d', strtotime("-{$i} days"));
                    $labels[] = $date;
                    $costs[] = rand(1100, 1400) / 100;
                    $savings[] = rand(50, 200);
                }
                break;
        }
        
        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => '每餐成本',
                    'data' => $costs,
                    'type' => 'cost'
                ],
                [
                    'label' => '日节约金额',
                    'data' => $savings,
                    'type' => 'savings'
                ]
            ],
            'summary' => [
                'avg_cost_per_meal' => 12.50,
                'total_savings' => array_sum($savings),
                'cost_reduction' => 8.5
            ]
        ];
    }

    /**
     * 供应商趋势
     */
    private function getSupplierTrends($timeRange)
    {
        return [
            'performance_trends' => [
                'labels' => ['1月', '2月', '3月', '4月', '5月', '6月'],
                'datasets' => [
                    [
                        'label' => '平均质量评分',
                        'data' => [88, 90, 92, 89, 94, 95],
                        'type' => 'score'
                    ],
                    [
                        'label' => '准时交付率',
                        'data' => [85, 88, 92, 95, 96, 98],
                        'type' => 'rate'
                    ]
                ]
            ],
            'supplier_count' => [
                'labels' => ['1月', '2月', '3月', '4月', '5月', '6月'],
                'active' => [8, 9, 10, 11, 12, 12],
                'new' => [1, 1, 2, 1, 1, 0]
            ]
        ];
    }

    /**
     * 获取预测数据
     */
    private function getPredictionData($timeRange, $metric)
    {
        // 简单的线性预测
        return [
            'next_month_purchase' => 135000,
            'predicted_growth' => 8.5,
            'confidence_level' => 85,
            'risk_factors' => [
                '季节性价格波动',
                '供应商产能限制',
                '学生人数变化'
            ]
        ];
    }

    /**
     * 获取洞察分析
     */
    private function getInsights($trends)
    {
        return [
            'key_findings' => [
                '采购金额呈稳定增长趋势，月增长率约8.5%',
                '蔬菜类采购占比持续上升，符合健康饮食趋势',
                '供应商准时交付率显著提升，达到98%',
                '库存周转率保持在合理范围内'
            ],
            'recommendations' => [
                '建议与主要供应商签订长期合作协议',
                '可考虑引入更多有机蔬菜供应商',
                '优化库存管理，减少积压和浪费',
                '建立供应商绩效评估体系'
            ],
            'alerts' => [
                '部分调料类食材价格上涨较快，需关注',
                '新供应商质量评分偏低，需加强管理'
            ]
        ];
    }

    /**
     * 获取对比数据
     */
    private function getComparisons($timeRange)
    {
        return [
            'year_over_year' => [
                'purchase_growth' => 15.2,
                'cost_reduction' => 8.5,
                'supplier_improvement' => 12.8
            ],
            'month_over_month' => [
                'purchase_change' => 8.5,
                'inventory_change' => -2.1,
                'efficiency_change' => 5.3
            ]
        ];
    }

    /**
     * 计算增长率
     */
    private function calculateGrowthRate($data)
    {
        if (count($data) < 2) return 0;
        
        $first = $data[0];
        $last = end($data);
        
        if ($first == 0) return 0;
        
        return round((($last - $first) / $first) * 100, 2);
    }
}
