# PWA 图标说明

## 需要的图标文件

为了完整支持PWA功能，需要在此目录下放置以下图标文件：

### 应用图标
- `icon-72x72.png` - 72x72像素
- `icon-96x96.png` - 96x96像素  
- `icon-128x128.png` - 128x128像素
- `icon-144x144.png` - 144x144像素
- `icon-152x152.png` - 152x152像素
- `icon-192x192.png` - 192x192像素
- `icon-384x384.png` - 384x384像素
- `icon-512x512.png` - 512x512像素

### 快捷方式图标
- `shortcut-inbound.png` - 入库快捷方式图标 (96x96)
- `shortcut-inventory.png` - 库存快捷方式图标 (96x96)
- `shortcut-purchase.png` - 采购快捷方式图标 (96x96)
- `shortcut-reports.png` - 报表快捷方式图标 (96x96)

### 通知图标
- `badge-72x72.png` - 通知徽章图标 (72x72)
- `checkmark.png` - 确认操作图标
- `xmark.png` - 关闭操作图标

## 图标设计要求

1. **格式**: PNG格式，支持透明背景
2. **设计**: 简洁明了，符合Material Design或iOS设计规范
3. **颜色**: 主色调使用系统主题色 #667eea
4. **内容**: 
   - 主图标：食材管理相关元素（如餐具、食物、管理图标等）
   - 快捷方式图标：对应功能的象征性图标
   - 通知图标：简单的操作符号

## 临时解决方案

在正式图标制作完成前，可以使用以下方式生成临时图标：

1. 使用在线图标生成器（如 favicon.io）
2. 使用设计工具（如 Figma、Sketch）
3. 使用代码生成简单的SVG图标

## 图标更新

当图标文件更新后，需要：

1. 更新 `manifest.json` 中的图标路径
2. 清除浏览器缓存
3. 重新安装PWA应用（如果已安装）
4. 更新Service Worker版本号
