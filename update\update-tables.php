<?php
// 数据库表更新脚本 - 添加缺失的字段

// 数据库配置
$host = '************';
$port = 3306;
$dbname = 'sc';
$username = 'sc';
$password = 'pw5K4SsM7kZsjdxy';

echo "<h2>数据库表更新脚本</h2>";
echo "<p>为现有表添加缺失的字段...</p>";

// 连接数据库
$mysqli = new mysqli($host, $username, $password, $dbname, $port);

if ($mysqli->connect_error) {
    die("<p style='color: red;'>连接失败: " . $mysqli->connect_error . "</p>");
}

echo "<p style='color: green;'>数据库连接成功！</p>";

// 设置字符集
$mysqli->set_charset("utf8");

// 更新语句
$updates = [
    // 为 ingredient_categories 表添加缺失字段
    'ingredient_categories_description' => "ALTER TABLE `ingredient_categories` ADD COLUMN `description` text DEFAULT NULL",
    'ingredient_categories_status' => "ALTER TABLE `ingredient_categories` ADD COLUMN `status` tinyint(1) DEFAULT 1",
    'ingredient_categories_updated_at' => "ALTER TABLE `ingredient_categories` ADD COLUMN `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",

    // 为 ingredients 表添加缺失字段
    'ingredients_code' => "ALTER TABLE `ingredients` ADD COLUMN `code` varchar(50) DEFAULT NULL",
    'ingredients_specification' => "ALTER TABLE `ingredients` ADD COLUMN `specification` varchar(100) DEFAULT NULL",
    'ingredients_max_stock' => "ALTER TABLE `ingredients` ADD COLUMN `max_stock` decimal(10,2) DEFAULT NULL",
    'ingredients_updated_at' => "ALTER TABLE `ingredients` ADD COLUMN `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",

    // 为 suppliers 表添加缺失字段（如果需要）
    'suppliers_email' => "ALTER TABLE `suppliers` ADD COLUMN `email` varchar(100) DEFAULT NULL",
    'suppliers_description' => "ALTER TABLE `suppliers` ADD COLUMN `description` text DEFAULT NULL",
    'suppliers_created_by' => "ALTER TABLE `suppliers` ADD COLUMN `created_by` int(11) DEFAULT 1",
    'suppliers_updated_at' => "ALTER TABLE `suppliers` ADD COLUMN `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
];

$success_count = 0;
$error_count = 0;
$skip_count = 0;

foreach ($updates as $update_name => $sql) {
    echo "<h3>执行更新: $update_name</h3>";
    
    // 检查字段是否已存在
    $parts = explode('_', $update_name);
    if (count($parts) >= 3) {
        $table_name = $parts[0] . '_' . $parts[1];
        $field_name = $parts[2];
    } else {
        $table_name = $parts[0];
        $field_name = $parts[1];
    }
    
    $check_sql = "SHOW COLUMNS FROM `$table_name` LIKE '$field_name'";
    $check_result = $mysqli->query($check_sql);
    
    if ($check_result && $check_result->num_rows > 0) {
        echo "<p style='color: orange;'>⚠️ 字段 $field_name 已存在于表 $table_name 中，跳过</p>";
        $skip_count++;
        continue;
    }
    
    if ($mysqli->query($sql)) {
        echo "<p style='color: green;'>✅ 更新 $update_name 成功</p>";
        $success_count++;
    } else {
        echo "<p style='color: red;'>❌ 更新 $update_name 失败: " . $mysqli->error . "</p>";
        $error_count++;
    }
}

// 更新现有数据的默认值
echo "<h3>更新现有数据的默认值</h3>";

// 为现有分类设置默认状态
$update_status = $mysqli->query("UPDATE `ingredient_categories` SET `status` = 1 WHERE `status` IS NULL");
if ($update_status) {
    echo "<p style='color: green;'>✅ 分类状态默认值更新成功</p>";
} else {
    echo "<p style='color: red;'>❌ 分类状态默认值更新失败: " . $mysqli->error . "</p>";
}

// 关闭连接
$mysqli->close();

echo "<hr>";
echo "<h3>更新完成统计</h3>";
echo "<p><strong>成功:</strong> $success_count 项</p>";
echo "<p><strong>失败:</strong> $error_count 项</p>";
echo "<p><strong>跳过:</strong> $skip_count 项</p>";

if ($error_count == 0) {
    echo "<p style='color: green; font-weight: bold;'>🎉 所有更新都已成功完成！</p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>⚠️ 有 $error_count 项更新失败，请检查错误信息。</p>";
}

echo "<hr>";
echo "<p><a href='dashboard.php'>返回仪表板</a> | <a href='categories.php'>查看分类管理</a></p>";
?>
