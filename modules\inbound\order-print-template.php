<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>入库单 - <?= htmlspecialchars($order['order_number']) ?></title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none !important; }
            .page-break { page-break-after: always; }
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 20px;
            color: #333;
        }
        
        .print-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        
        .print-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .print-subtitle {
            font-size: 14px;
            color: #666;
        }
        
        .order-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        
        .order-info-left, .order-info-right {
            flex: 1;
        }
        
        .order-info-right {
            text-align: right;
        }
        
        .info-item {
            margin-bottom: 8px;
        }
        
        .info-label {
            font-weight: bold;
            display: inline-block;
            width: 80px;
        }
        
        .order-number {
            font-size: 16px;
            font-weight: bold;
            color: #0066cc;
            font-family: 'Courier New', monospace;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .items-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: center;
        }
        
        .items-table td.number {
            text-align: center;
        }
        
        .items-table td.amount {
            text-align: right;
            font-family: 'Courier New', monospace;
        }
        
        .batch-number {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            background: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .total-row {
            background-color: #e9ecef;
            font-weight: bold;
        }
        
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            text-align: center;
            width: 150px;
        }
        
        .signature-line {
            border-bottom: 1px solid #333;
            height: 40px;
            margin-bottom: 5px;
        }
        
        .print-footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .status-completed { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .print-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
        
        .btn {
            padding: 8px 16px;
            margin-left: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <!-- 打印控制按钮 -->
    <div class="print-controls no-print">
        <button class="btn btn-primary" onclick="window.print()">
            <i class="fas fa-print"></i> 打印
        </button>
        <button class="btn btn-secondary" onclick="window.close()">
            <i class="fas fa-times"></i> 关闭
        </button>
    </div>

    <!-- 打印内容 -->
    <div class="print-content">
        <!-- 头部 -->
        <div class="print-header">
            <div class="print-title">学校食堂食材入库单</div>
            <div class="print-subtitle">Food Material Inbound Order</div>
        </div>

        <!-- 入库单信息 -->
        <div class="order-info">
            <div class="order-info-left">
                <div class="info-item">
                    <span class="info-label">入库单号：</span>
                    <span class="order-number"><?= htmlspecialchars($order['order_number']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">供应商：</span>
                    <span><?= htmlspecialchars($order['supplier_name']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">联系人：</span>
                    <span><?= htmlspecialchars($order['contact_person'] ?? '-') ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">联系电话：</span>
                    <span><?= htmlspecialchars($order['phone'] ?? '-') ?></span>
                </div>
            </div>
            <div class="order-info-right">
                <div class="info-item">
                    <span class="info-label">入库日期：</span>
                    <span><?= date('Y年m月d日', strtotime($order['inbound_date'])) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">操作员：</span>
                    <span><?= htmlspecialchars($order['operator_name'] ?? '系统') ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">状态：</span>
                    <?php
                    $status_class = [
                        'pending' => 'status-pending',
                        'completed' => 'status-completed',
                        'cancelled' => 'status-cancelled'
                    ];
                    $status_text = [
                        'pending' => '待处理',
                        'completed' => '已完成',
                        'cancelled' => '已取消'
                    ];
                    ?>
                    <span class="status-badge <?= $status_class[$order['status']] ?? '' ?>">
                        <?= $status_text[$order['status']] ?? $order['status'] ?>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">打印时间：</span>
                    <span><?= date('Y-m-d H:i:s') ?></span>
                </div>
            </div>
        </div>

        <!-- 入库明细表 -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 40px;">序号</th>
                    <th style="width: 120px;">食材名称</th>
                    <th style="width: 60px;">分类</th>
                    <th style="width: 100px;">批次号</th>
                    <th style="width: 70px;">计划数量</th>
                    <th style="width: 70px;">实际数量</th>
                    <th style="width: 60px;">单价</th>
                    <th style="width: 80px;">总价</th>
                    <th style="width: 80px;">生产日期</th>
                    <th style="width: 80px;">过期日期</th>
                    <th style="width: 60px;">验收状态</th>
                    <th>备注</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($items)): ?>
                    <?php 
                    $total_planned = 0;
                    $total_actual = 0;
                    $total_amount = 0;
                    ?>
                    <?php foreach ($items as $index => $item): ?>
                        <?php
                        $total_planned += $item['planned_quantity'];
                        $total_actual += $item['actual_quantity'];
                        $total_amount += $item['total_price'];
                        ?>
                        <tr>
                            <td class="number"><?= $index + 1 ?></td>
                            <td><?= htmlspecialchars($item['ingredient_name']) ?></td>
                            <td class="number"><?= htmlspecialchars($item['category_name'] ?? '-') ?></td>
                            <td><span class="batch-number"><?= htmlspecialchars($item['batch_number']) ?></span></td>
                            <td class="amount"><?= number_format($item['planned_quantity'], 2) ?></td>
                            <td class="amount"><?= number_format($item['actual_quantity'], 2) ?></td>
                            <td class="amount">¥<?= number_format($item['unit_price'], 2) ?></td>
                            <td class="amount">¥<?= number_format($item['total_price'], 2) ?></td>
                            <td class="number"><?= $item['production_date'] ? date('Y-m-d', strtotime($item['production_date'])) : '-' ?></td>
                            <td class="number"><?= date('Y-m-d', strtotime($item['expired_at'])) ?></td>
                            <td class="number">
                                <?php
                                $acceptance_text = [
                                    'pending' => '待验收',
                                    'accepted' => '已验收',
                                    'rejected' => '已拒收'
                                ];
                                echo $acceptance_text[$item['acceptance_status']] ?? $item['acceptance_status'];
                                ?>
                            </td>
                            <td><?= htmlspecialchars($item['notes'] ?: '-') ?></td>
                        </tr>
                    <?php endforeach; ?>
                    
                    <!-- 合计行 -->
                    <tr class="total-row">
                        <td colspan="4" style="text-align: center;">合计</td>
                        <td class="amount"><?= number_format($total_planned, 2) ?></td>
                        <td class="amount"><?= number_format($total_actual, 2) ?></td>
                        <td></td>
                        <td class="amount">¥<?= number_format($total_amount, 2) ?></td>
                        <td colspan="4"></td>
                    </tr>
                <?php else: ?>
                    <tr>
                        <td colspan="12" style="text-align: center; padding: 20px;">暂无入库明细</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>

        <!-- 备注 -->
        <?php if ($order['notes']): ?>
        <div style="margin-bottom: 20px;">
            <strong>备注：</strong><?= nl2br(htmlspecialchars($order['notes'])) ?>
        </div>
        <?php endif; ?>

        <!-- 签名区域 -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line"></div>
                <div>供应商签字</div>
            </div>
            <div class="signature-box">
                <div class="signature-line"></div>
                <div>验收员签字</div>
            </div>
            <div class="signature-box">
                <div class="signature-line"></div>
                <div>仓管员签字</div>
            </div>
            <div class="signature-box">
                <div class="signature-line"></div>
                <div>负责人签字</div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="print-footer">
            <div>学校食堂食材出入库管理系统 - 入库单</div>
            <div>打印时间：<?= date('Y年m月d日 H:i:s') ?> | 单据编号：<?= htmlspecialchars($order['order_number']) ?></div>
        </div>
    </div>

    <script>
        // 页面加载完成后自动聚焦到打印按钮
        window.onload = function() {
            // 可以在这里添加自动打印逻辑
            // window.print();
        };
        
        // 监听键盘事件
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html>
