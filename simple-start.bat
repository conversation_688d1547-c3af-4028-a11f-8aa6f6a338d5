@echo off
setlocal enabledelayedexpansion

title Simple PHP Server

echo.
echo ================================================================
echo           Simple PHP Development Server
echo ================================================================
echo.

REM Database configuration
set DB_HOST=************
set DB_PORT=3306
set DB_USER=sc
set DB_PASS=pw5K4SsM7kZsjdxy
set DB_NAME=sc

echo [1/3] Checking PHP...
php --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] PHP not found
    pause
    exit /b 1
)
echo [OK] PHP found

echo.
echo [2/3] Testing database connection...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% -e "SELECT 1;" %DB_NAME% >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Database connection failed
    echo Host: %DB_HOST%:%DB_PORT%
    pause
    exit /b 1
)
echo [OK] Database connected

echo.
echo [3/3] Creating simple PHP application...

REM Create a simple index.php if it doesn't exist
if not exist "index.php" (
    echo Creating index.php...
    (
        echo ^<?php
        echo // Simple Canteen Management System
        echo.
        echo // Database configuration
        echo $host = '%DB_HOST%';
        echo $port = %DB_PORT%;
        echo $dbname = '%DB_NAME%';
        echo $username = '%DB_USER%';
        echo $password = '%DB_PASS%';
        echo.
        echo try {
        echo     $pdo = new PDO^("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8", $username, $password^);
        echo     $pdo-^>setAttribute^(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION^);
        echo     $connection_status = "Connected successfully";
        echo } catch^(PDOException $e^) {
        echo     $connection_status = "Connection failed: " . $e-^>getMessage^(^);
        echo }
        echo.
        echo ?^>
        echo ^<!DOCTYPE html^>
        echo ^<html lang="zh-CN"^>
        echo ^<head^>
        echo     ^<meta charset="UTF-8"^>
        echo     ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^>
        echo     ^<title^>学校食堂食材出入库管理系统^</title^>
        echo     ^<style^>
        echo         body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        echo         .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba^(0,0,0,0.1^); }
        echo         h1 { color: #333; text-align: center; margin-bottom: 30px; }
        echo         .status { padding: 15px; border-radius: 5px; margin: 20px 0; }
        echo         .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        echo         .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        echo         .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        echo         .nav { display: flex; gap: 15px; margin: 20px 0; }
        echo         .nav a { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        echo         .nav a:hover { background: #0056b3; }
        echo         table { width: 100%%; border-collapse: collapse; margin: 20px 0; }
        echo         th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        echo         th { background: #f8f9fa; }
        echo     ^</style^>
        echo ^</head^>
        echo ^<body^>
        echo     ^<div class="container"^>
        echo         ^<h1^>🏫 学校食堂食材出入库管理系统^</h1^>
        echo.
        echo         ^<div class="status ^<?php echo strpos^($connection_status, 'successfully'^) !== false ? 'success' : 'error'; ?^>"^>
        echo             ^<strong^>数据库连接状态:^</strong^> ^<?php echo $connection_status; ?^>
        echo         ^</div^>
        echo.
        echo         ^<div class="info"^>
        echo             ^<strong^>系统信息:^</strong^>^<br^>
        echo             PHP版本: ^<?php echo PHP_VERSION; ?^>^<br^>
        echo             服务器时间: ^<?php echo date^('Y-m-d H:i:s'^); ?^>^<br^>
        echo             数据库: ^<?php echo $host . ':' . $port . '/' . $dbname; ?^>
        echo         ^</div^>
        echo.
        echo         ^<div class="nav"^>
        echo             ^<a href="?page=ingredients"^>食材管理^</a^>
        echo             ^<a href="?page=suppliers"^>供应商管理^</a^>
        echo             ^<a href="?page=inbound"^>入库管理^</a^>
        echo             ^<a href="?page=outbound"^>出库管理^</a^>
        echo             ^<a href="?page=inventory"^>库存查询^</a^>
        echo             ^<a href="?page=reports"^>报表统计^</a^>
        echo         ^</div^>
        echo.
        echo         ^<?php
        echo         $page = $_GET['page'] ?? 'home';
        echo.
        echo         switch^($page^) {
        echo             case 'ingredients':
        echo                 echo "^<h2^>食材管理^</h2^>";
        echo                 if^($pdo^) {
        echo                     try {
        echo                         $stmt = $pdo-^>query^("SELECT * FROM ingredients LIMIT 10"^);
        echo                         echo "^<table^>^<tr^>^<th^>ID^</th^>^<th^>名称^</th^>^<th^>分类^</th^>^<th^>单位^</th^>^<th^>状态^</th^>^</tr^>";
        echo                         while^($row = $stmt-^>fetch^(PDO::FETCH_ASSOC^)^) {
        echo                             echo "^<tr^>^<td^>{$row['id']}^</td^>^<td^>{$row['name']}^</td^>^<td^>{$row['category_id']}^</td^>^<td^>{$row['unit']}^</td^>^<td^>" . ^($row['status'] ? '启用' : '停用'^) . "^</td^>^</tr^>";
        echo                         }
        echo                         echo "^</table^>";
        echo                     } catch^(Exception $e^) {
        echo                         echo "^<p^>暂无数据或表不存在^</p^>";
        echo                     }
        echo                 }
        echo                 break;
        echo.
        echo             case 'suppliers':
        echo                 echo "^<h2^>供应商管理^</h2^>";
        echo                 if^($pdo^) {
        echo                     try {
        echo                         $stmt = $pdo-^>query^("SELECT * FROM suppliers LIMIT 10"^);
        echo                         echo "^<table^>^<tr^>^<th^>ID^</th^>^<th^>名称^</th^>^<th^>联系人^</th^>^<th^>电话^</th^>^<th^>评级^</th^>^</tr^>";
        echo                         while^($row = $stmt-^>fetch^(PDO::FETCH_ASSOC^)^) {
        echo                             echo "^<tr^>^<td^>{$row['id']}^</td^>^<td^>{$row['name']}^</td^>^<td^>{$row['contact_person']}^</td^>^<td^>{$row['phone']}^</td^>^<td^>{$row['rating']}星^</td^>^</tr^>";
        echo                         }
        echo                         echo "^</table^>";
        echo                     } catch^(Exception $e^) {
        echo                         echo "^<p^>暂无数据或表不存在^</p^>";
        echo                     }
        echo                 }
        echo                 break;
        echo.
        echo             case 'inventory':
        echo                 echo "^<h2^>库存查询^</h2^>";
        echo                 if^($pdo^) {
        echo                     try {
        echo                         $stmt = $pdo-^>query^("SELECT i.name, inv.current_quantity, inv.total_value FROM inventory inv JOIN ingredients i ON inv.ingredient_id = i.id LIMIT 10"^);
        echo                         echo "^<table^>^<tr^>^<th^>食材名称^</th^>^<th^>当前库存^</th^>^<th^>库存价值^</th^>^</tr^>";
        echo                         while^($row = $stmt-^>fetch^(PDO::FETCH_ASSOC^)^) {
        echo                             echo "^<tr^>^<td^>{$row['name']}^</td^>^<td^>{$row['current_quantity']}^</td^>^<td^>￥{$row['total_value']}^</td^>^</tr^>";
        echo                         }
        echo                         echo "^</table^>";
        echo                     } catch^(Exception $e^) {
        echo                         echo "^<p^>暂无数据或表不存在^</p^>";
        echo                     }
        echo                 }
        echo                 break;
        echo.
        echo             default:
        echo                 echo "^<h2^>系统概览^</h2^>";
        echo                 echo "^<p^>欢迎使用学校食堂食材出入库管理系统！^</p^>";
        echo                 echo "^<p^>请使用上方导航菜单访问各个功能模块。^</p^>";
        echo                 if^($pdo^) {
        echo                     try {
        echo                         $stmt = $pdo-^>query^("SELECT COUNT^(*^) as count FROM ingredients"^);
        echo                         $ingredient_count = $stmt-^>fetchColumn^(^);
        echo                         echo "^<p^>食材总数: $ingredient_count^</p^>";
        echo                     } catch^(Exception $e^) {
        echo                         echo "^<p^>数据库表尚未创建，请先运行数据库初始化脚本。^</p^>";
        echo                     }
        echo                 }
        echo         }
        echo         ?^>
        echo.
        echo         ^<div style="margin-top: 40px; text-align: center; color: #666;"^>
        echo             ^<p^>学校食堂食材出入库管理系统 v2.0^</p^>
        echo             ^<p^>技术支持: <EMAIL>^</p^>
        echo         ^</div^>
        echo     ^</div^>
        echo ^</body^>
        echo ^</html^>
    ) > index.php
    
    echo [OK] index.php created
)

echo.
echo ================================================================
echo                    Starting PHP Server
echo ================================================================
echo.
echo Server URL: http://localhost:8080
echo Document Root: %CD%
echo Database: %DB_HOST%:%DB_PORT%/%DB_NAME%
echo.
echo Press Ctrl+C to stop the server
echo ================================================================
echo.

REM Start PHP built-in server
php -S localhost:8080
