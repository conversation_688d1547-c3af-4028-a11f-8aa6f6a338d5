/* 食材管理模块样式 */

/* 输入组样式 */
.input-group {
    display: flex;
    align-items: center;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    overflow: hidden;
    background: white;
}

.input-group .form-control {
    border: none;
    border-radius: 0;
    flex: 1;
    padding: 10px 12px;
    font-size: 14px;
}

.input-group .form-control:focus {
    outline: none;
    box-shadow: none;
}

.input-group:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-group-text {
    background: #f9fafb;
    border: none;
    padding: 10px 12px;
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
    white-space: nowrap;
}

.input-group-text:first-child {
    border-right: 1px solid #e5e7eb;
}

.input-group-text:last-child {
    border-left: 1px solid #e5e7eb;
}

/* 标签说明样式 */
.label-note {
    font-weight: 400;
    color: #6b7280;
    font-size: 12px;
    margin-left: 4px;
}

/* 添加食材按钮美化 */
.btn-add-ingredient {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
    border: none !important;
    border-radius: 10px !important;
    padding: 10px 20px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.25) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn-add-ingredient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-add-ingredient:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(72, 187, 120, 0.35) !important;
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%) !important;
}

.btn-add-ingredient:hover::before {
    left: 100%;
}

.btn-add-ingredient:active {
    transform: translateY(-1px) !important;
}

.btn-add-ingredient i {
    font-size: 14px;
    margin-right: 6px;
}

/* 空状态下的添加按钮 */
.empty-state .btn-add-ingredient {
    margin-top: 20px;
    padding: 14px 28px !important;
    font-size: 16px !important;
}

/* 导入按钮美化 */
.btn-import {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
    border: none !important;
    border-radius: 10px !important;
    padding: 10px 20px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.25) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    color: white !important;
}

.btn-import::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-import:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(66, 153, 225, 0.35) !important;
    background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%) !important;
}

.btn-import:hover::before {
    left: 100%;
}

.btn-import:active {
    transform: translateY(-1px) !important;
}

/* 库存警告样式 */
.stock-warning {
    background-color: #fed7d7 !important;
    border-left: 4px solid #e53e3e;
}

.stock-warning:hover {
    background-color: #fbb6ce !important;
}

.stock-low {
    background-color: #fef5e7 !important;
    border-left: 4px solid #d69e2e;
}

.stock-low:hover {
    background-color: #faf089 !important;
}

/* 闪烁警告 */
.blink-warning {
    animation: blink 1.5s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 按钮组样式 */
.btn-group {
    display: flex;
    gap: 8px;
}

.btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: #4299e1;
    color: white;
    border-color: #4299e1;
}

.btn-primary:hover {
    background: #3182ce;
    border-color: #3182ce;
    color: white;
    text-decoration: none;
}

.btn-outline-secondary {
    background: transparent;
    color: #718096;
    border-color: #e2e8f0;
}

.btn-outline-secondary:hover {
    background: #f7fafc;
    color: #4a5568;
    border-color: #cbd5e0;
}

.btn-outline-info {
    background: transparent;
    color: #4299e1;
    border-color: #4299e1;
}

.btn-outline-info:hover {
    background: #4299e1;
    color: white;
}

.btn-sm {
    padding: 8px 14px;
    font-size: 13px;
    min-width: 36px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* 操作按钮组样式 */
.action-buttons {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    align-items: center;
}

.action-buttons .btn {
    border-radius: 6px;
    transition: all 0.2s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.action-buttons .btn i {
    margin-right: 4px;
    font-size: 12px;
}

.action-buttons .btn span {
    font-size: 12px;
    font-weight: 500;
}

.btn-outline-primary {
    background: transparent;
    color: #4299e1;
    border-color: #4299e1;
}

.btn-outline-primary:hover {
    background: #4299e1;
    color: white;
}

.btn-outline-danger {
    background: transparent;
    color: #e53e3e;
    border-color: #e53e3e;
}

.btn-outline-danger:hover {
    background: #e53e3e;
    color: white;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    table-layout: fixed;
}

.table th {
    background: #dbeafe;
    border-bottom: 2px solid #bfdbfe;
    font-weight: 600;
    color: #1e40af;
    padding: 12px 10px;
    font-size: 13px;
    text-align: left;
}

.table td {
    padding: 12px 10px;
    vertical-align: middle;
    border-bottom: 1px solid #e2e8f0;
    word-wrap: break-word;
}

/* 表格列宽优化 */
.table th:nth-child(1), .table td:nth-child(1) { width: 22%; } /* 食材信息 */
.table th:nth-child(2), .table td:nth-child(2) { width: 9%; }  /* 分类 */
.table th:nth-child(3), .table td:nth-child(3) { width: 7%; }  /* 计量单位 */
.table th:nth-child(4), .table td:nth-child(4) { width: 8%; }  /* 当前库存 */
.table th:nth-child(5), .table td:nth-child(5) { width: 8%; }  /* 最低库存 */
.table th:nth-child(6), .table td:nth-child(6) { width: 8%; }  /* 单价 */
.table th:nth-child(7), .table td:nth-child(7) { width: 7%; }  /* 状态 */
.table th:nth-child(8), .table td:nth-child(8) { width: 9%; }  /* 创建时间 */
.table th:nth-child(9), .table td:nth-child(9) { width: 22%; } /* 操作 */

/* 食材信息样式优化 */
.ingredient-info {
    line-height: 1.4;
}

.ingredient-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
    margin-bottom: 2px;
}

.ingredient-desc {
    font-size: 12px;
    color: #718096;
    margin-bottom: 2px;
    line-height: 1.3;
}

.ingredient-spec {
    font-size: 11px;
    color: #a0aec0;
    font-style: italic;
}

/* 分类和单位徽章优化 */
.category-badge, .unit-badge {
    font-size: 11px;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.category-badge {
    background: #e6fffa;
    color: #234e52;
    border: 1px solid #b2f5ea;
}

.unit-badge {
    background: #fef5e7;
    color: #744210;
    border: 1px solid #f6e05e;
}

/* 库存信息样式 */
.stock-info {
    text-align: center;
}

.stock-value {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
}

.stock-value.stock-normal {
    color: #38a169;
}

.stock-value.stock-low {
    color: #e53e3e;
}

.stock-unit {
    font-size: 10px;
    color: #a0aec0;
    text-transform: uppercase;
}

/* 最低库存信息 */
.min-stock-info {
    font-size: 13px;
    color: #4a5568;
    text-align: center;
    font-weight: 500;
}

/* 价格信息 */
.price-info {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    text-align: center;
}

/* 时间信息 */
.time-info {
    font-size: 12px;
    color: #718096;
    text-align: center;
    font-family: 'Courier New', monospace;
}

/* 状态开关样式 */
.status-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    margin: 0 auto;
}

.status-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e0;
    transition: 0.3s;
    border-radius: 24px;
}

.switch-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input:checked + .switch-slider {
    background-color: #48bb78;
}

input:checked + .switch-slider:before {
    transform: translateX(20px);
}

/* 内容区域优化 */
.content {
    padding: 20px 25px;
    max-width: none;
}

.content-header {
    margin-bottom: 20px;
    padding: 20px 25px;
}

/* 表格容器优化 */
.table-container {
    margin: 0;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.table tbody tr:nth-child(even):hover {
    background: #f1f3f4;
}

/* 徽章样式 */
.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.badge-success {
    background: #d1fae5;
    color: #065f46;
}

.badge-warning {
    background: #fed7aa;
    color: #9c4221;
}

.badge-danger {
    background: #fecaca;
    color: #991b1b;
}

.badge-secondary {
    background: #f1f5f9;
    color: #475569;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-label {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
}

.form-control {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    height: 40px;
    box-sizing: border-box;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

/* 卡片容器 */
.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
    background: #f8f9fa;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
    color: #4299e1;
}

.empty-state h5 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #2d3748;
}

.empty-state p {
    font-size: 16px;
    margin-bottom: 20px;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 16px 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-left: 4px solid #4299e1;
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
    max-width: 400px;
}

.notification-warning {
    border-left-color: #d69e2e;
    color: #9c4221;
}

.notification-info {
    border-left-color: #4299e1;
    color: #2b6cb0;
}

.notification-close {
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 4px;
    margin-left: auto;
}

.notification-close:hover {
    color: #2d3748;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 动画效果 */
.slide-in-up {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
        gap: 8px;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .table-container {
        overflow-x: auto;
        margin: 0 -10px;
    }

    .table {
        min-width: 800px;
        font-size: 12px;
    }

    .table th, .table td {
        padding: 8px 6px;
    }

    /* 移动端列宽调整 */
    .table th:nth-child(1), .table td:nth-child(1) { width: 25%; }
    .table th:nth-child(2), .table td:nth-child(2) { width: 8%; }
    .table th:nth-child(3), .table td:nth-child(3) { width: 6%; }
    .table th:nth-child(4), .table td:nth-child(4) { width: 8%; }
    .table th:nth-child(5), .table td:nth-child(5) { width: 8%; }
    .table th:nth-child(6), .table td:nth-child(6) { width: 8%; }
    .table th:nth-child(7), .table td:nth-child(7) { width: 6%; }
    .table th:nth-child(8), .table td:nth-child(8) { width: 8%; }
    .table th:nth-child(9), .table td:nth-child(9) { width: 23%; }
    
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* 表单容器样式 */
.form-container {
    max-width: 2600px;
    margin: 0 auto;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    padding: 30px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.form-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.form-title h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
}

.form-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

/* 表单网格 */
.form-grid {
    padding: 30px;
}

.form-section {
    margin-bottom: 30px;
    padding-bottom: 25px;
    border-bottom: 1px solid #e2e8f0;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: #48bb78;
    font-size: 16px;
}

/* 表单行和组 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-label.required::after {
    content: ' *';
    color: #e53e3e;
}

.form-control {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-control:focus {
    outline: none;
    border-color: #48bb78;
    background: white;
    box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1);
}

.form-control::placeholder {
    color: #a0aec0;
}

.form-text {
    font-size: 12px;
    color: #718096;
    margin-top: 4px;
}

/* 表单操作按钮 */
.form-actions {
    padding: 25px 30px;
    background: #f7fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.btn-submit {
    min-width: 150px;
    padding: 12px 28px;
    font-size: 15px;
    font-weight: 600;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border: none;
    border-radius: 10px;
    color: white;
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.btn-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(72, 187, 120, 0.35);
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
}

.btn-submit:hover::before {
    left: 100%;
}

.btn-submit:active {
    transform: translateY(-1px);
}

/* 响应式表单 */
@media (max-width: 768px) {
    .form-container {
        margin: 0 10px;
        border-radius: 12px;
    }

    .form-header {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .form-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .form-title h2 {
        font-size: 20px;
    }

    .form-grid {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-section {
        margin-bottom: 25px;
        padding-bottom: 20px;
    }

    .form-actions {
        padding: 20px;
        flex-direction: column-reverse;
        gap: 10px;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
        padding: 14px 20px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .form-header {
        padding: 15px;
    }

    .form-grid {
        padding: 15px;
    }

    .form-actions {
        padding: 15px;
    }
}

/* 导入模态框样式 */
.modal {
    display: none !important;
    position: fixed !important;
    z-index: 10000 !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(4px) !important;
    align-items: center !important;
    justify-content: center !important;
}

.modal.show {
    display: flex !important;
}

.modal-content {
    background-color: white !important;
    border-radius: 16px !important;
    width: 90% !important;
    max-width: 800px !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2) !important;
    animation: modalSlideIn 0.3s ease-out !important;
    position: relative !important;
    margin: 0 !important;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 16px 16px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.close:hover {
    opacity: 1;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    background: #f7fafc;
    border-radius: 0 0 16px 16px;
}

/* 导入步骤样式 */
.import-steps {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.step {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 16px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h4 {
    margin: 0 0 8px 0;
    color: #2d3748;
    font-size: 16px;
    font-weight: 600;
}

.step-content p {
    margin: 0 0 15px 0;
    color: #4a5568;
    line-height: 1.5;
}

.step-content ul {
    margin: 10px 0;
    padding-left: 20px;
    color: #4a5568;
}

.step-content li {
    margin-bottom: 5px;
}

/* 文件上传样式 */
.file-upload {
    position: relative;
    margin: 15px 0;
}

.file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: block;
    padding: 40px 20px;
    border: 2px dashed #cbd5e0;
    border-radius: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.file-upload-label:hover {
    border-color: #4299e1;
    background: #edf2f7;
}

.file-upload-label i {
    font-size: 32px;
    color: #4299e1;
    margin-bottom: 10px;
    display: block;
}

.file-upload-label span {
    display: block;
    color: #2d3748;
    font-weight: 500;
    margin-bottom: 5px;
}

.file-upload-label small {
    color: #718096;
    font-size: 12px;
}

/* 导入选项样式 */
.import-options {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    color: #2d3748;
    font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #cbd5e0;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #4299e1;
    border-color: #4299e1;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 进度条样式 */
.import-progress {
    text-align: center;
    padding: 30px 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4299e1, #3182ce);
    width: 0%;
    transition: width 0.3s ease;
    animation: progressAnimation 2s infinite;
}

@keyframes progressAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.progress-text {
    color: #4a5568;
    font-size: 14px;
    margin: 0;
}

/* 导入结果样式 */
.import-result {
    text-align: center;
    padding: 20px 0;
}

.result-summary h4 {
    color: #2d3748;
    margin: 0 0 20px 0;
    font-size: 18px;
}

.result-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-item.success .stat-number {
    color: #48bb78;
}

.stat-item.warning .stat-number {
    color: #ed8936;
}

.stat-item.error .stat-number {
    color: #e53e3e;
}

.error-details {
    background: #fed7d7;
    border: 1px solid #feb2b2;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    text-align: left;
}

.error-details h5 {
    color: #c53030;
    margin: 0 0 10px 0;
    font-size: 14px;
}

.error-details ul {
    margin: 0;
    padding-left: 20px;
    color: #c53030;
    font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 20px;
    }

    .import-steps {
        gap: 20px;
    }

    .step {
        flex-direction: column;
        gap: 10px;
    }

    .step-number {
        align-self: flex-start;
    }

    .result-stats {
        flex-direction: column;
        gap: 15px;
    }

    .modal-footer {
        flex-direction: column-reverse;
        gap: 10px;
    }

    .modal-footer .btn {
        width: 100%;
        justify-content: center;
    }
}
