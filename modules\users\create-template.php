<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-user-plus"></i>
                <?= $action === 'edit' ? '编辑人员' : '添加人员' ?>
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <?= htmlspecialchars($error) ?>
        </div>
        <?php endif; ?>

        <div class="form-container">
            <form method="POST" action="index.php?action=<?= $action === 'edit' ? 'update' : 'store' ?>" class="user-form">
                <?php if ($action === 'edit'): ?>
                    <input type="hidden" name="id" value="<?= $user['id'] ?? '' ?>">
                <?php endif; ?>

                <div class="form-grid">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-user"></i> 基本信息</h3>
                        
                        <div class="form-group">
                            <label for="username">用户名 <span class="required">*</span></label>
                            <input type="text" id="username" name="username" class="form-control" 
                                   value="<?= htmlspecialchars($user['username'] ?? '') ?>" required>
                            <small class="form-text">用于系统登录的唯一标识</small>
                        </div>

                        <div class="form-group">
                            <label for="name">姓名 <span class="required">*</span></label>
                            <input type="text" id="name" name="name" class="form-control" 
                                   value="<?= htmlspecialchars($user['name'] ?? '') ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="role">角色 <span class="required">*</span></label>
                            <select id="role" name="role" class="form-control" required>
                                <option value="">请选择角色</option>
                                <option value="admin" <?= ($user['role'] ?? '') === 'admin' ? 'selected' : '' ?>>管理员</option>
                                <option value="chef" <?= ($user['role'] ?? '') === 'chef' ? 'selected' : '' ?>>厨师</option>
                                <option value="buyer" <?= ($user['role'] ?? '') === 'buyer' ? 'selected' : '' ?>>采购员</option>
                                <option value="staff" <?= ($user['role'] ?? '') === 'staff' ? 'selected' : '' ?>>员工</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="department">部门</label>
                            <input type="text" id="department" name="department" class="form-control" 
                                   value="<?= htmlspecialchars($user['department'] ?? '') ?>"
                                   placeholder="如：厨房部、采购部、管理部">
                        </div>
                    </div>

                    <!-- 联系信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-address-book"></i> 联系信息</h3>
                        
                        <div class="form-group">
                            <label for="phone">手机号码</label>
                            <input type="tel" id="phone" name="phone" class="form-control" 
                                   value="<?= htmlspecialchars($user['phone'] ?? '') ?>"
                                   placeholder="请输入11位手机号码">
                        </div>

                        <div class="form-group">
                            <label for="email">邮箱地址</label>
                            <input type="email" id="email" name="email" class="form-control" 
                                   value="<?= htmlspecialchars($user['email'] ?? '') ?>"
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 
                        <?= $action === 'edit' ? '更新人员' : '添加人员' ?>
                    </button>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 取消
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* 表单容器 */
.form-container {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表单网格 */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
}

/* 表单分组 */
.form-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 24px;
    border: 1px solid #e9ecef;
}

.form-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-section h3 i {
    color: #667eea;
}

/* 表单组 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 6px;
    font-size: 14px;
}

.required {
    color: #e53e3e;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-text {
    font-size: 12px;
    color: #718096;
    margin-top: 4px;
}

/* 表单操作 */
.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.form-actions .btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.form-actions .btn:hover {
    transform: translateY(-2px);
}

/* 警告框 */
.alert {
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.alert-danger {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

/* 表单容器样式 - 与其他页面保持一致 */
.form-container {
    max-width: 2600px;
    margin: 0 auto;
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
