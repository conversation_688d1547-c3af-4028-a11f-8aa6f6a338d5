<?php
/**
 * 基于test.xlsx修正的订货单模板
 */

// 设置响应头
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="修正版test.xlsx模板.xlsx"');
header('Cache-Control: max-age=0');

/**
 * 创建修正版的订货单模板
 */
function createFixedTestTemplate()
{
    // 基于调试结果，创建100行20列的标准订货单格式
    $templateData = [];
    
    // 第1行：订单基本信息（关键：第2列必须有订单号）
    $row1 = array_fill(0, 20, '');
    $row1[0] = '订单号:';
    $row1[1] = 'DD' . date('YmdHis'); // 第2列：订单号（长度>10）
    $row1[4] = date('Y-m-d'); // 第5列：订单日期
    $templateData[] = $row1;
    
    // 第2行：联系信息（关键：第12列必须有联系人）
    $row2 = array_fill(0, 20, '');
    $row2[11] = '张三'; // 第12列：联系人
    $templateData[] = $row2;
    
    // 第3行：地址信息
    $row3 = array_fill(0, 20, '');
    $row3[1] = '学校食堂一楼'; // 第2列：送货地址
    $row3[11] = '13800138000'; // 第12列：联系电话
    $templateData[] = $row3;
    
    // 第4行：金额信息
    $row4 = array_fill(0, 20, '');
    $row4[1] = '1500.00'; // 第2列：订单金额
    $row4[5] = '1500.00'; // 第6列：实际金额
    $row4[11] = date('Y-m-d'); // 第12列：预期交货日期
    $templateData[] = $row4;
    
    // 第5行：空行
    $templateData[] = array_fill(0, 20, '');
    
    // 第6行：明细标题
    $headerRow = array_fill(0, 20, '');
    $headerRow[0] = '商品编码';
    $headerRow[1] = '商品名称';
    $headerRow[2] = '规格';
    $headerRow[3] = '单位';
    $headerRow[4] = '品牌';
    $headerRow[5] = '产地';
    $headerRow[6] = '保质期';
    $headerRow[7] = '单价';
    $headerRow[8] = '数量';
    $headerRow[9] = '小计';
    $headerRow[10] = '税率';
    $headerRow[11] = '实收数量';
    $headerRow[12] = '合格数量';
    $headerRow[13] = '不合格数量';
    $headerRow[14] = '损耗数量';
    $headerRow[15] = '拒收数量';
    $headerRow[16] = '备注';
    $templateData[] = $headerRow;
    
    // 第7-15行：示例明细数据
    $sampleItems = [
        ['VEG001', '白菜', '500g/包', '包', '绿源', '本地', '3天', '2.50', '20', '50.00', '0%', '20', '20', '0', '0', '0', '新鲜'],
        ['MEAT001', '猪肉', '1kg/块', 'kg', '优质', '本地', '2天', '25.00', '10', '250.00', '0%', '10', '10', '0', '0', '0', ''],
        ['FISH001', '鲫鱼', '500g/条', '条', '鲜活', '本地', '1天', '12.00', '15', '180.00', '0%', '15', '15', '0', '0', '0', '活鱼'],
        ['GRAIN001', '大米', '10kg/袋', '袋', '优质', '东北', '12个月', '45.00', '5', '225.00', '0%', '5', '5', '0', '0', '0', ''],
        ['OIL001', '食用油', '5L/桶', '桶', '金龙鱼', '本地', '18个月', '35.00', '3', '105.00', '0%', '3', '3', '0', '0', '0', ''],
        ['SEASON001', '生抽', '500ml/瓶', '瓶', '海天', '广东', '24个月', '8.50', '10', '85.00', '0%', '10', '10', '0', '0', '0', ''],
        ['VEG002', '土豆', '1kg/袋', 'kg', '新鲜', '本地', '7天', '3.00', '30', '90.00', '0%', '30', '30', '0', '0', '0', ''],
        ['MEAT002', '鸡蛋', '30个/盒', '盒', '土鸡蛋', '本地', '15天', '15.00', '8', '120.00', '0%', '8', '8', '0', '0', '0', ''],
        ['SEASON002', '老抽', '500ml/瓶', '瓶', '海天', '广东', '24个月', '9.50', '5', '47.50', '0%', '5', '5', '0', '0', '0', '']
    ];
    
    foreach ($sampleItems as $item) {
        $itemRow = array_fill(0, 20, '');
        for ($i = 0; $i < count($item); $i++) {
            $itemRow[$i] = $item[$i];
        }
        $templateData[] = $itemRow;
    }
    
    // 填充剩余行到100行
    while (count($templateData) < 100) {
        $templateData[] = array_fill(0, 20, '');
    }
    
    return createXlsxFile($templateData, '订货单');
}

/**
 * 创建XLSX文件
 */
function createXlsxFile($data, $sheetName = 'Sheet1')
{
    $tempDir = sys_get_temp_dir() . '/xlsx_' . uniqid();
    mkdir($tempDir);
    mkdir($tempDir . '/xl');
    mkdir($tempDir . '/xl/worksheets');
    mkdir($tempDir . '/_rels');
    mkdir($tempDir . '/xl/_rels');

    // 创建基本的XLSX文件结构
    createXlsxStructure($tempDir, $sheetName);
    
    // 处理数据
    $strings = [];
    $stringMap = [];
    foreach ($data as $row) {
        foreach ($row as $cell) {
            if (is_string($cell) && !is_numeric($cell) && !empty($cell)) {
                if (!isset($stringMap[$cell])) {
                    $stringMap[$cell] = count($strings);
                    $strings[] = $cell;
                }
            }
        }
    }

    // 创建共享字符串文件
    createSharedStrings($tempDir, $strings);
    
    // 创建工作表
    createWorksheet($tempDir, $data, $stringMap);

    // 创建ZIP文件
    return createZipFile($tempDir);
}

/**
 * 创建XLSX基本结构
 */
function createXlsxStructure($tempDir, $sheetName)
{
    // [Content_Types].xml
    $contentTypes = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml"/>
    <Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>
    <Override PartName="/xl/sharedStrings.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml"/>
</Types>';
    file_put_contents($tempDir . '/[Content_Types].xml', $contentTypes);

    // _rels/.rels
    $rels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/>
</Relationships>';
    file_put_contents($tempDir . '/_rels/.rels', $rels);

    // xl/_rels/workbook.xml.rels
    $workbookRels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/>
    <Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings" Target="sharedStrings.xml"/>
</Relationships>';
    file_put_contents($tempDir . '/xl/_rels/workbook.xml.rels', $workbookRels);

    // xl/workbook.xml
    $workbook = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
    <sheets>
        <sheet name="' . htmlspecialchars($sheetName, ENT_XML1, 'UTF-8') . '" sheetId="1" r:id="rId1"/>
    </sheets>
</workbook>';
    file_put_contents($tempDir . '/xl/workbook.xml', $workbook);
}

/**
 * 创建共享字符串文件
 */
function createSharedStrings($tempDir, $strings)
{
    $sharedStrings = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="' . count($strings) . '" uniqueCount="' . count($strings) . '">';
    foreach ($strings as $string) {
        $sharedStrings .= '<si><t>' . htmlspecialchars($string, ENT_XML1, 'UTF-8') . '</t></si>';
    }
    $sharedStrings .= '</sst>';
    file_put_contents($tempDir . '/xl/sharedStrings.xml', $sharedStrings);
}

/**
 * 创建工作表
 */
function createWorksheet($tempDir, $data, $stringMap)
{
    $worksheet = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
    <sheetData>';

    foreach ($data as $rowIndex => $row) {
        $rowNum = $rowIndex + 1;
        $worksheet .= '<row r="' . $rowNum . '">';
        
        foreach ($row as $colIndex => $cell) {
            $colLetter = chr(65 + ($colIndex % 26));
            if ($colIndex >= 26) {
                $colLetter = chr(64 + intval($colIndex / 26)) . chr(65 + ($colIndex % 26));
            }
            $cellRef = $colLetter . $rowNum;
            
            if (is_string($cell) && !is_numeric($cell) && !empty($cell)) {
                $stringIndex = $stringMap[$cell];
                $worksheet .= '<c r="' . $cellRef . '" t="s"><v>' . $stringIndex . '</v></c>';
            } elseif (!empty($cell)) {
                $worksheet .= '<c r="' . $cellRef . '"><v>' . htmlspecialchars($cell, ENT_XML1, 'UTF-8') . '</v></c>';
            }
        }
        
        $worksheet .= '</row>';
    }

    $worksheet .= '</sheetData></worksheet>';
    file_put_contents($tempDir . '/xl/worksheets/sheet1.xml', $worksheet);
}

/**
 * 创建ZIP文件
 */
function createZipFile($tempDir)
{
    $zipFile = tempnam(sys_get_temp_dir(), 'xlsx') . '.xlsx';
    $zip = new ZipArchive();
    
    if ($zip->open($zipFile, ZipArchive::CREATE) !== TRUE) {
        throw new Exception('无法创建XLSX文件');
    }

    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($tempDir));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $relativePath = str_replace($tempDir . DIRECTORY_SEPARATOR, '', $file->getPathname());
            $relativePath = str_replace('\\', '/', $relativePath);
            $zip->addFile($file->getPathname(), $relativePath);
        }
    }

    $zip->close();

    // 清理临时目录
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($tempDir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    foreach ($iterator as $file) {
        if ($file->isDir()) {
            rmdir($file->getPathname());
        } else {
            unlink($file->getPathname());
        }
    }
    rmdir($tempDir);

    return $zipFile;
}

try {
    // 创建修正版模板
    $excelFile = createFixedTestTemplate();
    
    // 输出文件内容
    readfile($excelFile);
    
    // 清理临时文件
    unlink($excelFile);
    
} catch (Exception $e) {
    // 如果创建失败，返回错误信息
    header('Content-Type: text/plain; charset=utf-8');
    echo '修正版模板生成失败: ' . $e->getMessage();
}
?>
