# 🏫 学校食堂食材出入库管理系统开发文档

## 📋 目录

- [项目概述](#一项目概述)
- [系统功能结构](#二系统功能结构)
- [技术架构](#三技术架构)
- [数据库设计](#四数据库设计)
- [关键模块设计](#五关键模块设计)
- [权限系统设计](#六权限系统设计)
- [安全与日志](#七安全与日志)
- [开发计划](#八开发计划)
- [部署指南](#九部署指南)
- [扩展功能](#十扩展功能)

---

## 一、项目概述

### 🎯 项目目标

建设一套规范、高效、可追溯的食材出入库管理系统，提升学校食堂的食品安全与库存管理能力。

### 🎭 目标用户

- **食堂管理员**：负责整体食材管理和系统配置
- **仓库员**：执行日常出入库操作
- **审核员**：负责数据审核和报表导出
- **校方监督人员**：查看统计报表和监督食品安全

### 🔧 技术选型

| 技术栈 | 选择 | 版本要求 | 说明 |
|--------|------|----------|------|
| **后端语言** | PHP | 8.1+ | 稳定性好，社区支持完善 |
| **后端框架** | Laravel | 10+ | 成熟的 MVC 框架，内置安全特性 |
| **前端框架** | Vue 3 + Element Plus | 3.3+ | 现代化 SPA，用户体验佳 |
| **备选前端** | Laravel Blade + Bootstrap | 5.3+ | 传统 SSR，开发简单 |
| **数据库** | MySQL | 8.0+ | 关系型数据库，事务支持完善 |
| **缓存** | Redis | 6.0+ | 提升系统性能 |
| **文件存储** | 本地存储/阿里云OSS | - | 支持图片和文档上传 |
| **部署环境** | Docker / LAMP | - | 容器化部署或传统部署 |

### 📊 核心业务价值

- **食品安全保障**：完整的食材追溯链，确保食品安全
- **库存精准管理**：实时库存监控，减少浪费和缺货
- **流程规范化**：标准化的出入库流程，提高工作效率
- **数据可视化**：直观的报表和图表，支持决策分析

---

## 二、系统功能结构

### 🏗️ 功能模块架构

```mermaid
graph TB
    A[食材出入库管理系统] --> B[基础数据管理]
    A --> C[业务流程管理]
    A --> D[监控预警]
    A --> E[统计分析]
    A --> F[系统管理]
    
    B --> B1[食材信息管理]
    B --> B2[供应商管理]
    B --> B3[分类单位管理]
    
    C --> C1[入库管理]
    C --> C2[出库管理]
    C --> C3[库存管理]
    
    D --> D1[库存预警]
    D --> D2[过期提醒]
    D --> D3[异常监控]
    
    E --> E1[报表统计]
    E --> E2[趋势分析]
    E --> E3[数据导出]
    
    F --> F1[用户权限]
    F --> F2[系统配置]
    F --> F3[数据备份]
```

### 📦 详细功能清单

#### 🥬 基础数据管理

- **食材信息管理**
  - ✅ 食材基础信息维护（名称、分类、单位、保质期等）
  - ✅ 食材图片上传和展示
  - ✅ 食材启用/停用状态管理
  - ✅ 批量导入食材信息

- **供应商管理**
  - ✅ 供应商基础信息维护
  - ✅ 供应商资质文件管理
  - ✅ 供应商评级和黑名单管理

#### 📥 入库管理

- **入库登记**
  - ✅ 食材入库信息录入
  - ✅ 称重图片上传（支持多张）
  - ✅ 采购单据上传（必填项）
  - ✅ 自动生成批次号
  - ✅ 保质期自动计算

- **入库审核**
  - ✅ 入库单据审核流程
  - ✅ 异常入库处理
  - ✅ 入库记录查询和统计

#### 📤 出库管理

- **出库操作**
  - ✅ 按批次出库（FIFO原则）
  - ✅ 用途标注（早餐/午餐/晚餐）
  - ✅ 出库数量验证
  - ✅ 库存自动更新

- **出库记录**
  - ✅ 出库历史查询
  - ✅ 出库统计分析
  - ✅ 异常出库处理

#### 📊 库存监控

- **实时库存**
  - ✅ 当前库存查看
  - ✅ 批次库存明细
  - ✅ 库存价值统计

- **预警系统**
  - ⚠️ 低库存预警
  - ⚠️ 过期食材提醒
  - ⚠️ 长期滞销预警

#### 📈 报表统计

- **基础报表**
  - 📋 日/周/月出入库明细
  - 📋 库存盘点报表
  - 📋 供应商采购统计

- **分析图表**
  - 📊 食材消耗趋势图
  - 📊 成本分析图表
  - 📊 库存周转率分析

---

## 三、技术架构

### 🏛️ 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A1[Vue 3 + Element Plus]
        A2[移动端适配]
    end
    
    subgraph "应用层"
        B1[Laravel API]
        B2[认证授权]
        B3[业务逻辑]
    end
    
    subgraph "数据层"
        C1[MySQL 数据库]
        C2[Redis 缓存]
        C3[文件存储]
    end
    
    subgraph "基础设施"
        D1[Nginx/Apache]
        D2[PHP-FPM]
        D3[Docker容器]
    end
    
    A1 --> B1
    A2 --> B1
    B1 --> C1
    B1 --> C2
    B1 --> C3
    B1 --> D2
    D1 --> D2
```

### 🔧 技术栈详情

| 层级 | 技术 | 版本 | 用途 |
|------|------|------|------|
| **前端** | Vue 3 | 3.3+ | 用户界面框架 |
| | Element Plus | 2.4+ | UI 组件库 |
| | Axios | 1.5+ | HTTP 客户端 |
| **后端** | Laravel | 10+ | Web 应用框架 |
| | PHP | 8.1+ | 服务端语言 |
| | Laravel Sanctum | 3.3+ | API 认证 |
| **数据库** | MySQL | 8.0+ | 主数据库 |
| | Redis | 6.0+ | 缓存和会话 |
| **部署** | Docker | 20+ | 容器化部署 |
| | Nginx | 1.20+ | Web 服务器 |

---

## 四、数据库设计

### 🗄️ 数据库表结构

#### 1. 食材信息表 (`ingredients`)

| 字段名 | 类型 | 长度 | 约束 | 描述 | 示例 |
|--------|------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键 | 1 |
| name | VARCHAR | 100 | NOT NULL, UNIQUE | 食材名称 | 土豆 |
| category_id | INT | - | FK | 分类ID | 1 |
| unit | VARCHAR | 20 | NOT NULL | 计量单位 | 斤 |
| shelf_life_days | INT | - | NOT NULL | 保质期（天） | 7 |
| min_stock | DECIMAL | 10,2 | DEFAULT 0 | 最低库存预警值 | 10.00 |
| image_path | VARCHAR | 255 | NULL | 食材图片路径 | /images/potato.jpg |
| description | TEXT | - | NULL | 食材描述 | 新鲜土豆，产地山东 |
| status | TINYINT | 1 | DEFAULT 1 | 状态(1启用0停用) | 1 |
| created_by | BIGINT | - | FK | 创建人ID | 1 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-01 10:00:00 |
| updated_at | TIMESTAMP | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-01 10:00:00 |

#### 2. 食材分类表 (`ingredient_categories`)

| 字段名 | 类型 | 长度 | 约束 | 描述 | 示例 |
|--------|------|------|------|------|------|
| id | INT | - | PK, AUTO_INCREMENT | 主键 | 1 |
| name | VARCHAR | 50 | NOT NULL, UNIQUE | 分类名称 | 蔬菜类 |
| code | VARCHAR | 20 | NOT NULL, UNIQUE | 分类编码 | VEG |
| sort_order | INT | - | DEFAULT 0 | 排序 | 1 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-01 10:00:00 |

#### 3. 供应商表 (`suppliers`)

| 字段名 | 类型 | 长度 | 约束 | 描述 | 示例 |
|--------|------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键 | 1 |
| name | VARCHAR | 100 | NOT NULL | 供应商名称 | 绿色农场有限公司 |
| contact_person | VARCHAR | 50 | NOT NULL | 联系人 | 张三 |
| phone | VARCHAR | 20 | NOT NULL | 联系电话 | 13800138000 |
| address | VARCHAR | 255 | NULL | 地址 | 北京市朝阳区xxx |
| license_number | VARCHAR | 50 | NULL | 营业执照号 | 91110000000000000X |
| qualification_files | JSON | - | NULL | 资质文件路径 | ["cert1.pdf", "cert2.pdf"] |
| rating | TINYINT | 1 | DEFAULT 5 | 评级(1-5星) | 5 |
| status | TINYINT | 1 | DEFAULT 1 | 状态(1正常0黑名单) | 1 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-01 10:00:00 |
| updated_at | TIMESTAMP | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-01 10:00:00 |

#### 4. 入库记录表 (`inbound_records`)

| 字段名 | 类型 | 长度 | 约束 | 描述 | 示例 |
|--------|------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键 | 1 |
| ingredient_id | BIGINT | - | FK, NOT NULL | 食材ID | 1 |
| supplier_id | BIGINT | - | FK, NOT NULL | 供应商ID | 1 |
| batch_number | VARCHAR | 64 | NOT NULL, UNIQUE | 批次号 | 20240101-001-A1B2 |
| quantity | DECIMAL | 10,2 | NOT NULL | 入库数量 | 100.00 |
| unit_price | DECIMAL | 10,2 | NOT NULL | 单价 | 3.50 |
| total_price | DECIMAL | 10,2 | GENERATED | 总价(自动计算) | 350.00 |
| production_date | DATE | - | NULL | 生产日期 | 2024-01-01 |
| expired_at | DATE | - | NOT NULL | 过期日期 | 2024-01-08 |
| weight_images | JSON | - | NULL | 称重图片路径 | ["weight1.jpg", "weight2.jpg"] |
| purchase_invoice | VARCHAR | 255 | NOT NULL | 采购单路径 | /invoices/20240101_001.pdf |
| quality_check | TINYINT | 1 | DEFAULT 1 | 质检状态(1合格0不合格) | 1 |
| notes | TEXT | - | NULL | 备注 | 质量良好 |
| status | TINYINT | 1 | DEFAULT 1 | 状态(1正常0作废) | 1 |
| created_by | BIGINT | - | FK, NOT NULL | 操作人ID | 1 |
| approved_by | BIGINT | - | FK, NULL | 审核人ID | 2 |
| approved_at | TIMESTAMP | - | NULL | 审核时间 | 2024-01-01 11:00:00 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-01 10:00:00 |
| updated_at | TIMESTAMP | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-01 10:00:00 |

#### 5. 出库记录表 (`outbound_records`)

| 字段名 | 类型 | 长度 | 约束 | 描述 | 示例 |
|--------|------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键 | 1 |
| ingredient_id | BIGINT | - | FK, NOT NULL | 食材ID | 1 |
| batch_number | VARCHAR | 64 | NOT NULL | 批次号 | 20240101-001-A1B2 |
| quantity | DECIMAL | 10,2 | NOT NULL | 出库数量 | 50.00 |
| unit_price | DECIMAL | 10,2 | NOT NULL | 单价 | 3.50 |
| total_price | DECIMAL | 10,2 | GENERATED | 总价(自动计算) | 175.00 |
| used_for | VARCHAR | 100 | NOT NULL | 用途 | 午餐 |
| meal_date | DATE | - | NOT NULL | 用餐日期 | 2024-01-01 |
| notes | TEXT | - | NULL | 备注 | 制作土豆丝 |
| created_by | BIGINT | - | FK, NOT NULL | 操作人ID | 1 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-01 10:00:00 |

#### 6. 库存表 (`inventory`)

| 字段名 | 类型 | 长度 | 约束 | 描述 | 示例 |
|--------|------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键 | 1 |
| ingredient_id | BIGINT | - | FK, NOT NULL, UNIQUE | 食材ID | 1 |
| current_quantity | DECIMAL | 10,2 | NOT NULL, DEFAULT 0 | 当前库存 | 50.00 |
| total_value | DECIMAL | 12,2 | NOT NULL, DEFAULT 0 | 库存总价值 | 175.00 |
| last_inbound_at | TIMESTAMP | - | NULL | 最后入库时间 | 2024-01-01 10:00:00 |
| last_outbound_at | TIMESTAMP | - | NULL | 最后出库时间 | 2024-01-01 12:00:00 |
| updated_at | TIMESTAMP | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-01 12:00:00 |

#### 7. 库存批次表 (`inventory_batches`)

| 字段名 | 类型 | 长度 | 约束 | 描述 | 示例 |
|--------|------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键 | 1 |
| ingredient_id | BIGINT | - | FK, NOT NULL | 食材ID | 1 |
| batch_number | VARCHAR | 64 | NOT NULL, UNIQUE | 批次号 | 20240101-001-A1B2 |
| remaining_quantity | DECIMAL | 10,2 | NOT NULL | 剩余数量 | 50.00 |
| unit_price | DECIMAL | 10,2 | NOT NULL | 单价 | 3.50 |
| expired_at | DATE | - | NOT NULL | 过期日期 | 2024-01-08 |
| status | TINYINT | 1 | DEFAULT 1 | 状态(1正常2即将过期3已过期) | 1 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-01 10:00:00 |
| updated_at | TIMESTAMP | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-01 10:00:00 |

#### 8. 用户表 (`users`)

| 字段名 | 类型 | 长度 | 约束 | 描述 | 示例 |
|--------|------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键 | 1 |
| username | VARCHAR | 50 | NOT NULL, UNIQUE | 用户名 | admin |
| email | VARCHAR | 100 | NOT NULL, UNIQUE | 邮箱 | <EMAIL> |
| password | VARCHAR | 255 | NOT NULL | 密码(加密) | $2y$10$... |
| real_name | VARCHAR | 50 | NOT NULL | 真实姓名 | 张三 |
| phone | VARCHAR | 20 | NULL | 手机号 | 13800138000 |
| avatar | VARCHAR | 255 | NULL | 头像路径 | /avatars/user1.jpg |
| status | TINYINT | 1 | DEFAULT 1 | 状态(1正常0禁用) | 1 |
| last_login_at | TIMESTAMP | - | NULL | 最后登录时间 | 2024-01-01 10:00:00 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-01 10:00:00 |
| updated_at | TIMESTAMP | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 | 2024-01-01 10:00:00 |

#### 9. 角色表 (`roles`)

| 字段名 | 类型 | 长度 | 约束 | 描述 | 示例 |
|--------|------|------|------|------|------|
| id | INT | - | PK, AUTO_INCREMENT | 主键 | 1 |
| name | VARCHAR | 50 | NOT NULL, UNIQUE | 角色名称 | 管理员 |
| code | VARCHAR | 20 | NOT NULL, UNIQUE | 角色编码 | admin |
| description | VARCHAR | 255 | NULL | 角色描述 | 系统管理员 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-01 10:00:00 |

#### 10. 权限表 (`permissions`)

| 字段名 | 类型 | 长度 | 约束 | 描述 | 示例 |
|--------|------|------|------|------|------|
| id | INT | - | PK, AUTO_INCREMENT | 主键 | 1 |
| name | VARCHAR | 50 | NOT NULL, UNIQUE | 权限名称 | 食材入库 |
| code | VARCHAR | 50 | NOT NULL, UNIQUE | 权限编码 | ingredient.inbound |
| module | VARCHAR | 30 | NOT NULL | 所属模块 | ingredient |
| description | VARCHAR | 255 | NULL | 权限描述 | 允许进行食材入库操作 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 | 2024-01-01 10:00:00 |

### 🔗 关联关系表

#### 用户角色关联表 (`user_roles`)

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| user_id | BIGINT | FK, NOT NULL | 用户ID |
| role_id | INT | FK, NOT NULL | 角色ID |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

#### 角色权限关联表 (`role_permissions`)

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| role_id | INT | FK, NOT NULL | 角色ID |
| permission_id | INT | FK, NOT NULL | 权限ID |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

### 📊 数据库索引设计

```sql
-- 食材表索引
CREATE INDEX idx_ingredients_category ON ingredients(category_id);
CREATE INDEX idx_ingredients_status ON ingredients(status);

-- 入库记录索引
CREATE INDEX idx_inbound_ingredient ON inbound_records(ingredient_id);
CREATE INDEX idx_inbound_supplier ON inbound_records(supplier_id);
CREATE INDEX idx_inbound_batch ON inbound_records(batch_number);
CREATE INDEX idx_inbound_date ON inbound_records(created_at);
CREATE INDEX idx_inbound_expired ON inbound_records(expired_at);

-- 出库记录索引
CREATE INDEX idx_outbound_ingredient ON outbound_records(ingredient_id);
CREATE INDEX idx_outbound_batch ON outbound_records(batch_number);
CREATE INDEX idx_outbound_date ON outbound_records(created_at);
CREATE INDEX idx_outbound_meal_date ON outbound_records(meal_date);

-- 库存批次索引
CREATE INDEX idx_batch_ingredient ON inventory_batches(ingredient_id);
CREATE INDEX idx_batch_expired ON inventory_batches(expired_at);
CREATE INDEX idx_batch_status ON inventory_batches(status);
```

---

## 五、关键模块设计

### 📦 入库模块

#### 🎯 核心功能

- **食材信息录入**：选择食材、录入数量、单价、生产日期
- **自动计算**：根据保质期自动计算过期日期和总价
- **批次管理**：系统自动生成唯一批次号
- **文件上传**：支持称重图片和采购单据上传
- **库存更新**：入库后自动更新库存数量和价值

#### 📋 业务流程

```mermaid
flowchart TD
    A[开始入库] --> B[选择食材]
    B --> C[录入基本信息]
    C --> D[上传称重图片]
    D --> E[上传采购单据]
    E --> F[系统验证]
    F --> G{验证通过?}
    G -->|是| H[生成批次号]
    G -->|否| I[显示错误信息]
    I --> C
    H --> J[更新库存]
    J --> K[记录入库日志]
    K --> L[入库完成]
```

#### 📁 文件上传规范

| 文件类型 | 格式要求 | 大小限制 | 数量限制 | 必填性 |
|----------|----------|----------|----------|--------|
| 称重图片 | JPG、PNG、WEBP | 2MB/张 | 最多3张 | 可选 |
| 采购单据 | PDF、JPG、PNG | 5MB | 1个 | **必填** |
| 质检报告 | PDF | 3MB | 1个 | 可选 |

#### 🏷️ 批次号规则

- **格式**：`YYYYMMDD-{ingredient_id}-{random}`
- **示例**：`20240101-032-A1B2C3`
- **说明**：日期(8位) + 食材ID(3位) + 随机码(6位)

#### ✅ 数据验证规则

- 数量必须大于0
- 单价必须大于0
- 生产日期不能晚于当前日期
- 过期日期必须晚于生产日期
- 采购单据必须上传

### 📤 出库模块

#### 🎯 核心功能

- **批次选择**：按FIFO原则自动推荐批次
- **数量控制**：验证出库数量不超过库存
- **用途标注**：记录食材用途和用餐时间
- **库存更新**：出库后自动减少库存数量

#### 📋 业务流程

```mermaid
flowchart TD
    A[开始出库] --> B[选择食材]
    B --> C[系统推荐批次]
    C --> D[确认出库数量]
    D --> E[选择用途]
    E --> F[系统验证]
    F --> G{库存充足?}
    G -->|是| H[执行出库]
    G -->|否| I[提示库存不足]
    I --> D
    H --> J[更新库存]
    J --> K[更新批次数量]
    K --> L[记录出库日志]
    L --> M[出库完成]
```

#### 🔄 FIFO出库策略

1. **优先级排序**：按过期日期升序排列
2. **自动推荐**：优先推荐即将过期的批次
3. **手动调整**：允许用户手动选择其他批次
4. **预警提示**：过期食材禁止出库并显示警告

#### 🍽️ 用途分类

- **早餐**：06:00-09:00
- **午餐**：11:00-14:00
- **晚餐**：17:00-20:00
- **加餐**：其他时间段
- **其他**：非餐饮用途

### 📊 报表统计模块

#### 📈 报表类型

##### 基础报表

- **日报表**：当日出入库明细
- **周报表**：一周出入库汇总
- **月报表**：月度出入库统计
- **库存报表**：当前库存状况

##### 分析报表

- **消耗趋势**：食材消耗量变化趋势
- **成本分析**：采购成本和消耗成本分析
- **供应商分析**：各供应商采购统计
- **浪费分析**：过期和损耗统计

#### 📊 图表展示

```mermaid
graph LR
    A[数据源] --> B[数据处理]
    B --> C[图表生成]
    C --> D[柱状图]
    C --> E[折线图]
    C --> F[饼图]
    C --> G[表格]
    D --> H[导出功能]
    E --> H
    F --> H
    G --> H
```

#### 📤 导出功能

- **Excel格式**：支持多工作表导出
- **PDF格式**：包含图表的完整报告
- **CSV格式**：纯数据导出，便于二次分析
- **图片格式**：单独导出图表为PNG/JPG

#### 🔍 查询条件

- **时间范围**：支持自定义日期区间
- **食材筛选**：按分类或具体食材筛选
- **供应商筛选**：按供应商查看采购情况
- **操作人筛选**：按操作人查看操作记录

---

## 六、权限系统设计

### 🔐 角色权限矩阵

| 功能模块 | 仓库员 | 审核员 | 管理员 | 说明 |
|----------|--------|--------|--------|------|
| **基础数据管理** |  |  |  |  |
| 食材信息维护 | ❌ | ❌ | ✅ | 仅管理员可维护基础数据 |
| 供应商管理 | ❌ | ❌ | ✅ | 仅管理员可管理供应商 |
| **入库管理** |  |  |  |  |
| 食材入库 | ✅ | ❌ | ✅ | 仓库员执行日常入库 |
| 上传称重图 | ✅ | ❌ | ✅ | 配合入库操作 |
| 上传采购单 | ✅ | ❌ | ✅ | 必填项，确保合规 |
| 入库审核 | ❌ | ✅ | ✅ | 审核员负责审核 |
| **出库管理** |  |  |  |  |
| 食材出库 | ✅ | ❌ | ✅ | 仓库员执行日常出库 |
| 批次选择 | ✅ | ❌ | ✅ | 支持FIFO和手动选择 |
| **库存管理** |  |  |  |  |
| 库存查看 | ✅ | ✅ | ✅ | 所有角色可查看 |
| 库存盘点 | ✅ | ✅ | ✅ | 支持盘点调整 |
| **报表统计** |  |  |  |  |
| 报表查看 | ✅ | ✅ | ✅ | 所有角色可查看 |
| 报表导出 | ❌ | ✅ | ✅ | 审核员以上可导出 |
| **系统管理** |  |  |  |  |
| 用户管理 | ❌ | ❌ | ✅ | 仅管理员可管理用户 |
| 系统配置 | ❌ | ❌ | ✅ | 仅管理员可配置系统 |
| 数据备份 | ❌ | ❌ | ✅ | 仅管理员可备份数据 |

### 🎭 角色定义

#### 仓库员 (Warehouse Staff)
- **主要职责**：执行日常出入库操作
- **权限范围**：入库、出库、库存查看、基础报表
- **操作限制**：不能修改基础数据，不能导出敏感报表

#### 审核员 (Auditor)
- **主要职责**：审核入库单据，导出分析报表
- **权限范围**：审核、报表查看和导出、库存监控
- **操作限制**：不能执行出入库操作，不能修改系统配置

#### 管理员 (Administrator)
- **主要职责**：系统管理和配置
- **权限范围**：所有功能权限
- **操作限制**：无限制

### 🔒 数据权限控制

#### 行级权限
- **操作人限制**：用户只能查看自己创建的记录
- **部门隔离**：支持多部门数据隔离（扩展功能）
- **时间范围**：限制用户查看特定时间范围的数据

#### 字段级权限
- **敏感信息**：价格信息仅管理员和审核员可见
- **供应商信息**：联系方式等敏感信息权限控制
- **操作日志**：详细日志仅管理员可查看

### 🛡️ 安全机制

#### 登录安全
- **密码策略**：强制复杂密码，定期更换
- **登录限制**：失败次数限制，账户锁定机制
- **会话管理**：超时自动登出，单点登录控制

#### 操作审计
- **操作日志**：记录所有关键操作
- **数据变更**：记录数据修改前后状态
- **异常监控**：异常操作实时告警

---

## 七、安全与日志

### 🔐 安全防护体系

#### 认证授权
- **多因素认证**：支持短信验证码、邮箱验证
- **JWT Token**：使用Laravel Sanctum进行API认证
- **权限中间件**：路由级别的权限控制
- **RBAC模型**：基于角色的访问控制

#### 数据安全
- **数据加密**：敏感数据加密存储
- **传输加密**：HTTPS强制加密传输
- **SQL注入防护**：使用ORM防止SQL注入
- **XSS防护**：输入输出过滤和转义

#### 文件安全
- **上传限制**：文件类型、大小、数量限制
- **病毒扫描**：文件上传病毒检测
- **访问控制**：文件访问权限控制
- **存储隔离**：敏感文件独立存储

### 📝 日志管理体系

#### 操作日志
```php
// 日志记录示例
[
    'user_id' => 1,
    'action' => 'ingredient.inbound',
    'description' => '食材入库：土豆 100斤',
    'ip_address' => '*************',
    'user_agent' => 'Mozilla/5.0...',
    'before_data' => null,
    'after_data' => ['quantity' => 100, 'price' => 350],
    'created_at' => '2024-01-01 10:00:00'
]
```

#### 系统日志
- **错误日志**：系统异常和错误记录
- **性能日志**：慢查询和性能监控
- **安全日志**：登录失败、权限异常等
- **业务日志**：关键业务操作记录

#### 日志分析
- **实时监控**：关键指标实时监控
- **异常告警**：异常情况自动告警
- **统计分析**：操作频次和趋势分析
- **审计报告**：定期生成审计报告

### 🔄 备份恢复策略

#### 数据备份
- **全量备份**：每日凌晨自动全量备份
- **增量备份**：每小时增量备份
- **异地备份**：备份文件异地存储
- **备份验证**：定期验证备份完整性

#### 恢复策略
- **快速恢复**：支持指定时间点恢复
- **分级恢复**：根据重要性分级恢复
- **灾难恢复**：完整的灾难恢复预案
- **恢复测试**：定期进行恢复演练

---

## 八、开发计划

### 📅 开发时间表

| 阶段 | 时间周期 | 主要任务 | 交付物 | 负责人 |
|------|----------|----------|--------|--------|
| **第1周** | 项目启动 | 环境搭建、需求确认 | 开发环境、需求文档 | 项目经理 |
| | | 数据库设计、技术选型 | 数据库脚本、技术方案 | 架构师 |
| **第2周** | 基础开发 | 用户认证、权限系统 | 登录模块、权限模块 | 后端开发 |
| | | 基础数据管理 | 食材管理、供应商管理 | 后端开发 |
| **第3周** | 核心功能 | 入库模块开发 | 入库功能、文件上传 | 全栈开发 |
| | | 出库模块开发 | 出库功能、批次管理 | 全栈开发 |
| **第4周** | 高级功能 | 库存管理、预警系统 | 库存模块、预警功能 | 全栈开发 |
| | | 报表统计模块 | 报表功能、图表展示 | 前端开发 |
| **第5周** | 测试优化 | 功能测试、性能优化 | 测试报告、优化方案 | 测试工程师 |
| | | 部署上线、用户培训 | 生产环境、培训材料 | 运维工程师 |

### 🎯 里程碑节点

#### 第1周里程碑：基础架构完成
- ✅ 开发环境搭建完成
- ✅ 数据库设计确认
- ✅ 技术架构搭建
- ✅ 项目框架初始化

#### 第2周里程碑：基础模块完成
- ✅ 用户登录注册功能
- ✅ 权限管理系统
- ✅ 食材信息管理
- ✅ 供应商管理

#### 第3周里程碑：核心业务完成
- ✅ 入库管理功能
- ✅ 出库管理功能
- ✅ 文件上传功能
- ✅ 批次管理功能

#### 第4周里程碑：完整功能实现
- ✅ 库存管理功能
- ✅ 预警系统
- ✅ 报表统计功能
- ✅ 数据导出功能

#### 第5周里程碑：系统上线
- ✅ 系统测试完成
- ✅ 性能优化完成
- ✅ 生产环境部署
- ✅ 用户培训完成

### 👥 团队分工

#### 项目经理 (1人)
- 项目计划制定和进度控制
- 需求管理和变更控制
- 团队协调和沟通管理
- 风险识别和问题解决

#### 架构师 (1人)
- 技术架构设计
- 数据库设计
- 技术选型和标准制定
- 代码审查和质量控制

#### 后端开发工程师 (2人)
- API接口开发
- 业务逻辑实现
- 数据库操作
- 安全机制实现

#### 前端开发工程师 (1人)
- 用户界面开发
- 交互逻辑实现
- 图表和报表展示
- 移动端适配

#### 测试工程师 (1人)
- 测试用例设计
- 功能测试执行
- 性能测试
- 缺陷跟踪管理

#### 运维工程师 (1人)
- 环境搭建和配置
- 部署脚本编写
- 监控系统搭建
- 备份恢复策略实施

### ⚠️ 风险控制

#### 技术风险
- **风险**：技术选型不当导致性能问题
- **应对**：前期充分调研，制定备选方案

#### 进度风险
- **风险**：需求变更导致进度延期
- **应对**：严格需求管理，控制变更范围

#### 质量风险
- **风险**：测试不充分导致线上问题
- **应对**：制定完善测试计划，多轮测试验证

#### 人员风险
- **风险**：关键人员离职影响项目进度
- **应对**：知识共享，关键模块多人参与

---

## 九、部署指南

### 🐳 Docker容器化部署

#### 环境要求
- Docker 20.0+
- Docker Compose 2.0+
- 服务器内存 4GB+
- 磁盘空间 50GB+

#### 部署步骤

```bash
# 1. 克隆项目代码
git clone https://github.com/school/canteen-management.git
cd canteen-management

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库等信息

# 3. 构建和启动容器
docker-compose up -d

# 4. 安装依赖和初始化
docker-compose exec app composer install
docker-compose exec app php artisan key:generate
docker-compose exec app php artisan migrate --seed

# 5. 配置文件权限
docker-compose exec app chown -R www-data:www-data storage bootstrap/cache
```

#### Docker Compose 配置示例

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www/html
      - ./storage/app/public:/var/www/html/public/storage
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: canteen_db
      MYSQL_USER: canteen_user
      MYSQL_PASSWORD: user_password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

  redis:
    image: redis:6.0-alpine
    ports:
      - "6379:6379"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app

volumes:
  mysql_data:
```

### 🖥️ 传统LAMP部署

#### 环境要求
- Linux (Ubuntu 20.04+ / CentOS 8+)
- Apache 2.4+ / Nginx 1.18+
- PHP 8.1+
- MySQL 8.0+
- Redis 6.0+

#### 安装步骤

```bash
# Ubuntu 系统安装示例

# 1. 更新系统
sudo apt update && sudo apt upgrade -y

# 2. 安装 PHP 8.1
sudo apt install software-properties-common
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install php8.1 php8.1-fpm php8.1-mysql php8.1-redis \
    php8.1-gd php8.1-curl php8.1-zip php8.1-xml php8.1-mbstring

# 3. 安装 MySQL 8.0
sudo apt install mysql-server-8.0
sudo mysql_secure_installation

# 4. 安装 Redis
sudo apt install redis-server

# 5. 安装 Nginx
sudo apt install nginx

# 6. 配置 Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# 7. 部署应用
cd /var/www
sudo git clone https://github.com/school/canteen-management.git
cd canteen-management
sudo composer install --optimize-autoloader --no-dev
sudo cp .env.example .env
# 编辑 .env 配置文件
sudo php artisan key:generate
sudo php artisan migrate --seed
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache
```

### ⚙️ 系统配置

#### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name canteen.school.edu.cn;
    root /var/www/canteen-management/public;
    index index.php;

    # 安全配置
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";

    # 文件上传大小限制
    client_max_body_size 10M;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
    }
}
```

#### 环境变量配置

```env
# 应用配置
APP_NAME="食堂食材管理系统"
APP_ENV=production
APP_KEY=base64:your-app-key-here
APP_DEBUG=false
APP_URL=https://canteen.school.edu.cn

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=canteen_db
DB_USERNAME=canteen_user
DB_PASSWORD=secure_password

# Redis 配置
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# 邮件配置
MAIL_MAILER=smtp
MAIL_HOST=smtp.school.edu.cn
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=mail_password
MAIL_ENCRYPTION=tls

# 文件存储配置
FILESYSTEM_DISK=local
# 如使用阿里云OSS
# FILESYSTEM_DISK=oss
# OSS_ACCESS_KEY_ID=your_access_key
# OSS_ACCESS_KEY_SECRET=your_secret_key
# OSS_BUCKET=your_bucket_name
# OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
```

### 📊 监控和维护

#### 系统监控
- **服务器监控**：CPU、内存、磁盘使用率
- **应用监控**：响应时间、错误率、并发数
- **数据库监控**：连接数、慢查询、锁等待
- **业务监控**：出入库频次、用户活跃度

#### 日常维护
- **日志清理**：定期清理应用和系统日志
- **数据备份**：每日自动备份数据库和文件
- **安全更新**：及时更新系统和应用补丁
- **性能优化**：定期分析性能瓶颈并优化

---

## 十、扩展功能

### 🚀 近期扩展功能

#### 移动端支持
- **响应式设计**：适配手机和平板设备
- **PWA应用**：支持离线使用和推送通知
- **扫码功能**：二维码扫描出入库
- **语音输入**：语音录入数量和备注

#### 智能化功能
- **OCR识别**：自动识别采购单据信息
- **智能推荐**：基于历史数据推荐采购量
- **异常检测**：自动检测异常出入库行为
- **预测分析**：预测食材需求量和过期风险

#### 集成功能
- **财务系统集成**：与学校财务系统对接
- **供应商平台**：供应商在线下单和配送跟踪
- **营养分析**：食材营养成分分析和搭配建议
- **菜谱管理**：菜谱与食材需求量关联

### 🌟 中期扩展功能

#### 多校区支持
- **多租户架构**：支持多个学校独立使用
- **数据隔离**：不同学校数据完全隔离
- **统一管理**：教育局统一监管平台
- **数据同步**：校区间数据同步和共享

#### 高级分析
- **BI报表**：商业智能分析报表
- **成本分析**：精细化成本核算和分析
- **供应链优化**：供应链效率分析和优化建议
- **风险评估**：食品安全风险评估模型

#### 自动化功能
- **自动补货**：基于消耗量自动生成采购计划
- **智能排班**：根据工作量智能安排人员
- **自动对账**：与供应商系统自动对账
- **智能预警**：多维度智能预警系统

### 🔮 远期扩展功能

#### 物联网集成
- **智能秤具**：电子秤数据自动上传
- **温湿度监控**：仓库环境实时监控
- **RFID标签**：食材RFID标签管理
- **智能货架**：智能货架重量监控

#### 人工智能
- **图像识别**：食材质量AI识别
- **需求预测**：AI预测食材需求
- **价格预测**：市场价格趋势预测
- **智能客服**：AI客服解答常见问题

#### 区块链技术
- **食品溯源**：区块链食品溯源体系
- **供应链透明**：供应链全程透明化
- **数据不可篡改**：关键数据区块链存储
- **智能合约**：自动化合约执行

### 💡 创新功能设想

#### 虚拟现实
- **VR培训**：食品安全VR培训系统
- **AR识别**：AR食材信息识别
- **3D仓库**：3D虚拟仓库管理

#### 社交功能
- **经验分享**：食堂管理经验分享平台
- **评价系统**：食材和供应商评价系统
- **社区互动**：学校食堂管理社区

#### 绿色环保
- **碳足迹追踪**：食材碳足迹计算
- **减废建议**：智能减废建议系统
- **环保报告**：环保效益分析报告

---

## 📞 联系方式

### 技术支持
- **邮箱**：<EMAIL>
- **电话**：400-123-4567
- **QQ群**：123456789

### 项目团队
- **项目经理**：张三 (<EMAIL>)
- **技术负责人**：李四 (<EMAIL>)
- **产品经理**：王五 (<EMAIL>)

### 文档版本
- **版本号**：v2.0
- **更新日期**：2024-01-01
- **下次更新**：2024-03-01

---

*本文档为学校食堂食材出入库管理系统的完整开发指南，涵盖了从需求分析到部署维护的全流程。如有疑问或建议，请联系项目团队。*
