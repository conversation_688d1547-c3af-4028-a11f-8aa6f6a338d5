@echo off
setlocal enabledelayedexpansion

title Install PDO MySQL Extension

echo.
echo ================================================================
echo           PHP PDO MySQL Extension Installer
echo ================================================================
echo.

echo [1/4] Checking PHP installation...

REM Get PHP version and path
for /f "tokens=2" %%i in ('php --version ^| findstr "PHP"') do set php_version=%%i
for /f "tokens=*" %%i in ('php -r "echo PHP_BINARY;"') do set php_path=%%i

echo PHP Version: %php_version%
echo PHP Path: %php_path%

REM Get PHP directory
for %%i in ("%php_path%") do set php_dir=%%~dpi

echo PHP Directory: %php_dir%

echo.
echo [2/4] Checking php.ini location...

REM Get php.ini path
for /f "tokens=*" %%i in ('php --ini ^| findstr "Loaded Configuration File"') do set ini_line=%%i
for /f "tokens=4*" %%i in ("%ini_line%") do set php_ini=%%j

if "%php_ini%"=="(none)" (
    echo No php.ini found, will create one...
    set php_ini=%php_dir%php.ini
    if not exist "%php_ini%" (
        echo Creating basic php.ini...
        echo ; Basic PHP Configuration > "%php_ini%"
        echo extension_dir = "%php_dir%ext" >> "%php_ini%"
        echo extension=pdo_mysql >> "%php_ini%"
        echo extension=mysqli >> "%php_ini%"
    )
) else (
    echo php.ini found: %php_ini%
)

echo.
echo [3/4] Checking extensions directory...

set ext_dir=%php_dir%ext
echo Extensions directory: %ext_dir%

if not exist "%ext_dir%" (
    echo Creating extensions directory...
    mkdir "%ext_dir%"
)

REM Check if PDO MySQL extension files exist
set pdo_mysql_dll=%ext_dir%\php_pdo_mysql.dll
set mysqli_dll=%ext_dir%\php_mysqli.dll

echo.
echo [4/4] Checking extension files...

if exist "%pdo_mysql_dll%" (
    echo [OK] php_pdo_mysql.dll found
) else (
    echo [WARNING] php_pdo_mysql.dll not found
    echo You need to download it from: https://windows.php.net/downloads/pecl/releases/
)

if exist "%mysqli_dll%" (
    echo [OK] php_mysqli.dll found
) else (
    echo [WARNING] php_mysqli.dll not found
)

echo.
echo ================================================================
echo                    Configuration Check
echo ================================================================

REM Check if extensions are enabled in php.ini
findstr /C:"extension=pdo_mysql" "%php_ini%" >nul
if errorlevel 1 (
    echo [ACTION NEEDED] Adding pdo_mysql to php.ini...
    echo extension=pdo_mysql >> "%php_ini%"
) else (
    echo [OK] pdo_mysql already in php.ini
)

findstr /C:"extension=mysqli" "%php_ini%" >nul
if errorlevel 1 (
    echo [ACTION NEEDED] Adding mysqli to php.ini...
    echo extension=mysqli >> "%php_ini%"
) else (
    echo [OK] mysqli already in php.ini
)

echo.
echo ================================================================
echo                    Installation Summary
echo ================================================================
echo.
echo PHP Directory: %php_dir%
echo PHP.ini File: %php_ini%
echo Extensions Dir: %ext_dir%
echo.

if exist "%pdo_mysql_dll%" (
    echo [OK] PDO MySQL extension file exists
) else (
    echo [ERROR] PDO MySQL extension file missing
    echo.
    echo SOLUTION: Download PHP extensions
    echo 1. Visit: https://windows.php.net/downloads/pecl/releases/
    echo 2. Download php_pdo_mysql.dll for your PHP version
    echo 3. Place it in: %ext_dir%
    echo 4. Restart your web server
)

echo.
echo ================================================================
echo                    Alternative Solutions
echo ================================================================
echo.
echo 1. Install XAMPP (Recommended):
echo    - Download from: https://www.apachefriends.org/
echo    - Includes PHP with all MySQL extensions
echo.
echo 2. Use Portable PHP:
echo    - Download from: https://windows.php.net/download/
echo    - Choose "Thread Safe" version with extensions
echo.
echo 3. Manual Configuration:
echo    - Edit: %php_ini%
echo    - Uncomment: extension=pdo_mysql
echo    - Uncomment: extension=mysqli
echo.

echo Testing current PHP extensions...
php -m | findstr -i mysql
if errorlevel 1 (
    echo [ERROR] No MySQL extensions found
) else (
    echo [OK] MySQL extensions detected
)

echo.
echo Press any key to test database connection...
pause >nul

REM Test database connection
echo Testing database connection...
php -r "
try {
    if (extension_loaded('mysqli')) {
        $mysqli = new mysqli('************', 'sc', 'pw5K4SsM7kZsjdxy', 'sc', 3306);
        if ($mysqli->connect_error) {
            echo 'Connection failed: ' . $mysqli->connect_error . PHP_EOL;
        } else {
            echo 'Database connection successful using MySQLi!' . PHP_EOL;
            $mysqli->close();
        }
    } else if (extension_loaded('pdo_mysql')) {
        $pdo = new PDO('mysql:host=************;port=3306;dbname=sc', 'sc', 'pw5K4SsM7kZsjdxy');
        echo 'Database connection successful using PDO!' . PHP_EOL;
    } else {
        echo 'No MySQL extensions available' . PHP_EOL;
    }
} catch (Exception $e) {
    echo 'Connection error: ' . $e->getMessage() . PHP_EOL;
}
"

echo.
pause
