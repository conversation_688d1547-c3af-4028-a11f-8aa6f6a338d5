<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-shopping-cart"></i>
                采购管理
            </h1>
            <div class="header-actions">
                <a href="?action=create" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    新建采购单
                </a>
                <a href="index.php?action=import" class="btn btn-success">
                    <i class="fas fa-upload"></i>
                    批量导入
                </a>
                <button type="button" class="btn btn-secondary" onclick="exportData()">
                    <i class="fas fa-download"></i>
                    导出
                </button>
            </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="search-bar" style="padding: 15px !important;">
            <form method="GET" class="search-bar-form" style="display: flex !important; flex-wrap: nowrap !important; gap: 10px !important; align-items: center !important; padding: 0 !important;">
                <div class="form-field text-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">采购信息</label>
                    <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search ?? '') ?>" placeholder="搜索采购单号或供应商" style="height: 36px !important; width: 200px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">状态</label>
                    <select class="form-control" name="status" style="height: 36px !important; width: 120px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部状态</option>
                        <option value="draft" <?= ($status ?? '') === 'draft' ? 'selected' : '' ?>>草稿</option>
                        <option value="pending" <?= ($status ?? '') === 'pending' ? 'selected' : '' ?>>待审核</option>
                        <option value="approved" <?= ($status ?? '') === 'approved' ? 'selected' : '' ?>>已审核</option>
                        <option value="completed" <?= ($status ?? '') === 'completed' ? 'selected' : '' ?>>已完成</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #3b82f6 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                <a href="index.php" class="btn btn-secondary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #6b7280 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </form>
        </div>

        <!-- 采购单列表 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>采购单信息</th>
                        <th>供应商</th>
                        <th>采购金额</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($purchase_orders)): ?>
                        <?php foreach ($purchase_orders as $order): ?>
                            <tr>
                                <td>
                                    <div class="order-info">
                                        <div class="order-number"><?= htmlspecialchars($order['order_number']) ?></div>
                                        <div class="order-desc"><?= $order['items_count'] ?? 0 ?> 个商品</div>
                                    </div>
                                </td>
                                <td>
                                    <span class="supplier-name"><?= htmlspecialchars($order['supplier_name'] ?? '') ?></span>
                                </td>
                                <td>
                                    <div class="amount-info">¥<?= number_format($order['total_amount'] ?? 0, 2) ?></div>
                                </td>
                                <td>
                                    <?php
                                    $statusConfig = [
                                        'draft' => ['class' => 'status-draft', 'text' => '草稿'],
                                        'pending' => ['class' => 'status-pending', 'text' => '待审核'],
                                        'approved' => ['class' => 'status-approved', 'text' => '已审核'],
                                        'completed' => ['class' => 'status-completed', 'text' => '已完成']
                                    ];
                                    $config = $statusConfig[$order['status']] ?? $statusConfig['draft'];
                                    ?>
                                    <span class="status-badge <?= $config['class'] ?>"><?= $config['text'] ?></span>
                                </td>
                                <td>
                                    <div class="time-info"><?= date('m-d H:i', strtotime($order['created_at'])) ?></div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="?action=view&id=<?= $order['id'] ?>"
                                           class="btn btn-sm btn-info" title="查看采购单详情">
                                            <i class="fas fa-eye"></i>
                                            <span>查看</span>
                                        </a>
                                        <?php if ($order['status'] === 'draft'): ?>
                                        <a href="?action=edit&id=<?= $order['id'] ?>"
                                           class="btn btn-sm btn-warning" title="编辑采购单">
                                            <i class="fas fa-edit"></i>
                                            <span>编辑</span>
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center text-muted">
                                <i class="fas fa-inbox"></i>
                                暂无采购单数据
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
/* 采购管理模块特有样式 */

.order-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.order-number {
    font-weight: 600;
    color: #2d3748;
    font-size: 15px;
    font-family: 'Courier New', monospace;
}

.order-desc {
    font-size: 12px;
    color: #718096;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

.supplier-name {
    color: #4a5568;
    font-weight: 500;
}

.amount-info {
    font-weight: 600;
    color: #2d3748;
    font-size: 16px;
}

.status-badge {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
}

.status-draft { background: #f7fafc; color: #2d3748; }
.status-pending { background: #fff5d6; color: #975a16; }
.status-approved { background: #c6f6d5; color: #22543d; }
.status-completed { background: #bee3f8; color: #2b6cb0; }

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.time-info {
    font-size: 14px;
    color: #4a5568;
}

@media (max-width: 768px) {
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 800px;
    }
}
</style>

<script>
function exportData() {
    alert('导出功能开发中...');
}
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>