<?php
$db = new PDO('mysql:host=************;dbname=sc;charset=utf8mb4', 'sc', 'pw5K4SsM7kZsjdxy');
$stmt = $db->query("SELECT * FROM inbound_records ORDER BY id DESC LIMIT 5");
echo "最新的入库记录:\n";
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    echo "ID: " . $row['id'] . "\n";
    echo "食材ID: " . $row['ingredient_id'] . "\n";
    echo "供应商ID: " . $row['supplier_id'] . "\n";
    echo "批次号: " . $row['batch_number'] . "\n";
    echo "数量: " . $row['quantity'] . "\n";
    echo "单价: " . $row['unit_price'] . "\n";
    echo "创建时间: " . $row['created_at'] . "\n";
    echo "---\n";
}
?>