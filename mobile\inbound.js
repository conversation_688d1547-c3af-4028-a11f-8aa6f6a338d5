/**
 * 移动端入库页面JavaScript
 */

// 全局变量
let currentStep = 1;
let addedItems = [];
let itemIndex = 0;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initInboundPage();
});

// 初始化入库页面
function initInboundPage() {
    // 初始化步骤
    updateStepIndicator();

    // 初始化采购单选择
    initPurchaseOrderSelect();

    // 检查URL参数，自动选择采购单
    checkUrlParams();

    // 初始化表单验证
    initFormValidation();

    // 初始化拍照功能
    initPhotoUpload();

    // 初始化表单提交
    initFormSubmit();
}

// 下一步
function nextStep() {
    if (validateCurrentStep()) {
        if (currentStep < 3) {
            currentStep++;
            showStep(currentStep);
            updateStepIndicator();
        }
    }
}

// 上一步
function prevStep() {
    if (currentStep > 1) {
        currentStep--;
        showStep(currentStep);
        updateStepIndicator();
    }
}

// 显示指定步骤
function showStep(step) {
    // 隐藏所有步骤
    document.querySelectorAll('.step-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // 显示当前步骤
    const currentSection = document.getElementById(`step${step}`);
    if (currentSection) {
        currentSection.classList.add('active');
    }
}

// 更新步骤指示器
function updateStepIndicator() {
    document.querySelectorAll('.progress-step').forEach((step, index) => {
        if (index + 1 <= currentStep) {
            step.classList.add('active');
        } else {
            step.classList.remove('active');
        }
    });
}

// 验证当前步骤
function validateCurrentStep() {
    switch (currentStep) {
        case 1:
            return validateStep1();
        case 2:
            return validateStep2();
        case 3:
            return validateStep3();
        default:
            return true;
    }
}

// 验证步骤1
function validateStep1() {
    const batchNumber = document.querySelector('input[name="batch_number"]').value.trim();
    const inboundDate = document.querySelector('input[name="inbound_date"]').value;
    const operatorName = document.querySelector('input[name="operator_name"]').value.trim();
    const supplierId = document.querySelector('select[name="supplier_id"]').value;
    
    if (!batchNumber) {
        showToast('请输入批次号', 'error');
        return false;
    }
    
    if (!inboundDate) {
        showToast('请选择入库日期', 'error');
        return false;
    }
    
    if (!operatorName) {
        showToast('请输入操作员姓名', 'error');
        return false;
    }
    
    if (!supplierId) {
        showToast('请选择供应商', 'error');
        return false;
    }
    
    return true;
}

// 验证步骤2
function validateStep2() {
    if (addedItems.length === 0) {
        showToast('请至少添加一个食材', 'error');
        return false;
    }
    
    return true;
}

// 验证步骤3
function validateStep3() {
    // 步骤3的验证（可选）
    return true;
}

// 添加食材
function addItem() {
    const ingredientSelect = document.getElementById('ingredientSelect');
    const quantityInput = document.getElementById('quantityInput');
    const priceInput = document.getElementById('priceInput');
    
    const ingredientId = ingredientSelect.value;
    const ingredientText = ingredientSelect.options[ingredientSelect.selectedIndex].text;
    const unit = ingredientSelect.options[ingredientSelect.selectedIndex].dataset.unit || '';
    const quantity = parseFloat(quantityInput.value) || 0;
    const price = parseFloat(priceInput.value) || 0;
    
    // 验证输入
    if (!ingredientId) {
        showToast('请选择食材', 'error');
        return;
    }
    
    if (quantity <= 0) {
        showToast('请输入有效的数量', 'error');
        return;
    }
    
    if (price <= 0) {
        showToast('请输入有效的单价', 'error');
        return;
    }
    
    // 检查是否已添加
    if (addedItems.find(item => item.ingredient_id === ingredientId)) {
        showToast('该食材已添加', 'error');
        return;
    }
    
    // 创建食材项目
    const item = {
        index: itemIndex++,
        ingredient_id: ingredientId,
        ingredient_name: ingredientText.split('(')[0].trim(),
        unit: unit,
        quantity: quantity,
        unit_price: price, // 统一使用 unit_price
        total: quantity * price
    };
    
    addedItems.push(item);
    renderItems();
    updateStep2Button();
    
    // 清空输入
    ingredientSelect.value = '';
    quantityInput.value = '';
    priceInput.value = '';
    
    showToast('食材添加成功', 'success');
}

// 渲染食材列表（兼容两种模式）
function renderItems() {
    // 检查是否有来自采购单的商品
    const hasOrderItems = addedItems.some(item => item.from_order);

    if (hasOrderItems) {
        // 使用采购单模式渲染
        renderOrderItems();
    } else {
        // 使用手动模式渲染
        renderManualItems();
    }
}

// 渲染手动添加的商品列表
function renderManualItems() {
    const itemsContainer = document.getElementById('itemsContainer');
    const itemsHeader = document.getElementById('itemsHeader');
    const itemsList = document.getElementById('itemsList');
    const itemsCount = document.getElementById('itemsCount');
    const itemsSummary = document.getElementById('itemsSummary');
    const totalAmount = document.getElementById('totalAmount');
    const emptyItems = document.getElementById('emptyItems');

    if (addedItems.length === 0) {
        itemsHeader.style.display = 'none';
        itemsSummary.style.display = 'none';
        emptyItems.style.display = 'block';
        return;
    }

    emptyItems.style.display = 'none';
    itemsHeader.style.display = 'flex';
    itemsSummary.style.display = 'block';
    itemsCount.textContent = addedItems.length;
    
    // 渲染食材列表
    itemsList.innerHTML = addedItems.map(item => `
        <div class="item-card ${item.from_order ? 'from-order' : ''}">
            <div class="item-header">
                <div class="item-name">
                    ${item.ingredient_name}
                    ${item.from_order ? '<span class="order-badge">采购单</span>' : ''}
                </div>
                <div class="item-price">¥${item.total.toFixed(2)}</div>
            </div>
            <div class="item-details">
                <div><strong>数量:</strong> ${item.quantity}${item.unit}</div>
                <div><strong>单价:</strong> ¥${item.unit_price.toFixed(2)}</div>
            </div>
            <div class="item-quantity">
                <label>实际数量:</label>
                <input type="number" name="items[${item.index}][actual_quantity]"
                       value="${item.quantity}" step="0.01" min="0" required>
                <input type="hidden" name="items[${item.index}][ingredient_id]" value="${item.ingredient_id}">
                <input type="hidden" name="items[${item.index}][unit_price]" value="${item.unit_price}">
            </div>
            <div class="item-photo">
                <button type="button" class="photo-btn" onclick="takeItemPhoto(${item.index})" title="拍照">
                    <i class="fas fa-camera"></i>
                </button>
                <input type="file" id="itemPhoto_${item.index}" name="items[${item.index}][weight_photo]"
                       accept="image/*" capture="environment" style="display: none;"
                       onchange="handleItemPhoto(${item.index})">
            </div>
            <button type="button" class="remove-btn" onclick="removeItem(${item.index})"
                    ${item.from_order ? 'title="来自采购单的商品"' : ''}>
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');
    
    // 更新总计
    const total = addedItems.reduce((sum, item) => sum + item.total, 0);
    totalAmount.textContent = `¥${total.toFixed(2)}`;
}

// 拍摄食材照片
function takeItemPhoto(index) {
    document.getElementById(`itemPhoto_${index}`).click();
}

// 处理食材照片
function handleItemPhoto(index) {
    const input = document.getElementById(`itemPhoto_${index}`);

    if (input.files.length > 0) {
        const file = input.files[0];

        // 更新商品数据
        const item = addedItems.find(item => item.index === index);
        if (item) {
            item.weight_photo = file;
            // 创建预览URL
            item.photo_url = URL.createObjectURL(file);
        }

        // 更新照片区域UI
        updatePhotoSection(index);

        // 更新商品状态
        updateItemStatus(index);

        // 更新统计信息
        if (typeof updateStats === 'function') {
            updateStats();
        }

        showToast('称重照片上传成功', 'success');
    }
}

// 更新照片区域UI
function updatePhotoSection(index, photoSection = null) {
    const item = addedItems.find(item => item.index === index);
    if (!item || !item.photo_url) return;

    // 如果没有传入photoSection，则查找它
    if (!photoSection) {
        photoSection = document.getElementById(`photoSection_${index}`);
    }

    if (!photoSection) return;

    // 创建新的照片区域HTML
    photoSection.innerHTML = `
        <div class="photo-actions">
            <button type="button" class="photo-btn has-photo" onclick="takeItemPhoto(${index})" title="重新拍摄">
                <i class="fas fa-check"></i>
            </button>
            <button type="button" class="view-photo-btn" onclick="viewItemPhoto(${index})" title="查看">
                <i class="fas fa-eye"></i>
            </button>
        </div>
        <input type="file"
               id="itemPhoto_${index}"
               name="items[${index}][weight_photo]"
               accept="image/*"
               capture="environment"
               style="display: none;"
               onchange="handleItemPhoto(${index})">
    `;
}

// 查看商品照片
function viewItemPhoto(index) {
    const item = addedItems.find(item => item.index === index);
    if (!item || !item.photo_url) {
        showToast('没有找到照片', 'error');
        return;
    }

    // 创建照片查看模态框
    showPhotoModal(item.photo_url, `${item.ingredient_name} - 称重照片`);
}

// 显示照片查看模态框
function showPhotoModal(imageUrl, title) {
    // 移除现有的模态框
    const existingModal = document.querySelector('.photo-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'photo-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closePhotoModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn" onclick="closePhotoModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <img src="${imageUrl}" alt="${title}" class="photo-preview">
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closePhotoModal()">
                    <i class="fas fa-times"></i> 关闭
                </button>
                <button class="btn btn-primary" onclick="retakePhoto('${imageUrl}')" title="重新拍摄">
                    <i class="fas fa-camera"></i>
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加样式
    addPhotoModalStyles();

    // 显示动画
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

// 关闭照片模态框
function closePhotoModal() {
    const modal = document.querySelector('.photo-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// 重新拍摄照片
function retakePhoto(currentUrl) {
    // 找到对应的商品
    const item = addedItems.find(item => item.photo_url === currentUrl);
    if (item) {
        // 关闭模态框
        closePhotoModal();

        // 触发拍照
        takeItemPhoto(item.index);
    }
}

// 添加照片模态框样式
function addPhotoModalStyles() {
    if (document.getElementById('photo-modal-styles')) return;

    const style = document.createElement('style');
    style.id = 'photo-modal-styles';
    style.textContent = `
        .photo-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .photo-modal.show {
            opacity: 1;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: relative;
            background: white;
            margin: 20px;
            border-radius: 16px;
            max-height: calc(100vh - 40px);
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px 16px 0 0;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            flex: 1;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .photo-preview {
            max-width: 100%;
            max-height: 100%;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        @media (max-width: 480px) {
            .modal-content {
                margin: 10px;
                max-height: calc(100vh - 20px);
            }

            .modal-header {
                padding: 15px;
            }

            .modal-body {
                padding: 15px;
            }

            .modal-footer {
                padding: 15px;
                flex-direction: column;
            }

            .modal-footer .btn {
                width: 100%;
                margin: 0;
            }
        }
    `;

    document.head.appendChild(style);
}

// 删除食材
function removeItem(index) {
    addedItems = addedItems.filter(item => item.index !== index);
    renderItems();
    updateStep2Button();
    showToast('食材已删除', 'info');
}

// 更新步骤2按钮状态
function updateStep2Button() {
    const step2NextBtn = document.getElementById('step2NextBtn');
    step2NextBtn.disabled = addedItems.length === 0;
}

// 检查URL参数，自动选择采购单
function checkUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const fromPurchase = urlParams.get('from_purchase');
    const orderId = urlParams.get('order_id');
    const orderNumber = urlParams.get('order_number');

    console.log('URL参数检查:', { fromPurchase, orderId, orderNumber });

    if (fromPurchase === '1' && orderId && orderNumber) {
        // 延迟执行，确保DOM已完全加载
        setTimeout(() => {
            selectPurchaseOrderById(orderId, orderNumber);
        }, 100);
    }
}

// 根据订单ID自动选择采购单
function selectPurchaseOrderById(orderId, orderNumber) {
    const purchaseOrderSelect = document.getElementById('purchase_order_select');

    if (!purchaseOrderSelect) {
        console.error('采购单选择框未找到');
        return;
    }

    // 遍历所有选项，找到匹配的采购单
    const options = purchaseOrderSelect.options;
    let found = false;

    for (let i = 0; i < options.length; i++) {
        const option = options[i];
        if (option.value && option.value !== '' && option.value !== 'create_new') {
            try {
                const orderData = JSON.parse(option.value);
                if (orderData.order_info && orderData.order_info.id == orderId) {
                    purchaseOrderSelect.selectedIndex = i;
                    // 触发change事件
                    purchaseOrderSelect.dispatchEvent(new Event('change'));
                    found = true;
                    console.log('自动选择了采购单:', orderNumber);
                    showToast(`已自动选择采购单：${orderNumber}`, 'success');
                    break;
                }
            } catch (e) {
                console.error('解析采购单选项数据失败:', e);
            }
        }
    }

    if (!found) {
        console.warn('未找到匹配的采购单:', orderId, orderNumber);
        showToast(`未找到采购单：${orderNumber}`, 'warning');
    }
}

// 初始化采购单选择
function initPurchaseOrderSelect() {
    const purchaseOrderSelect = document.getElementById('purchase_order_select');

    if (purchaseOrderSelect) {
        purchaseOrderSelect.addEventListener('change', function() {
            const selectedValue = this.value;
            
            console.log('=== 采购单选择调试 ===');
            console.log('选择的值:', selectedValue);

            if (selectedValue === 'create_new') {
                // 显示创建新采购单模式
                showCreateNewOrderMode();
                showToast('请填写采购单信息并添加商品', 'info');
            } else if (selectedValue && selectedValue !== '') {
                try {
                    console.log('原始采购单数据:', selectedValue);

                    // 尝试解析JSON数据
                    const orderGroup = JSON.parse(selectedValue);
                    console.log('解析后的采购单数据:', orderGroup);

                    // 验证数据结构
                    if (!orderGroup.order_info || !orderGroup.items) {
                        throw new Error('采购单数据结构不完整');
                    }
                    
                    console.log('商品数量:', orderGroup.items.length);
                    console.log('订单信息:', orderGroup.order_info);

                    loadPurchaseOrderData(orderGroup);
                    showToast('已加载采购单商品，请确认实际入库数量', 'success');
                    
                    console.log('加载完成，addedItems数量:', addedItems.length);
                    console.log('addedItems内容:', addedItems);
                } catch (e) {
                    console.error('解析采购单数据失败:', e);
                    console.error('错误的数据:', selectedValue);
                    showToast('采购单数据格式错误: ' + e.message, 'error');
                }
            } else {
                // 清空显示，回到手动输入模式
                clearOrderData();
                showManualInputMode();
            }
            
            console.log('=== 采购单选择调试结束 ===');
        });
    }
}

// 加载采购单数据
function loadPurchaseOrderData(orderGroup) {
    const orderInfo = orderGroup.order_info;
    const items = orderGroup.items;

    // 显示采购单信息
    showOrderInfo(orderInfo);

    // 填充基本信息
    fillBasicInfo(orderInfo);

    // 预加载商品到步骤2
    preloadOrderItems(items);

    // 隐藏手动输入区域
    hideManualInputArea();

    // 设置步骤2为采购单模式
    setStep2PurchaseOrderMode();

    // 设置隐藏字段
    document.querySelector('input[name="order_id"]').value = orderInfo.id;
    document.querySelector('input[name="is_new_order"]').value = '0';
}

// 显示采购单信息
function showOrderInfo(orderInfo) {
    const orderInfoDisplay = document.getElementById('order_info_display');
    const orderTitle = document.getElementById('order_title');
    const orderSupplier = document.getElementById('order_supplier');
    const orderDate = document.getElementById('order_date');
    const orderAmount = document.getElementById('order_amount');

    orderTitle.textContent = orderInfo.order_number;
    orderSupplier.textContent = orderInfo.supplier_name;
    orderDate.textContent = orderInfo.order_date;
    orderAmount.textContent = '¥' + parseFloat(orderInfo.order_amount).toFixed(2);

    orderInfoDisplay.style.display = 'block';
}

// 填充基本信息
function fillBasicInfo(orderInfo) {
    // 填充供应商
    const supplierSelect = document.querySelector('select[name="supplier_id"]');
    if (supplierSelect) {
        supplierSelect.value = orderInfo.supplier_id;
        // 不要禁用select，而是设置为只读样式，确保值能被提交
        supplierSelect.style.backgroundColor = '#f8f9fa';
        supplierSelect.style.pointerEvents = 'none';

        // 添加一个隐藏字段确保supplier_id被提交
        let hiddenSupplierInput = document.querySelector('input[name="supplier_id_hidden"]');
        if (!hiddenSupplierInput) {
            hiddenSupplierInput = document.createElement('input');
            hiddenSupplierInput.type = 'hidden';
            hiddenSupplierInput.name = 'supplier_id_hidden';
            document.getElementById('inboundForm').appendChild(hiddenSupplierInput);
        }
        hiddenSupplierInput.value = orderInfo.supplier_id;
    }

    // 其他字段保持默认值
}

// 预加载采购单商品
function preloadOrderItems(items) {
    // 清空现有商品
    addedItems = [];
    itemIndex = 0;

    // 添加采购单中的商品
    items.forEach(item => {
        const newItem = {
            index: itemIndex++,
            ingredient_id: item.ingredient_id,
            ingredient_name: item.ingredient_name,
            unit: item.ingredient_unit,
            quantity: parseFloat(item.quantity),
            unit_price: parseFloat(item.unit_price), // 使用 unit_price 而不是 price
            total: parseFloat(item.total_price),
            from_order: true, // 标记来自采购单
            actual_quantity: parseFloat(item.quantity), // 默认实际数量等于采购数量
            weight_photo: null // 称重照片
        };
        addedItems.push(newItem);
    });

    // 渲染商品列表
    renderOrderItems();

    // 更新步骤2按钮状态
    updateStep2Button();
}

// 显示创建新采购单模式
function showCreateNewOrderMode() {
    // 隐藏采购单信息显示
    document.getElementById('order_info_display').style.display = 'none';

    // 显示手动输入区域
    showManualInputArea();

    // 设置步骤2为手动模式
    setStep2ManualMode();

    // 清空商品列表
    addedItems = [];
    itemIndex = 0;
    renderOrderItems();
    updateStep2Button();

    // 设置为创建新采购单模式
    document.querySelector('input[name="is_new_order"]').value = '1';

    // 启用供应商选择
    const supplierSelect = document.querySelector('select[name="supplier_id"]');
    if (supplierSelect) {
        supplierSelect.disabled = false;
        supplierSelect.style.backgroundColor = '';
        supplierSelect.style.pointerEvents = '';
    }

    // 移除隐藏的supplier_id字段
    const hiddenSupplierInput = document.querySelector('input[name="supplier_id_hidden"]');
    if (hiddenSupplierInput) {
        hiddenSupplierInput.remove();
    }
}

// 显示手动输入模式
function showManualInputMode() {
    // 隐藏采购单信息显示
    document.getElementById('order_info_display').style.display = 'none';

    // 显示手动输入区域
    showManualInputArea();

    // 设置步骤2为手动模式
    setStep2ManualMode();

    // 清空商品列表
    addedItems = [];
    itemIndex = 0;
    renderOrderItems();
    updateStep2Button();

    // 重置隐藏字段
    document.querySelector('input[name="order_id"]').value = '';
    document.querySelector('input[name="is_new_order"]').value = '0';

    // 启用供应商选择
    const supplierSelect = document.querySelector('select[name="supplier_id"]');
    if (supplierSelect) {
        supplierSelect.disabled = false;
        supplierSelect.value = '';
    }
}

// 显示手动输入区域
function showManualInputArea() {
    const manualInputArea = document.getElementById('manual_input_area');
    if (manualInputArea) {
        manualInputArea.classList.remove('hidden');
    }
}

// 隐藏手动输入区域
function hideManualInputArea() {
    const manualInputArea = document.getElementById('manual_input_area');
    if (manualInputArea) {
        manualInputArea.classList.add('hidden');
    }
}

// 设置步骤2为采购单模式
function setStep2PurchaseOrderMode() {
    // 隐藏手动添加表单
    document.getElementById('manualAddForm').style.display = 'none';

    // 更新步骤描述
    document.getElementById('step2Description').textContent = '请确认采购单商品的实际重量并拍照验证';

    // 更新标题
    document.getElementById('itemsTitle').textContent = '采购单商品';
}

// 设置步骤2为手动模式
function setStep2ManualMode() {
    // 显示手动添加表单
    document.getElementById('manualAddForm').style.display = 'block';

    // 更新步骤描述
    document.getElementById('step2Description').textContent = '选择食材并输入数量信息';

    // 更新标题
    document.getElementById('itemsTitle').textContent = '已添加食材';
}

// 渲染采购单商品列表
function renderOrderItems() {
    const itemsContainer = document.getElementById('itemsContainer');
    const itemsHeader = document.getElementById('itemsHeader');
    const itemsList = document.getElementById('itemsList');
    const itemsCount = document.getElementById('itemsCount');
    const itemsSummary = document.getElementById('itemsSummary');
    const totalAmount = document.getElementById('totalAmount');
    const emptyItems = document.getElementById('emptyItems');

    if (addedItems.length === 0) {
        itemsHeader.style.display = 'none';
        itemsSummary.style.display = 'none';
        emptyItems.style.display = 'block';
        return;
    }

    emptyItems.style.display = 'none';
    itemsHeader.style.display = 'flex';
    itemsSummary.style.display = 'none'; // 隐藏金额汇总
    itemsCount.textContent = addedItems.length;

    // 更新统计信息
    if (typeof updateStats === 'function') {
        updateStats();
    }

    // 显示分类筛选
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.style.display = 'block';

        // 初始化分类筛选
        if (typeof initCategoryFilters === 'function') {
            initCategoryFilters();
        }
    }

    // 渲染采购单商品列表（表格形式）
    console.log('=== 渲染商品列表调试 ===');
    console.log('准备渲染的商品数量:', addedItems.length);
    
    const itemsHtml = addedItems.map((item, arrayIndex) => {
        console.log(`渲染商品${arrayIndex}(原index:${item.index}):`, item.ingredient_name);
        return `
            <div class="order-item-table-row" data-index="${item.index}" data-array-index="${arrayIndex}">
                <div class="item-name-cell">
                    ${item.ingredient_name}
                </div>

                <div class="item-quantity-cell">
                    ${item.quantity}${item.unit}
                </div>

                <div class="item-weight-cell">
                    <div class="weight-input-compact">
                        <input type="number"
                               name="items[${arrayIndex}][actual_quantity]"
                               class="weight-input-small"
                               value="${item.actual_quantity}"
                               step="0.01"
                               min="0"
                               placeholder="0.00"
                               onchange="updateItemWeight(${item.index}, this.value)"
                               required>
                        <span class="weight-unit-small">${item.unit}</span>
                    </div>
                </div>

                <div class="item-photo-cell" id="photoSection_${item.index}">
                    ${item.photo_url ? `
                        <div class="photo-actions-compact">
                            <button type="button" class="photo-btn-small has-photo" onclick="takeItemPhoto(${item.index})" title="重新拍摄">
                                <i class="fas fa-check"></i>
                            </button>
                            <button type="button" class="view-photo-btn-small" onclick="viewItemPhoto(${item.index})" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    ` : `
                        <button type="button"
                                class="photo-btn-small"
                                onclick="takeItemPhoto(${item.index})"
                                title="拍照">
                            <i class="fas fa-camera"></i>
                        </button>
                    `}
                    <input type="file"
                           id="itemPhoto_${item.index}"
                           name="items[${arrayIndex}][weight_photo]"
                           accept="image/*"
                           capture="environment"
                           style="display: none;"
                           onchange="handleItemPhoto(${item.index})">
                </div>

                <div class="item-status-cell" id="itemStatus_${item.index}">
                    <i class="fas fa-clock text-warning"></i>
                </div>

                <!-- 隐藏字段使用连续的数组索引 -->
                <input type="hidden" name="items[${arrayIndex}][ingredient_id]" value="${item.ingredient_id}">
                <input type="hidden" name="items[${arrayIndex}][unit_price]" value="${item.unit_price}">
                <input type="hidden" name="items[${arrayIndex}][order_quantity]" value="${item.quantity}">
            </div>
        `;
    }).join('');
    
    itemsList.innerHTML = `
        <!-- 表头 -->
        <div class="items-table-header">
            <div class="header-name">商品名称</div>
            <div class="header-quantity">采购数量</div>
            <div class="header-weight">实际重量</div>
            <div class="header-photo">拍照</div>
            <div class="header-status">状态</div>
        </div>

        <!-- 商品列表 -->
        ${itemsHtml}
    `;
    
    console.log('HTML渲染完成');
    console.log('生成的HTML长度:', itemsList.innerHTML.length);
    
    // 验证渲染后的DOM
    setTimeout(() => {
        const renderedItems = document.querySelectorAll('.order-item-table-row');
        console.log('DOM中实际渲染的商品行数:', renderedItems.length);
        
        const hiddenInputs = document.querySelectorAll('input[name^="items["][name$="][ingredient_id]"]');
        console.log('DOM中的ingredient_id隐藏字段数量:', hiddenInputs.length);
    }, 100);
    
    console.log('=========================');

    // 更新总计
    updateTotalAmount();
}

// 更新商品重量
function updateItemWeight(index, weight) {
    const item = addedItems.find(item => item.index === index);
    if (item) {
        item.actual_quantity = parseFloat(weight) || 0;
        updateItemStatus(index);
        updateTotalAmount();
    }
}

// 更新商品状态
function updateItemStatus(index) {
    const item = addedItems.find(item => item.index === index);
    const statusElement = document.getElementById(`itemStatus_${index}`);

    if (!item || !statusElement) return;

    const hasWeight = item.actual_quantity > 0;
    const hasPhoto = item.weight_photo !== null;

    if (hasWeight && hasPhoto) {
        statusElement.innerHTML = '<i class="fas fa-check-circle text-success"></i>';
    } else if (hasWeight) {
        statusElement.innerHTML = '<i class="fas fa-camera text-info"></i>';
    } else {
        statusElement.innerHTML = '<i class="fas fa-clock text-warning"></i>';
    }
}

// 更新总金额（隐藏显示，但保留计算用于后端）
function updateTotalAmount() {
    const total = addedItems.reduce((sum, item) => {
        return sum + (item.actual_quantity * item.unit_price);
    }, 0);

    // 保存总金额用于表单提交
    window.currentTotalAmount = total;

    // 如果有总金额显示元素，则更新（主要用于手动模式）
    const totalAmountElement = document.getElementById('totalAmount');
    if (totalAmountElement) {
        totalAmountElement.textContent = `¥${total.toFixed(2)}`;
    }
}

// 清空采购单数据
function clearOrderData() {
    // 清空商品列表
    addedItems = [];
    itemIndex = 0;
    renderOrderItems();
    updateStep2Button();

    // 设置为手动模式
    setStep2ManualMode();

    // 重置表单
    document.querySelector('input[name="batch_number"]').value = 'BATCH' + new Date().toISOString().replace(/[-:T.]/g, '').substr(0, 14);
    document.querySelector('input[name="inbound_date"]').value = new Date().toISOString().substr(0, 10);
    document.querySelector('input[name="operator_name"]').value = '管理员';

    // 重置供应商选择
    const supplierSelect = document.querySelector('select[name="supplier_id"]');
    if (supplierSelect) {
        supplierSelect.disabled = false;
        supplierSelect.style.backgroundColor = '';
        supplierSelect.style.pointerEvents = '';
        supplierSelect.value = '';
    }

    // 移除隐藏的supplier_id字段
    const hiddenSupplierInput = document.querySelector('input[name="supplier_id_hidden"]');
    if (hiddenSupplierInput) {
        hiddenSupplierInput.remove();
    }
}

// 初始化表单验证
function initFormValidation() {
    // 供应商选择变化时的处理
    const supplierSelect = document.querySelector('select[name="supplier_id"]');
    if (supplierSelect) {
        supplierSelect.addEventListener('change', function() {
            // 可以在这里添加供应商相关的逻辑
        });
    }
}

// 初始化拍照功能
function initPhotoUpload() {
    const deliveryPhoto = document.getElementById('deliveryPhoto');
    const photoArea = document.getElementById('photoArea');

    if (deliveryPhoto) {
        deliveryPhoto.addEventListener('change', function(e) {
            const file = e.target.files[0];

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const imageUrl = e.target.result;

                    // 更新照片区域
                    photoArea.innerHTML = `
                        <img src="${imageUrl}" class="photo-preview" alt="送货单照片">
                        <div class="delivery-photo-actions">
                            <button type="button" class="btn btn-success" onclick="retakeDeliveryPhoto()" title="重新拍摄">
                                <i class="fas fa-camera"></i>
                            </button>
                            <button type="button" class="btn btn-primary" onclick="viewDeliveryPhoto('${imageUrl}')" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    `;
                    photoArea.classList.add('has-photo');

                    // 保存照片URL用于查看
                    window.deliveryPhotoUrl = imageUrl;
                };
                reader.readAsDataURL(file);
                showToast('送货单照片上传成功', 'success');
            }
        });
    }
}

// 重新拍摄送货单照片
function retakeDeliveryPhoto() {
    document.getElementById('deliveryPhoto').click();
}

// 查看送货单照片
function viewDeliveryPhoto(imageUrl) {
    showPhotoModal(imageUrl || window.deliveryPhotoUrl, '送货单照片');
}

// 初始化表单提交
function initFormSubmit() {
    const form = document.getElementById('inboundForm');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (addedItems.length === 0) {
                showToast('请至少添加一个食材', 'error');
                return;
            }
            
            // 显示加载状态
            showLoading(true);
            
            // 验证所有商品都有实际数量
            let hasValidItems = false;
            let validItemsCount = 0;
            
            console.log('=== 商品验证调试 ===');
            console.log('addedItems总数:', addedItems.length);
            
            for (let i = 0; i < addedItems.length; i++) {
                const item = addedItems[i];
                const actualQuantityInput = document.querySelector(`input[name="items[${i}][actual_quantity]"]`);
                console.log(`商品${i}(原index:${item.index}):`, {
                    ingredient_id: item.ingredient_id,
                    ingredient_name: item.ingredient_name,
                    找到输入框: !!actualQuantityInput,
                    输入框值: actualQuantityInput?.value,
                    实际数量: actualQuantityInput ? parseFloat(actualQuantityInput.value) : 'N/A'
                });
                
                if (actualQuantityInput && parseFloat(actualQuantityInput.value) > 0) {
                    hasValidItems = true;
                    validItemsCount++;
                }
            }
            
            console.log('有效商品数量:', validItemsCount);
            console.log('====================');
            
            if (!hasValidItems) {
                showLoading(false);
                showToast('请为至少一个食材输入有效的实际数量', 'error');
                return;
            }
            
            // 如果是创建新采购单模式，需要构造采购单数据
            const isNewOrder = document.querySelector('input[name="is_new_order"]').value === '1';
            if (isNewOrder) {
                const supplierSelect = document.querySelector('select[name="supplier_id"]');
                const orderNumber = 'PO' + new Date().toISOString().replace(/[-:T.]/g, '').substr(0, 14);
                const orderDate = document.querySelector('input[name="inbound_date"]').value;
                const notes = document.querySelector('textarea[name="notes"]').value;

                const orderGroup = {
                    order_info: {
                        id: 'new',
                        order_number: orderNumber,
                        supplier_id: supplierSelect.value,
                        supplier_name: supplierSelect.options[supplierSelect.selectedIndex].text,
                        order_date: orderDate,
                        order_amount: addedItems.reduce((sum, item) => sum + item.total, 0),
                        notes: notes
                    },
                    items: addedItems.map(item => ({
                        id: item.index,
                        ingredient_id: item.ingredient_id,
                        ingredient_name: item.ingredient_name,
                        unit: item.unit,
                        quantity: item.quantity,
                        unit_price: item.unit_price,
                        purpose: ''
                    }))
                };

                document.querySelector('input[name="new_order_data"]').value = JSON.stringify(orderGroup);
            }

            // 提交表单
            const formData = new FormData(form);

            // 确保supplier_id被正确设置
            const supplierSelect = document.querySelector('select[name="supplier_id"]');
            const hiddenSupplierInput = document.querySelector('input[name="supplier_id_hidden"]');
            if (supplierSelect && supplierSelect.value) {
                formData.set('supplier_id', supplierSelect.value);
            } else if (hiddenSupplierInput && hiddenSupplierInput.value) {
                formData.set('supplier_id', hiddenSupplierInput.value);
            }

            // 确保order_id被正确设置
            const orderIdInput = document.querySelector('input[name="order_id"]');
            if (orderIdInput && orderIdInput.value) {
                formData.set('order_id', orderIdInput.value);
                console.log('*** 设置order_id:', orderIdInput.value, '***');
            }

            // 强制确保所有商品数据都被添加到FormData中
            addedItems.forEach((item, arrayIndex) => {
                const actualQuantityInput = document.querySelector(`input[name="items[${arrayIndex}][actual_quantity]"]`);
                const ingredientIdInput = document.querySelector(`input[name="items[${arrayIndex}][ingredient_id]"]`);
                const unitPriceInput = document.querySelector(`input[name="items[${arrayIndex}][unit_price]"]`);
                const orderQuantityInput = document.querySelector(`input[name="items[${arrayIndex}][order_quantity]"]`);

                if (actualQuantityInput && parseFloat(actualQuantityInput.value) > 0) {
                    // 强制设置FormData，确保数据传输
                    formData.set(`items[${arrayIndex}][ingredient_id]`, item.ingredient_id);
                    formData.set(`items[${arrayIndex}][actual_quantity]`, actualQuantityInput.value);
                    formData.set(`items[${arrayIndex}][unit_price]`, item.unit_price);
                    formData.set(`items[${arrayIndex}][order_quantity]`, orderQuantityInput ? orderQuantityInput.value : item.quantity);
                }
            });
            
            // 调试：打印所有表单数据
            console.log('=== 表单数据调试 ===');
            console.log('FormData entries (强制设置后):');
            let hasItemsData = false;
            let itemsCount = 0;
            let formDataItems = {};
            let supplierIdFound = false;

            for (let [key, value] of formData.entries()) {
                console.log(`${key}: ${value}`);
                if (key === 'supplier_id' || key === 'supplier_id_hidden') {
                    supplierIdFound = true;
                    console.log(`*** 找到供应商ID: ${key} = ${value} ***`);
                }
                if (key.startsWith('items[')) {
                    hasItemsData = true;
                    if (key.includes('][ingredient_id]')) {
                        itemsCount++;
                    }
                    // 收集items数据结构
                    const match = key.match(/items\[(\d+)\]\[(\w+)\]/);
                    if (match) {
                        const [, index, field] = match;
                        if (!formDataItems[index]) formDataItems[index] = {};
                        formDataItems[index][field] = value;
                    }
                }
            }

            console.log('发现商品数据:', hasItemsData);
            console.log('商品数量:', itemsCount);
            console.log('addedItems长度:', addedItems.length);
            console.log('供应商ID是否存在:', supplierIdFound);
            console.log('FormData中的items结构:', formDataItems);
            console.log('FormData中items的索引:', Object.keys(formDataItems));
            console.log('====================');

            fetch('inbound.php', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('响应状态:', response.status);
                console.log('响应头:', [...response.headers.entries()]);
                
                if (!response.ok) {
                    throw new Error(`网络响应错误: ${response.status} ${response.statusText}`);
                }
                return response.json().catch(() => response.text());
            })
            .then(data => {
                showLoading(false);
                console.log('服务器响应数据:', data);
                
                // 检查是否有 JSON 响应
                if (typeof data === 'object' && data.success) {
                    showToast('入库成功！', 'success');
                    setTimeout(() => {
                        window.location.href = 'index.php';
                    }, 2000);
                } else if (typeof data === 'object' && data.success === false) {
                    // JSON 错误响应
                    showToast('入库失败：' + data.message, 'error');
                    console.error('服务器错误:', data.message);
                } else if (typeof data === 'string') {
                    // 检查文本响应中是否包含成功标识
                    if (data.includes('成功') || data.includes('success') || data.includes('redirect')) {
                        showToast('入库成功！', 'success');
                        setTimeout(() => {
                            window.location.href = 'index.php';
                        }, 2000);
                    } else {
                        // 检查是否是PHP错误
                        if (data.includes('Fatal error') || data.includes('Parse error') || data.includes('Notice') || data.includes('Warning')) {
                            showToast('服务器内部错误，请联系管理员', 'error');
                            console.error('PHP错误:', data);
                        } else {
                            showToast('入库失败，请重试', 'error');
                            console.error('未知响应格式:', data);
                        }
                    }
                } else {
                    showToast('入库失败，服务器响应格式错误', 'error');
                    console.error('未知响应类型:', typeof data, data);
                }
            })
            .catch(error => {
                showLoading(false);
                showToast('网络错误，请重试', 'error');
                console.error('Error:', error);
            });
        });
    }
}

// 显示/隐藏加载状态
function showLoading(show) {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = show ? 'flex' : 'none';
    }
}

// 显示帮助信息
function showHelp() {
    const helpContent = `
        <div class="help-content">
            <h3>入库操作指南</h3>
            <div class="help-steps">
                <div class="help-step">
                    <div class="help-step-number">1</div>
                    <div class="help-step-content">
                        <h4>填写基本信息</h4>
                        <p>输入批次号、入库日期、操作员和选择供应商</p>
                    </div>
                </div>
                <div class="help-step">
                    <div class="help-step-number">2</div>
                    <div class="help-step-content">
                        <h4>添加食材</h4>
                        <p>选择食材，输入数量和单价，可以添加多个食材</p>
                    </div>
                </div>
                <div class="help-step">
                    <div class="help-step-number">3</div>
                    <div class="help-step-content">
                        <h4>拍照确认</h4>
                        <p>拍摄送货单照片，填写备注信息，完成入库</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 这里可以显示帮助弹窗
    showToast('操作指南：按步骤填写信息即可完成入库', 'info');
}

// 工具函数：显示提示消息
function showToast(message, type = 'info') {
    // 检查main.js中的showToast函数是否存在且不是当前函数
    if (typeof window.showToast === 'function' && window.showToast !== showToast) {
        window.showToast(message, type);
    } else {
        // 创建简单的提示框
        createSimpleToast(message, type);
    }
}

// 创建简单的提示框
function createSimpleToast(message, type) {
    // 移除现有的提示框
    const existingToast = document.querySelector('.simple-toast');
    if (existingToast) {
        existingToast.remove();
    }

    // 创建新的提示框
    const toast = document.createElement('div');
    toast.className = `simple-toast simple-toast-${type}`;
    toast.textContent = message;

    // 添加样式
    const style = `
        position: fixed;
        top: 100px;
        left: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 12px;
        color: white;
        font-weight: 500;
        z-index: 9999;
        transform: translateY(-100px);
        transition: transform 0.3s ease;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;

    toast.style.cssText = style;

    // 设置背景颜色
    switch (type) {
        case 'success':
            toast.style.background = 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)';
            break;
        case 'error':
            toast.style.background = 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)';
            break;
        case 'warning':
            toast.style.background = 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
            break;
        default:
            toast.style.background = 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
    }

    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.style.transform = 'translateY(0)';
    }, 10);

    // 自动隐藏
    setTimeout(() => {
        toast.style.transform = 'translateY(-100px)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 300);
    }, 3000);
}
