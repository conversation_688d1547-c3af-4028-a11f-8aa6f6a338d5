{"name": "学校食堂食材出入库管理系统", "short_name": "食堂管理", "description": "学校食堂食材出入库管理系统移动端应用", "start_url": "/mobile/index.php", "display": "standalone", "background_color": "#667eea", "theme_color": "#667eea", "orientation": "portrait-primary", "scope": "/mobile/", "lang": "zh-CN", "categories": ["business", "productivity"], "icons": [{"src": "../logo.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "../logo.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "../logo.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "../logo.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "食材入库", "short_name": "入库", "description": "快速进入食材入库页面", "url": "/mobile/inbound.php", "icons": [{"src": "icons/shortcut-inbound.png", "sizes": "96x96"}]}, {"name": "库存查询", "short_name": "库存", "description": "查看当前库存状态", "url": "/mobile/inventory.php", "icons": [{"src": "icons/shortcut-inventory.png", "sizes": "96x96"}]}, {"name": "采购管理", "short_name": "采购", "description": "管理采购订单", "url": "/mobile/purchase.php", "icons": [{"src": "icons/shortcut-purchase.png", "sizes": "96x96"}]}, {"name": "数据报表", "short_name": "报表", "description": "查看统计报表", "url": "/mobile/reports.php", "icons": [{"src": "icons/shortcut-reports.png", "sizes": "96x96"}]}], "screenshots": [{"src": "screenshots/mobile-home.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "主页界面"}, {"src": "screenshots/mobile-inbound.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "入库界面"}], "prefer_related_applications": false, "related_applications": [], "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}