<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接测试API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-box { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        input, button { padding: 8px; margin: 5px; }
        .result { margin-top: 10px; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>

<h2>API直接测试</h2>

<div class="test-box">
    <h3>测试简化版API</h3>
    <input type="text" id="testName1" placeholder="输入分类名称" value="测试分类">
    <button onclick="testSimpleAPI()">测试简化版API</button>
    <div id="result1" class="result"></div>
</div>

<div class="test-box">
    <h3>测试原版API</h3>
    <input type="text" id="testName2" placeholder="输入分类名称" value="测试分类">
    <button onclick="testOriginalAPI()">测试原版API</button>
    <div id="result2" class="result"></div>
</div>

<div class="test-box">
    <h3>查看调试日志</h3>
    <button onclick="viewDebugLog()">查看调试日志</button>
    <div id="debugLog" class="result"></div>
</div>

<div class="test-box">
    <h3>手动POST测试</h3>
    <form method="POST" action="../modules/categories/check-name-simple.php" target="_blank">
        <input type="text" name="name" placeholder="分类名称" value="蔬菜类">
        <button type="submit">手动POST测试</button>
    </form>
</div>

<script>
function testSimpleAPI() {
    const name = document.getElementById('testName1').value;
    const resultDiv = document.getElementById('result1');
    
    resultDiv.innerHTML = '正在测试简化版API...';
    resultDiv.className = 'result';
    
    console.log('测试简化版API:', name);
    
    fetch('../modules/categories/check-name-simple.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'name=' + encodeURIComponent(name)
    })
    .then(response => {
        console.log('简化版API响应状态:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('简化版API响应文本:', text);
        try {
            const data = JSON.parse(text);
            console.log('简化版API解析数据:', data);
            
            if (data.available) {
                resultDiv.innerHTML = '✅ ' + data.message;
                resultDiv.className = 'result success';
            } else if (data.error) {
                resultDiv.innerHTML = '❌ 错误: ' + data.message;
                resultDiv.className = 'result error';
            } else {
                resultDiv.innerHTML = '⚠️ ' + data.message;
                resultDiv.className = 'result warning';
            }
        } catch (e) {
            console.error('简化版API JSON解析错误:', e);
            resultDiv.innerHTML = '❌ 响应格式错误: ' + text;
            resultDiv.className = 'result error';
        }
    })
    .catch(error => {
        console.error('简化版API请求错误:', error);
        resultDiv.innerHTML = '❌ 请求失败: ' + error.message;
        resultDiv.className = 'result error';
    });
}

function testOriginalAPI() {
    const name = document.getElementById('testName2').value;
    const resultDiv = document.getElementById('result2');
    
    resultDiv.innerHTML = '正在测试原版API...';
    resultDiv.className = 'result';
    
    console.log('测试原版API:', name);
    
    fetch('../modules/categories/check-category-name.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'name=' + encodeURIComponent(name)
    })
    .then(response => {
        console.log('原版API响应状态:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('原版API响应文本:', text);
        try {
            const data = JSON.parse(text);
            console.log('原版API解析数据:', data);
            
            if (data.available) {
                resultDiv.innerHTML = '✅ ' + data.message;
                resultDiv.className = 'result success';
            } else if (data.error) {
                resultDiv.innerHTML = '❌ 错误: ' + data.message;
                resultDiv.className = 'result error';
            } else {
                resultDiv.innerHTML = '⚠️ ' + data.message;
                resultDiv.className = 'result warning';
            }
        } catch (e) {
            console.error('原版API JSON解析错误:', e);
            resultDiv.innerHTML = '❌ 响应格式错误: ' + text;
            resultDiv.className = 'result error';
        }
    })
    .catch(error => {
        console.error('原版API请求错误:', error);
        resultDiv.innerHTML = '❌ 请求失败: ' + error.message;
        resultDiv.className = 'result error';
    });
}

function viewDebugLog() {
    const resultDiv = document.getElementById('debugLog');
    
    fetch('../modules/categories/debug.log')
    .then(response => response.text())
    .then(text => {
        resultDiv.innerHTML = '<pre>' + text + '</pre>';
        resultDiv.className = 'result';
    })
    .catch(error => {
        resultDiv.innerHTML = '无法读取调试日志: ' + error.message;
        resultDiv.className = 'result error';
    });
}
</script>

<hr>
<p><a href="../modules/categories/index.php?action=create">返回分类创建页面</a> | <a href="../index.php">返回首页</a></p>

</body>
</html>
