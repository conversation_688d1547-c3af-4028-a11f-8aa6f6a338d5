// 食品留样管理模块 JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 日期控件默认值设置
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 2);
    
    // 如果新增页面且没有设置过期日期，则设置默认值
    const expirationDateInput = document.querySelector('input[name="expiration_date"]');
    if (expirationDateInput && !expirationDateInput.value) {
        expirationDateInput.value = tomorrow.toISOString().split('T')[0];
    }
    
    // 如果新增页面且没有设置用餐日期，则设置默认值
    const mealDateInput = document.querySelector('input[name="meal_date"]');
    if (mealDateInput && !mealDateInput.value) {
        mealDateInput.value = today.toISOString().split('T')[0];
    }
    
    // 表单验证
    const sampleForm = document.getElementById('sampleForm');
    if (sampleForm) {
        sampleForm.addEventListener('submit', function(e) {
            const mealDate = new Date(document.querySelector('input[name="meal_date"]').value);
            const expirationDate = new Date(document.querySelector('input[name="expiration_date"]').value);
            const sampleQuantity = parseFloat(document.querySelector('input[name="sample_quantity"]').value);
            
            if (expirationDate <= mealDate) {
                e.preventDefault();
                alert('过期日期必须晚于用餐日期');
                return false;
            }
            
            if (sampleQuantity <= 0) {
                e.preventDefault();
                alert('留样数量必须大于0');
                return false;
            }
        });
    }
});

// 删除留样记录
function deleteSample(id) {
    if (confirm('确定要删除这条留样记录吗？此操作不可恢复！')) {
        fetch('index.php?action=delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id=' + id
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('删除成功');
                location.reload();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败: 网络错误');
        });
    }
}

// 导出数据功能
function exportData() {
    alert('导出功能正在开发中...');
}

// 刷新数据功能
function refreshData() {
    location.reload();
}