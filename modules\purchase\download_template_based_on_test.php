<?php
/**
 * 基于test.xlsx格式的采购单模板下载
 */

// 设置响应头
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="采购单模板(基于test.xlsx格式).xlsx"');
header('Cache-Control: max-age=0');

// 首先读取test.xlsx来获取准确的格式
require_once '../../includes/ExcelReader.php';

/**
 * 创建基于test.xlsx格式的模板
 */
function createTemplateBasedOnTest()
{
    try {
        // 读取test.xlsx获取格式
        $testFile = '../../test.xlsx';
        if (!file_exists($testFile)) {
            throw new Exception('test.xlsx文件不存在，无法创建基于其格式的模板');
        }
        
        $reader = new ExcelReader();
        $originalData = $reader->read($testFile, 'test.xlsx');
        
        if (empty($originalData)) {
            throw new Exception('test.xlsx文件为空');
        }
        
        // 检测格式类型
        $formatType = detectFormat($originalData);
        
        if ($formatType === 'order_form') {
            return createOrderFormTemplate($originalData);
        } else {
            return createSimpleListTemplate($originalData);
        }
        
    } catch (Exception $e) {
        throw new Exception('创建模板失败: ' . $e->getMessage());
    }
}

/**
 * 检测格式类型
 */
function detectFormat($data)
{
    if (count($data) >= 4) {
        $firstRow = $data[0] ?? [];
        $secondRow = $data[1] ?? [];
        
        if (count($firstRow) > 4 && count($secondRow) > 11) {
            $orderNumber = trim($firstRow[1] ?? '');
            if (!empty($orderNumber) && (strlen($orderNumber) > 10 || preg_match('/^[A-Z0-9]+/', $orderNumber))) {
                return 'order_form';
            }
        }
    }
    return 'simple_list';
}

/**
 * 创建订货单格式模板（严格按照参考逻辑）
 */
function createOrderFormTemplate($originalData)
{
    // 严格按照参考逻辑创建标准订货单格式
    $templateData = [
        // 第1行：订单基本信息
        ['订单号:', 'DD' . date('YmdHis'), '', '', date('Y-m-d'), '', '', '', '', '', '', '', '', '', '', '', ''],
        // 第2行：联系信息
        ['', '', '', '', '', '', '', '', '', '', '', '张三', '', '', '', '', ''],
        // 第3行：地址信息
        ['', '学校食堂一楼', '', '', '', '', '', '', '', '', '', '13800138000', '', '', '', '', ''],
        // 第4行：金额信息
        ['', '1500.00', '', '', '', '1500.00', '', '', '', '', '', date('Y-m-d'), '', '', '', '', ''],
        // 第5行：空行
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
        // 第6行：明细标题
        ['商品编码', '商品名称', '规格', '单位', '品牌', '产地', '保质期', '单价', '数量', '小计', '税率', '实收数量', '合格数量', '不合格数量', '损耗数量', '拒收数量', '备注'],
        // 第7行开始：明细数据（严格按照参考逻辑的列位置）
        ['VEG001', '白菜', '500g/包', '包', '绿源', '本地', '3天', '2.50', '20', '50.00', '0%', '20', '20', '0', '0', '0', '新鲜'],
        ['MEAT001', '猪肉', '1kg/块', 'kg', '优质', '本地', '2天', '25.00', '10', '250.00', '0%', '10', '10', '0', '0', '0', ''],
        ['FISH001', '鲫鱼', '500g/条', '条', '鲜活', '本地', '1天', '12.00', '15', '180.00', '0%', '15', '15', '0', '0', '0', '活鱼'],
        ['GRAIN001', '大米', '10kg/袋', '袋', '优质', '东北', '12个月', '45.00', '5', '225.00', '0%', '5', '5', '0', '0', '0', ''],
        ['OIL001', '食用油', '5L/桶', '桶', '金龙鱼', '本地', '18个月', '35.00', '3', '105.00', '0%', '3', '3', '0', '0', '0', ''],
        ['SEASON001', '生抽', '500ml/瓶', '瓶', '海天', '广东', '24个月', '8.50', '10', '85.00', '0%', '10', '10', '0', '0', '0', ''],
    ];

    return createXlsxFile($templateData, '订货单');
}

/**
 * 创建简单列表格式模板
 */
function createSimpleListTemplate($originalData)
{
    // 基于原始数据的结构，但使用标准的简单列表数据
    $templateData = [
        ['供应商名称', '订单日期', '食材名称', '数量', '单价', '备注'], // 标题行
        ['新鲜蔬菜供应商', '2024-01-15', '白菜', '50', '2.50', '早餐用'],
        ['新鲜蔬菜供应商', '2024-01-15', '土豆', '30', '3.00', '午餐用'],
        ['肉类批发商', '2024-01-15', '猪肉', '20', '25.00', '午餐用'],
        ['肉类批发商', '2024-01-15', '鸡蛋', '40', '8.50', '早餐用'],
        ['水产供应商', '2024-01-15', '鲫鱼', '15', '12.00', '晚餐用'],
        ['粮食供应商', '2024-01-15', '大米', '100', '4.20', '主食用'],
        ['调料供应商', '2024-01-15', '食用油', '5', '35.00', '厨房用'],
        ['调料供应商', '2024-01-15', '生抽', '10', '8.50', '调味用']
    ];
    
    // 如果原始数据有更多列，保持相同的列数
    if (!empty($originalData[0]) && count($originalData[0]) > 6) {
        $maxCols = count($originalData[0]);
        foreach ($templateData as &$row) {
            while (count($row) < $maxCols) {
                $row[] = '';
            }
        }
    }
    
    return createXlsxFile($templateData, '采购单模板');
}

/**
 * 创建XLSX文件
 */
function createXlsxFile($data, $sheetName = 'Sheet1')
{
    $tempDir = sys_get_temp_dir() . '/xlsx_' . uniqid();
    mkdir($tempDir);
    mkdir($tempDir . '/xl');
    mkdir($tempDir . '/xl/worksheets');
    mkdir($tempDir . '/_rels');
    mkdir($tempDir . '/xl/_rels');

    // 创建基本的XLSX文件结构
    createXlsxStructure($tempDir, $sheetName);
    
    // 处理数据
    $strings = [];
    $stringMap = [];
    foreach ($data as $row) {
        foreach ($row as $cell) {
            if (is_string($cell) && !is_numeric($cell) && !empty($cell)) {
                if (!isset($stringMap[$cell])) {
                    $stringMap[$cell] = count($strings);
                    $strings[] = $cell;
                }
            }
        }
    }

    // 创建共享字符串文件
    createSharedStrings($tempDir, $strings);
    
    // 创建工作表
    createWorksheet($tempDir, $data, $stringMap);

    // 创建ZIP文件
    return createZipFile($tempDir);
}

/**
 * 创建XLSX基本结构
 */
function createXlsxStructure($tempDir, $sheetName)
{
    // [Content_Types].xml
    $contentTypes = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml"/>
    <Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>
    <Override PartName="/xl/sharedStrings.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml"/>
</Types>';
    file_put_contents($tempDir . '/[Content_Types].xml', $contentTypes);

    // _rels/.rels
    $rels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/>
</Relationships>';
    file_put_contents($tempDir . '/_rels/.rels', $rels);

    // xl/_rels/workbook.xml.rels
    $workbookRels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/>
    <Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings" Target="sharedStrings.xml"/>
</Relationships>';
    file_put_contents($tempDir . '/xl/_rels/workbook.xml.rels', $workbookRels);

    // xl/workbook.xml
    $workbook = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
    <sheets>
        <sheet name="' . htmlspecialchars($sheetName, ENT_XML1, 'UTF-8') . '" sheetId="1" r:id="rId1"/>
    </sheets>
</workbook>';
    file_put_contents($tempDir . '/xl/workbook.xml', $workbook);
}

/**
 * 创建共享字符串文件
 */
function createSharedStrings($tempDir, $strings)
{
    $sharedStrings = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="' . count($strings) . '" uniqueCount="' . count($strings) . '">';
    foreach ($strings as $string) {
        $sharedStrings .= '<si><t>' . htmlspecialchars($string, ENT_XML1, 'UTF-8') . '</t></si>';
    }
    $sharedStrings .= '</sst>';
    file_put_contents($tempDir . '/xl/sharedStrings.xml', $sharedStrings);
}

/**
 * 创建工作表
 */
function createWorksheet($tempDir, $data, $stringMap)
{
    $worksheet = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
    <sheetData>';

    foreach ($data as $rowIndex => $row) {
        $rowNum = $rowIndex + 1;
        $worksheet .= '<row r="' . $rowNum . '">';
        
        foreach ($row as $colIndex => $cell) {
            $colLetter = chr(65 + $colIndex);
            if ($colIndex >= 26) {
                $colLetter = chr(64 + intval($colIndex / 26)) . chr(65 + ($colIndex % 26));
            }
            $cellRef = $colLetter . $rowNum;
            
            if (is_string($cell) && !is_numeric($cell) && !empty($cell)) {
                $stringIndex = $stringMap[$cell];
                $worksheet .= '<c r="' . $cellRef . '" t="s"><v>' . $stringIndex . '</v></c>';
            } elseif (!empty($cell)) {
                $worksheet .= '<c r="' . $cellRef . '"><v>' . htmlspecialchars($cell, ENT_XML1, 'UTF-8') . '</v></c>';
            }
        }
        
        $worksheet .= '</row>';
    }

    $worksheet .= '</sheetData></worksheet>';
    file_put_contents($tempDir . '/xl/worksheets/sheet1.xml', $worksheet);
}

/**
 * 创建ZIP文件
 */
function createZipFile($tempDir)
{
    $zipFile = tempnam(sys_get_temp_dir(), 'xlsx') . '.xlsx';
    $zip = new ZipArchive();
    
    if ($zip->open($zipFile, ZipArchive::CREATE) !== TRUE) {
        throw new Exception('无法创建XLSX文件');
    }

    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($tempDir));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $relativePath = str_replace($tempDir . DIRECTORY_SEPARATOR, '', $file->getPathname());
            $relativePath = str_replace('\\', '/', $relativePath);
            $zip->addFile($file->getPathname(), $relativePath);
        }
    }

    $zip->close();

    // 清理临时目录
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($tempDir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    foreach ($iterator as $file) {
        if ($file->isDir()) {
            rmdir($file->getPathname());
        } else {
            unlink($file->getPathname());
        }
    }
    rmdir($tempDir);

    return $zipFile;
}

try {
    // 创建基于test.xlsx格式的模板
    $excelFile = createTemplateBasedOnTest();
    
    // 输出文件内容
    readfile($excelFile);
    
    // 清理临时文件
    unlink($excelFile);
    
} catch (Exception $e) {
    // 如果创建失败，返回错误信息
    header('Content-Type: text/plain; charset=utf-8');
    echo '基于test.xlsx的模板生成失败: ' . $e->getMessage();
    echo "\n\n请确保test.xlsx文件存在于项目根目录下。";
}
?>
