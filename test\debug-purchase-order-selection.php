<?php
/**
 * 验证移动端采购单选择功能
 */

// 模拟一个完整的采购单选择和入库流程
$orderData = [
    'batch_type' => 'batch',
    'supplier_id' => '2',
    'batch_number' => 'DEBUG_' . microtime(true) * 10000,
    'inbound_date' => date('Y-m-d'),
    'operator_name' => '调试操作员',
    'notes' => '调试采购单选择功能',
    'order_id' => '16', // 使用真实采购单ID
    'is_new_order' => '0', // 使用现有采购单
    'items' => [
        '0' => [
            'ingredient_id' => '141',
            'actual_quantity' => '35.0',
            'unit_price' => '1.11',
            'order_quantity' => '40.00'
        ],
        '1' => [
            'ingredient_id' => '142', 
            'actual_quantity' => '38.0',
            'unit_price' => '1.07',
            'order_quantity' => '40.00'
        ]
    ]
];

echo "=== 调试采购单入库功能 ===\n";
echo "采购单ID: 16\n";
echo "供应商ID: 2\n";
echo "商品数量: " . count($orderData['items']) . "\n";
echo "批次号: " . $orderData['batch_number'] . "\n\n";

// 检查提交的数据格式
echo "提交的数据:\n";
foreach ($orderData as $key => $value) {
    if (is_array($value)) {
        echo "$key: [数组，" . count($value) . " 项]\n";
        foreach ($value as $subKey => $subValue) {
            if (is_array($subValue)) {
                echo "  $subKey: [数组，" . count($subValue) . " 项]\n";
                foreach ($subValue as $itemKey => $itemValue) {
                    echo "    $itemKey: $itemValue\n";
                }
            } else {
                echo "  $subKey: $subValue\n";
            }
        }
    } else {
        echo "$key: $value\n";
    }
}
echo "\n";

// 发送请求
$postData = http_build_query($orderData);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/x-www-form-urlencoded',
            'X-Requested-With: XMLHttpRequest'
        ],
        'content' => $postData
    ]
]);

echo "发送请求到 http://localhost:8000/mobile/inbound.php\n";
$response = file_get_contents('http://localhost:8000/mobile/inbound.php', false, $context);

if ($response === false) {
    echo "❌ 请求失败\n";
    exit;
}

echo "服务器响应:\n";
echo "响应长度: " . strlen($response) . " 字符\n";
echo "前200字符: " . substr($response, 0, 200) . "\n\n";

// 尝试解析JSON
$jsonResponse = json_decode($response, true);
if ($jsonResponse) {
    echo "✅ JSON响应解析成功\n";
    echo "成功状态: " . ($jsonResponse['success'] ? '✅ 成功' : '❌ 失败') . "\n";
    echo "消息: " . $jsonResponse['message'] . "\n";
    
    if (isset($jsonResponse['data'])) {
        echo "数据:\n";
        foreach ($jsonResponse['data'] as $key => $value) {
            echo "  $key: $value\n";
        }
    }
    
    if (isset($jsonResponse['error_code'])) {
        echo "错误代码: " . $jsonResponse['error_code'] . "\n";
    }
} else {
    echo "❌ 无法解析为JSON响应\n";
    
    // 检查是否包含PHP错误
    if (strpos($response, 'Fatal error') !== false) {
        echo "检测到 Fatal error\n";
    } elseif (strpos($response, 'Parse error') !== false) {
        echo "检测到 Parse error\n";
    } elseif (strpos($response, 'Notice') !== false) {
        echo "检测到 Notice\n";
    } elseif (strpos($response, 'Warning') !== false) {
        echo "检测到 Warning\n";
    }
    
    // 显示完整响应（如果不太长）
    if (strlen($response) < 1000) {
        echo "\n完整响应:\n";
        echo $response . "\n";
    }
}

echo "\n=== 调试完成 ===\n";
?>