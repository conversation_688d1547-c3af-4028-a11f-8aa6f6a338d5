<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试自动创建食材功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        .data-table th { background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>🧪 测试自动创建食材功能</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../modules/purchase/PurchaseController.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>📊 当前食材统计</h2>";
        
        $ingredientCount = $db->fetchOne("SELECT COUNT(*) as count FROM ingredients")['count'];
        echo "<p class='info'>当前食材总数: <strong>{$ingredientCount}</strong></p>";
        
        // 显示最近的食材
        $recentIngredients = $db->fetchAll("SELECT * FROM ingredients ORDER BY created_at DESC LIMIT 10");
        
        if (!empty($recentIngredients)) {
            echo "<h4>最近的10个食材：</h4>";
            echo "<table class='data-table'>";
            echo "<tr><th>ID</th><th>编码</th><th>名称</th><th>规格</th><th>单位</th><th>品牌</th><th>产地</th><th>创建时间</th></tr>";
            
            foreach ($recentIngredients as $ingredient) {
                echo "<tr>";
                echo "<td>{$ingredient['id']}</td>";
                echo "<td>{$ingredient['code']}</td>";
                echo "<td>{$ingredient['name']}</td>";
                echo "<td>{$ingredient['specification']}</td>";
                echo "<td>{$ingredient['unit']}</td>";
                echo "<td>{$ingredient['brand']}</td>";
                echo "<td>{$ingredient['origin']}</td>";
                echo "<td>{$ingredient['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 测试自动创建功能</h2>";
        
        // 测试数据
        $testIngredients = [
            ['code' => 'TEST001', 'name' => '测试食材1', 'specification' => '500g', 'unit' => '包', 'brand' => '测试品牌', 'origin' => '测试产地'],
            ['code' => 'TEST002', 'name' => '测试食材2', 'specification' => '1kg', 'unit' => '袋', 'brand' => '', 'origin' => ''],
            ['code' => '', 'name' => '无编码测试食材', 'specification' => '', 'unit' => '个', 'brand' => '', 'origin' => ''],
        ];
        
        $controller = new PurchaseController();
        
        echo "<div class='debug-log'>";
        
        foreach ($testIngredients as $index => $testData) {
            echo "=== 测试 " . ($index + 1) . " ===\n";
            echo "编码: '{$testData['code']}'\n";
            echo "名称: '{$testData['name']}'\n";
            echo "规格: '{$testData['specification']}'\n";
            echo "单位: '{$testData['unit']}'\n";
            echo "品牌: '{$testData['brand']}'\n";
            echo "产地: '{$testData['origin']}'\n";
            
            try {
                // 使用反射调用私有方法
                $reflection = new ReflectionClass($controller);
                $method = $reflection->getMethod('findOrCreateIngredientByCode');
                $method->setAccessible(true);
                
                $ingredientId = $method->invoke(
                    $controller,
                    $testData['code'],
                    $testData['name'],
                    $testData['specification'],
                    $testData['unit'],
                    $testData['brand'],
                    $testData['origin']
                );
                
                echo "✅ 成功: 食材ID = {$ingredientId}\n";
                
                // 验证创建的食材
                $createdIngredient = $db->fetchOne("SELECT * FROM ingredients WHERE id = ?", [$ingredientId]);
                if ($createdIngredient) {
                    echo "   创建的食材: {$createdIngredient['name']} ({$createdIngredient['code']})\n";
                }
                
            } catch (Exception $e) {
                echo "❌ 失败: " . $e->getMessage() . "\n";
            }
            
            echo "\n";
        }
        
        echo "</div>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>📈 测试后统计</h2>";
        
        $newIngredientCount = $db->fetchOne("SELECT COUNT(*) as count FROM ingredients")['count'];
        $addedCount = $newIngredientCount - $ingredientCount;
        
        echo "<p class='info'>测试后食材总数: <strong>{$newIngredientCount}</strong></p>";
        echo "<p class='success'>新增食材数量: <strong>{$addedCount}</strong></p>";
        
        if ($addedCount > 0) {
            echo "<h4>新增的食材：</h4>";
            $newIngredients = $db->fetchAll("SELECT * FROM ingredients ORDER BY created_at DESC LIMIT ?", [$addedCount]);
            
            echo "<table class='data-table'>";
            echo "<tr><th>ID</th><th>编码</th><th>名称</th><th>规格</th><th>单位</th><th>品牌</th><th>产地</th><th>创建时间</th></tr>";
            
            foreach ($newIngredients as $ingredient) {
                echo "<tr style='background: #e8f5e8;'>";
                echo "<td>{$ingredient['id']}</td>";
                echo "<td>{$ingredient['code']}</td>";
                echo "<td>{$ingredient['name']}</td>";
                echo "<td>{$ingredient['specification']}</td>";
                echo "<td>{$ingredient['unit']}</td>";
                echo "<td>{$ingredient['brand']}</td>";
                echo "<td>{$ingredient['origin']}</td>";
                echo "<td>{$ingredient['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        echo "</div>";
        
        // 显示导入日志
        $logFile = '../logs/import_debug.log';
        if (file_exists($logFile)) {
            echo "<div class='test-section'>";
            echo "<h2>📋 导入日志（最后20行）</h2>";
            
            $logContent = file_get_contents($logFile);
            $lines = explode("\n", $logContent);
            $lastLines = array_slice($lines, -20);
            
            echo "<div class='debug-log'>";
            foreach ($lastLines as $line) {
                if (!empty(trim($line))) {
                    $class = '';
                    if (strpos($line, '✅') !== false) $class = 'success';
                    elseif (strpos($line, '❌') !== false) $class = 'error';
                    elseif (strpos($line, '⚠️') !== false) $class = 'warning';
                    
                    echo "<div class='{$class}'>" . htmlspecialchars($line) . "</div>";
                }
            }
            echo "</div>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 测试过程出错: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>
    
    <div class="test-section">
        <h2>🎯 下一步操作</h2>
        <div class="step">
            <p>
                <a href="../modules/purchase/index.php?action=import" class="btn">🔄 测试Excel导入</a>
                <a href="../test/view-import-log.php" class="btn">📋 查看导入日志</a>
                <a href="../modules/ingredients/index.php" class="btn">📊 查看食材管理</a>
            </p>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
