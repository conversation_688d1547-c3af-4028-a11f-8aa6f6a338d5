<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-plus"></i>
                新增出库记录
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <?php if (!empty($data['error_message'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <?= htmlspecialchars($data['error_message']) ?>
            </div>
        <?php endif; ?>

        <div class="form-container">
            <form method="POST" action="index.php?action=create" class="form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="ingredient_id">
                            <i class="fas fa-carrot"></i>
                            选择食材 <span class="required">*</span>
                        </label>
                        <select name="ingredient_id" id="ingredient_id" class="form-control" required onchange="updateStockInfo()">
                            <option value="">请选择食材</option>
                            <?php if (!empty($data['ingredients'])): ?>
                                <?php 
                                $currentCategory = '';
                                foreach ($data['ingredients'] as $ingredient): 
                                    if ($currentCategory !== $ingredient['category_name']):
                                        if ($currentCategory !== '') echo '</optgroup>';
                                        $currentCategory = $ingredient['category_name'];
                                        echo '<optgroup label="' . htmlspecialchars($currentCategory) . '">';
                                    endif;
                                ?>
                                    <option value="<?= $ingredient['id'] ?>" 
                                            data-stock="<?= $ingredient['current_stock'] ?>"
                                            data-unit="<?= htmlspecialchars($ingredient['unit']) ?>">
                                        <?= htmlspecialchars($ingredient['name']) ?> 
                                        (库存: <?= number_format($ingredient['current_stock'], 1) ?><?= htmlspecialchars($ingredient['unit']) ?>)
                                    </option>
                                <?php endforeach; ?>
                                <?php if ($currentCategory !== '') echo '</optgroup>'; ?>
                            <?php endif; ?>
                        </select>
                        <div id="stock_info" class="form-help"></div>
                    </div>

                    <div class="form-group">
                        <label for="quantity">
                            <i class="fas fa-weight"></i>
                            出库数量 <span class="required">*</span>
                        </label>
                        <div class="input-group">
                            <input type="number" name="quantity" id="quantity" class="form-control" 
                                   step="0.1" min="0.1" required placeholder="请输入出库数量">
                            <span class="input-group-text" id="quantity_unit">单位</span>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="meal_type">
                            <i class="fas fa-utensils"></i>
                            餐次 <span class="required">*</span>
                        </label>
                        <select name="meal_type" id="meal_type" class="form-control" required>
                            <option value="">请选择餐次</option>
                            <option value="breakfast">早餐</option>
                            <option value="lunch">午餐</option>
                            <option value="dinner">晚餐</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="meal_date">
                            <i class="fas fa-calendar-alt"></i>
                            用餐日期 <span class="required">*</span>
                        </label>
                        <input type="date" name="meal_date" id="meal_date" class="form-control" 
                               value="<?= date('Y-m-d') ?>" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="purpose">
                            <i class="fas fa-utensils"></i>
                            具体用途
                        </label>
                        <input type="text" name="purpose" id="purpose" class="form-control" 
                               placeholder="如：炒白菜、红烧肉等">
                    </div>

                    <div class="form-group">
                        <label for="operator_name">
                            <i class="fas fa-user"></i>
                            操作员 <span class="required">*</span>
                        </label>
                        <input type="text" name="operator_name" id="operator_name" class="form-control" 
                               value="管理员" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="notes">
                        <i class="fas fa-sticky-note"></i>
                        备注信息
                    </label>
                    <textarea name="notes" id="notes" class="form-control" rows="3" 
                              placeholder="请输入备注信息..."></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存出库记录
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        取消
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.form-container {
    max-width: 2600px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
}

.form-group label i {
    margin-right: 8px;
    color: #6b7280;
}

.required {
    color: #ef4444;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-group {
    display: flex;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group-text {
    padding: 12px;
    background: #f9fafb;
    border: 1px solid #d1d5db;
    border-left: none;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    font-size: 14px;
    color: #6b7280;
}

.form-help {
    margin-top: 5px;
    font-size: 12px;
    color: #6b7280;
}

.form-help.warning {
    color: #f59e0b;
}

.form-help.error {
    color: #ef4444;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.alert {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.alert-danger {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.alert i {
    margin-right: 10px;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<script>
function updateStockInfo() {
    const select = document.getElementById('ingredient_id');
    const stockInfo = document.getElementById('stock_info');
    const quantityUnit = document.getElementById('quantity_unit');
    const quantityInput = document.getElementById('quantity');
    
    if (select.value) {
        const option = select.options[select.selectedIndex];
        const stock = parseFloat(option.dataset.stock);
        const unit = option.dataset.unit;
        
        quantityUnit.textContent = unit;
        quantityInput.setAttribute('max', stock);
        
        if (stock > 0) {
            stockInfo.innerHTML = `<i class="fas fa-info-circle"></i> 当前库存：${stock} ${unit}`;
            stockInfo.className = 'form-help';
        } else {
            stockInfo.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 库存不足，无法出库`;
            stockInfo.className = 'form-help error';
        }
    } else {
        stockInfo.innerHTML = '';
        quantityUnit.textContent = '单位';
        quantityInput.removeAttribute('max');
    }
}

// 实时检查出库数量
document.getElementById('quantity').addEventListener('input', function() {
    const select = document.getElementById('ingredient_id');
    const stockInfo = document.getElementById('stock_info');
    
    if (select.value) {
        const option = select.options[select.selectedIndex];
        const stock = parseFloat(option.dataset.stock);
        const unit = option.dataset.unit;
        const quantity = parseFloat(this.value);
        
        if (quantity > stock) {
            stockInfo.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 出库数量不能超过库存 ${stock} ${unit}`;
            stockInfo.className = 'form-help error';
        } else if (quantity > stock * 0.8) {
            stockInfo.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 注意：出库数量较大，剩余库存将很少`;
            stockInfo.className = 'form-help warning';
        } else {
            stockInfo.innerHTML = `<i class="fas fa-info-circle"></i> 当前库存：${stock} ${unit}`;
            stockInfo.className = 'form-help';
        }
    }
});
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>