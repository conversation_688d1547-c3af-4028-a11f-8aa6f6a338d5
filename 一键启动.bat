@echo off
setlocal enabledelayedexpansion

REM School Canteen Management System - One-Click Startup Script
REM Environment: PHP 7.2 + MySQL 5.6 + Remote Database

title Canteen Management System - Startup

echo.
echo ================================================================
echo.
echo           School Canteen Management System
echo.
echo                  One-Click Startup
echo.
echo          PHP 7.2 + MySQL 5.6 + Remote Database
echo.
echo ================================================================
echo.

REM Set remote database information
set DB_HOST=************
set DB_PORT=3306
set DB_USER=sc
set DB_PASS=pw5K4SsM7kZsjdxy
set DB_NAME=sc

echo Checking environment...
echo.

REM Check PHP
php --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] PHP not installed or not in PATH
    echo Please install XAMPP, WAMP or standalone PHP
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('php --version ^| findstr "PHP"') do set php_version=%%i
echo [OK] PHP Version: %php_version%

REM Check Composer
composer --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Composer not installed
    echo Please download from https://getcomposer.org/
    pause
    exit /b 1
)
echo [OK] Composer installed

REM Check MySQL client
mysql --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] MySQL client not installed
    echo Please install MySQL client or XAMPP/WAMP
    pause
    exit /b 1
)
echo [OK] MySQL client installed

echo.
echo Testing remote database connection...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% -e "SELECT 1;" %DB_NAME% >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Remote database connection failed
    echo Host: %DB_HOST%:%DB_PORT%
    echo Database: %DB_NAME%
    echo User: %DB_USER%
    echo.
    echo Please check:
    echo 1. Network connection
    echo 2. Database server status
    echo 3. Firewall settings
    pause
    exit /b 1
)
echo [OK] Remote database connection successful

echo.
echo Checking Laravel project...

if not exist "composer.json" (
    echo Laravel project not found, creating...
    echo.

    echo Creating Laravel 8.x project (PHP 7.2 compatible)...
    composer create-project laravel/laravel . "^8.0" --prefer-dist
    if errorlevel 1 (
        echo [ERROR] Laravel project creation failed
        pause
        exit /b 1
    )
    echo [OK] Laravel project created

    echo.
    echo Installing compatible dependencies...
    composer require "laravel/sanctum:^2.15" --no-update
    composer require "spatie/laravel-permission:^4.4" --no-update
    composer require "maatwebsite/excel:^3.1.40" --no-update
    composer require "barryvdh/laravel-dompdf:^0.9" --no-update
    composer require "intervention/image:^2.7" --no-update
    composer update

    echo [OK] Dependencies installed
) else (
    echo [OK] Laravel project exists
)

echo.
echo ⚙️ 配置环境文件...

if not exist ".env" (
    if exist ".env.example" (
        copy .env.example .env >nul
    ) else (
        REM 创建基本的.env文件
        (
            echo APP_NAME="食堂食材管理系统"
            echo APP_ENV=local
            echo APP_KEY=
            echo APP_DEBUG=true
            echo APP_URL=http://localhost:8000
            echo.
            echo LOG_CHANNEL=stack
            echo LOG_LEVEL=debug
            echo.
            echo DB_CONNECTION=mysql
            echo DB_HOST=%DB_HOST%
            echo DB_PORT=%DB_PORT%
            echo DB_DATABASE=%DB_NAME%
            echo DB_USERNAME=%DB_USER%
            echo DB_PASSWORD=%DB_PASS%
            echo DB_CHARSET=utf8
            echo DB_COLLATION=utf8_unicode_ci
            echo.
            echo BROADCAST_DRIVER=log
            echo CACHE_DRIVER=file
            echo FILESYSTEM_DRIVER=local
            echo QUEUE_CONNECTION=sync
            echo SESSION_DRIVER=file
            echo SESSION_LIFETIME=120
        ) > .env
    )
    
    php artisan key:generate
    echo ✅ 环境文件配置完成
) else (
    echo 更新数据库配置...
    powershell -Command "(Get-Content .env) -replace 'DB_HOST=.*', 'DB_HOST=%DB_HOST%' | Set-Content .env"
    powershell -Command "(Get-Content .env) -replace 'DB_PORT=.*', 'DB_PORT=%DB_PORT%' | Set-Content .env"
    powershell -Command "(Get-Content .env) -replace 'DB_DATABASE=.*', 'DB_DATABASE=%DB_NAME%' | Set-Content .env"
    powershell -Command "(Get-Content .env) -replace 'DB_USERNAME=.*', 'DB_USERNAME=%DB_USER%' | Set-Content .env"
    powershell -Command "(Get-Content .env) -replace 'DB_PASSWORD=.*', 'DB_PASSWORD=%DB_PASS%' | Set-Content .env"
    echo ✅ 数据库配置已更新
)

echo.
echo 🗄️ 配置MySQL 5.6兼容性...

REM 更新AppServiceProvider以支持MySQL 5.6
if exist "app\Providers\AppServiceProvider.php" (
    echo 配置字符串长度限制...
    
    REM 备份原文件
    copy "app\Providers\AppServiceProvider.php" "app\Providers\AppServiceProvider.php.backup" >nul 2>&1
    
    REM 创建兼容的AppServiceProvider
    (
        echo ^<?php
        echo.
        echo namespace App\Providers;
        echo.
        echo use Illuminate\Support\ServiceProvider;
        echo use Illuminate\Support\Facades\Schema;
        echo.
        echo class AppServiceProvider extends ServiceProvider
        echo {
        echo     public function register^(^)
        echo     {
        echo         //
        echo     }
        echo.
        echo     public function boot^(^)
        echo     {
        echo         // MySQL 5.6 兼容性：设置默认字符串长度
        echo         Schema::defaultStringLength^(191^);
        echo     }
        echo }
    ) > "app\Providers\AppServiceProvider.php"
    
    echo ✅ MySQL 5.6兼容性配置完成
)

echo.
echo 📋 初始化数据库...

echo 检查数据库表...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% -e "SHOW TABLES;" %DB_NAME% > temp_tables.txt 2>&1

set table_count=0
for /f %%i in ('type temp_tables.txt ^| find /c /v ""') do set table_count=%%i
del temp_tables.txt >nul 2>&1

if %table_count% LEQ 1 (
    echo 数据库为空，创建表结构...
    
    if exist "scripts\database\mysql56_compatible.sql" (
        mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% < scripts\database\mysql56_compatible.sql
        if errorlevel 1 (
            echo ❌ 数据库表创建失败
            pause
            exit /b 1
        )
        echo ✅ 数据库表创建完成
    ) else (
        echo ⚠️  数据库脚本不存在，将使用Laravel迁移
    )
) else (
    echo ✅ 数据库表已存在
)

echo.
echo 🔧 Laravel配置...

echo 发布配置文件...
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider" --quiet
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider" --quiet

echo 清理缓存...
php artisan config:clear >nul 2>&1
php artisan route:clear >nul 2>&1
php artisan view:clear >nul 2>&1
php artisan cache:clear >nul 2>&1

echo 创建存储链接...
php artisan storage:link >nul 2>&1

echo ✅ Laravel配置完成

echo.
echo 🧪 测试Laravel数据库连接...
php artisan migrate:status >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Laravel数据库连接测试失败，但将继续启动
) else (
    echo ✅ Laravel数据库连接正常
)

echo.
echo 🎉 配置完成！启动开发服务器...
echo.
echo ================================
echo 🌐 访问信息
echo ================================
echo 系统地址: http://localhost:8000
echo 数据库: %DB_HOST%:%DB_PORT%/%DB_NAME%
echo.
echo 默认账号:
echo 管理员: <EMAIL> / password
echo 审核员: <EMAIL> / password
echo 仓库员: <EMAIL> / password
echo ================================
echo.
echo 🚀 启动服务器中...
echo 按 Ctrl+C 停止服务器
echo.

php artisan serve
