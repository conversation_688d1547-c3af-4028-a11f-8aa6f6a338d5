<?php

/**
 * 学校食堂食材出入库管理系统 - API资源类创建脚本
 * 根据开发文档自动生成Laravel API资源类文件
 */

// 食材资源类
$ingredientResource = '<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class IngredientResource extends JsonResource
{
    /**
     * 将资源转换为数组
     */
    public function toArray($request): array
    {
        return [
            \'id\' => $this->id,
            \'name\' => $this->name,
            \'category\' => [
                \'id\' => $this->category->id,
                \'name\' => $this->category->name,
                \'code\' => $this->category->code
            ],
            \'unit\' => $this->unit,
            \'shelf_life_days\' => $this->shelf_life_days,
            \'min_stock\' => $this->min_stock,
            \'image_path\' => $this->image_path,
            \'image_url\' => $this->image_path ? asset(\'storage/\' . $this->image_path) : null,
            \'description\' => $this->description,
            \'status\' => $this->status,
            \'status_text\' => $this->status ? \'启用\' : \'停用\',
            
            // 库存信息
            \'inventory\' => $this->when($this->relationLoaded(\'inventory\'), function () {
                return [
                    \'current_quantity\' => $this->inventory->current_quantity ?? 0,
                    \'total_value\' => $this->inventory->total_value ?? 0,
                    \'last_inbound_at\' => $this->inventory->last_inbound_at,
                    \'last_outbound_at\' => $this->inventory->last_outbound_at,
                    \'is_low_stock\' => $this->isLowStock()
                ];
            }),
            
            // 创建人信息
            \'creator\' => $this->when($this->relationLoaded(\'creator\'), function () {
                return [
                    \'id\' => $this->creator->id,
                    \'name\' => $this->creator->name,
                    \'real_name\' => $this->creator->real_name
                ];
            }),
            
            // 库存批次信息
            \'batches\' => $this->when($this->relationLoaded(\'inventoryBatches\'), function () {
                return $this->inventoryBatches->map(function ($batch) {
                    return [
                        \'id\' => $batch->id,
                        \'batch_number\' => $batch->batch_number,
                        \'remaining_quantity\' => $batch->remaining_quantity,
                        \'unit_price\' => $batch->unit_price,
                        \'expired_at\' => $batch->expired_at,
                        \'status\' => $batch->status,
                        \'status_text\' => $this->getBatchStatusText($batch->status),
                        \'days_to_expire\' => $this->getDaysToExpire($batch->expired_at)
                    ];
                });
            }),
            
            \'created_at\' => $this->created_at,
            \'updated_at\' => $this->updated_at
        ];
    }

    /**
     * 获取批次状态文本
     */
    protected function getBatchStatusText(int $status): string
    {
        return match($status) {
            1 => \'正常\',
            2 => \'即将过期\',
            3 => \'已过期\',
            default => \'未知\'
        };
    }

    /**
     * 获取距离过期天数
     */
    protected function getDaysToExpire(string $expiredAt): int
    {
        return now()->diffInDays($expiredAt, false);
    }
}';

// 入库记录资源类
$inboundResource = '<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class InboundResource extends JsonResource
{
    /**
     * 将资源转换为数组
     */
    public function toArray($request): array
    {
        return [
            \'id\' => $this->id,
            \'batch_number\' => $this->batch_number,
            
            // 食材信息
            \'ingredient\' => [
                \'id\' => $this->ingredient->id,
                \'name\' => $this->ingredient->name,
                \'unit\' => $this->ingredient->unit,
                \'category\' => [
                    \'id\' => $this->ingredient->category->id,
                    \'name\' => $this->ingredient->category->name
                ]
            ],
            
            // 供应商信息
            \'supplier\' => [
                \'id\' => $this->supplier->id,
                \'name\' => $this->supplier->name,
                \'contact_person\' => $this->supplier->contact_person,
                \'phone\' => $this->supplier->phone,
                \'rating\' => $this->supplier->rating
            ],
            
            // 数量和价格
            \'quantity\' => $this->quantity,
            \'unit_price\' => $this->unit_price,
            \'total_price\' => $this->getTotalPriceAttribute(),
            
            // 日期信息
            \'production_date\' => $this->production_date,
            \'expired_at\' => $this->expired_at,
            \'days_to_expire\' => $this->expired_at ? now()->diffInDays($this->expired_at, false) : null,
            
            // 文件信息
            \'weight_images\' => $this->weight_images ? array_map(function($path) {
                return [
                    \'path\' => $path,
                    \'url\' => asset(\'storage/\' . $path)
                ];
            }, $this->weight_images) : [],
            
            \'purchase_invoice\' => $this->purchase_invoice ? [
                \'path\' => $this->purchase_invoice,
                \'url\' => asset(\'storage/\' . $this->purchase_invoice),
                \'filename\' => basename($this->purchase_invoice)
            ] : null,
            
            // 质检和状态
            \'quality_check\' => $this->quality_check,
            \'quality_check_text\' => $this->quality_check ? \'合格\' : \'不合格\',
            \'status\' => $this->status,
            \'status_text\' => $this->status ? \'正常\' : \'作废\',
            
            \'notes\' => $this->notes,
            
            // 创建人信息
            \'creator\' => $this->when($this->relationLoaded(\'creator\'), function () {
                return [
                    \'id\' => $this->creator->id,
                    \'name\' => $this->creator->name,
                    \'real_name\' => $this->creator->real_name
                ];
            }),
            
            // 审核信息
            \'approval\' => [
                \'is_approved\' => !is_null($this->approved_by),
                \'approved_by\' => $this->when($this->relationLoaded(\'approver\') && $this->approver, function () {
                    return [
                        \'id\' => $this->approver->id,
                        \'name\' => $this->approver->name,
                        \'real_name\' => $this->approver->real_name
                    ];
                }),
                \'approved_at\' => $this->approved_at
            ],
            
            \'created_at\' => $this->created_at,
            \'updated_at\' => $this->updated_at
        ];
    }
}';

// 供应商资源类
$supplierResource = '<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class SupplierResource extends JsonResource
{
    /**
     * 将资源转换为数组
     */
    public function toArray($request): array
    {
        return [
            \'id\' => $this->id,
            \'name\' => $this->name,
            \'contact_person\' => $this->contact_person,
            \'phone\' => $this->phone,
            \'address\' => $this->address,
            \'license_number\' => $this->license_number,
            
            // 资质文件
            \'qualification_files\' => $this->qualification_files ? array_map(function($path) {
                return [
                    \'path\' => $path,
                    \'url\' => asset(\'storage/\' . $path),
                    \'filename\' => basename($path)
                ];
            }, $this->qualification_files) : [],
            
            \'rating\' => $this->rating,
            \'rating_text\' => $this->getRatingText(),
            \'status\' => $this->status,
            \'status_text\' => $this->status ? \'正常\' : \'黑名单\',
            
            // 统计信息
            \'statistics\' => $this->when($this->relationLoaded(\'inboundRecords\'), function () {
                return [
                    \'total_orders\' => $this->inboundRecords->count(),
                    \'total_value\' => $this->inboundRecords->sum(function($record) {
                        return $record->quantity * $record->unit_price;
                    }),
                    \'last_order_at\' => $this->inboundRecords->max(\'created_at\')
                ];
            }),
            
            \'created_at\' => $this->created_at,
            \'updated_at\' => $this->updated_at
        ];
    }

    /**
     * 获取评级文本
     */
    protected function getRatingText(): string
    {
        return match($this->rating) {
            5 => \'优秀\',
            4 => \'良好\',
            3 => \'一般\',
            2 => \'较差\',
            1 => \'很差\',
            default => \'未评级\'
        };
    }
}';

// 库存资源类
$inventoryResource = '<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class InventoryResource extends JsonResource
{
    /**
     * 将资源转换为数组
     */
    public function toArray($request): array
    {
        return [
            \'id\' => $this->id,
            
            // 食材信息
            \'ingredient\' => [
                \'id\' => $this->ingredient->id,
                \'name\' => $this->ingredient->name,
                \'unit\' => $this->ingredient->unit,
                \'min_stock\' => $this->ingredient->min_stock,
                \'category\' => [
                    \'id\' => $this->ingredient->category->id,
                    \'name\' => $this->ingredient->category->name
                ]
            ],
            
            // 库存数据
            \'current_quantity\' => $this->current_quantity,
            \'total_value\' => $this->total_value,
            \'average_price\' => $this->current_quantity > 0 ? 
                round($this->total_value / $this->current_quantity, 2) : 0,
            
            // 库存状态
            \'is_low_stock\' => $this->current_quantity <= $this->ingredient->min_stock,
            \'stock_status\' => $this->getStockStatus(),
            \'stock_level\' => $this->getStockLevel(),
            
            // 时间信息
            \'last_inbound_at\' => $this->last_inbound_at,
            \'last_outbound_at\' => $this->last_outbound_at,
            \'days_since_last_inbound\' => $this->last_inbound_at ? 
                now()->diffInDays($this->last_inbound_at) : null,
            
            \'updated_at\' => $this->updated_at
        ];
    }

    /**
     * 获取库存状态
     */
    protected function getStockStatus(): string
    {
        if ($this->current_quantity <= 0) {
            return \'缺货\';
        } elseif ($this->current_quantity <= $this->ingredient->min_stock) {
            return \'低库存\';
        } elseif ($this->current_quantity <= $this->ingredient->min_stock * 2) {
            return \'正常\';
        } else {
            return \'充足\';
        }
    }

    /**
     * 获取库存等级
     */
    protected function getStockLevel(): int
    {
        if ($this->current_quantity <= 0) {
            return 0; // 缺货
        } elseif ($this->current_quantity <= $this->ingredient->min_stock) {
            return 1; // 低库存
        } elseif ($this->current_quantity <= $this->ingredient->min_stock * 2) {
            return 2; // 正常
        } else {
            return 3; // 充足
        }
    }
}';

// 创建文件
if (!is_dir('app/Http/Resources')) {
    mkdir('app/Http/Resources', 0755, true);
}

file_put_contents('app/Http/Resources/IngredientResource.php', $ingredientResource);
file_put_contents('app/Http/Resources/InboundResource.php', $inboundResource);
file_put_contents('app/Http/Resources/SupplierResource.php', $supplierResource);
file_put_contents('app/Http/Resources/InventoryResource.php', $inventoryResource);

echo "✅ API资源类文件创建完成！\n";
echo "已创建以下资源类：\n";
echo "- IngredientResource (食材资源)\n";
echo "- InboundResource (入库记录资源)\n";
echo "- SupplierResource (供应商资源)\n";
echo "- InventoryResource (库存资源)\n";
echo "\n继续创建其他资源类...\n";
