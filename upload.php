<?php
// 文件上传处理脚本

header('Content-Type: application/json');

// 检查是否有文件上传
if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => '文件上传失败']);
    exit;
}

$file = $_FILES['file'];
$upload_type = $_POST['type'] ?? 'general'; // delivery_photo, weight_photo, general

// 验证文件类型
$allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
if (!in_array($file['type'], $allowed_types)) {
    echo json_encode(['success' => false, 'message' => '只允许上传图片文件 (JPG, PNG, GIF)']);
    exit;
}

// 验证文件大小 (最大5MB)
$max_size = 5 * 1024 * 1024; // 5MB
if ($file['size'] > $max_size) {
    echo json_encode(['success' => false, 'message' => '文件大小不能超过5MB']);
    exit;
}

// 创建上传目录
$upload_dir = 'uploads/';
if (!is_dir($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

// 根据类型创建子目录
$sub_dir = '';
switch ($upload_type) {
    case 'delivery_photo':
        $sub_dir = 'delivery/';
        break;
    case 'weight_photo':
        $sub_dir = 'weight/';
        break;
    default:
        $sub_dir = 'general/';
        break;
}

$target_dir = $upload_dir . $sub_dir;
if (!is_dir($target_dir)) {
    mkdir($target_dir, 0755, true);
}

// 生成唯一文件名
$file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
$file_name = date('YmdHis') . '_' . uniqid() . '.' . $file_extension;
$target_file = $target_dir . $file_name;

// 移动文件
if (move_uploaded_file($file['tmp_name'], $target_file)) {
    echo json_encode([
        'success' => true,
        'message' => '文件上传成功',
        'file_path' => $target_file,
        'file_name' => $file_name,
        'file_url' => $target_file
    ]);
} else {
    echo json_encode(['success' => false, 'message' => '文件保存失败']);
}
?>
