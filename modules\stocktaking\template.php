<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-clipboard-check"></i>
                食材盘点
            </h1>
            <div class="header-actions">
                <a href="index.php?action=create" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    新建盘点任务
                </a>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon bg-blue">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['total_tasks'] ?? 0) ?></h3>
                    <p>总盘点任务</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-orange">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['in_progress_tasks'] ?? 0) ?></h3>
                    <p>进行中任务</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-yellow">
                    <i class="fas fa-hourglass-half"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['pending_tasks'] ?? 0) ?></h3>
                    <p>待审核任务</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-green">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['approved_tasks'] ?? 0) ?></h3>
                    <p>已完成任务</p>
                </div>
            </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="search-bar" style="padding: 15px !important;">
            <form method="GET" action="index.php" class="search-bar-form" style="display: flex !important; flex-wrap: nowrap !important; gap: 10px !important; align-items: center !important; padding: 0 !important;">
                <div class="form-field text-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">任务信息</label>
                    <input type="text" name="search" placeholder="任务名称或编号..." 
                           value="<?= htmlspecialchars($data['search'] ?? '') ?>" class="form-control" style="height: 36px !important; width: 180px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">状态</label>
                    <select name="status" class="form-control" style="height: 36px !important; width: 140px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部状态</option>
                        <option value="draft" <?= ($data['status'] ?? '') === 'draft' ? 'selected' : '' ?>>草稿</option>
                        <option value="in_progress" <?= ($data['status'] ?? '') === 'in_progress' ? 'selected' : '' ?>>进行中</option>
                        <option value="pending_approval" <?= ($data['status'] ?? '') === 'pending_approval' ? 'selected' : '' ?>>待审核</option>
                        <option value="approved" <?= ($data['status'] ?? '') === 'approved' ? 'selected' : '' ?>>已审核</option>
                        <option value="rejected" <?= ($data['status'] ?? '') === 'rejected' ? 'selected' : '' ?>>已拒绝</option>
                    </select>
                </div>
                
                <div class="form-field date-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">开始日期</label>
                    <input type="date" name="date_from" 
                           value="<?= htmlspecialchars($data['date_from'] ?? '') ?>" class="form-control" style="height: 36px !important; width: 140px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                
                <div class="form-field date-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">结束日期</label>
                    <input type="date" name="date_to" 
                           value="<?= htmlspecialchars($data['date_to'] ?? '') ?>" class="form-control" style="height: 36px !important; width: 140px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                
                <button type="submit" class="btn btn-primary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #3b82f6 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                
                <a href="index.php" class="btn btn-secondary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #6b7280 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </form>
        </div>

        <!-- 盘点任务列表 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>任务信息</th>
                        <th>盘点类型</th>
                        <th>状态</th>
                        <th>执行人</th>
                        <th>盘点进度</th>
                        <th>差异项目</th>
                        <th>计划时间</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($data['tasks'])): ?>
                        <?php foreach ($data['tasks'] as $task): ?>
                            <tr>
                                <td>
                                    <div class="task-info">
                                        <div class="task-name"><?= htmlspecialchars($task['task_name']) ?></div>
                                        <div class="task-number">编号: <?= htmlspecialchars($task['task_number']) ?></div>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $taskTypes = [
                                        'full' => '<span class="type-badge type-full">全库盘点</span>',
                                        'category' => '<span class="type-badge type-category">分类盘点</span>',
                                        'specific' => '<span class="type-badge type-specific">指定盘点</span>',
                                        'random' => '<span class="type-badge type-random">抽样盘点</span>'
                                    ];
                                    echo $taskTypes[$task['task_type']] ?? $task['task_type'];
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    $statusMap = [
                                        'draft' => '<span class="status-badge status-draft">草稿</span>',
                                        'in_progress' => '<span class="status-badge status-progress">进行中</span>',
                                        'pending_approval' => '<span class="status-badge status-pending">待审核</span>',
                                        'approved' => '<span class="status-badge status-approved">已审核</span>',
                                        'rejected' => '<span class="status-badge status-rejected">已拒绝</span>'
                                    ];
                                    echo $statusMap[$task['status']] ?? $task['status'];
                                    ?>
                                </td>
                                <td>
                                    <div class="executor-info"><?= htmlspecialchars($task['executor_name'] ?? '') ?></div>
                                </td>
                                <td>
                                    <div class="progress-info">
                                        <div class="progress-bar">
                                            <?php 
                                            $progress = $task['total_items'] > 0 ? ($task['completed_items'] / $task['total_items']) * 100 : 0;
                                            ?>
                                            <div class="progress-fill" style="width: <?= $progress ?>%"></div>
                                        </div>
                                        <span class="progress-text"><?= $task['completed_items'] ?>/<?= $task['total_items'] ?></span>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($task['difference_items'] > 0): ?>
                                        <span class="difference-count"><?= $task['difference_items'] ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">无差异</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="date-range">
                                        <div><?= date('m-d', strtotime($task['start_date'])) ?></div>
                                        <small class="text-muted">至</small>
                                        <div><?= date('m-d', strtotime($task['end_date'])) ?></div>
                                    </div>
                                </td>
                                <td>
                                    <div class="time-info"><?= date('m-d H:i', strtotime($task['created_at'])) ?></div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="index.php?action=view&id=<?= $task['id'] ?>"
                                           class="btn btn-sm btn-info" title="查看盘点详情">
                                            <i class="fas fa-eye"></i>
                                            <span>查看</span>
                                        </a>

                                        <?php if ($task['status'] === 'draft'): ?>
                                            <button type="button" class="btn btn-sm btn-success"
                                                    onclick="startTask(<?= $task['id'] ?>)" title="开始盘点">
                                                <i class="fas fa-play"></i>
                                                <span>开始</span>
                                            </button>
                                            <a href="index.php?action=edit&id=<?= $task['id'] ?>"
                                               class="btn btn-sm btn-warning" title="编辑盘点任务">
                                                <i class="fas fa-edit"></i>
                                                <span>编辑</span>
                                            </a>
                                        <?php endif; ?>

                                        <?php if ($task['status'] === 'pending_approval'): ?>
                                            <button type="button" class="btn btn-sm btn-success"
                                                    onclick="approveTask(<?= $task['id'] ?>)" title="审核通过">
                                                <i class="fas fa-check"></i>
                                                <span>通过</span>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger"
                                                    onclick="rejectTask(<?= $task['id'] ?>)" title="拒绝审核">
                                                <i class="fas fa-times"></i>
                                                <span>拒绝</span>
                                            </button>
                                        <?php endif; ?>

                                        <?php if ($task['status'] === 'approved'): ?>
                                            <a href="index.php?action=report&id=<?= $task['id'] ?>"
                                               class="btn btn-sm btn-primary" title="查看盘点报告">
                                                <i class="fas fa-chart-bar"></i>
                                                <span>报告</span>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="9" class="text-center text-muted">
                                <i class="fas fa-inbox"></i>
                                暂无盘点任务
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 审核弹窗 -->
<div id="approveModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>审核盘点任务</h3>
            <button type="button" class="modal-close" onclick="closeModal('approveModal')">&times;</button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label>
                    <input type="checkbox" id="adjustInventory" checked>
                    同时调整库存（将差异更新到实际库存）
                </label>
            </div>
            <p class="text-muted">确认审核通过此盘点任务？</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-success" onclick="confirmApprove()">确认审核</button>
            <button type="button" class="btn btn-secondary" onclick="closeModal('approveModal')">取消</button>
        </div>
    </div>
</div>

<!-- 拒绝弹窗 -->
<div id="rejectModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>拒绝盘点任务</h3>
            <button type="button" class="modal-close" onclick="closeModal('rejectModal')">&times;</button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="rejectReason">拒绝原因</label>
                <textarea id="rejectReason" class="form-control" rows="3" placeholder="请输入拒绝原因..."></textarea>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-danger" onclick="confirmReject()">确认拒绝</button>
            <button type="button" class="btn btn-secondary" onclick="closeModal('rejectModal')">取消</button>
        </div>
    </div>
</div>

<style>
/* 食材盘点模块特有样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
}

.stat-icon.bg-blue { background: #4299e1; }
.stat-icon.bg-orange { background: #ed8936; }
.stat-icon.bg-yellow { background: #f6e05e; }
.stat-icon.bg-green { background: #48bb78; }

.stat-content h3 {
    margin: 0;
    font-size: 26px;
    font-weight: bold;
    color: #2d3748;
}

.stat-content p {
    margin: 5px 0 0 0;
    color: #718096;
    font-size: 16px;
}


.task-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.task-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 15px;
}

.task-number {
    font-size: 13px;
    color: #718096;
    font-family: 'Courier New', monospace;
}

.type-badge {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
}

.type-full { background: #bee3f8; color: #2b6cb0; }
.type-category { background: #c6f6d5; color: #22543d; }
.type-specific { background: #faf089; color: #744210; }
.type-random { background: #e2e8f0; color: #4a5568; }

.status-badge {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
}

.status-draft { background: #f7fafc; color: #2d3748; }
.status-progress { background: #fef5e7; color: #c05621; }
.status-pending { background: #fff5d6; color: #975a16; }
.status-approved { background: #f0fff4; color: #22543d; }
.status-rejected { background: #fed7d7; color: #c53030; }

.executor-info, .time-info {
    font-size: 14px;
    color: #4a5568;
}

.progress-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.progress-bar {
    width: 60px;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #48bb78;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 13px;
    color: #4a5568;
    font-weight: 500;
}

.difference-count {
    background: #fed7d7;
    color: #c53030;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
}

.date-range {
    text-align: center;
    font-size: 13px;
    color: #4a5568;
}

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #a0aec0;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 1000px;
    }
}
</style>

<script>
let currentTaskId = null;

function startTask(taskId) {
    if (confirm('确定要开始此盘点任务吗？')) {
        fetch('index.php?action=start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `id=${taskId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('操作失败: ' + error.message);
        });
    }
}

function approveTask(taskId) {
    currentTaskId = taskId;
    document.getElementById('approveModal').style.display = 'flex';
}

function rejectTask(taskId) {
    currentTaskId = taskId;
    document.getElementById('rejectModal').style.display = 'flex';
}

function confirmApprove() {
    const adjustInventory = document.getElementById('adjustInventory').checked ? 1 : 0;
    
    fetch('index.php?action=approve', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${currentTaskId}&adjust_inventory=${adjustInventory}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('approveModal');
            location.reload();
        } else {
            alert('操作失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('操作失败: ' + error.message);
    });
}

function confirmReject() {
    const reason = document.getElementById('rejectReason').value.trim();
    if (!reason) {
        alert('请输入拒绝原因');
        return;
    }
    
    fetch('index.php?action=reject', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${currentTaskId}&reason=${encodeURIComponent(reason)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('rejectModal');
            location.reload();
        } else {
            alert('操作失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('操作失败: ' + error.message);
    });
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    currentTaskId = null;
    if (modalId === 'rejectModal') {
        document.getElementById('rejectReason').value = '';
    }
}

// 点击模态框背景关闭
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
        currentTaskId = null;
    }
});
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>