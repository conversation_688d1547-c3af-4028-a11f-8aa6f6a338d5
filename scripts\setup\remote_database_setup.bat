@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 远程数据库配置脚本
REM 数据库信息: ************:3306 用户:sc 数据库:sc

title 食堂管理系统 - 远程数据库配置

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║           🏫 学校食堂食材出入库管理系统 🏫                    ║
echo ║                                                              ║
echo ║                   远程数据库配置脚本                          ║
echo ║                                                              ║
echo ║                    版本: v2.0                                ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 设置数据库连接信息
set DB_HOST=************
set DB_PORT=3306
set DB_USER=sc
set DB_PASS=pw5K4SsM7kZsjdxy
set DB_NAME=sc

echo 🔗 远程数据库连接信息：
echo 主机: %DB_HOST%
echo 端口: %DB_PORT%
echo 用户: %DB_USER%
echo 数据库: %DB_NAME%
echo.

echo 🔍 测试数据库连接...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% -e "SELECT 1 as test;" %DB_NAME% >nul 2>&1
if errorlevel 1 (
    echo ❌ 数据库连接失败
    echo.
    echo 可能的原因：
    echo 1. 网络连接问题
    echo 2. 防火墙阻止连接
    echo 3. 数据库服务器未启动
    echo 4. 用户权限不足
    echo.
    echo 请检查以下事项：
    echo - 确保网络连接正常
    echo - 确保MySQL客户端已安装
    echo - 确保数据库服务器允许远程连接
    echo.
    pause
    exit /b 1
)

echo ✅ 数据库连接成功！
echo.

echo 📋 检查数据库状态...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% -e "SHOW TABLES;" %DB_NAME% > temp_tables.txt 2>&1
if errorlevel 1 (
    echo ❌ 无法查看数据库表
    del temp_tables.txt 2>nul
    pause
    exit /b 1
)

set table_count=0
for /f %%i in ('type temp_tables.txt ^| find /c /v ""') do set table_count=%%i
del temp_tables.txt

echo 当前数据库中有 %table_count% 个对象
echo.

if %table_count% GTR 1 (
    echo ⚠️  数据库中已有数据，请选择操作：
    echo 1. 清空数据库并重新创建表结构 (危险操作)
    echo 2. 仅创建缺失的表
    echo 3. 跳过数据库初始化
    echo.
    set /p init_choice="请选择 (1-3): "
) else (
    echo 数据库为空，将创建完整的表结构
    set init_choice=1
)

if "%init_choice%"=="1" (
    echo.
    echo ⚠️  警告：这将删除数据库中的所有数据！
    set /p confirm="确认要继续吗？(输入 YES 确认): "
    if not "!confirm!"=="YES" (
        echo 操作已取消
        pause
        exit /b 0
    )
    
    echo 正在清空数据库...
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% -e "DROP DATABASE IF EXISTS %DB_NAME%; CREATE DATABASE %DB_NAME% CHARACTER SET utf8 COLLATE utf8_unicode_ci;" 2>nul
    
    echo 创建表结构...
    if exist "scripts\database\mysql56_compatible.sql" (
        mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% < scripts\database\mysql56_compatible.sql
        if errorlevel 1 (
            echo ❌ 表结构创建失败
            pause
            exit /b 1
        )
        echo ✅ 表结构创建完成
    ) else (
        echo ❌ 找不到数据库脚本文件
        pause
        exit /b 1
    )
    
) else if "%init_choice%"=="2" (
    echo 检查并创建缺失的表...
    REM 这里可以添加检查特定表是否存在的逻辑
    echo ⚠️  此功能需要手动实现
    
) else (
    echo 跳过数据库初始化
)

echo.
echo 📝 更新Laravel配置文件...

if not exist ".env" (
    if exist ".env.example" (
        copy .env.example .env
        php artisan key:generate 2>nul
    ) else (
        echo ❌ .env.example文件不存在
        pause
        exit /b 1
    )
)

echo 配置数据库连接...
powershell -Command "(Get-Content .env) -replace 'DB_HOST=.*', 'DB_HOST=%DB_HOST%' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'DB_PORT=.*', 'DB_PORT=%DB_PORT%' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'DB_DATABASE=.*', 'DB_DATABASE=%DB_NAME%' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'DB_USERNAME=.*', 'DB_USERNAME=%DB_USER%' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'DB_PASSWORD=.*', 'DB_PASSWORD=%DB_PASS%' | Set-Content .env"

REM 添加MySQL 5.6兼容配置
echo # 远程数据库配置 >> .env
echo DB_CHARSET=utf8 >> .env
echo DB_COLLATION=utf8_unicode_ci >> .env

echo ✅ Laravel配置已更新
echo.

echo 🧪 测试Laravel数据库连接...
php artisan migrate:status >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Laravel数据库连接测试失败
    echo 请检查.env配置是否正确
) else (
    echo ✅ Laravel数据库连接正常
)

echo.
echo 📊 数据库信息摘要：
echo ================================
echo 主机地址: %DB_HOST%
echo 端口: %DB_PORT%
echo 数据库名: %DB_NAME%
echo 用户名: %DB_USER%
echo 字符集: UTF8
echo 连接状态: 正常
echo ================================
echo.

echo 🎉 远程数据库配置完成！
echo.
echo 下一步操作：
echo 1. 运行 php artisan migrate (如果需要Laravel迁移)
echo 2. 运行 php artisan serve 启动开发服务器
echo 3. 访问 http://localhost:8000
echo.

echo 如果遇到连接问题，请检查：
echo - 网络连接是否正常
echo - 防火墙设置
echo - 数据库服务器是否允许远程连接
echo - 用户权限是否足够
echo.

pause
