<?php
/**
 * 测试移动端入库功能
 */

// 模拟完整的入库POST数据
$testData = [
    'batch_type' => 'batch',
    'supplier_id' => '1',
    'batch_number' => 'TEST' . microtime(true) * 10000,
    'inbound_date' => date('Y-m-d'),
    'operator_name' => '测试员',
    'notes' => '移动端入库测试',
    'order_id' => '',
    'is_new_order' => '1',
    'new_order_data' => json_encode([
        'order_info' => [
            'id' => 'new',
            'order_number' => 'PO' . date('YmdHis'),
            'supplier_id' => '1',
            'supplier_name' => '测试供应商',
            'order_date' => date('Y-m-d'),
            'order_amount' => 50.00,
            'notes' => '测试采购单'
        ],
        'items' => [
            [
                'id' => 0,
                'ingredient_id' => '1',
                'ingredient_name' => '白菜',
                'unit' => '斤',
                'quantity' => 10.0,
                'unit_price' => 2.5,
                'purpose' => '测试'
            ],
            [
                'id' => 1,
                'ingredient_id' => '2',
                'ingredient_name' => '萝卜',
                'unit' => '斤',
                'quantity' => 5.0,
                'unit_price' => 3.0,
                'purpose' => '测试'
            ]
        ]
    ]),
    'items' => [
        '0' => [
            'ingredient_id' => '1',
            'actual_quantity' => '10.0',
            'unit_price' => '2.5',
            'order_quantity' => '10.0'
        ],
        '1' => [
            'ingredient_id' => '2',
            'actual_quantity' => '5.0',
            'unit_price' => '3.0',
            'order_quantity' => '5.0'
        ]
    ]
];

// 使用 file_get_contents 替代 cURL
$postData = http_build_query($testData);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/x-www-form-urlencoded',
            'X-Requested-With: XMLHttpRequest'
        ],
        'content' => $postData
    ]
]);

// 执行请求
$response = file_get_contents('http://localhost:8000/mobile/inbound.php', false, $context);

// 输出结果
if ($response === false) {
    echo "请求失败\n";
    exit;
}

echo "响应内容: " . $response . "\n";

// 尝试解析JSON响应
$jsonResponse = json_decode($response, true);
if ($jsonResponse) {
    echo "\n解析后的JSON响应:\n";
    echo "成功: " . ($jsonResponse['success'] ? '是' : '否') . "\n";
    echo "消息: " . $jsonResponse['message'] . "\n";
    if (isset($jsonResponse['data'])) {
        echo "数据: " . print_r($jsonResponse['data'], true) . "\n";
    }
} else {
    echo "\n无法解析为JSON，原始响应:\n" . $response . "\n";
}
?>