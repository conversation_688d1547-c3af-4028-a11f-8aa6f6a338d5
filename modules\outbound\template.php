<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-file-export"></i>
                出库单管理
            </h1>
            <div class="header-actions">
                <a href="index.php?action=create" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    单个出库
                </a>
                <a href="index.php?action=create&list=1" class="btn btn-success">
                    <i class="fas fa-list-check"></i>
                    选择入库单出库
                </a>
                <a href="index.php?action=batch_create" class="btn btn-info">
                    <i class="fas fa-layer-group"></i>
                    批量出库
                </a>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon bg-blue">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format(count($records)) ?></h3>
                    <p>出库单数量</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-green">
                    <i class="fas fa-list"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format(array_sum(array_column($records, 'item_count'))) ?></h3>
                    <p>出库项目数</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-orange">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-content">
                    <h3>¥<?= number_format(array_sum(array_column($records, 'total_amount')), 2) ?></h3>
                    <p>总出库价值</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-purple">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['meal_days'] ?? 0) ?></h3>
                    <p>供餐天数</p>
                </div>
            </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="search-bar" style="padding: 15px !important;">
            <form method="GET" action="index.php" class="search-bar-form" style="display: flex !important; flex-wrap: nowrap !important; gap: 10px !important; align-items: center !important; padding: 0 !important;">
                <div class="form-field text-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">搜索内容</label>
                    <input type="text" name="search" placeholder="食材名称或批次号..." 
                           value="<?= htmlspecialchars($data['search'] ?? '') ?>" class="form-control" style="height: 36px !important; width: 180px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">餐次</label>
                    <select name="meal_type" class="form-control" style="height: 36px !important; width: 120px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部餐次</option>
                        <option value="breakfast" <?= ($data['meal_type'] ?? '') === 'breakfast' ? 'selected' : '' ?>>早餐</option>
                        <option value="lunch" <?= ($data['meal_type'] ?? '') === 'lunch' ? 'selected' : '' ?>>午餐</option>
                        <option value="dinner" <?= ($data['meal_type'] ?? '') === 'dinner' ? 'selected' : '' ?>>晚餐</option>
                    </select>
                </div>
                
                <div class="form-field date-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">开始日期</label>
                    <input type="date" name="date_from" 
                           value="<?= htmlspecialchars($data['date_from'] ?? '') ?>" class="form-control" style="height: 36px !important; width: 140px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                
                <div class="form-field date-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">结束日期</label>
                    <input type="date" name="date_to" 
                           value="<?= htmlspecialchars($data['date_to'] ?? '') ?>" class="form-control" style="height: 36px !important; width: 140px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                
                <button type="submit" class="btn btn-primary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #3b82f6 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                
                <a href="index.php" class="btn btn-secondary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #6b7280 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </form>
        </div>

        <!-- 出库记录列表 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>出库单号</th>
                        <th>餐次类型</th>
                        <th>用餐日期</th>
                        <th>项目数量</th>
                        <th>总金额</th>
                        <th>状态</th>
                        <th>操作员</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($records)): ?>
                        <?php foreach ($records as $record): ?>
                            <tr>
                                <td>
                                    <div class="order-number">
                                        <strong><?= htmlspecialchars($record['order_number'] ?? $record['batch_number'] ?? 'N/A') ?></strong>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $mealTypes = [
                                        'breakfast' => '<span class="meal-badge meal-breakfast">早餐</span>',
                                        'lunch' => '<span class="meal-badge meal-lunch">午餐</span>',
                                        'dinner' => '<span class="meal-badge meal-dinner">晚餐</span>',
                                        'other' => '<span class="meal-badge meal-other">其他</span>'
                                    ];
                                    echo $mealTypes[$record['meal_type'] ?? 'other'] ?? '<span class="meal-badge meal-other">其他</span>';
                                    ?>
                                </td>
                                <td>
                                    <div class="date-info"><?= date('Y-m-d', strtotime($record['meal_date'] ?? $record['created_at'])) ?></div>
                                </td>
                                <td>
                                    <div class="item-count">
                                        <span class="badge bg-info"><?= ($record['item_count'] ?? 1) ?> 项</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="amount-info">
                                        <strong>¥<?= number_format($record['total_amount'] ?? 0, 2) ?></strong>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $status_class = [
                                        'pending' => 'warning',
                                        'completed' => 'success',
                                        'cancelled' => 'danger'
                                    ];
                                    $status_text = [
                                        'pending' => '待处理',
                                        'completed' => '已完成',
                                        'cancelled' => '已取消'
                                    ];
                                    ?>
                                    <span class="badge bg-<?= $status_class[$record['status'] ?? 'completed'] ?? 'success' ?>">
                                        <?= $status_text[$record['status'] ?? 'completed'] ?? '已完成' ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="operator-info"><?= htmlspecialchars($record['operator_name'] ?? '系统') ?></div>
                                </td>
                                <td>
                                    <div class="time-info"><?= date('m-d H:i', strtotime($record['created_at'])) ?></div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="index.php?action=view&id=<?= $record['id'] ?>"
                                           class="btn btn-sm btn-info" title="查看出库详情">
                                            <i class="fas fa-eye"></i>
                                            <span>查看</span>
                                        </a>
                                        <a href="index.php?action=delete&id=<?= $record['id'] ?>"
                                           class="btn btn-sm btn-danger" title="删除出库记录"
                                           onclick="return confirm('确定要删除这条出库记录吗？删除后会恢复相应的库存。')">
                                            <i class="fas fa-trash"></i>
                                            <span>删除</span>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="11" class="text-center text-muted">
                                <i class="fas fa-inbox"></i>
                                暂无出库记录
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
/* 食材出库模块特有样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
}

.stat-icon.bg-blue { background: #4299e1; }
.stat-icon.bg-green { background: #48bb78; }
.stat-icon.bg-orange { background: #ed8936; }
.stat-icon.bg-purple { background: #9f7aea; }

.stat-content h3 {
    margin: 0;
    font-size: 26px;
    font-weight: bold;
    color: #2d3748;
}

.stat-content p {
    margin: 5px 0 0 0;
    color: #718096;
    font-size: 16px;
}


.ingredient-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.ingredient-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 15px;
}

.batch-number {
    font-size: 13px;
    color: #718096;
    font-family: 'Courier New', monospace;
}

.category-badge {
    background: #bee3f8;
    color: #2b6cb0;
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
}

.quantity-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.quantity-value {
    font-weight: bold;
    color: #2d3748;
    font-size: 16px;
}

.quantity-unit {
    font-size: 12px;
    color: #718096;
}

.meal-badge {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
}

.meal-breakfast {
    background: #faf089;
    color: #744210;
}

.meal-lunch {
    background: #c6f6d5;
    color: #22543d;
}

.meal-dinner {
    background: #bee3f8;
    color: #2b6cb0;
}

.date-info, .purpose-info, .operator-info, .time-info {
    font-size: 14px;
    color: #4a5568;
}

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 1000px;
    }
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>