<?php
/**
 * 库存盘点控制器
 */
require_once dirname(__DIR__, 2) . '/includes/BaseController.php';
require_once dirname(__DIR__, 2) . '/includes/helpers.php';

class StocktakingController extends BaseController
{
    protected function init()
    {
        $this->setTemplateData([
            'page_title' => '库存盘点 - ' . $this->config['name'],
            'current_module' => 'stocktaking'
        ]);
    }

    public function handleRequest()
    {
        switch ($this->request['action']) {
            case 'create':
                return $this->create();
            case 'edit':
                return $this->edit();
            case 'view':
                return $this->view();
            case 'delete':
                return $this->delete();
            case 'start':
                return $this->start();
            case 'submit':
                return $this->submit();
            case 'approve':
                return $this->approve();
            case 'reject':
                return $this->reject();
            case 'report':
                return $this->report();
            case 'export':
                return $this->export();
            case 'index':
            default:
                return $this->index();
        }
    }

    /**
     * 盘点任务列表
     */
    private function index()
    {
        try {
            // 获取搜索参数
            $search = $this->request['get']['search'] ?? '';
            $status = $this->request['get']['status'] ?? '';
            $date_from = $this->request['get']['date_from'] ?? '';
            $date_to = $this->request['get']['date_to'] ?? '';

            // 构建查询条件
            $where = ['st.status != "deleted"'];
            $params = [];

            if ($search) {
                $where[] = '(st.task_name LIKE ? OR st.task_number LIKE ?)';
                $params[] = '%' . $search . '%';
                $params[] = '%' . $search . '%';
            }

            if ($status) {
                $where[] = 'st.status = ?';
                $params[] = $status;
            }

            if ($date_from) {
                $where[] = 'DATE(st.start_date) >= ?';
                $params[] = $date_from;
            }

            if ($date_to) {
                $where[] = 'DATE(st.start_date) <= ?';
                $params[] = $date_to;
            }

            $whereClause = 'WHERE ' . implode(' AND ', $where);

            // 获取盘点任务列表
            $tasks = $this->db->fetchAll("
                SELECT 
                    st.*,
                    u1.name as creator_name,
                    u2.name as executor_name,
                    u3.name as approver_name,
                    COUNT(sti.id) as total_items,
                    SUM(CASE WHEN sti.status = 'completed' THEN 1 ELSE 0 END) as completed_items,
                    SUM(CASE WHEN sti.difference_quantity != 0 THEN 1 ELSE 0 END) as difference_items
                FROM stocktaking_tasks st
                LEFT JOIN users u1 ON st.created_by = u1.id
                LEFT JOIN users u2 ON st.executor_id = u2.id
                LEFT JOIN users u3 ON st.approved_by = u3.id
                LEFT JOIN stocktaking_items sti ON st.id = sti.task_id
                $whereClause
                GROUP BY st.id
                ORDER BY st.created_at DESC
                LIMIT 50
            ", $params);

            // 获取统计信息
            $stats = $this->getStocktakingStats();

        } catch (Exception $e) {
            // 使用模拟数据
            $tasks = $this->getMockTasks();
            $stats = $this->getMockStats();
        }

        $this->setTemplateData([
            'tasks' => $tasks,
            'stats' => $stats,
            'search' => $search,
            'status' => $status,
            'date_from' => $date_from,
            'date_to' => $date_to
        ]);

        $this->render('template.php');
    }

    /**
     * 创建盘点任务
     */
    private function create()
    {
        if ($this->request['method'] === 'POST') {
            try {
                $data = [
                    'task_number' => $this->generateTaskNumber(),
                    'task_name' => trim($this->request['post']['task_name']),
                    'task_type' => $this->request['post']['task_type'],
                    'description' => trim($this->request['post']['description'] ?? ''),
                    'start_date' => $this->request['post']['start_date'],
                    'end_date' => $this->request['post']['end_date'],
                    'executor_id' => intval($this->request['post']['executor_id']),
                    'category_ids' => $this->request['post']['category_ids'] ?? [],
                    'ingredient_ids' => $this->request['post']['ingredient_ids'] ?? [],
                    'status' => 'draft',
                    'created_by' => 1, // TODO: 获取当前用户ID
                    'created_at' => date('Y-m-d H:i:s')
                ];

                // 验证数据
                $this->validateTaskData($data);

                // 开始事务
                $this->db->beginTransaction();

                // 插入盘点任务
                $taskId = $this->db->insert('stocktaking_tasks', [
                    'task_number' => $data['task_number'],
                    'task_name' => $data['task_name'],
                    'task_type' => $data['task_type'],
                    'description' => $data['description'],
                    'start_date' => $data['start_date'],
                    'end_date' => $data['end_date'],
                    'executor_id' => $data['executor_id'],
                    'status' => $data['status'],
                    'created_by' => $data['created_by'],
                    'created_at' => $data['created_at']
                ]);

                // 生成盘点项目
                $this->generateStocktakingItems($taskId, $data);

                $this->db->commit();
                $this->redirect('index.php', '盘点任务创建成功', 'success');

            } catch (Exception $e) {
                try {
                    $this->db->rollback();
                } catch (Exception $rollbackException) {
                    // 忽略回滚错误
                }
                $this->setTemplateData('error_message', $e->getMessage());
            }
        }

        $this->loadFormData();
        $this->render('create-template.php');
    }

    /**
     * 查看盘点任务详情
     */
    private function view()
    {
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            // 获取盘点任务信息
            $task = $this->db->fetchOne("
                SELECT 
                    st.*,
                    u1.name as creator_name,
                    u2.name as executor_name,
                    u3.name as approver_name
                FROM stocktaking_tasks st
                LEFT JOIN users u1 ON st.created_by = u1.id
                LEFT JOIN users u2 ON st.executor_id = u2.id
                LEFT JOIN users u3 ON st.approved_by = u3.id
                WHERE st.id = ?
            ", [$id]);

            if (!$task) {
                $this->redirect('index.php', '盘点任务不存在', 'error');
                return;
            }

            // 获取盘点项目列表
            $items = $this->db->fetchAll("
                SELECT 
                    sti.*,
                    i.name as ingredient_name,
                    i.unit,
                    ic.name as category_name
                FROM stocktaking_items sti
                LEFT JOIN ingredients i ON sti.ingredient_id = i.id
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                WHERE sti.task_id = ?
                ORDER BY ic.name ASC, i.name ASC
            ", [$id]);

            // 计算统计信息
            $itemStats = [
                'total_items' => count($items),
                'completed_items' => 0,
                'difference_items' => 0,
                'total_book_value' => 0,
                'total_actual_value' => 0,
                'total_difference_value' => 0
            ];

            foreach ($items as $item) {
                if ($item['status'] === 'completed') {
                    $itemStats['completed_items']++;
                }
                if ($item['difference_quantity'] != 0) {
                    $itemStats['difference_items']++;
                }
                $itemStats['total_book_value'] += $item['book_value'];
                $itemStats['total_actual_value'] += $item['actual_value'];
                $itemStats['total_difference_value'] += $item['difference_value'];
            }

        } catch (Exception $e) {
            $task = $this->getMockTasks()[0] ?? null;
            $items = $this->getMockItems();
            $itemStats = $this->getMockItemStats();
        }

        $this->setTemplateData([
            'task' => $task,
            'items' => $items,
            'item_stats' => $itemStats
        ]);

        $this->render('view-template.php');
    }

    /**
     * 开始盘点
     */
    private function start()
    {
        $id = intval($this->request['post']['id'] ?? 0);

        if (!$id) {
            $this->jsonResponse(['success' => false, 'message' => '参数错误']);
            return;
        }

        try {
            // 更新任务状态
            $this->db->update('stocktaking_tasks', [
                'status' => 'in_progress',
                'actual_start_date' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$id]);

            $this->jsonResponse(['success' => true, 'message' => '盘点任务已开始']);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 提交盘点结果
     */
    private function submit()
    {
        $taskId = intval($this->request['post']['task_id'] ?? 0);
        $items = $this->request['post']['items'] ?? [];

        if (!$taskId || empty($items)) {
            $this->jsonResponse(['success' => false, 'message' => '参数错误']);
            return;
        }

        try {
            // 开始事务
            $this->db->beginTransaction();

            foreach ($items as $itemId => $itemData) {
                $actualQuantity = floatval($itemData['actual_quantity']);
                $notes = trim($itemData['notes'] ?? '');

                // 获取盘点项目信息
                $item = $this->db->fetchOne("
                    SELECT sti.*, i.unit_price 
                    FROM stocktaking_items sti
                    LEFT JOIN ingredients i ON sti.ingredient_id = i.id
                    WHERE sti.id = ? AND sti.task_id = ?
                ", [$itemId, $taskId]);

                if (!$item) continue;

                // 计算差异
                $differenceQuantity = $actualQuantity - $item['book_quantity'];
                $actualValue = $actualQuantity * $item['unit_price'];
                $differenceValue = $differenceQuantity * $item['unit_price'];

                // 更新盘点项目
                $this->db->update('stocktaking_items', [
                    'actual_quantity' => $actualQuantity,
                    'difference_quantity' => $differenceQuantity,
                    'actual_value' => $actualValue,
                    'difference_value' => $differenceValue,
                    'notes' => $notes,
                    'status' => 'completed',
                    'completed_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$itemId]);
            }

            // 检查是否所有项目都已完成
            $uncompletedCount = $this->db->fetchOne("
                SELECT COUNT(*) as count 
                FROM stocktaking_items 
                WHERE task_id = ? AND status != 'completed'
            ", [$taskId])['count'];

            // 如果所有项目都已完成，更新任务状态为待审核
            if ($uncompletedCount == 0) {
                $this->db->update('stocktaking_tasks', [
                    'status' => 'pending_approval',
                    'completed_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$taskId]);
            }

            $this->db->commit();
            $this->jsonResponse(['success' => true, 'message' => '盘点结果提交成功']);

        } catch (Exception $e) {
            try {
                $this->db->rollback();
            } catch (Exception $rollbackException) {
                // 忽略回滚错误
            }
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 审批盘点结果
     */
    private function approve()
    {
        $id = intval($this->request['post']['id'] ?? 0);
        $adjustInventory = intval($this->request['post']['adjust_inventory'] ?? 0);

        if (!$id) {
            $this->jsonResponse(['success' => false, 'message' => '参数错误']);
            return;
        }

        try {
            // 开始事务
            $this->db->beginTransaction();

            // 如果选择调整库存，更新实际库存
            if ($adjustInventory) {
                $items = $this->db->fetchAll("
                    SELECT ingredient_id, difference_quantity 
                    FROM stocktaking_items 
                    WHERE task_id = ? AND difference_quantity != 0
                ", [$id]);

                foreach ($items as $item) {
                    $this->db->query("
                        UPDATE ingredients 
                        SET current_stock = current_stock + ? 
                        WHERE id = ?
                    ", [$item['difference_quantity'], $item['ingredient_id']]);
                }
            }

            // 更新任务状态
            $this->db->update('stocktaking_tasks', [
                'status' => 'approved',
                'approved_by' => 1, // TODO: 获取当前用户ID
                'approved_at' => date('Y-m-d H:i:s'),
                'adjust_inventory' => $adjustInventory,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$id]);

            $this->db->commit();
            $this->jsonResponse(['success' => true, 'message' => '盘点结果审批成功']);

        } catch (Exception $e) {
            try {
                $this->db->rollback();
            } catch (Exception $rollbackException) {
                // 忽略回滚错误
            }
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 拒绝盘点结果
     */
    private function reject()
    {
        $id = intval($this->request['post']['id'] ?? 0);
        $reason = trim($this->request['post']['reason'] ?? '');

        if (!$id) {
            $this->jsonResponse(['success' => false, 'message' => '参数错误']);
            return;
        }

        try {
            $this->db->update('stocktaking_tasks', [
                'status' => 'rejected',
                'approved_by' => 1, // TODO: 获取当前用户ID
                'approved_at' => date('Y-m-d H:i:s'),
                'reject_reason' => $reason,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$id]);

            $this->jsonResponse(['success' => true, 'message' => '盘点结果已拒绝']);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 盘点报告
     */
    private function report()
    {
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            // 获取盘点任务信息
            $task = $this->db->fetchOne("
                SELECT * FROM stocktaking_tasks WHERE id = ?
            ", [$id]);

            if (!$task || $task['status'] !== 'approved') {
                $this->redirect('index.php', '盘点任务不存在或未审批', 'error');
                return;
            }

            // 获取盘点统计数据
            $reportData = $this->generateReportData($id);

        } catch (Exception $e) {
            $task = $this->getMockTasks()[0];
            $reportData = $this->getMockReportData();
        }

        $this->setTemplateData([
            'task' => $task,
            'report_data' => $reportData
        ]);

        $this->render('report-template.php');
    }

    /**
     * 生成任务编号
     */
    private function generateTaskNumber()
    {
        return 'ST' . date('YmdHis') . rand(100, 999);
    }

    /**
     * 验证任务数据
     */
    private function validateTaskData($data)
    {
        if (empty($data['task_name'])) {
            throw new Exception('任务名称不能为空');
        }
        if (empty($data['task_type'])) {
            throw new Exception('盘点类型不能为空');
        }
        if (empty($data['start_date'])) {
            throw new Exception('开始日期不能为空');
        }
        if (empty($data['end_date'])) {
            throw new Exception('结束日期不能为空');
        }
        if (strtotime($data['end_date']) < strtotime($data['start_date'])) {
            throw new Exception('结束日期不能早于开始日期');
        }
        if (!$data['executor_id']) {
            throw new Exception('请选择执行人');
        }
    }

    /**
     * 生成盘点项目
     */
    private function generateStocktakingItems($taskId, $data)
    {
        $sql = "SELECT i.*, ic.name as category_name FROM ingredients i 
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id 
                WHERE i.status = 1";
        $params = [];

        // 根据盘点类型筛选食材
        if ($data['task_type'] === 'category' && !empty($data['category_ids'])) {
            $placeholders = str_repeat('?,', count($data['category_ids']) - 1) . '?';
            $sql .= " AND i.category_id IN ($placeholders)";
            $params = $data['category_ids'];
        } elseif ($data['task_type'] === 'specific' && !empty($data['ingredient_ids'])) {
            $placeholders = str_repeat('?,', count($data['ingredient_ids']) - 1) . '?';
            $sql .= " AND i.id IN ($placeholders)";
            $params = $data['ingredient_ids'];
        }

        $sql .= " ORDER BY ic.name ASC, i.name ASC";

        $ingredients = $this->db->fetchAll($sql, $params);

        foreach ($ingredients as $ingredient) {
            $bookValue = $ingredient['current_stock'] * $ingredient['unit_price'];
            
            $this->db->insert('stocktaking_items', [
                'task_id' => $taskId,
                'ingredient_id' => $ingredient['id'],
                'book_quantity' => $ingredient['current_stock'],
                'unit_price' => $ingredient['unit_price'],
                'book_value' => $bookValue,
                'actual_quantity' => 0,
                'actual_value' => 0,
                'difference_quantity' => 0,
                'difference_value' => 0,
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * 加载表单数据
     */
    private function loadFormData()
    {
        try {
            $categories = $this->db->fetchAll("
                SELECT id, name 
                FROM ingredient_categories 
                WHERE status = 1 
                ORDER BY name ASC
            ");

            $ingredients = $this->db->fetchAll("
                SELECT i.id, i.name, i.unit, ic.name as category_name
                FROM ingredients i
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                WHERE i.status = 1
                ORDER BY ic.name ASC, i.name ASC
            ");

            $users = $this->db->fetchAll("
                SELECT id, name, real_name
                FROM users 
                WHERE status = 1 
                ORDER BY name ASC
            ");

        } catch (Exception $e) {
            $categories = $this->getMockCategories();
            $ingredients = $this->getMockIngredients();
            $users = $this->getMockUsers();
        }

        $this->setTemplateData([
            'categories' => $categories,
            'ingredients' => $ingredients,
            'users' => $users
        ]);
    }

    /**
     * 获取盘点统计信息
     */
    private function getStocktakingStats()
    {
        try {
            return $this->db->fetchOne("
                SELECT 
                    COUNT(*) as total_tasks,
                    SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_tasks,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_tasks,
                    SUM(CASE WHEN status = 'pending_approval' THEN 1 ELSE 0 END) as pending_tasks,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_tasks,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_tasks
                FROM stocktaking_tasks
                WHERE status != 'deleted'
            ");
        } catch (Exception $e) {
            return $this->getMockStats();
        }
    }

    /**
     * 生成报告数据
     */
    private function generateReportData($taskId)
    {
        try {
            $summary = $this->db->fetchOne("
                SELECT 
                    COUNT(*) as total_items,
                    SUM(book_value) as total_book_value,
                    SUM(actual_value) as total_actual_value,
                    SUM(difference_value) as total_difference_value,
                    SUM(CASE WHEN difference_quantity > 0 THEN 1 ELSE 0 END) as surplus_items,
                    SUM(CASE WHEN difference_quantity < 0 THEN 1 ELSE 0 END) as shortage_items,
                    SUM(CASE WHEN difference_quantity = 0 THEN 1 ELSE 0 END) as normal_items
                FROM stocktaking_items 
                WHERE task_id = ?
            ", [$taskId]);

            $differences = $this->db->fetchAll("
                SELECT 
                    sti.*,
                    i.name as ingredient_name,
                    i.unit,
                    ic.name as category_name
                FROM stocktaking_items sti
                LEFT JOIN ingredients i ON sti.ingredient_id = i.id
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                WHERE sti.task_id = ? AND sti.difference_quantity != 0
                ORDER BY ABS(sti.difference_value) DESC
            ", [$taskId]);

            return [
                'summary' => $summary,
                'differences' => $differences
            ];

        } catch (Exception $e) {
            return $this->getMockReportData();
        }
    }

    /**
     * 获取模拟数据
     */
    private function getMockTasks()
    {
        return [
            [
                'id' => 1,
                'task_number' => 'ST20241201001',
                'task_name' => '12月全库盘点',
                'task_type' => 'full',
                'status' => 'approved',
                'start_date' => '2024-12-01',
                'end_date' => '2024-12-02',
                'creator_name' => '管理员',
                'executor_name' => '张仓管',
                'total_items' => 150,
                'completed_items' => 150,
                'difference_items' => 25,
                'created_at' => '2024-11-30 10:00:00'
            ]
        ];
    }

    private function getMockStats()
    {
        return [
            'total_tasks' => 8,
            'draft_tasks' => 1,
            'in_progress_tasks' => 2,
            'pending_tasks' => 1,
            'approved_tasks' => 4,
            'today_tasks' => 1
        ];
    }

    private function getMockCategories()
    {
        return [
            ['id' => 1, 'name' => '蔬菜类'],
            ['id' => 2, 'name' => '肉类'],
            ['id' => 3, 'name' => '粮食类']
        ];
    }

    private function getMockIngredients()
    {
        return [
            ['id' => 1, 'name' => '白菜', 'unit' => '斤', 'category_name' => '蔬菜类'],
            ['id' => 2, 'name' => '猪肉', 'unit' => '斤', 'category_name' => '肉类']
        ];
    }

    private function getMockUsers()
    {
        return [
            ['id' => 1, 'name' => 'admin', 'real_name' => '管理员'],
            ['id' => 2, 'name' => 'warehouse', 'real_name' => '张仓管']
        ];
    }

    private function getMockItems()
    {
        return [
            [
                'id' => 1,
                'ingredient_name' => '白菜',
                'unit' => '斤',
                'category_name' => '蔬菜类',
                'book_quantity' => 50.0,
                'actual_quantity' => 48.5,
                'difference_quantity' => -1.5,
                'unit_price' => 2.50,
                'book_value' => 125.00,
                'actual_value' => 121.25,
                'difference_value' => -3.75,
                'status' => 'completed'
            ]
        ];
    }

    private function getMockItemStats()
    {
        return [
            'total_items' => 150,
            'completed_items' => 150,
            'difference_items' => 25,
            'total_book_value' => 15680.50,
            'total_actual_value' => 15456.25,
            'total_difference_value' => -224.25
        ];
    }

    private function getMockReportData()
    {
        return [
            'summary' => [
                'total_items' => 150,
                'total_book_value' => 15680.50,
                'total_actual_value' => 15456.25,
                'total_difference_value' => -224.25,
                'surplus_items' => 12,
                'shortage_items' => 13,
                'normal_items' => 125
            ],
            'differences' => []
        ];
    }
}
?>