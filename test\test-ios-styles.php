<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试iOS样式统一优化</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-mobile { background: #17a2b8; }
        .btn-primary { background: #667eea; }
        .feature-box { background: #e6f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 15px 0; border-radius: 5px; }
        .step-box { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; }
        .mobile-demo { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; }
        .mobile-frame { width: 320px; height: 640px; border: 8px solid #333; border-radius: 25px; margin: 0 auto; background: white; position: relative; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .mobile-screen { width: 100%; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; display: flex; flex-direction: column; }
        .mobile-header { background: rgba(255,255,255,0.95); color: #667eea; padding: 15px; display: flex; align-items: center; justify-content: center; font-weight: bold; }
        .mobile-content { flex: 1; padding: 20px; display: flex; flex-direction: column; gap: 15px; overflow-y: auto; }
        
        /* iOS统一样式演示 */
        .form-demo {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin: 10px 0;
        }
        
        .form-group-demo {
            margin-bottom: 15px;
        }
        
        .form-label-demo {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        /* 统一的iOS样式 */
        .ios-input, .ios-select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 16px;
            color: #333 !important;
            transition: all 0.3s ease;
            box-sizing: border-box;
            width: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            height: 48px;
            min-height: 48px;
            line-height: 1.2;
            vertical-align: top;
        }
        
        .ios-input:focus, .ios-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
        
        .ios-select {
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23667eea' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .ios-date {
            color: #333 !important;
            background: white !important;
        }
        
        .comparison-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        .comparison-table th { background: #f5f5f5; }
        .before-col { background: #fff3cd; }
        .after-col { background: #d4edda; }
        .code-box { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>✅ iOS样式统一优化完成！</h1>
    
    <div class="test-section">
        <h2>🎯 优化目标</h2>
        
        <div class="feature-box">
            <h4>📱 iOS样式风格统一</h4>
            <p>解决苹果手机上表单元素样式不统一的问题，提供一致的用户体验。</p>
            
            <h5>主要问题：</h5>
            <ul>
                <li>🔧 <strong>选择框样式不一致</strong>：采购单和供应商下拉框外观不同</li>
                <li>🎨 <strong>日期显示颜色异常</strong>：iOS上日期输入框显示为蓝色</li>
                <li>📐 <strong>边框和圆角不统一</strong>：各输入框的边框粗细、圆角大小不一致</li>
                <li>🍎 <strong>iOS特有样式问题</strong>：系统默认样式覆盖了自定义样式</li>
            </ul>
            
            <h5>解决方案：</h5>
            <ul>
                <li>🔧 <strong>重置系统样式</strong>：使用-webkit-appearance: none移除系统默认样式</li>
                <li>🎨 <strong>统一视觉风格</strong>：所有表单元素使用相同的边框、圆角、内边距</li>
                <li>📱 <strong>iOS字体优化</strong>：使用-apple-system字体栈确保最佳显示</li>
                <li>🎯 <strong>强制颜色显示</strong>：使用!important确保颜色不被系统覆盖</li>
                <li>🔽 <strong>自定义下拉箭头</strong>：使用SVG图标替换系统默认箭头</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 样式对比</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>元素类型</th>
                    <th class="before-col">优化前问题</th>
                    <th class="after-col">优化后效果</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>选择框</strong></td>
                    <td class="before-col">系统默认样式，箭头不统一</td>
                    <td class="after-col">自定义SVG箭头，样式统一</td>
                </tr>
                <tr>
                    <td><strong>日期输入</strong></td>
                    <td class="before-col">iOS上显示为蓝色文字</td>
                    <td class="after-col">强制显示为黑色文字</td>
                </tr>
                <tr>
                    <td><strong>边框圆角</strong></td>
                    <td class="before-col">6px、8px混用</td>
                    <td class="after-col">统一使用8px圆角</td>
                </tr>
                <tr>
                    <td><strong>内边距</strong></td>
                    <td class="before-col">8px、12px不统一</td>
                    <td class="after-col">统一使用12px 15px</td>
                </tr>
                <tr>
                    <td><strong>字体大小</strong></td>
                    <td class="before-col">13px、14px混用</td>
                    <td class="after-col">统一使用16px（iOS推荐）</td>
                </tr>
                <tr>
                    <td><strong>焦点效果</strong></td>
                    <td class="before-col">不一致的边框和阴影</td>
                    <td class="after-col">统一的蓝色边框和阴影</td>
                </tr>
                <tr>
                    <td><strong>元素高度</strong></td>
                    <td class="before-col">选择框、日期框高度不统一</td>
                    <td class="after-col">所有表单元素统一48px高度</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <h2>📱 iOS样式演示</h2>
        
        <div class="mobile-demo">
            <h4>统一后的表单样式</h4>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <div class="mobile-header">
                        🍎 iOS样式统一
                    </div>
                    <div class="mobile-content">
                        <div class="form-demo">
                            <div class="form-group-demo">
                                <label class="form-label-demo">📋 选择采购单</label>
                                <select class="ios-select">
                                    <option>请选择采购单</option>
                                    <option>PO202506300001 - 蔬菜采购</option>
                                    <option>PO202506300002 - 肉类采购</option>
                                </select>
                            </div>
                            
                            <div class="form-group-demo">
                                <label class="form-label-demo">📅 入库日期</label>
                                <input type="date" class="ios-input ios-date" value="2025-06-30">
                            </div>
                            
                            <div class="form-group-demo">
                                <label class="form-label-demo">👤 操作员</label>
                                <input type="text" class="ios-input" value="管理员">
                            </div>
                            
                            <div class="form-group-demo">
                                <label class="form-label-demo">🚚 供应商</label>
                                <select class="ios-select">
                                    <option>请选择供应商</option>
                                    <option>新鲜蔬菜供应商</option>
                                    <option>优质肉类供应商</option>
                                </select>
                            </div>
                        </div>
                        
                        <div style="text-align: center; font-size: 12px; color: rgba(255,255,255,0.8);">
                            ✅ 所有表单元素样式统一<br>
                            🍎 完美适配iOS系统<br>
                            🎨 视觉风格一致
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔧 技术实现</h2>
        
        <div class="code-box">
/* iOS样式重置和统一 */
input, select, textarea, button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 统一的表单元素样式 */
input[type="text"], input[type="number"], input[type="date"], select {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 16px;
    color: #333 !important;
    transition: all 0.3s ease;
    box-sizing: border-box;
    height: 48px;
    min-height: 48px;
    line-height: 1.2;
    vertical-align: top;
}

/* iOS特定高度修复 */
@supports (-webkit-touch-callout: none) {
    input[type="text"], input[type="number"], input[type="date"], select {
        height: 48px !important;
        min-height: 48px !important;
        max-height: 48px !important;
        display: flex;
        align-items: center;
    }
}

/* 自定义选择框箭头 */
select {
    background-image: url("data:image/svg+xml;...");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}
        </div>
    </div>
</body>
</html>
