<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-tags"></i>
                食材分类管理
            </h1>
            <div class="header-actions">
                <a href="index.php?action=create" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    添加分类
                </a>
                <a href="index.php?action=import" class="btn btn-success">
                    <i class="fas fa-upload"></i>
                    批量导入
                </a>
                <button type="button" class="btn btn-secondary" onclick="exportData()">
                    <i class="fas fa-download"></i>
                    导出
                </button>
                <button type="button" class="btn btn-secondary" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </button>
            </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="search-bar" style="padding: 15px !important;">
            <form method="GET" class="search-bar-form" style="display: flex !important; flex-wrap: nowrap !important; gap: 10px !important; align-items: center !important; padding: 0 !important;">
                <div class="form-field text-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">分类名称</label>
                    <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="搜索分类名称" style="height: 36px !important; width: 180px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">级别</label>
                    <select class="form-control" name="level" style="height: 36px !important; width: 140px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部级别</option>
                        <option value="1" <?= ($level ?? '') == '1' ? 'selected' : '' ?>>一级分类</option>
                        <option value="2" <?= ($level ?? '') == '2' ? 'selected' : '' ?>>二级分类</option>
                    </select>
                </div>
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">父分类</label>
                    <select class="form-control" name="parent_id" style="height: 36px !important; width: 140px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部父分类</option>
                        <?php if (!empty($parent_categories)): ?>
                            <?php foreach ($parent_categories as $parent): ?>
                                <option value="<?= $parent['id'] ?>" <?= ($parent_id ?? '') == $parent['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($parent['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #3b82f6 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                <a href="index.php" class="btn btn-secondary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #6b7280 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </form>
        </div>

        <!-- 分类列表 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>分类名称</th>
                        <th>分类级别</th>
                        <th>父分类</th>
                        <th>子分类数量</th>
                        <th>食材数量</th>
                        <th>排序</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($categories)): ?>
                        <?php foreach ($categories as $category): ?>
                            <tr>
                                <td>
                                    <div class="category-info">
                                        <div class="category-name">
                                            <?php if ($category['level'] == 2): ?>
                                                <span class="level-indent">└─</span>
                                            <?php endif; ?>
                                            <?= htmlspecialchars($category['name']) ?>
                                        </div>
                                        <?php if (!empty($category['description'])): ?>
                                            <div class="category-desc"><?= htmlspecialchars($category['description']) ?></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="level-badge level-<?= $category['level'] ?>">
                                        <?= $category['level'] == 1 ? '一级分类' : '二级分类' ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($category['level'] == 2 && !empty($category['parent_name'])): ?>
                                        <span class="parent-category"><?= htmlspecialchars($category['parent_name']) ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">--</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($category['level'] == 1): ?>
                                        <span class="count-badge"><?= $category['children_count'] ?? 0 ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">--</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="count-badge"><?= $category['ingredients_count'] ?? 0 ?></span>
                                </td>
                                <td>
                                    <div class="sort-controls">
                                        <input type="number" class="sort-input" value="<?= $category['sort_order'] ?>" 
                                               onchange="updateSort(<?= $category['id'] ?>, this.value)" min="0">
                                    </div>
                                </td>
                                <td>
                                    <label class="status-switch">
                                        <input type="checkbox" <?= $category['status'] == 1 ? 'checked' : '' ?> 
                                               onchange="toggleStatus(<?= $category['id'] ?>, this.checked)">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td>
                                    <div class="time-info"><?= date('m-d H:i', strtotime($category['created_at'])) ?></div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="index.php?action=edit&id=<?= $category['id'] ?>"
                                           class="btn btn-sm btn-warning" title="编辑分类">
                                            <i class="fas fa-edit"></i>
                                            <span>编辑</span>
                                        </a>
                                        <a href="index.php?action=view&id=<?= $category['id'] ?>"
                                           class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                            <span>查看</span>
                                        </a>
                                        <?php if (($category['children_count'] ?? 0) == 0 && ($category['ingredients_count'] ?? 0) == 0): ?>
                                            <button type="button" class="btn btn-sm btn-danger"
                                                    onclick="deleteCategory(<?= $category['id'] ?>)" title="删除分类">
                                                <i class="fas fa-trash"></i>
                                                <span>删除</span>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="9" class="text-center text-muted">
                                <i class="fas fa-inbox"></i>
                                暂无分类数据
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
/* 食材分类模块特有样式 */
.category-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.category-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 15px;
}

.level-indent {
    color: #a0aec0;
    margin-right: 5px;
}

.category-desc {
    font-size: 12px;
    color: #718096;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

.level-badge {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
}

.level-1 {
    background: #bee3f8;
    color: #2b6cb0;
}

.level-2 {
    background: #c6f6d5;
    color: #22543d;
}

.parent-category {
    font-size: 14px;
    color: #4a5568;
    background: #f7fafc;
    padding: 4px 8px;
    border-radius: 4px;
}

.count-badge {
    background: #e2e8f0;
    color: #4a5568;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
}

.sort-controls {
    display: flex;
    align-items: center;
}

.sort-input {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    text-align: center;
    font-size: 13px;
}

.status-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.status-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.switch-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .switch-slider {
    background-color: #48bb78;
}

input:checked + .switch-slider:before {
    transform: translateX(20px);
}

.time-info {
    font-size: 14px;
    color: #4a5568;
}

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 1000px;
    }
}
</style>

<script>
// 导出数据
function exportData() {
    alert('导出功能开发中...');
}

// 刷新数据
function refreshData() {
    location.reload();
}

// 删除分类
function deleteCategory(id) {
    if (confirm('确定要删除这个分类吗？此操作不可恢复。')) {
        fetch('index.php?action=delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `id=${id}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error.message);
        });
    }
}

// 切换状态
function toggleStatus(id, checked) {
    const status = checked ? 1 : 0;

    fetch('index.php?action=toggle_status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${id}&status=${status}`
    })
    .then(response => {
        // 检查响应是否为JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回了非JSON响应，可能是页面错误');
        }
        return response.json();
    })
    .then(data => {
        if (!data.success) {
            alert('状态更新失败: ' + (data.message || '未知错误'));
            // 恢复原状态
            document.querySelector(`input[onchange*="${id}"]`).checked = !checked;
        } else {
            console.log('状态更新成功:', data.message);
        }
    })
    .catch(error => {
        console.error('状态更新错误:', error);
        alert('状态更新失败: ' + error.message);
        // 恢复原状态
        document.querySelector(`input[onchange*="${id}"]`).checked = !checked;
    });
}

// 更新排序
function updateSort(id, sortOrder) {
    fetch('index.php?action=update_sort', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${id}&sort_order=${sortOrder}`
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            alert('排序更新失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('排序更新失败: ' + error.message);
    });
}
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>