<?php
/**
 * 采购管理控制器
 */
require_once dirname(__DIR__, 2) . '/includes/BaseController.php';
require_once dirname(__DIR__, 2) . '/includes/helpers.php';

class PurchaseController extends BaseController
{
    protected function init()
    {
        $this->setTemplateData([
            'page_title' => '采购管理 - ' . $this->config['name'],
            'current_module' => 'purchase'
        ]);
    }

    public function handleRequest()
    {
        switch ($this->request['action']) {
            case 'create':
                return $this->create();
            case 'edit':
                return $this->edit();
            case 'delete':
                return $this->delete();
            case 'view':
                return $this->view();
            case 'update_status':
                return $this->updateStatus();
            case 'import':
                return $this->import();
            case 'index':
            default:
                return $this->index();
        }
    }

    /**
     * 采购订单列表页面
     */
    private function index()
    {
        try {
            // 获取搜索参数
            $search = $this->request['get']['search'] ?? '';
            $status = $this->request['get']['status'] ?? '';
            $supplier = $this->request['get']['supplier'] ?? '';
            $date_from = $this->request['get']['date_from'] ?? '';
            $date_to = $this->request['get']['date_to'] ?? '';

            // 构建查询条件
            $where = ['po.status != 0'];
            $params = [];

            if ($search) {
                $where[] = '(po.order_number LIKE ? OR s.name LIKE ?)';
                $params[] = '%' . $search . '%';
                $params[] = '%' . $search . '%';
            }

            if ($status !== '') {
                $where[] = 'po.status = ?';
                $params[] = intval($status);
            }

            if ($supplier) {
                $where[] = 'po.supplier_id = ?';
                $params[] = intval($supplier);
            }

            if ($date_from) {
                $where[] = 'DATE(po.order_date) >= ?';
                $params[] = $date_from;
            }

            if ($date_to) {
                $where[] = 'DATE(po.order_date) <= ?';
                $params[] = $date_to;
            }

            $whereClause = 'WHERE ' . implode(' AND ', $where);

            // 获取采购订单列表
            $orders = $this->db->fetchAll("
                SELECT po.*, s.name as supplier_name,
                       COUNT(poi.id) as item_count,
                       u.name as creator_name
                FROM purchase_orders po
                LEFT JOIN suppliers s ON po.supplier_id = s.id
                LEFT JOIN purchase_order_items poi ON po.id = poi.order_id
                LEFT JOIN users u ON po.created_by = u.id
                $whereClause
                GROUP BY po.id
                ORDER BY po.created_at DESC
                LIMIT 50
            ", $params);

            // 获取供应商列表
            $suppliers = $this->db->fetchAll("
                SELECT id, name FROM suppliers 
                WHERE status = 1 
                ORDER BY name ASC
            ");

            // 获取统计数据
            $stats = $this->getOrderStats();

            $this->setTemplateData([
                'orders' => $orders,
                'suppliers' => $suppliers,
                'stats' => $stats,
                'search' => $search,
                'selected_status' => $status,
                'selected_supplier' => $supplier,
                'date_from' => $date_from,
                'date_to' => $date_to
            ]);

        } catch (Exception $e) {
            // 使用模拟数据
            $this->setTemplateData([
                'orders' => $this->getMockOrders(),
                'suppliers' => $this->getMockSuppliers(),
                'stats' => $this->getMockStats(),
                'search' => $search ?? '',
                'selected_status' => $status ?? '',
                'selected_supplier' => $supplier ?? '',
                'date_from' => $date_from ?? '',
                'date_to' => $date_to ?? ''
            ]);
        }

        $this->render('template.php');
    }

    /**
     * 创建采购单
     */
    private function create()
    {
        if ($this->request['method'] === 'POST') {
            try {
                // 处理表单提交
                $data = [
                    'order_number' => trim($this->request['post']['order_number'] ?? 'PO' . date('YmdHis')),
                    'supplier_id' => intval($this->request['post']['supplier_id']),
                    'order_date' => $this->request['post']['order_date'],
                    'expected_delivery_date' => $this->request['post']['delivery_date'],
                    'canteen_id' => intval($this->request['post']['canteen_id']),
                    'contact_person' => trim($this->request['post']['contact_person']),
                    'delivery_address' => trim($this->request['post']['delivery_address']),
                    'contact_phone' => trim($this->request['post']['contact_phone']),
                    'order_amount' => floatval($this->request['post']['order_amount'] ?? 0),
                    'actual_amount' => floatval($this->request['post']['actual_amount'] ?? 0),
                    'payment_status' => trim($this->request['post']['payment_status']),
                    'notes' => trim($this->request['post']['notes'] ?? ''),
                    'status' => 1, // 待确认
                    'created_by' => 1, // 暂时固定为1
                    'created_at' => date('Y-m-d H:i:s')
                ];

                // 验证必填字段
                $errors = [];
                if (empty($data['supplier_id'])) {
                    $errors[] = '请选择供应商';
                }
                if (empty($data['order_date'])) {
                    $errors[] = '请选择订货日期';
                }

                if (!empty($errors)) {
                    throw new Exception(implode(', ', $errors));
                }

                // 插入采购单
                $orderId = $this->db->insert('purchase_orders', $data);

                // 处理采购单项目
                if (isset($this->request['post']['items']) && is_array($this->request['post']['items'])) {
                    $totalAmount = 0;

                    foreach ($this->request['post']['items'] as $item) {
                        if (!empty($item['ingredient_id']) && !empty($item['quantity'])) {
                            $itemData = [
                                'order_id' => $orderId,
                                'ingredient_id' => intval($item['ingredient_id']),
                                'quantity' => floatval($item['quantity']),
                                'unit_price' => floatval($item['unit_price'] ?? 0),
                                'total_price' => floatval($item['quantity']) * floatval($item['unit_price'] ?? 0),
                                'notes' => trim($item['purpose'] ?? '') // 将用途存储在notes字段中
                            ];

                            $this->db->insert('purchase_order_items', $itemData);
                            $totalAmount += $itemData['total_price'];
                        }
                    }

                    // 更新采购单总金额
                    $this->db->update('purchase_orders', ['total_amount' => $totalAmount], 'id = ?', [$orderId]);
                }

                $this->redirect('index.php', '采购单创建成功', 'success');

            } catch (Exception $e) {
                $this->setTemplateData('error_message', $e->getMessage());
            }
        }

        // 获取供应商列表
        try {
            $suppliers = $this->db->fetchAll("SELECT id, name, contact_person, phone FROM suppliers WHERE status = 1 ORDER BY name ASC");
        } catch (Exception $e) {
            $suppliers = $this->getMockSuppliers();
        }

        // 获取食材列表（包含分类信息）
        try {
            $ingredients = $this->db->fetchAll("
                SELECT i.id, i.name, i.unit, i.unit_price, i.category_id,
                       ic.name as category_name, ic.parent_id,
                       CASE WHEN ic.parent_id IS NOT NULL THEN ic.id ELSE NULL END as subcategory_id,
                       CASE WHEN ic.parent_id IS NOT NULL THEN ic.name ELSE NULL END as subcategory_name
                FROM ingredients i
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                WHERE i.status = 1
                ORDER BY ic.name, i.name
            ");
        } catch (Exception $e) {
            $ingredients = $this->getMockIngredients();
        }

        // 获取一级分类列表
        try {
            $primary_categories = $this->db->fetchAll("
                SELECT id, name
                FROM ingredient_categories
                WHERE (parent_id IS NULL OR parent_id = 0) AND status = 1
                ORDER BY name ASC
            ");
        } catch (Exception $e) {
            $primary_categories = [];
        }

        // 获取二级分类列表
        try {
            $subcategories = $this->db->fetchAll("
                SELECT id, name, parent_id
                FROM ingredient_categories
                WHERE parent_id IS NOT NULL AND parent_id > 0 AND status = 1
                ORDER BY parent_id, name ASC
            ");
        } catch (Exception $e) {
            $subcategories = [];
        }

        $this->setTemplateData([
            'suppliers' => $suppliers,
            'ingredients' => $ingredients,
            'primary_categories' => $primary_categories,
            'categories' => $primary_categories, // 兼容性
            'subcategories' => $subcategories
        ]);

        $this->render('create-template.php');
    }

    /**
     * 删除订单
     */
    private function delete()
    {
        $id = intval($this->request['get']['id'] ?? 0);
        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            // 开始事务
            $this->db->beginTransaction();

            // 删除订单项
            $this->db->query("DELETE FROM purchase_order_items WHERE order_id = ?", [$id]);

            // 删除订单
            $this->db->query("DELETE FROM purchase_orders WHERE id = ?", [$id]);

            $this->db->commit();
            $this->redirect('index.php', '订单删除成功', 'success');

        } catch (Exception $e) {
            $this->db->rollback();
            $this->redirect('index.php', '删除失败：' . $e->getMessage(), 'error');
        }
    }

    /**
     * 查看采购单详情
     */
    private function view()
    {
        $id = intval($this->request['get']['id'] ?? 0);
        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            // 获取采购单基本信息
            $order = $this->db->fetchOne("
                SELECT po.*, s.name as supplier_name, s.contact_person as supplier_contact,
                       s.phone as supplier_phone, s.address as supplier_address,
                       u.real_name as creator_name
                FROM purchase_orders po
                LEFT JOIN suppliers s ON po.supplier_id = s.id
                LEFT JOIN users u ON po.created_by = u.id
                WHERE po.id = ?
            ", [$id]);

            if (!$order) {
                $this->redirect('index.php', '采购单不存在', 'error');
                return;
            }

            // 获取采购单明细
            $items = $this->db->fetchAll("
                SELECT poi.*, i.name as ingredient_name, i.unit, ic.name as category_name
                FROM purchase_order_items poi
                LEFT JOIN ingredients i ON poi.ingredient_id = i.id
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                WHERE poi.order_id = ?
                ORDER BY poi.id
            ", [$id]);

            $this->setTemplateData([
                'order' => $order,
                'items' => $items
            ]);

            $this->render('view-template.php');

        } catch (Exception $e) {
            $this->redirect('index.php', '获取详情失败：' . $e->getMessage(), 'error');
        }
    }

    /**
     * 更新订单状态
     */
    private function updateStatus()
    {
        if ($this->request['method'] !== 'POST') {
            $this->redirect('index.php', '请求方法错误', 'error');
            return;
        }

        $id = intval($this->request['post']['id'] ?? 0);
        $status = intval($this->request['post']['status'] ?? 0);

        if (!$id || !$status) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            $this->db->update('purchase_orders', ['status' => $status], 'id = ?', [$id]);
            $this->redirect('index.php', '状态更新成功', 'success');

        } catch (Exception $e) {
            $this->redirect('index.php', '更新失败：' . $e->getMessage(), 'error');
        }
    }

    /**
     * 获取订单统计
     */
    private function getOrderStats()
    {
        try {
            $stats = $this->db->fetchOne("
                SELECT
                    COUNT(*) as total_orders,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as pending_orders,
                    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as confirmed_orders,
                    SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as completed_orders,
                    SUM(COALESCE(actual_amount, order_amount, 0)) as total_amount
                FROM purchase_orders
                WHERE status IN (1, 2, 4, 5)
            ");

            return $stats;
        } catch (Exception $e) {
            return $this->getMockStats();
        }
    }

    /**
     * 获取模拟订单数据
     */
    private function getMockOrders()
    {
        return [
            [
                'id' => 1,
                'order_number' => 'PO20241201001',
                'supplier_name' => '绿色蔬菜供应商',
                'total_amount' => 1250.00,
                'status' => 2,
                'order_date' => '2024-12-01',
                'item_count' => 5,
                'creator_name' => '张三',
                'created_at' => '2024-12-01 09:30:00'
            ],
            [
                'id' => 2,
                'order_number' => 'PO20241130001',
                'supplier_name' => '优质肉类供应商',
                'total_amount' => 2800.00,
                'status' => 4,
                'order_date' => '2024-11-30',
                'item_count' => 3,
                'creator_name' => '李四',
                'created_at' => '2024-11-30 14:20:00'
            ],
            [
                'id' => 3,
                'order_number' => 'PO20241129001',
                'supplier_name' => '粮油批发商',
                'total_amount' => 1680.50,
                'status' => 1,
                'order_date' => '2024-11-29',
                'item_count' => 4,
                'creator_name' => '王五',
                'created_at' => '2024-11-29 16:45:00'
            ]
        ];
    }

    /**
     * 获取模拟供应商数据
     */
    private function getMockSuppliers()
    {
        return [
            ['id' => 1, 'name' => '绿色蔬菜供应商'],
            ['id' => 2, 'name' => '优质肉类供应商'],
            ['id' => 3, 'name' => '新鲜水产供应商'],
            ['id' => 4, 'name' => '粮油批发商']
        ];
    }

    /**
     * 获取模拟食材数据
     */
    private function getMockIngredients()
    {
        return [
            ['id' => 1, 'name' => '白菜', 'unit' => '斤', 'unit_price' => 2.50, 'category_name' => '蔬菜类'],
            ['id' => 2, 'name' => '土豆', 'unit' => '斤', 'unit_price' => 3.00, 'category_name' => '蔬菜类'],
            ['id' => 3, 'name' => '猪肉', 'unit' => '斤', 'unit_price' => 28.00, 'category_name' => '肉类'],
            ['id' => 4, 'name' => '鸡蛋', 'unit' => '斤', 'unit_price' => 8.50, 'category_name' => '肉类'],
            ['id' => 5, 'name' => '大米', 'unit' => '斤', 'unit_price' => 4.20, 'category_name' => '粮油类']
        ];
    }

    /**
     * 批量导入采购单
     */
    private function import()
    {
        if ($this->request['method'] === 'POST') {
            try {
                // 检查是否有上传文件
                if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
                    throw new Exception('请选择要导入的文件');
                }

                // 检查供应商选择
                $supplierId = isset($this->request['post']['supplier_id']) ? intval($this->request['post']['supplier_id']) : 0;
                if ($supplierId <= 0) {
                    throw new Exception('请选择供应商');
                }

                $file = $_FILES['import_file'];
                $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

                if (!in_array($fileExtension, ['csv', 'xlsx', 'xls'])) {
                    throw new Exception('只支持 CSV、Excel 格式的文件');
                }

                // 处理 CSV 文件
                if ($fileExtension === 'csv') {
                    $result = $this->importFromCSV($file['tmp_name'], $supplierId);
                } else {
                    // 处理 Excel 文件，使用改进的 ExcelReader
                    $result = $this->importFromExcel($file['tmp_name'], $file['name'], $supplierId);
                }

                $this->redirect('index.php', "导入成功！共导入 {$result['success']} 条记录", 'success');

            } catch (Exception $e) {
                $this->setTemplateData('error_message', $e->getMessage());
            }
        }

        // 获取供应商列表用于模板显示
        try {
            $suppliers = $this->db->fetchAll("SELECT id, name FROM suppliers WHERE status = 1 ORDER BY name ASC");
        } catch (Exception $e) {
            $suppliers = $this->getMockSuppliers();
        }

        $this->setTemplateData([
            'suppliers' => $suppliers
        ]);

        $this->render('import-template.php');
    }

    /**
     * 从 CSV 文件导入数据
     */
    private function importFromCSV($filePath, $supplierId = null)
    {
        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        if (($handle = fopen($filePath, "r")) !== FALSE) {
            // 跳过标题行
            fgetcsv($handle, 1000, ",");

            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                try {
                    $this->importOrderRow($data, $supplierId);
                    $successCount++;
                } catch (Exception $e) {
                    $errorCount++;
                    $errors[] = "第" . ($successCount + $errorCount + 1) . "行: " . $e->getMessage();
                }
            }
            fclose($handle);
        }

        if (!empty($errors)) {
            throw new Exception("导入完成，成功 {$successCount} 条，失败 {$errorCount} 条。错误详情：" . implode('; ', array_slice($errors, 0, 5)));
        }

        return ['success' => $successCount, 'error' => $errorCount];
    }

    /**
     * 从 Excel 文件导入数据
     */
    private function importFromExcel($filePath, $originalFileName = null, $supplierId = null)
    {
        require_once '../../includes/ExcelReader.php';

        try {
            $reader = new ExcelReader();
            $data = $reader->read($filePath, $originalFileName);

            if (empty($data)) {
                throw new Exception('Excel文件为空或无法读取');
            }

            // 检测Excel格式类型
            $importType = $this->detectExcelFormat($data);

            switch ($importType) {
                case 'order_form':
                    // 订货单格式（您提供的格式）
                    return $this->importOrderForm($data, $supplierId);
                case 'simple_list':
                    // 简单列表格式
                    return $this->importSimpleList($data, $supplierId);
                default:
                    throw new Exception('不支持的Excel格式，请使用标准模板');
            }

        } catch (Exception $e) {
            throw new Exception('Excel文件解析失败: ' . $e->getMessage());
        }
    }

    /**
     * 检测Excel格式类型
     */
    private function detectExcelFormat($data)
    {
        if (empty($data) || count($data) < 4) {
            return 'simple_list';
        }

        // 检查是否是订货单格式（基于实际格式）
        $firstRow = $data[0] ?? [];
        $secondRow = $data[1] ?? [];

        // 方法1：检查第1行是否包含"订货单"标题
        $firstRowText = implode('', $firstRow);
        if (strpos($firstRowText, '订货单') !== false) {
            return 'order_form';
        }

        // 方法2：检查第2行第1列是否有订单号（实际格式：订单号在第2行第1列）
        if (count($data) >= 2) {
            $orderNumber = trim($secondRow[0] ?? '');
            if (!empty($orderNumber) && (strlen($orderNumber) > 10 || preg_match('/^[A-Z0-9]+/', $orderNumber))) {
                return 'order_form';
            }
        }

        // 方法3：兼容原有格式（订单号在第1行第2列）
        if (count($firstRow) > 4 && count($secondRow) > 11) {
            $orderNumberOld = trim($firstRow[1] ?? '');
            if (!empty($orderNumberOld) && (strlen($orderNumberOld) > 10 || preg_match('/^[A-Z0-9]+/', $orderNumberOld))) {
                return 'order_form';
            }
        }

        return 'simple_list';
    }

    /**
     * 导入订货单格式（您提供的格式）
     */
    private function importOrderForm($data, $supplierId = null)
    {
        try {
            // 提取头部信息
            $orderInfo = $this->extractOrderInfo($data);

            // 如果提供了供应商ID，覆盖Excel中的供应商信息
            if ($supplierId) {
                $orderInfo['supplier_id'] = $supplierId;
            }

            // 创建采购订单
            $orderId = $this->createPurchaseOrder($orderInfo);

            // 导入订单明细
            $itemsResult = $this->importOrderItems($data, $orderId);

            return [
                'total' => 1,
                'success' => 1,
                'error' => 0,
                'errors' => [],
                'order_id' => $orderId,
                'items_imported' => $itemsResult['success'],
                'items_failed' => $itemsResult['failed']
            ];

        } catch (Exception $e) {
            throw new Exception('订货单导入失败: ' . $e->getMessage());
        }
    }

    /**
     * 导入简单列表格式
     */
    private function importSimpleList($data, $supplierId = null)
    {
        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        // 跳过标题行
        $rows = array_slice($data, 1);

        foreach ($rows as $index => $row) {
            try {
                // 改进的空行检测：检查是否有非空的有意义内容
                $filteredRow = array_filter($row, function($cell) {
                    return !empty(trim($cell));
                });

                if (empty($filteredRow)) {
                    continue; // 跳过完全空的行
                }

                // 检查是否有足够的必要字段
                $supplier = trim($row[0] ?? '');
                $ingredient = trim($row[2] ?? '');
                $quantity = trim($row[3] ?? '');
                $price = trim($row[4] ?? '');

                if (empty($supplier) && empty($ingredient) && empty($quantity) && empty($price)) {
                    continue; // 跳过关键字段都为空的行
                }

                $this->importPurchaseRow($row);
                $successCount++;
            } catch (Exception $e) {
                $errorCount++;
                $errors[] = "第" . ($index + 2) . "行: " . $e->getMessage();
            }
        }

        return [
            'total' => $successCount + $errorCount,
            'success' => $successCount,
            'error' => $errorCount,
            'errors' => $errors
        ];
    }

    /**
     * 提取订货单头部信息（基于实际订货单格式）
     */
    private function extractOrderInfo($data)
    {
        try {
            // 基于实际订货单格式提取信息（修正版）
            // 注意：标签和值在不同的列
            // 第2行：订单号标签在第1列，值在第2列；下单日期标签在第4列，值在第5列
            // 第3行：客户名称在第1列；联系人标签在第8列，值在第9列
            // 第4行：收货地址在第1列；联系电话标签在第9列，值在第11列
            // 第5行：下单金额在第1列；实际金额标签在第2列，值在第3列

            $orderNumber = trim($data[1][1] ?? '');        // 第2行第2列：订单号值
            $orderDateStr = trim($data[1][4] ?? '');       // 第2行第5列：下单日期值
            $expectedDateStr = trim($data[1][11] ?? '');   // 第2行第12列：预交货日期值
            $customerName = trim($data[2][0] ?? '');       // 第3行第1列：客户名称
            $contactPerson = trim($data[2][11] ?? '');     // 第3行第13列：联系人值
            $deliveryAddress = trim($data[3][1] ?? '');    // 第4行第1列：收货地址
            $contactPhone = trim($data[3][11] ?? '');      // 第4行第12列：联系电话值
            $orderAmount = floatval($data[4][1] ?? 0);     // 第5行第1列：下单金额
            $actualAmount = floatval($data[4][4] ?? 0);    // 第5行第5列：实际金额值

            // 验证必填字段
            if (empty($orderNumber)) {
                throw new Exception('订单号不能为空（第1行第2列）');
            }

            // 处理日期格式（使用strtotime，与参考逻辑一致）
            $orderDate = $this->parseOrderDate($orderDateStr);
            $expectedDate = $this->parseOrderDate($expectedDateStr);

            // 智能匹配供应商（改进：不使用固定值）
            $supplierId = $this->findSupplierByOrderInfo($deliveryAddress, $contactPerson, $contactPhone);

            return [
                'order_number' => $orderNumber,
                'order_date' => $orderDate,
                'expected_delivery_date' => $expectedDate,
                'contact_person' => $contactPerson,
                'delivery_address' => $deliveryAddress,
                'contact_phone' => $contactPhone,
                'order_amount' => $orderAmount,
                'actual_amount' => $actualAmount,
                'total_amount' => $orderAmount, // 使用订单金额作为总金额
                'supplier_id' => $supplierId,
                'canteen_id' => 1,  // 默认食堂
                'payment_status' => 'unpaid',
                'status' => 1,
                'created_by' => 1
            ];

        } catch (Exception $e) {
            throw new Exception('提取订单信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 解析订单日期（与参考逻辑一致，使用strtotime）
     */
    private function parseOrderDate($dateStr)
    {
        if (empty($dateStr)) {
            return date('Y-m-d');
        }

        // 首先尝试strtotime（与参考逻辑一致）
        $timestamp = strtotime($dateStr);
        if ($timestamp !== false) {
            return date('Y-m-d', $timestamp);
        }

        // 如果strtotime失败，尝试Excel日期序列号
        if (is_numeric($dateStr)) {
            $excelDate = floatval($dateStr);
            if ($excelDate > 25569) { // Excel日期基准检查
                $unixTimestamp = ($excelDate - 25569) * 86400;
                return date('Y-m-d', $unixTimestamp);
            }
        }

        // 如果都失败，返回当前日期
        return date('Y-m-d');
    }

    /**
     * 智能匹配供应商
     */
    private function findSupplierByOrderInfo($deliveryAddress, $contactPerson, $contactPhone)
    {
        try {
            // 尝试通过联系电话匹配
            if (!empty($contactPhone)) {
                $supplier = $this->db->fetchOne(
                    "SELECT id FROM suppliers WHERE phone = ? OR phone LIKE ?",
                    [$contactPhone, '%' . $contactPhone . '%']
                );
                if ($supplier) {
                    return $supplier['id'];
                }
            }

            // 尝试通过联系人匹配
            if (!empty($contactPerson)) {
                $supplier = $this->db->fetchOne(
                    "SELECT id FROM suppliers WHERE contact_person = ?",
                    [$contactPerson]
                );
                if ($supplier) {
                    return $supplier['id'];
                }
            }

            // 尝试通过地址关键词匹配
            if (!empty($deliveryAddress)) {
                $supplier = $this->db->fetchOne(
                    "SELECT id FROM suppliers WHERE address LIKE ?",
                    ['%' . $deliveryAddress . '%']
                );
                if ($supplier) {
                    return $supplier['id'];
                }
            }

            // 如果都没找到，返回默认供应商ID
            return 1;

        } catch (Exception $e) {
            // 出错时返回默认供应商ID
            return 1;
        }
    }

    /**
     * 解析Excel日期
     */
    private function parseExcelDate($dateStr)
    {
        if (empty($dateStr)) {
            return date('Y-m-d');
        }

        // 如果是数字，可能是Excel日期序列号
        if (is_numeric($dateStr)) {
            $excelDate = floatval($dateStr);
            if ($excelDate > 25569) { // Excel日期基准检查
                $unixTimestamp = ($excelDate - 25569) * 86400;
                return date('Y-m-d', $unixTimestamp);
            }
        }

        // 尝试解析字符串日期
        $timestamp = strtotime($dateStr);
        if ($timestamp !== false) {
            return date('Y-m-d', $timestamp);
        }

        // 如果都失败，返回当前日期
        return date('Y-m-d');
    }

    /**
     * 创建采购订单
     */
    private function createPurchaseOrder($orderInfo)
    {
        try {
            // 检查订单号是否已存在
            $existing = $this->db->fetchOne(
                "SELECT id FROM purchase_orders WHERE order_number = ?",
                [$orderInfo['order_number']]
            );

            if ($existing) {
                throw new Exception("订单号 '{$orderInfo['order_number']}' 已存在");
            }

            // 插入订单数据
            $orderData = [
                'order_number' => $orderInfo['order_number'],
                'supplier_id' => $orderInfo['supplier_id'],
                'order_date' => $orderInfo['order_date'],
                'expected_delivery_date' => $orderInfo['expected_delivery_date'],
                'canteen_id' => $orderInfo['canteen_id'],
                'contact_person' => $orderInfo['contact_person'],
                'delivery_address' => $orderInfo['delivery_address'],
                'contact_phone' => $orderInfo['contact_phone'],
                'order_amount' => $orderInfo['order_amount'],
                'actual_amount' => $orderInfo['actual_amount'],
                'payment_status' => $orderInfo['payment_status'],
                'total_amount' => $orderInfo['total_amount'],
                'status' => $orderInfo['status'],
                'created_by' => $orderInfo['created_by'],
                'created_at' => date('Y-m-d H:i:s')
            ];

            $orderId = $this->db->insert('purchase_orders', $orderData);

            if (!$orderId) {
                throw new Exception('创建采购订单失败');
            }

            return $orderId;

        } catch (Exception $e) {
            throw new Exception('创建采购订单失败: ' . $e->getMessage());
        }
    }

    /**
     * 导入订单明细
     */
    private function importOrderItems($data, $orderId)
    {
        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        try {
            // 添加日志记录
            $this->writeImportLog("=== 开始处理明细数据 ===");
            $this->writeImportLog("数据总行数: " . count($data));

            // 动态查找明细数据开始行
            $detailStartRow = -1;
            for ($i = 5; $i < count($data); $i++) {
                $row = $data[$i];
                $firstCell = trim($row[0] ?? '');

                $this->writeImportLog("检查第" . ($i + 1) . "行第1列: '{$firstCell}'");

                // 查找包含"序号"或"商品"等关键词的标题行
                if (strpos($firstCell, '序号') !== false ||
                    strpos($firstCell, '商品') !== false ||
                    strpos($firstCell, '编码') !== false) {
                    $detailStartRow = $i;
                    $this->writeImportLog("找到明细标题行: 第" . ($i + 1) . "行");
                    break;
                }
            }

            if ($detailStartRow === -1) {
                $this->writeImportLog("❌ 未找到明细数据开始行");
                throw new Exception('未找到明细数据开始行');
            }

            $this->writeImportLog("明细数据从第" . ($detailStartRow + 2) . "行开始处理");

            // 从明细标题行的下一行开始处理数据
            for ($i = $detailStartRow + 1; $i < count($data); $i++) {
                $row = $data[$i];
                $rowNum = $i + 1;

                $this->writeImportLog("--- 处理第{$rowNum}行 ---");

                // 改进的空行检测
                $filteredRow = array_filter($row, function($cell) {
                    return !empty(trim($cell));
                });

                if (empty($filteredRow)) {
                    $this->writeImportLog("第{$rowNum}行: 空行，跳过");
                    continue; // 跳过完全空的行
                }

                // 检查关键字段（根据实际列位置）
                $itemCode = trim($row[1] ?? '');    // 第2列：商品编码
                $itemName = trim($row[2] ?? '');    // 第3列：商品名称
                $quantity = trim($row[8] ?? '');    // 第9列：下单数量

                $this->writeImportLog("第{$rowNum}行字段: 编码='{$itemCode}', 名称='{$itemName}', 数量='{$quantity}'");

                if (empty($itemCode) && empty($itemName)) {
                    $this->writeImportLog("第{$rowNum}行: 关键字段为空，跳过");
                    continue; // 跳过关键字段为空的行
                }

                try {
                    $this->writeImportLog("第{$rowNum}行: 开始导入明细");
                    $this->importOrderItem($row, $orderId, $rowNum);
                    $successCount++;
                    $this->writeImportLog("第{$rowNum}行: 导入成功");
                } catch (Exception $e) {
                    $errorCount++;
                    $errorMsg = "第{$rowNum}行明细: " . $e->getMessage();
                    $errors[] = $errorMsg;
                    $this->writeImportLog("第{$rowNum}行: 导入失败 - " . $e->getMessage());
                }
            }

            $this->writeImportLog("=== 明细数据处理完成 ===");
            $this->writeImportLog("成功: {$successCount}, 失败: {$errorCount}");

            return [
                'success' => $successCount,
                'failed' => $errorCount,
                'errors' => $errors
            ];

        } catch (Exception $e) {
            throw new Exception('导入订单明细失败: ' . $e->getMessage());
        }
    }

    /**
     * 导入单个订单明细项（严格按照参考逻辑）
     */
    private function importOrderItem($row, $orderId, $rowNumber)
    {
        try {
            // 根据正确的订货单明细格式解析数据
            $seqNo = trim($row[0] ?? '');              // 第1列：序号
            $itemCode = trim($row[1] ?? '');           // 第2列：商品编码
            $itemName = trim($row[2] ?? '');           // 第3列：商品名称
            $specification = trim($row[3] ?? '');      // 第4列：描述
            $primaryCategory = trim($row[4] ?? '');    // 第5列：一级分类名称
            $secondaryCategory = trim($row[5] ?? '');  // 第6列：二级分类名称
            $unit = trim($row[6] ?? '');               // 第7列：单位
            $unitPrice = floatval($row[7] ?? 0);       // 第8列：下单单价
            $quantity = floatval($row[8] ?? 0);        // 第9列：下单数量
            $totalPrice = floatval($row[9] ?? 0);      // 第10列：金额小计

            // 其他可能的字段（如果Excel有更多列）
            $receivedQuantity = floatval($row[10] ?? 0); // 第11列：实收数量（如果有）
            $qualifiedQuantity = floatval($row[11] ?? 0); // 第12列：合格数量（如果有）
            $unqualifiedQuantity = floatval($row[12] ?? 0); // 第13列：不合格数量（如果有）
            $lossQuantity = floatval($row[13] ?? 0);   // 第14列：损耗数量（如果有）
            $rejectedQuantity = floatval($row[14] ?? 0); // 第15列：拒收数量（如果有）
            $notes = trim($row[15] ?? '');             // 第16列：备注（如果有）

            // 从分类名称推导品牌和产地信息
            $brand = $primaryCategory;   // 使用一级分类作为品牌
            $origin = $secondaryCategory; // 使用二级分类作为产地

            // 验证必填字段
            if (empty($itemCode)) {
                throw new Exception('商品编码不能为空（第2列）');
            }

            if ($quantity <= 0) {
                throw new Exception('数量必须大于0（第9列）');
            }

            if ($unitPrice < 0) {
                throw new Exception('单价不能为负数（第8列）');
            }

            // 智能查找或创建食材（包含分类处理）
            $ingredientId = $this->findOrCreateIngredientByCode($itemCode, $itemName, $specification, $unit, $brand, $origin, $secondaryCategory);

            // 计算总价（如果Excel中的总价为0，则自动计算）
            if ($totalPrice <= 0) {
                $totalPrice = $quantity * $unitPrice;
            }

            // 插入订单明细（包含更多字段）
            $itemData = [
                'order_id' => $orderId,
                'ingredient_id' => $ingredientId,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_price' => $totalPrice,
                'received_quantity' => $receivedQuantity,
                'notes' => $notes
            ];

            // 如果数据库支持更多字段，可以添加
            if (!empty($qualifiedQuantity)) {
                $itemData['qualified_quantity'] = $qualifiedQuantity;
            }
            if (!empty($unqualifiedQuantity)) {
                $itemData['unqualified_quantity'] = $unqualifiedQuantity;
            }
            if (!empty($lossQuantity)) {
                $itemData['loss_quantity'] = $lossQuantity;
            }
            if (!empty($rejectedQuantity)) {
                $itemData['rejected_quantity'] = $rejectedQuantity;
            }

            $itemId = $this->db->insert('purchase_order_items', $itemData);

            if (!$itemId) {
                throw new Exception('插入订单明细失败');
            }

        } catch (Exception $e) {
            throw new Exception("第{$rowNumber}行: " . $e->getMessage());
        }
    }

    /**
     * 根据商品编码智能查找或创建食材
     */
    private function findOrCreateIngredientByCode($itemCode, $itemName = '', $specification = '', $unit = '', $brand = '', $origin = '', $categoryName = '')
    {
        try {
            $this->writeImportLog("查找食材: 编码='{$itemCode}', 名称='{$itemName}'");

            // 首先尝试通过商品编码查找
            if (!empty($itemCode)) {
                $ingredient = $this->db->fetchOne(
                    "SELECT id, name FROM ingredients WHERE code = ?",
                    [$itemCode]
                );

                if ($ingredient) {
                    $this->writeImportLog("✅ 通过编码找到食材: ID={$ingredient['id']}, 名称={$ingredient['name']}");
                    return $ingredient['id'];
                }
            }

            // 如果有商品名称，尝试通过名称查找
            if (!empty($itemName)) {
                $ingredient = $this->db->fetchOne(
                    "SELECT id, code FROM ingredients WHERE name = ?",
                    [$itemName]
                );

                if ($ingredient) {
                    $this->writeImportLog("✅ 通过名称找到食材: ID={$ingredient['id']}, 编码={$ingredient['code']}");
                    return $ingredient['id'];
                }
            }

            // 如果找不到，自动创建新的食材
            $this->writeImportLog("⚠️ 食材不存在，开始自动创建");

            // 查找分类ID
            $categoryId = $this->findOrCreateCategoryByName($categoryName);

            $ingredientData = [
                'code' => !empty($itemCode) ? $itemCode : 'AUTO_' . time() . '_' . rand(1000, 9999),
                'name' => !empty($itemName) ? $itemName : (!empty($itemCode) ? $itemCode : '未知食材'),
                'specification' => $specification ?: '',
                'unit' => !empty($unit) ? $unit : '个',
                'category_id' => $categoryId, // 使用查找到的分类ID
                'brand' => $brand ?: '',
                'origin' => $origin ?: '',
                'shelf_life' => '1天', // 保质期文本
                'shelf_life_days' => 1, // 保质期天数：默认1天
                'min_stock' => 10, // 最小库存：默认10
                'created_by' => 1, // 默认创建者ID
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $this->writeImportLog("准备创建食材: " . json_encode($ingredientData));

            $ingredientId = $this->db->insert('ingredients', $ingredientData);

            if (!$ingredientId) {
                throw new Exception("创建食材失败：编码={$itemCode}, 名称={$itemName}");
            }

            $this->writeImportLog("✅ 食材创建成功: ID={$ingredientId}");
            return $ingredientId;

        } catch (Exception $e) {
            $this->writeImportLog("❌ 处理食材失败: " . $e->getMessage());
            throw new Exception("处理食材失败: " . $e->getMessage());
        }
    }

    /**
     * 查找或创建食材
     */
    private function findOrCreateIngredient($name, $code = '', $specification = '', $unit = '')
    {
        try {
            // 首先尝试通过名称查找
            $ingredient = $this->db->fetchOne(
                "SELECT id FROM ingredients WHERE name = ?",
                [$name]
            );

            if ($ingredient) {
                return $ingredient['id'];
            }

            // 如果有编码，尝试通过编码查找
            if (!empty($code)) {
                $ingredient = $this->db->fetchOne(
                    "SELECT id FROM ingredients WHERE code = ?",
                    [$code]
                );

                if ($ingredient) {
                    return $ingredient['id'];
                }
            }

            // 如果找不到，创建新的食材
            $ingredientData = [
                'name' => $name,
                'code' => !empty($code) ? $code : 'AUTO_' . time() . '_' . rand(1000, 9999),
                'specification' => $specification,
                'unit' => !empty($unit) ? $unit : '个',
                'category_id' => 1, // 默认分类，可以根据需要修改
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $ingredientId = $this->db->insert('ingredients', $ingredientData);

            if (!$ingredientId) {
                throw new Exception("创建食材 '{$name}' 失败");
            }

            return $ingredientId;

        } catch (Exception $e) {
            throw new Exception("处理食材 '{$name}' 失败: " . $e->getMessage());
        }
    }

    /**
     * 导入单行采购数据
     */
    private function importPurchaseRow($row)
    {
        // 确保有足够的列
        if (count($row) < 6) {
            throw new Exception('数据列数不足，至少需要6列');
        }

        // 解析数据 [供应商名称, 订单日期, 食材名称, 数量, 单价, 备注]
        $supplierName = trim($row[0]);
        $orderDate = trim($row[1]);
        $ingredientName = trim($row[2]);
        $quantity = trim($row[3]);
        $unitPrice = trim($row[4]);
        $notes = trim($row[5] ?? '');

        // 验证必填字段
        if (empty($supplierName)) {
            throw new Exception('供应商名称不能为空');
        }
        if (empty($orderDate)) {
            throw new Exception('订单日期不能为空');
        }
        if (empty($ingredientName)) {
            throw new Exception('食材名称不能为空');
        }
        if (empty($quantity) || !is_numeric($quantity)) {
            throw new Exception('数量必须是有效数字');
        }
        if (empty($unitPrice) || !is_numeric($unitPrice)) {
            throw new Exception('单价必须是有效数字');
        }

        // 查找或创建供应商
        $supplier = $this->db->fetchOne("SELECT id FROM suppliers WHERE name = ?", [$supplierName]);
        if (!$supplier) {
            throw new Exception("供应商 '{$supplierName}' 不存在");
        }

        // 查找食材
        $ingredient = $this->db->fetchOne("SELECT id FROM ingredients WHERE name = ?", [$ingredientName]);
        if (!$ingredient) {
            throw new Exception("食材 '{$ingredientName}' 不存在");
        }

        // 验证日期格式
        $orderDateTime = DateTime::createFromFormat('Y-m-d', $orderDate);
        if (!$orderDateTime) {
            $orderDateTime = DateTime::createFromFormat('Y/m/d', $orderDate);
        }
        if (!$orderDateTime) {
            throw new Exception("日期格式错误，请使用 YYYY-MM-DD 或 YYYY/MM/DD 格式");
        }

        // 生成订单号
        $orderNumber = 'IMP_' . date('YmdHis') . '_' . rand(1000, 9999);

        // 创建采购订单
        $orderData = [
            'order_number' => $orderNumber,
            'supplier_id' => $supplier['id'],
            'order_date' => $orderDateTime->format('Y-m-d'),
            'total_amount' => floatval($quantity) * floatval($unitPrice),
            'status' => 1, // 待确认
            'notes' => $notes,
            'created_by' => 1, // 默认用户
            'created_at' => date('Y-m-d H:i:s')
        ];

        $orderId = $this->db->insert('purchase_orders', $orderData);

        // 创建订单明细
        $itemData = [
            'order_id' => $orderId,
            'ingredient_id' => $ingredient['id'],
            'quantity' => floatval($quantity),
            'unit_price' => floatval($unitPrice),
            'total_price' => floatval($quantity) * floatval($unitPrice),
            'notes' => $notes
        ];

        $this->db->insert('purchase_order_items', $itemData);
    }

    /**
     * 导入单行订单数据
     */
    private function importOrderRow($data)
    {
        // CSV 格式：供应商名称,订货日期,交货日期,备注,食材名称,数量,单价
        if (count($data) < 7) {
            throw new Exception('数据格式不正确，缺少必要字段');
        }

        $supplierName = trim($data[0]);
        $orderDate = trim($data[1]);
        $deliveryDate = trim($data[2]);
        $notes = trim($data[3]);
        $ingredientName = trim($data[4]);
        $quantity = floatval($data[5]);
        $unitPrice = floatval($data[6]);

        // 验证必填字段
        if (empty($supplierName) || empty($orderDate) || empty($ingredientName) || $quantity <= 0) {
            throw new Exception('供应商名称、订货日期、食材名称和数量不能为空');
        }

        // 查找供应商ID
        $supplier = $this->db->fetchOne("SELECT id FROM suppliers WHERE name = ? AND status = 1", [$supplierName]);
        if (!$supplier) {
            throw new Exception("找不到供应商：{$supplierName}");
        }

        // 查找食材ID
        $ingredient = $this->db->fetchOne("SELECT id FROM ingredients WHERE name = ? AND status = 1", [$ingredientName]);
        if (!$ingredient) {
            throw new Exception("找不到食材：{$ingredientName}");
        }

        // 检查是否已存在相同的订单
        $existingOrder = $this->db->fetchOne("
            SELECT id FROM purchase_orders
            WHERE supplier_id = ? AND order_date = ? AND notes = ?
        ", [$supplier['id'], $orderDate, $notes]);

        if ($existingOrder) {
            $orderId = $existingOrder['id'];
        } else {
            // 创建新订单
            $orderData = [
                'order_number' => 'PO' . date('YmdHis') . rand(100, 999),
                'supplier_id' => $supplier['id'],
                'order_date' => $orderDate,
                'expected_delivery_date' => $deliveryDate ?: null,
                'notes' => $notes,
                'status' => 1,
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $orderId = $this->db->insert('purchase_orders', $orderData);
        }

        // 添加订单项
        $itemData = [
            'order_id' => $orderId,
            'ingredient_id' => $ingredient['id'],
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'total_price' => $quantity * $unitPrice
        ];

        $this->db->insert('purchase_order_items', $itemData);

        // 更新订单总金额
        $totalAmount = $this->db->fetchOne("
            SELECT SUM(total_price) as total
            FROM purchase_order_items
            WHERE order_id = ?
        ", [$orderId])['total'];

        $this->db->update('purchase_orders', ['total_amount' => $totalAmount], 'id = ?', [$orderId]);
    }

    /**
     * 获取模拟统计数据
     */
    private function getMockStats()
    {
        return [
            'total_orders' => 15,
            'pending_orders' => 3,
            'confirmed_orders' => 8,
            'completed_orders' => 4,
            'total_amount' => 25680.50
        ];
    }

    /**
     * 根据分类名称查找或创建分类
     */
    private function findOrCreateCategoryByName($categoryName)
    {
        try {
            // 如果分类名称为空，返回默认分类ID
            if (empty($categoryName)) {
                $this->writeImportLog("分类名称为空，使用默认分类ID=1");
                return 1;
            }

            $this->writeImportLog("查找分类: 名称='{$categoryName}'");

            // 首先尝试查找现有分类
            $category = $this->db->fetchOne(
                "SELECT id FROM ingredient_categories WHERE name = ? AND status = 1",
                [$categoryName]
            );

            if ($category) {
                $this->writeImportLog("✅ 找到分类: ID={$category['id']}, 名称='{$categoryName}'");
                return $category['id'];
            }

            // 如果找不到，自动创建新分类
            $this->writeImportLog("⚠️ 分类不存在，开始自动创建: '{$categoryName}'");

            $categoryData = [
                'code' => 'AUTO_' . strtoupper(substr(md5($categoryName), 0, 8)), // 自动生成编码
                'name' => $categoryName,
                'parent_id' => 0, // 作为二级分类，可以设置父分类ID
                'description' => '自动创建的分类：' . $categoryName,
                'status' => 1
            ];

            $categoryId = $this->db->insert('ingredient_categories', $categoryData);

            if (!$categoryId) {
                $this->writeImportLog("❌ 创建分类失败，使用默认分类ID=1");
                return 1;
            }

            $this->writeImportLog("✅ 分类创建成功: ID={$categoryId}, 名称='{$categoryName}'");
            return $categoryId;

        } catch (Exception $e) {
            $this->writeImportLog("❌ 处理分类失败: " . $e->getMessage() . "，使用默认分类ID=1");
            return 1; // 出错时返回默认分类
        }
    }

    /**
     * 写入导入日志
     */
    private function writeImportLog($message)
    {
        $logFile = __DIR__ . '/../../logs/import_debug.log';
        $logDir = dirname($logFile);

        // 创建日志目录
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($logFile, "[{$timestamp}] {$message}\n", FILE_APPEND);
    }
}
?>
