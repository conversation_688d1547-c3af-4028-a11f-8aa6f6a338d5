<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-plus"></i>
                新建盘点任务
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <?php if (!empty($data['error_message'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <?= htmlspecialchars($data['error_message']) ?>
            </div>
        <?php endif; ?>

        <div class="form-container">
            <form method="POST" action="index.php?action=create" class="form" id="create-form">
                <!-- 基本信息 -->
                <div class="section">
                    <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="task_name">
                                <i class="fas fa-tag"></i>
                                任务名称 <span class="required">*</span>
                            </label>
                            <input type="text" name="task_name" id="task_name" class="form-control" 
                                   required placeholder="如：12月全库盘点、蔬菜类专项盘点">
                        </div>

                        <div class="form-group">
                            <label for="task_type">
                                <i class="fas fa-list"></i>
                                盘点类型 <span class="required">*</span>
                            </label>
                            <select name="task_type" id="task_type" class="form-control" required onchange="handleTaskTypeChange()">
                                <option value="">请选择盘点类型</option>
                                <option value="full">全库盘点</option>
                                <option value="category">分类盘点</option>
                                <option value="specific">指定盘点</option>
                                <option value="random">抽样盘点</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="start_date">
                                <i class="fas fa-calendar-alt"></i>
                                开始日期 <span class="required">*</span>
                            </label>
                            <input type="date" name="start_date" id="start_date" class="form-control" 
                                   value="<?= date('Y-m-d') ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="end_date">
                                <i class="fas fa-calendar-alt"></i>
                                结束日期 <span class="required">*</span>
                            </label>
                            <input type="date" name="end_date" id="end_date" class="form-control" 
                                   value="<?= date('Y-m-d', strtotime('+3 days')) ?>" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="executor_id">
                                <i class="fas fa-user"></i>
                                执行人 <span class="required">*</span>
                            </label>
                            <select name="executor_id" id="executor_id" class="form-control" required>
                                <option value="">请选择执行人</option>
                                <?php if (!empty($data['users'])): ?>
                                    <?php foreach ($data['users'] as $user): ?>
                                        <option value="<?= $user['id'] ?>">
                                            <?= htmlspecialchars($user['real_name'] ?? $user['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="description">
                                <i class="fas fa-sticky-note"></i>
                                任务描述
                            </label>
                            <input type="text" name="description" id="description" class="form-control" 
                                   placeholder="任务描述或备注信息">
                        </div>
                    </div>
                </div>

                <!-- 盘点范围 -->
                <div class="section" id="scope-section" style="display: none;">
                    <h3><i class="fas fa-bullseye"></i> 盘点范围</h3>
                    
                    <!-- 分类盘点 -->
                    <div id="category-scope" style="display: none;">
                        <div class="form-group">
                            <label>选择盘点分类</label>
                            <div class="checkbox-grid">
                                <?php if (!empty($data['categories'])): ?>
                                    <?php foreach ($data['categories'] as $category): ?>
                                        <label class="checkbox-item">
                                            <input type="checkbox" name="category_ids[]" value="<?= $category['id'] ?>">
                                            <span class="checkmark"></span>
                                            <?= htmlspecialchars($category['name']) ?>
                                        </label>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- 指定盘点 -->
                    <div id="specific-scope" style="display: none;">
                        <div class="form-group">
                            <label>选择盘点食材</label>
                            <div class="ingredient-selector">
                                <div class="selector-header">
                                    <input type="text" id="ingredient-search" placeholder="搜索食材..." class="form-control">
                                    <div class="selector-actions">
                                        <button type="button" onclick="selectAll()" class="btn btn-sm btn-primary">全选</button>
                                        <button type="button" onclick="clearAll()" class="btn btn-sm btn-secondary">清空</button>
                                    </div>
                                </div>
                                <div class="ingredient-list" id="ingredient-list">
                                    <?php if (!empty($data['ingredients'])): ?>
                                        <?php 
                                        $currentCategory = '';
                                        foreach ($data['ingredients'] as $ingredient): 
                                            if ($currentCategory !== $ingredient['category_name']):
                                                if ($currentCategory !== '') echo '</div>';
                                                $currentCategory = $ingredient['category_name'];
                                                echo '<div class="ingredient-category">';
                                                echo '<h4>' . htmlspecialchars($currentCategory) . '</h4>';
                                            endif;
                                        ?>
                                            <label class="ingredient-item">
                                                <input type="checkbox" name="ingredient_ids[]" value="<?= $ingredient['id'] ?>"
                                                       data-name="<?= htmlspecialchars($ingredient['name']) ?>"
                                                       data-category="<?= htmlspecialchars($ingredient['category_name']) ?>">
                                                <span class="ingredient-name"><?= htmlspecialchars($ingredient['name']) ?></span>
                                                <span class="ingredient-unit"><?= htmlspecialchars($ingredient['unit']) ?></span>
                                            </label>
                                        <?php endforeach; ?>
                                        <?php if ($currentCategory !== '') echo '</div>'; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 抽样盘点 -->
                    <div id="random-scope" style="display: none;">
                        <div class="form-group">
                            <label for="sample_percentage">抽样比例</label>
                            <div class="input-group">
                                <input type="number" name="sample_percentage" id="sample_percentage" 
                                       class="form-control" min="1" max="100" value="10">
                                <span class="input-group-text">%</span>
                            </div>
                            <small class="form-help">系统将随机选择指定比例的食材进行盘点</small>
                        </div>
                    </div>
                </div>

                <!-- 预览信息 -->
                <div class="section" id="preview-section" style="display: none;">
                    <h3><i class="fas fa-eye"></i> 预览信息</h3>
                    <div class="preview-content">
                        <div class="preview-item">
                            <label>预计盘点项目数：</label>
                            <span id="estimated-items">0</span> 项
                        </div>
                        <div class="preview-item">
                            <label>预计盘点时间：</label>
                            <span id="estimated-time">0</span> 小时
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        创建盘点任务
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        取消
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.form-container {
    max-width: 2600px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.section:last-child {
    border-bottom: none;
}

.section h3 {
    margin-bottom: 20px;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
}

.form-group label i {
    margin-right: 8px;
    color: #6b7280;
}

.required {
    color: #ef4444;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-group {
    display: flex;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group-text {
    padding: 12px;
    background: #f9fafb;
    border: 1px solid #d1d5db;
    border-left: none;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    font-size: 14px;
    color: #6b7280;
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.checkbox-item:hover {
    background: #f9fafb;
    border-color: #3b82f6;
}

.checkbox-item input[type="checkbox"] {
    margin: 0;
}

.ingredient-selector {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
}

.selector-header {
    padding: 15px;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    gap: 10px;
    align-items: center;
}

.selector-header input {
    flex: 1;
}

.selector-actions {
    display: flex;
    gap: 5px;
}

.ingredient-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
}

.ingredient-category {
    margin-bottom: 20px;
}

.ingredient-category h4 {
    margin: 0 0 10px 0;
    color: #4b5563;
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 5px;
    border-bottom: 1px solid #e5e7eb;
}

.ingredient-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s;
}

.ingredient-item:hover {
    background: #f3f4f6;
}

.ingredient-name {
    flex: 1;
    font-weight: 500;
}

.ingredient-unit {
    color: #6b7280;
    font-size: 12px;
}

.preview-content {
    background: #f9fafb;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.preview-item:last-child {
    margin-bottom: 0;
}

.preview-item label {
    font-weight: 600;
    color: #374151;
}

.preview-item span {
    font-weight: 700;
    color: #059669;
}

.form-help {
    color: #6b7280;
    font-size: 12px;
    margin-top: 5px;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.alert {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.alert-danger {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.alert i {
    margin-right: 10px;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .checkbox-grid {
        grid-template-columns: 1fr;
    }
    
    .selector-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<script>
function handleTaskTypeChange() {
    const taskType = document.getElementById('task_type').value;
    const scopeSection = document.getElementById('scope-section');
    const previewSection = document.getElementById('preview-section');
    
    // 隐藏所有范围选择
    document.getElementById('category-scope').style.display = 'none';
    document.getElementById('specific-scope').style.display = 'none';
    document.getElementById('random-scope').style.display = 'none';
    
    if (taskType) {
        scopeSection.style.display = 'block';
        previewSection.style.display = 'block';
        
        switch (taskType) {
            case 'category':
                document.getElementById('category-scope').style.display = 'block';
                break;
            case 'specific':
                document.getElementById('specific-scope').style.display = 'block';
                break;
            case 'random':
                document.getElementById('random-scope').style.display = 'block';
                break;
        }
        
        updatePreview();
    } else {
        scopeSection.style.display = 'none';
        previewSection.style.display = 'none';
    }
}

function updatePreview() {
    const taskType = document.getElementById('task_type').value;
    let estimatedItems = 0;
    
    switch (taskType) {
        case 'full':
            estimatedItems = <?= count($data['ingredients'] ?? []) ?>;
            break;
        case 'category':
            const selectedCategories = document.querySelectorAll('input[name="category_ids[]"]:checked');
            // 简单估算：每个分类平均20个食材
            estimatedItems = selectedCategories.length * 20;
            break;
        case 'specific':
            const selectedIngredients = document.querySelectorAll('input[name="ingredient_ids[]"]:checked');
            estimatedItems = selectedIngredients.length;
            break;
        case 'random':
            const percentage = document.getElementById('sample_percentage').value || 10;
            estimatedItems = Math.ceil(<?= count($data['ingredients'] ?? []) ?> * percentage / 100);
            break;
    }
    
    document.getElementById('estimated-items').textContent = estimatedItems;
    
    // 估算时间：每个食材约3分钟
    const estimatedTime = Math.ceil(estimatedItems * 3 / 60);
    document.getElementById('estimated-time').textContent = estimatedTime;
}

function selectAll() {
    const checkboxes = document.querySelectorAll('#ingredient-list input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updatePreview();
}

function clearAll() {
    const checkboxes = document.querySelectorAll('#ingredient-list input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updatePreview();
}

// 食材搜索
document.getElementById('ingredient-search').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const items = document.querySelectorAll('.ingredient-item');
    
    items.forEach(item => {
        const name = item.querySelector('input').dataset.name.toLowerCase();
        const category = item.querySelector('input').dataset.category.toLowerCase();
        
        if (name.includes(searchTerm) || category.includes(searchTerm)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
});

// 监听复选框变化
document.addEventListener('change', function(e) {
    if (e.target.type === 'checkbox' || e.target.id === 'sample_percentage') {
        updatePreview();
    }
});

// 设置最小结束日期
document.getElementById('start_date').addEventListener('change', function() {
    const startDate = this.value;
    const endDateInput = document.getElementById('end_date');
    endDateInput.min = startDate;
    
    if (endDateInput.value < startDate) {
        const nextDay = new Date(startDate);
        nextDay.setDate(nextDay.getDate() + 1);
        endDateInput.value = nextDay.toISOString().split('T')[0];
    }
});

// 初始化最小结束日期
document.addEventListener('DOMContentLoaded', function() {
    const startDate = document.getElementById('start_date').value;
    document.getElementById('end_date').min = startDate;
});
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>