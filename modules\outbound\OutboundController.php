<?php
/**
 * 出库管理控制器
 */
require_once dirname(__DIR__, 2) . '/includes/BaseController.php';
require_once dirname(__DIR__, 2) . '/includes/helpers.php';

class OutboundController extends BaseController
{
    protected function init()
    {
        $this->setTemplateData([
            'page_title' => '出库管理 - ' . $this->config['name'],
            'current_module' => 'outbound'
        ]);
    }

    public function handleRequest()
    {
        switch ($this->request['action']) {
            case 'create':
                return $this->create();
            case 'edit':
                return $this->edit();
            case 'delete':
                return $this->delete();
            case 'view':
                return $this->view();
            case 'batch_create':
                return $this->batchCreate();
            case 'index':
            default:
                return $this->index();
        }
    }

    /**
     * 出库记录列表页面
     */
    private function index()
    {
        try {
            // 获取搜索参数
            $search = $this->request['get']['search'] ?? '';
            $meal_type = $this->request['get']['meal_type'] ?? '';
            $date_from = $this->request['get']['date_from'] ?? '';
            $date_to = $this->request['get']['date_to'] ?? '';

            // 构建查询条件
            $where = ['1=1'];
            $params = [];

            if ($search) {
                $where[] = '(oo.order_number LIKE ? OR oo.purpose LIKE ?)';
                $params[] = '%' . $search . '%';
                $params[] = '%' . $search . '%';
            }

            if ($meal_type) {
                $where[] = 'oo.meal_type = ?';
                $params[] = $meal_type;
            }

            if ($date_from) {
                $where[] = 'DATE(oo.meal_date) >= ?';
                $params[] = $date_from;
            }

            if ($date_to) {
                $where[] = 'DATE(oo.meal_date) <= ?';
                $params[] = $date_to;
            }

            $whereClause = 'WHERE ' . implode(' AND ', $where);

            // 获取出库单列表
            $records = $this->db->fetchAll("
                SELECT oo.*, '系统' as operator_name,
                       COUNT(ooi.id) as item_count,
                       SUM(ooi.quantity) as total_quantity,
                       oo.total_amount
                FROM outbound_orders oo
                LEFT JOIN outbound_order_items ooi ON oo.id = ooi.order_id
                $whereClause
                GROUP BY oo.id
                ORDER BY oo.meal_date DESC, oo.created_at DESC
                LIMIT 50
            ", $params);

            // 获取统计信息
            $stats = $this->getOutboundStats();

        } catch (Exception $e) {
            // 如果出库单查询失败，回退到出库记录查询
            try {
                $where = ['or.status = 1'];
                $params = [];

                if ($search) {
                    $where[] = '(i.name LIKE ? OR or.batch_number LIKE ?)';
                    $params[] = '%' . $search . '%';
                    $params[] = '%' . $search . '%';
                }

                if ($meal_type) {
                    $where[] = 'or.meal_type = ?';
                    $params[] = $meal_type;
                }

                if ($date_from) {
                    $where[] = 'DATE(or.meal_date) >= ?';
                    $params[] = $date_from;
                }

                if ($date_to) {
                    $where[] = 'DATE(or.meal_date) <= ?';
                    $params[] = $date_to;
                }

                $whereClause = 'WHERE ' . implode(' AND ', $where);

                // 获取出库记录列表（备用方案）
                $records = $this->db->fetchAll("
                    SELECT or.*, i.name as ingredient_name, i.unit,
                           ic.name as category_name,
                           u.name as operator_name,
                           or.batch_number as order_number,
                           1 as item_count,
                           'completed' as status,
                           (or.quantity * or.unit_price) as total_amount
                    FROM outbound_records or
                    LEFT JOIN ingredients i ON or.ingredient_id = i.id
                    LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                    LEFT JOIN users u ON or.created_by = u.id
                    $whereClause
                    ORDER BY or.meal_date DESC, or.created_at DESC
                    LIMIT 50
                ", $params);

                $stats = $this->getOutboundStats();

            } catch (Exception $e2) {
                // 使用模拟数据
                $records = $this->getMockRecords();
                $stats = $this->getMockStats();
            }
        }

        $this->setTemplateData([
            'records' => $records,
            'stats' => $stats,
            'search' => $search,
            'meal_type' => $meal_type,
            'date_from' => $date_from,
            'date_to' => $date_to
        ]);

        $this->render('template.php');
    }

    /**
     * 创建出库记录
     */
    private function create()
    {
        if ($this->request['method'] === 'POST') {
            try {
                // 检查是否是列表选择方式（多个食材）
                $selectedIngredients = $this->request['post']['selected_ingredients'] ?? [];
                $quantities = $this->request['post']['quantities'] ?? [];

                if (!empty($selectedIngredients)) {
                    // 列表选择方式 - 批量出库
                    return $this->createBatchOutbound();
                }

                // 单个食材出库（保持原有逻辑）
                $data = [
                    'ingredient_id' => intval($this->request['post']['ingredient_id']),
                    'quantity' => floatval($this->request['post']['quantity']),
                    'meal_type' => trim($this->request['post']['meal_type']),
                    'meal_date' => $this->request['post']['meal_date'] ?: date('Y-m-d'),
                    'purpose' => trim($this->request['post']['purpose'] ?? ''),
                    'notes' => trim($this->request['post']['notes'] ?? ''),
                    'operator_name' => trim($this->request['post']['operator_name'] ?? '管理员'),
                    'status' => 1,
                    'created_by' => 1, // TODO: 获取当前用户ID
                    'created_at' => date('Y-m-d H:i:s')
                ];

                // 验证必填字段
                $errors = [];
                if (!$data['ingredient_id']) $errors[] = '请选择食材';
                if ($data['quantity'] <= 0) $errors[] = '出库数量必须大于0';
                if (empty($data['meal_type'])) $errors[] = '请选择餐次';
                if (empty($data['meal_date'])) $errors[] = '请选择用餐日期';

                if (!empty($errors)) {
                    throw new Exception(implode(', ', $errors));
                }

                // 检查库存是否充足
                $ingredient = $this->db->fetchOne("SELECT name, current_stock, unit FROM ingredients WHERE id = ?", [$data['ingredient_id']]);
                if (!$ingredient) {
                    throw new Exception('食材不存在');
                }

                if ($ingredient['current_stock'] < $data['quantity']) {
                    throw new Exception("库存不足！当前库存：{$ingredient['current_stock']}{$ingredient['unit']}，出库数量：{$data['quantity']}{$ingredient['unit']}");
                }

                // 生成批次号
                $data['batch_number'] = $this->generateBatchNumber();

                // 获取平均单价（用于计算出库成本）
                $avgPrice = $this->getAveragePrice($data['ingredient_id']);
                $data['unit_price'] = $avgPrice;
                $data['total_amount'] = $data['quantity'] * $avgPrice;

                // 开始事务
                $this->db->beginTransaction();

                // 插入出库记录
                $recordId = $this->db->insert('outbound_records', $data);

                // 更新食材库存
                $this->updateIngredientStock($data['ingredient_id'], $data['quantity'], 'subtract');

                $this->db->commit();
                $this->redirect('index.php', '出库记录添加成功', 'success');

            } catch (Exception $e) {
                try {
                    $this->db->rollback();
                } catch (Exception $rollbackException) {
                    // 忽略回滚错误
                }
                $this->setTemplateData('error_message', $e->getMessage());
            }
        }

        $this->loadFormData();
    }

    /**
     * 批量出库（列表选择方式）
     */
    private function createBatchOutbound()
    {
        $selectedIngredients = $this->request['post']['selected_ingredients'] ?? [];
        $quantities = $this->request['post']['quantities'] ?? [];
        $mealType = trim($this->request['post']['meal_type']);
        $mealDate = $this->request['post']['meal_date'] ?: date('Y-m-d');
        $purpose = trim($this->request['post']['purpose'] ?? '');
        $notes = trim($this->request['post']['notes'] ?? '');

        // 验证基本信息
        if (empty($selectedIngredients)) {
            throw new Exception('请至少选择一个食材');
        }
        if (empty($mealType)) {
            throw new Exception('请选择餐次');
        }
        if (empty($mealDate)) {
            throw new Exception('请选择用餐日期');
        }

        // 验证每个选中的食材都有数量
        $validItems = [];
        foreach ($selectedIngredients as $ingredientId) {
            $ingredientId = intval($ingredientId);
            $quantity = floatval($quantities[$ingredientId] ?? 0);

            if ($quantity <= 0) {
                continue; // 跳过数量为0的食材
            }

            $validItems[] = [
                'ingredient_id' => $ingredientId,
                'quantity' => $quantity
            ];
        }

        if (empty($validItems)) {
            throw new Exception('请为选中的食材填写出库数量');
        }

        // 开始事务
        $this->db->beginTransaction();

        $totalAmount = 0;
        $successCount = 0;

        foreach ($validItems as $item) {
            $ingredientId = $item['ingredient_id'];
            $quantity = $item['quantity'];

            // 检查食材是否存在和库存是否充足
            $ingredient = $this->db->fetchOne("
                SELECT i.name, i.unit, inv.current_quantity
                FROM ingredients i
                LEFT JOIN inventory inv ON i.id = inv.ingredient_id
                WHERE i.id = ?
            ", [$ingredientId]);

            if (!$ingredient) {
                throw new Exception("食材ID {$ingredientId} 不存在");
            }

            $currentStock = $ingredient['current_quantity'] ?? 0;
            if ($currentStock < $quantity) {
                throw new Exception("食材 {$ingredient['name']} 库存不足！当前库存：{$currentStock}{$ingredient['unit']}，出库数量：{$quantity}{$ingredient['unit']}");
            }

            // 获取平均单价
            $avgPrice = $this->getAveragePrice($ingredientId);
            $itemTotal = $quantity * $avgPrice;
            $totalAmount += $itemTotal;

            // 生成批次号
            $batchNumber = $this->generateBatchNumber();

            // 插入出库记录
            $recordData = [
                'ingredient_id' => $ingredientId,
                'quantity' => $quantity,
                'unit_price' => $avgPrice,
                'total_amount' => $itemTotal,
                'batch_number' => $batchNumber,
                'meal_type' => $mealType,
                'meal_date' => $mealDate,
                'purpose' => $purpose,
                'notes' => $notes,
                'status' => 1,
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $recordId = $this->db->insert('outbound_records', $recordData);

            // 更新库存
            $this->updateIngredientStock($ingredientId, $quantity, 'subtract');

            $successCount++;
        }

        $this->db->commit();
        $this->redirect('index.php', "批量出库成功！共处理 {$successCount} 种食材，总价值 ¥" . number_format($totalAmount, 2), 'success');
    }

    /**
     * 批量出库
     */
    private function batchCreate()
    {
        if ($this->request['method'] === 'POST') {
            try {
                $mealType = trim($this->request['post']['meal_type']);
                $mealDate = $this->request['post']['meal_date'] ?: date('Y-m-d');
                $operatorName = trim($this->request['post']['operator_name'] ?? '管理员');
                $notes = trim($this->request['post']['notes'] ?? '');
                $items = $this->request['post']['items'] ?? [];

                if (empty($items)) {
                    throw new Exception('请至少添加一种食材');
                }

                // 开始事务
                $this->db->beginTransaction();

                $totalRecords = 0;
                $totalAmount = 0;

                foreach ($items as $item) {
                    $ingredientId = intval($item['ingredient_id']);
                    $quantity = floatval($item['quantity']);
                    $purpose = trim($item['purpose'] ?? '');

                    if ($quantity <= 0) {
                        continue; // 跳过数量为0的食材
                    }

                    // 检查库存
                    $ingredient = $this->db->fetchOne("SELECT name, current_stock, unit FROM ingredients WHERE id = ?", [$ingredientId]);
                    if (!$ingredient) {
                        throw new Exception("食材ID {$ingredientId} 不存在");
                    }

                    if ($ingredient['current_stock'] < $quantity) {
                        throw new Exception("{$ingredient['name']} 库存不足！当前库存：{$ingredient['current_stock']}{$ingredient['unit']}");
                    }

                    // 获取平均单价
                    $avgPrice = $this->getAveragePrice($ingredientId);

                    $data = [
                        'ingredient_id' => $ingredientId,
                        'quantity' => $quantity,
                        'unit_price' => $avgPrice,
                        'total_amount' => $quantity * $avgPrice,
                        'batch_number' => $this->generateBatchNumber(),
                        'meal_type' => $mealType,
                        'meal_date' => $mealDate,
                        'purpose' => $purpose,
                        'notes' => $notes,
                        'operator_name' => $operatorName,
                        'status' => 1,
                        'created_by' => 1, // TODO: 获取当前用户ID
                        'created_at' => date('Y-m-d H:i:s')
                    ];

                    // 插入出库记录
                    $this->db->insert('outbound_records', $data);

                    // 更新库存
                    $this->updateIngredientStock($ingredientId, $quantity, 'subtract');

                    $totalRecords++;
                    $totalAmount += $data['total_amount'];
                }

                $this->db->commit();
                $this->redirect('index.php', "批量出库成功，共出库 {$totalRecords} 种食材，总价值 ¥" . number_format($totalAmount, 2), 'success');

            } catch (Exception $e) {
                try {
                    $this->db->rollback();
                } catch (Exception $rollbackException) {
                    // 忽略回滚错误
                }
                $this->setTemplateData('error_message', $e->getMessage());
            }
        }

        $this->loadFormData();
        $this->render('batch-create-template.php');
    }

    /**
     * 加载表单数据
     */
    private function loadFormData()
    {
        try {
            // 获取有库存的食材列表，包含库存信息
            $ingredients = $this->db->fetchAll("
                SELECT i.id, i.name, i.unit,
                       COALESCE(inv.current_quantity, i.current_stock, 0) as current_stock,
                       ic.name as category_name
                FROM ingredients i
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                LEFT JOIN inventory inv ON i.id = inv.ingredient_id
                WHERE i.status = 1 AND COALESCE(inv.current_quantity, i.current_stock, 0) > 0
                ORDER BY ic.name ASC, i.name ASC
            ");

            $categories = $this->db->fetchAll("
                SELECT id, name
                FROM ingredient_categories
                WHERE status = 1
                ORDER BY name ASC
            ");

        } catch (Exception $e) {
            $ingredients = $this->getMockIngredients();
            $categories = $this->getMockCategories();
        }

        $this->setTemplateData([
            'ingredients' => $ingredients,
            'categories' => $categories
        ]);

        // 检查是否使用列表选择方式
        $useList = $this->request['get']['list'] ?? false;
        if ($useList) {
            $this->render('create-list-template.php');
        } else {
            $this->render('create-template.php');
        }
    }

    /**
     * 查看出库记录详情
     */
    private function view()
    {
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            $record = $this->db->fetchOne("
                SELECT or.*, i.name as ingredient_name, i.unit,
                       ic.name as category_name,
                       u.name as operator_name
                FROM outbound_records or
                LEFT JOIN ingredients i ON or.ingredient_id = i.id
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                LEFT JOIN users u ON or.created_by = u.id
                WHERE or.id = ?
            ", [$id]);

            if (!$record) {
                $this->redirect('index.php', '出库记录不存在', 'error');
                return;
            }

        } catch (Exception $e) {
            // 使用模拟数据
            $mockRecords = $this->getMockRecords();
            $record = array_filter($mockRecords, function($r) use ($id) {
                return $r['id'] == $id;
            });
            $record = !empty($record) ? array_values($record)[0] : null;

            if (!$record) {
                $this->redirect('index.php', '出库记录不存在', 'error');
                return;
            }
        }

        $this->setTemplateData([
            'record' => $record,
            'action' => 'view'
        ]);

        $this->render('view-template.php');
    }

    /**
     * 删除出库记录
     */
    private function delete()
    {
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            // 获取出库记录信息（用于回滚库存）
            $record = $this->db->fetchOne("SELECT ingredient_id, quantity FROM outbound_records WHERE id = ?", [$id]);

            if ($record) {
                // 开始事务
                $this->db->beginTransaction();

                // 删除出库记录
                $this->db->delete('outbound_records', 'id = ?', [$id]);

                // 回滚库存（增加库存）
                $this->updateIngredientStock($record['ingredient_id'], $record['quantity'], 'add');

                // 提交事务
                $this->db->commit();

                $this->redirect('index.php', '出库记录删除成功', 'success');
            } else {
                $this->redirect('index.php', '出库记录不存在', 'error');
            }

        } catch (Exception $e) {
            $this->db->rollback();
            $this->redirect('index.php', '删除失败：' . $e->getMessage(), 'error');
        }
    }

    /**
     * 生成批次号
     */
    private function generateBatchNumber()
    {
        return 'OUT' . date('YmdHis') . rand(100, 999);
    }

    /**
     * 获取食材平均价格
     */
    private function getAveragePrice($ingredientId)
    {
        try {
            $result = $this->db->fetchOne("
                SELECT AVG(unit_price) as avg_price 
                FROM inbound_records 
                WHERE ingredient_id = ? AND status = 1
            ", [$ingredientId]);

            return $result['avg_price'] ?? 0;
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 更新食材库存
     */
    private function updateIngredientStock($ingredientId, $quantity, $operation = 'subtract')
    {
        try {
            $sql = "UPDATE ingredients SET current_stock = current_stock " .
                   ($operation === 'add' ? '+' : '-') . " ? WHERE id = ?";
            $this->db->query($sql, [$quantity, $ingredientId]);
        } catch (Exception $e) {
            error_log("库存更新失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取出库统计信息
     */
    private function getOutboundStats()
    {
        try {
            return $this->db->fetchOne("
                SELECT 
                    COUNT(*) as total_records,
                    SUM(quantity) as total_quantity,
                    SUM(total_amount) as total_amount,
                    COUNT(DISTINCT ingredient_id) as unique_ingredients,
                    COUNT(DISTINCT DATE(meal_date)) as meal_days
                FROM outbound_records 
                WHERE status = 1 
                AND meal_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            ");
        } catch (Exception $e) {
            return $this->getMockStats();
        }
    }

    /**
     * 获取模拟出库记录数据
     */
    private function getMockRecords()
    {
        return [
            [
                'id' => 1,
                'ingredient_name' => '白菜',
                'unit' => '斤',
                'category_name' => '蔬菜类',
                'quantity' => 20.00,
                'unit_price' => 2.50,
                'total_amount' => 50.00,
                'batch_number' => 'OUT20241201001',
                'meal_type' => 'lunch',
                'meal_date' => '2024-12-01',
                'purpose' => '中午炒白菜',
                'operator_name' => '张三',
                'notes' => '新鲜蔬菜',
                'created_at' => '2024-12-01 10:30:00'
            ],
            [
                'id' => 2,
                'ingredient_name' => '猪肉',
                'unit' => '斤',
                'category_name' => '肉类',
                'quantity' => 15.00,
                'unit_price' => 28.00,
                'total_amount' => 420.00,
                'batch_number' => 'OUT20241201002',
                'meal_type' => 'dinner',
                'meal_date' => '2024-12-01',
                'purpose' => '晚餐红烧肉',
                'operator_name' => '李四',
                'notes' => '优质猪肉',
                'created_at' => '2024-12-01 15:45:00'
            ]
        ];
    }

    /**
     * 获取模拟食材数据
     */
    private function getMockIngredients()
    {
        return [
            ['id' => 1, 'name' => '白菜', 'unit' => '斤', 'current_stock' => 50.0, 'category_name' => '蔬菜类'],
            ['id' => 2, 'name' => '猪肉', 'unit' => '斤', 'current_stock' => 30.0, 'category_name' => '肉类'],
            ['id' => 3, 'name' => '大米', 'unit' => '袋', 'current_stock' => 10.0, 'category_name' => '粮食类']
        ];
    }

    /**
     * 获取模拟分类数据
     */
    private function getMockCategories()
    {
        return [
            ['id' => 1, 'name' => '蔬菜类'],
            ['id' => 2, 'name' => '肉类'],
            ['id' => 3, 'name' => '粮食类']
        ];
    }

    /**
     * 获取模拟统计数据
     */
    private function getMockStats()
    {
        return [
            'total_records' => 156,
            'total_quantity' => 1240.5,
            'total_amount' => 8650.00,
            'unique_ingredients' => 28,
            'meal_days' => 30
        ];
    }
}
?>