<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $data['page_title'] ?? '学校食堂管理系统' ?></title>
    
    <!-- CSS -->
    <!-- Bootstrap CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 模块特定样式 -->
    <?php if (file_exists(__DIR__ . '/../modules/' . basename(dirname($_SERVER['SCRIPT_NAME'])) . '/style.css')): ?>
    <link rel="stylesheet" href="style.css?v=<?= time() ?>">
    <?php endif; ?>
    
    <style>
        /* 基础布局样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #2d3748;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .main-content {
            margin-left: 260px;
            min-height: 100vh;
            background: #f8fafc;
        }
        
        .content {
            padding: 25px;
        }
        
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .content-header h1 {
            margin: 0;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 28px;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        
        /* 按钮样式 */
        .btn {
            padding: 12px 18px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            align-items: center;
            gap: 8px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #4299e1;
            color: white;
        }
        
        .btn-primary:hover {
            background: #3182ce;
            color: white;
            text-decoration: none;
        }
        
        .btn-success {
            background: #48bb78;
            color: white;
        }
        
        .btn-success:hover {
            background: #38a169;
            color: white;
            text-decoration: none;
        }
        
        .btn-secondary {
            background: #a0aec0;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #718096;
            color: white;
            text-decoration: none;
        }
        
        .btn-danger {
            background: #f56565;
            color: white;
        }
        
        .btn-danger:hover {
            background: #e53e3e;
            color: white;
            text-decoration: none;
        }
        
        .btn-info {
            background: #4299e1;
            color: white;
        }
        
        .btn-info:hover {
            background: #3182ce;
            color: white;
            text-decoration: none;
        }
        
        .btn-warning {
            background: #ed8936;
            color: white;
        }
        
        .btn-warning:hover {
            background: #dd6b20;
            color: white;
            text-decoration: none;
        }
        
        .btn-sm {
            padding: 8px 14px;
            font-size: 14px;
        }
        
        /* 表单样式 */
        .form-control {
            width: 100%;
            padding: 12px 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 15px;
            transition: border-color 0.2s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
            font-size: 15px;
        }
        
        /* 搜索表单全局样式 - 已移至 styles.css，此处保留兼容性 */
        .search-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        /* 旧版搜索表单样式 - 保持向后兼容，但优先级低于新样式 */
        .search-form {
            display: flex;
            gap: 20px;
            align-items: end;
            flex-wrap: wrap;
        }
        
        .search-form .form-group {
            margin-bottom: 0;
            box-sizing: border-box;
        }
        
        .search-form .form-group:first-child {
            flex: 0 0 250px;
            min-width: 250px;
            max-width: 250px;
        }
        
        .search-form .form-group:not(:first-child):not(.btn-group) {
            flex: 0 0 140px;
            min-width: 140px;
            max-width: 140px;
        }
        
        .search-form .btn {
            flex: 0 0 auto;
            white-space: nowrap;
        }
        
        @media (max-width: 768px) {
            .search-form {
                flex-wrap: wrap;
            }
            
            .search-form .form-group {
                flex: 1 1 100%;
                min-width: 100%;
                max-width: none;
            }
        }
        
        /* 表格样式 */
        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        .table th,
        .table td {
            padding: 15px 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
            font-size: 15px;
        }
        
        .table th {
            background: #dbeafe;
            font-weight: 600;
            color: #1e40af;
        }
        
        .table tbody tr:hover {
            background: #f7fafc;
        }
        
        /* 消息提示 */
        .alert {
            padding: 18px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            font-size: 15px;
        }
        
        .alert-success {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            color: #22543d;
        }
        
        .alert-danger {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }
        
        .alert-warning {
            background: #fffbeb;
            border: 1px solid #fed7aa;
            color: #92400e;
        }
        
        .alert-info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }
        
        .alert i {
            margin-right: 12px;
            font-size: 16px;
        }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
            }
            
            .content-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
                padding: 20px;
            }
            
            .content-header h1 {
                font-size: 24px;
            }
            
            .header-actions {
                justify-content: center;
            }
        }
        
        /* 文本样式 */
        .text-center {
            text-align: center;
        }
        
        .text-muted {
            color: #718096;
            font-size: 14px;
        }
        
        .text-primary {
            color: #4299e1;
        }
        
        .text-success {
            color: #48bb78;
        }
        
        .text-danger {
            color: #f56565;
        }
        
        .text-warning {
            color: #ed8936;
        }
        
        /* 模块化侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 260px;
            height: 100vh;
            background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            transition: transform 0.3s ease;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header {
            padding: 30px 25px;
            border-bottom: 1px solid #4a5568;
            text-align: center;
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }

        .sidebar-header h2 {
            font-size: 18px;
            font-weight: 800;
            margin-bottom: 10px;
            color: #ffffff;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 0.5px;
            line-height: 1.1;
            white-space: nowrap;
        }

        .sidebar-header p {
            font-size: 10px;
            color: #cbd5e0;
            margin-top: 6px;
            margin-bottom: 0;
            position: relative;
            z-index: 1;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-section {
            margin-bottom: 25px;
            position: relative;
        }

        .nav-section::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 25px;
            right: 25px;
            height: 1px;
            background: linear-gradient(90deg, transparent, #4a5568, transparent);
        }

        .nav-section:last-child::after {
            display: none;
        }

        .nav-section-title {
            padding: 14px 25px 10px;
            font-size: 16px;
            font-weight: 700;
            color: #a0aec0;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            display: flex;
            align-items: center;
        }

        .nav-section-title::before {
            content: '';
            width: 3px;
            height: 3px;
            background: #667eea;
            border-radius: 50%;
            margin-right: 8px;
            box-shadow: 0 0 6px rgba(102, 126, 234, 0.5);
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 16px 25px;
            color: #e2e8f0;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-left: 3px solid transparent;
            border-radius: 0 25px 25px 0;
            margin: 2px 8px 2px 0;
            position: relative;
            overflow: hidden;
            font-weight: 500;
            font-size: 15px;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s ease;
        }

        .nav-item:hover::before {
            left: 100%;
        }

        .nav-item:hover {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            border-left-color: #667eea;
            color: #ffffff;
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
            text-decoration: none;
        }

        .nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-left-color: #667eea;
            color: #ffffff;
            transform: translateX(5px);
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: #ffffff;
            border-radius: 2px 0 0 2px;
        }

        .nav-item i {
            width: 22px;
            margin-right: 14px;
            text-align: center;
            font-size: 17px;
            transition: transform 0.3s ease;
        }

        .nav-item:hover i,
        .nav-item.active i {
            transform: scale(1.1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
                transition: transform 0.3s ease;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <?php
    // 显示消息提示
    if (isset($_GET['message']) && isset($_GET['type'])) {
        $message = htmlspecialchars($_GET['message']);
        $type = htmlspecialchars($_GET['type']);
        $alertClass = 'alert-info';
        $icon = 'fas fa-info-circle';
        
        switch ($type) {
            case 'success':
                $alertClass = 'alert-success';
                $icon = 'fas fa-check-circle';
                break;
            case 'error':
            case 'danger':
                $alertClass = 'alert-danger';
                $icon = 'fas fa-exclamation-circle';
                break;
            case 'warning':
                $alertClass = 'alert-warning';
                $icon = 'fas fa-exclamation-triangle';
                break;
        }
        
        echo "<div class='alert {$alertClass}' style='position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;'>";
        echo "<i class='{$icon}'></i>";
        echo $message;
        echo "<button type='button' style='margin-left: auto; background: none; border: none; font-size: 18px; cursor: pointer; color: inherit;' onclick='this.parentElement.remove()'>&times;</button>";
        echo "</div>";
        
        echo "<script>";
        echo "setTimeout(function() {";
        echo "  var alert = document.querySelector('.alert');";
        echo "  if (alert) alert.remove();";
        echo "}, 5000);";
        echo "</script>";
    }
    ?>