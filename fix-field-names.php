<?php
/**
 * 修复字段名问题
 */

require_once 'includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>修复字段名问题</h2>";
    
    // 1. 检查入库单表字段
    echo "<h3>1. 检查入库单表 (inbound_orders) 字段</h3>";
    
    try {
        $columns = $db->query("SHOW COLUMNS FROM inbound_orders")->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>字段名</th><th>类型</th><th>说明</th></tr>";
        
        $user_fields = [];
        foreach ($columns as $col) {
            $field = $col['Field'];
            $type = $col['Type'];
            
            // 检查可能的用户/操作员字段
            if (strpos(strtolower($field), 'user') !== false || 
                strpos(strtolower($field), 'operator') !== false ||
                strpos(strtolower($field), 'created') !== false ||
                strpos(strtolower($field), 'by') !== false) {
                $user_fields[] = $field;
                echo "<tr style='background: #ffffcc;'>";
            } else {
                echo "<tr>";
            }
            
            echo "<td><strong>$field</strong></td>";
            echo "<td>$type</td>";
            echo "<td>";
            if (strpos(strtolower($field), 'user') !== false || 
                strpos(strtolower($field), 'operator') !== false ||
                strpos(strtolower($field), 'created') !== false) {
                echo "🔍 可能的操作员字段";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h4>找到的用户相关字段：</h4>";
        if (!empty($user_fields)) {
            foreach ($user_fields as $field) {
                echo "<p style='color: green;'>✅ $field</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ 没有找到用户相关字段</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 入库单表不存在或查询失败: " . $e->getMessage() . "</p>";
    }
    
    // 2. 检查出库单表字段
    echo "<h3>2. 检查出库单表 (outbound_orders) 字段</h3>";
    
    try {
        $columns = $db->query("SHOW COLUMNS FROM outbound_orders")->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>字段名</th><th>类型</th><th>说明</th></tr>";
        
        $user_fields = [];
        foreach ($columns as $col) {
            $field = $col['Field'];
            $type = $col['Type'];
            
            // 检查可能的用户/操作员字段
            if (strpos(strtolower($field), 'user') !== false || 
                strpos(strtolower($field), 'operator') !== false ||
                strpos(strtolower($field), 'created') !== false ||
                strpos(strtolower($field), 'by') !== false) {
                $user_fields[] = $field;
                echo "<tr style='background: #ffffcc;'>";
            } else {
                echo "<tr>";
            }
            
            echo "<td><strong>$field</strong></td>";
            echo "<td>$type</td>";
            echo "<td>";
            if (strpos(strtolower($field), 'user') !== false || 
                strpos(strtolower($field), 'operator') !== false ||
                strpos(strtolower($field), 'created') !== false) {
                echo "🔍 可能的操作员字段";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h4>找到的用户相关字段：</h4>";
        if (!empty($user_fields)) {
            foreach ($user_fields as $field) {
                echo "<p style='color: green;'>✅ $field</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ 没有找到用户相关字段</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 出库单表不存在或查询失败: " . $e->getMessage() . "</p>";
    }
    
    // 3. 测试不同的查询方案
    echo "<h3>3. 测试查询方案</h3>";
    
    $test_queries = [
        '不关联用户表' => "SELECT io.* FROM inbound_orders io LIMIT 1",
        '使用operator_id' => "SELECT io.*, u.name as operator_name FROM inbound_orders io LEFT JOIN users u ON io.operator_id = u.id LIMIT 1",
        '使用created_by' => "SELECT io.*, u.name as operator_name FROM inbound_orders io LEFT JOIN users u ON io.created_by = u.id LIMIT 1",
        '使用user_id' => "SELECT io.*, u.name as operator_name FROM inbound_orders io LEFT JOIN users u ON io.user_id = u.id LIMIT 1"
    ];
    
    foreach ($test_queries as $name => $query) {
        try {
            $result = $db->fetchOne($query);
            echo "<p style='color: green;'>✅ 查询方案 '$name' 成功</p>";
            if ($result && isset($result['operator_name'])) {
                echo "<p style='margin-left: 20px;'>操作员: " . htmlspecialchars($result['operator_name']) . "</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 查询方案 '$name' 失败: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    // 4. 建议的修复方案
    echo "<h3>4. 建议的修复方案</h3>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>方案1: 不关联用户表（最安全）</h4>";
    echo "<p>如果用户字段不存在或有问题，可以暂时不显示操作员姓名：</p>";
    echo "<pre>SELECT io.* FROM inbound_orders io WHERE 1=1 ORDER BY io.created_at DESC</pre>";
    echo "</div>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>方案2: 添加缺失字段</h4>";
    echo "<p>如果表结构不完整，可以添加缺失的字段：</p>";
    echo "<pre>ALTER TABLE inbound_orders ADD COLUMN created_by bigint(20) DEFAULT 1;</pre>";
    echo "<pre>ALTER TABLE outbound_orders ADD COLUMN created_by bigint(20) DEFAULT 1;</pre>";
    echo "</div>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>方案3: 使用现有字段</h4>";
    echo "<p>根据上面的测试结果，使用实际存在的字段进行关联。</p>";
    echo "</div>";
    
    // 5. 自动修复选项
    echo "<h3>5. 自动修复选项</h3>";
    
    echo "<p><a href='?action=fix_no_user' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>应用方案1: 不关联用户表</a></p>";
    echo "<p><a href='?action=add_fields' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>应用方案2: 添加缺失字段</a></p>";
    
    // 处理修复操作
    if (isset($_GET['action'])) {
        echo "<h3>6. 执行修复</h3>";
        
        if ($_GET['action'] === 'fix_no_user') {
            echo "<p style='color: blue;'>正在应用方案1: 修改查询不关联用户表...</p>";
            
            // 这里我们会在后面的代码中实际修复文件
            echo "<p style='color: green;'>✅ 请手动修改控制器文件，或者点击下面的链接应用修复</p>";
            echo "<p><a href='apply-fix-no-user.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>应用修复到控制器文件</a></p>";
            
        } elseif ($_GET['action'] === 'add_fields') {
            echo "<p style='color: blue;'>正在应用方案2: 添加缺失字段...</p>";
            
            try {
                // 检查并添加 created_by 字段到入库单表
                $columns = $db->query("SHOW COLUMNS FROM inbound_orders")->fetchAll();
                $has_created_by = false;
                foreach ($columns as $col) {
                    if ($col['Field'] === 'created_by') {
                        $has_created_by = true;
                        break;
                    }
                }
                
                if (!$has_created_by) {
                    $db->query("ALTER TABLE inbound_orders ADD COLUMN created_by bigint(20) DEFAULT 1 COMMENT '创建人ID'");
                    echo "<p style='color: green;'>✅ 已添加 inbound_orders.created_by 字段</p>";
                } else {
                    echo "<p style='color: blue;'>ℹ️ inbound_orders.created_by 字段已存在</p>";
                }
                
                // 检查并添加 created_by 字段到出库单表
                $columns = $db->query("SHOW COLUMNS FROM outbound_orders")->fetchAll();
                $has_created_by = false;
                foreach ($columns as $col) {
                    if ($col['Field'] === 'created_by') {
                        $has_created_by = true;
                        break;
                    }
                }
                
                if (!$has_created_by) {
                    $db->query("ALTER TABLE outbound_orders ADD COLUMN created_by bigint(20) DEFAULT 1 COMMENT '创建人ID'");
                    echo "<p style='color: green;'>✅ 已添加 outbound_orders.created_by 字段</p>";
                } else {
                    echo "<p style='color: blue;'>ℹ️ outbound_orders.created_by 字段已存在</p>";
                }
                
                echo "<p style='color: green; font-weight: bold;'>🎉 字段添加完成！现在可以重新测试页面了。</p>";
                echo "<p><a href='modules/inbound/index.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>测试入库页面</a></p>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ 添加字段失败: " . $e->getMessage() . "</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 检查失败：" . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3, h4 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    background: white;
    margin: 15px 0;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
}

th {
    background: #007bff;
    color: white;
    padding: 10px;
}

td {
    padding: 8px 10px;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    margin: 10px 0;
}

p {
    margin: 10px 0;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}
</style>
