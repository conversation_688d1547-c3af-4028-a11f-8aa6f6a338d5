<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查看导入日志</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .log-content { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 600px; overflow-y: auto; white-space: pre-wrap; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .log-line { margin: 2px 0; }
        .log-success { color: green; }
        .log-error { color: red; }
        .log-warning { color: orange; }
    </style>
</head>
<body>
    <h1>📋 查看导入日志</h1>
    
    <?php
    $logFile = '../logs/import_debug.log';
    
    echo "<div class='test-section'>";
    echo "<h2>📊 导入调试日志</h2>";
    
    if (!file_exists($logFile)) {
        echo "<p class='warning'>⚠️ 日志文件不存在: {$logFile}</p>";
        echo "<p class='info'>请先进行一次导入操作来生成日志</p>";
    } else {
        $logContent = file_get_contents($logFile);
        $logSize = filesize($logFile);
        $lastModified = date('Y-m-d H:i:s', filemtime($logFile));
        
        echo "<p class='info'>日志文件: {$logFile}</p>";
        echo "<p class='info'>文件大小: " . number_format($logSize) . " 字节</p>";
        echo "<p class='info'>最后修改: {$lastModified}</p>";
        
        if (empty($logContent)) {
            echo "<p class='warning'>⚠️ 日志文件为空</p>";
        } else {
            echo "<h3>📋 日志内容：</h3>";
            
            // 处理日志内容，添加颜色
            $lines = explode("\n", $logContent);
            $processedLines = [];
            
            foreach ($lines as $line) {
                if (empty(trim($line))) continue;
                
                $class = '';
                if (strpos($line, '✅') !== false || strpos($line, '成功') !== false) {
                    $class = 'log-success';
                } elseif (strpos($line, '❌') !== false || strpos($line, '失败') !== false || strpos($line, 'Exception') !== false) {
                    $class = 'log-error';
                } elseif (strpos($line, '⚠️') !== false || strpos($line, '警告') !== false || strpos($line, '跳过') !== false) {
                    $class = 'log-warning';
                }
                
                $processedLines[] = "<div class='log-line {$class}'>" . htmlspecialchars($line) . "</div>";
            }
            
            echo "<div class='log-content'>";
            echo implode('', $processedLines);
            echo "</div>";
            
            // 统计信息
            $totalLines = count($lines);
            $successLines = count(array_filter($lines, function($line) {
                return strpos($line, '✅') !== false || strpos($line, '成功') !== false;
            }));
            $errorLines = count(array_filter($lines, function($line) {
                return strpos($line, '❌') !== false || strpos($line, '失败') !== false;
            }));
            $warningLines = count(array_filter($lines, function($line) {
                return strpos($line, '⚠️') !== false || strpos($line, '警告') !== false;
            }));
            
            echo "<h3>📊 日志统计：</h3>";
            echo "<p class='info'>总行数: {$totalLines}</p>";
            echo "<p class='success'>成功: {$successLines}</p>";
            echo "<p class='error'>错误: {$errorLines}</p>";
            echo "<p class='warning'>警告: {$warningLines}</p>";
        }
    }
    echo "</div>";
    
    // 显示最近的导入结果
    echo "<div class='test-section'>";
    echo "<h2>🔍 最近的导入结果</h2>";
    
    try {
        require_once '../includes/Database.php';
        $db = Database::getInstance();
        
        $recentOrder = $db->fetchOne("SELECT * FROM purchase_orders ORDER BY created_at DESC LIMIT 1");
        if ($recentOrder) {
            echo "<p class='success'>✅ 最近订单: {$recentOrder['order_number']} (ID: {$recentOrder['id']})</p>";
            echo "<p class='info'>创建时间: {$recentOrder['created_at']}</p>";
            
            $itemCount = $db->fetchOne("SELECT COUNT(*) as count FROM purchase_order_items WHERE order_id = ?", [$recentOrder['id']])['count'];
            echo "<p class='info'>明细数量: <strong>{$itemCount}</strong></p>";
            
            if ($itemCount > 0) {
                echo "<h4>明细列表：</h4>";
                $items = $db->fetchAll("
                    SELECT poi.*, i.name, i.code 
                    FROM purchase_order_items poi 
                    LEFT JOIN ingredients i ON poi.ingredient_id = i.id 
                    WHERE poi.order_id = ? 
                    ORDER BY poi.id
                ", [$recentOrder['id']]);
                
                echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr style='background: #f5f5f5;'>";
                echo "<th style='border: 1px solid #ddd; padding: 8px;'>序号</th>";
                echo "<th style='border: 1px solid #ddd; padding: 8px;'>食材编码</th>";
                echo "<th style='border: 1px solid #ddd; padding: 8px;'>食材名称</th>";
                echo "<th style='border: 1px solid #ddd; padding: 8px;'>数量</th>";
                echo "<th style='border: 1px solid #ddd; padding: 8px;'>单价</th>";
                echo "<th style='border: 1px solid #ddd; padding: 8px;'>总价</th>";
                echo "</tr>";
                
                foreach ($items as $index => $item) {
                    echo "<tr>";
                    echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($index + 1) . "</td>";
                    echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$item['code']}</td>";
                    echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$item['name']}</td>";
                    echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$item['quantity']}</td>";
                    echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$item['unit_price']}</td>";
                    echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$item['total_price']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } else {
            echo "<p class='warning'>⚠️ 没有找到采购订单</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 查询数据库失败: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    ?>
    
    <div class="test-section">
        <h2>🛠️ 操作</h2>
        <div class="step">
            <p>
                <a href="../modules/purchase/index.php?action=import" class="btn">🔄 重新导入</a>
                <a href="debug-actual-import.php" class="btn">🔍 详细调试</a>
                <a href="../modules/purchase/index.php" class="btn">📊 查看采购订单</a>
            </p>
            
            <?php if (file_exists($logFile)): ?>
            <p>
                <a href="?clear_log=1" class="btn" style="background: #dc3545;">🗑️ 清空日志</a>
            </p>
            <?php endif; ?>
        </div>
    </div>
    
    <?php
    // 处理清空日志
    if (isset($_GET['clear_log']) && file_exists($logFile)) {
        unlink($logFile);
        echo "<script>alert('日志已清空'); window.location.href = 'view-import-log.php';</script>";
    }
    ?>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
