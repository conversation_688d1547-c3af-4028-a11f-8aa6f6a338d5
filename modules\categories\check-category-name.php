<?php
/**
 * 检查分类名称是否可用
 */

header('Content-Type: application/json');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    require_once '../../includes/Database.php';

    $name = trim($_POST['name'] ?? '');
    $excludeId = intval($_POST['exclude_id'] ?? 0); // 编辑时排除当前分类ID

    if (empty($name)) {
        echo json_encode(['available' => false, 'message' => '分类名称不能为空']);
        exit;
    }

    $db = Database::getInstance();
    
    // 检查名称是否已存在
    if ($excludeId > 0) {
        // 编辑模式：排除当前分类ID
        $existing = $db->fetchOne(
            "SELECT id FROM ingredient_categories WHERE name = ? AND id != ?", 
            [$name, $excludeId]
        );
    } else {
        // 新建模式：检查所有分类
        $existing = $db->fetchOne(
            "SELECT id FROM ingredient_categories WHERE name = ?", 
            [$name]
        );
    }
    
    if ($existing) {
        echo json_encode([
            'available' => false, 
            'message' => '分类名称已存在',
            'existing_id' => $existing['id']
        ]);
    } else {
        echo json_encode([
            'available' => true, 
            'message' => '分类名称可用'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => '检查失败', 
        'message' => $e->getMessage()
    ]);
}
?>
