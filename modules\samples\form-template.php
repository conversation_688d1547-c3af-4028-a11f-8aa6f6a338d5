<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-vial"></i>
                <?= (isset($action) && $action === 'create') ? '新增留样记录' : '编辑留样记录' ?>
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <?php if (!empty($error)): ?>
        <div class="alert alert-danger">
            <?= htmlspecialchars($error) ?>
        </div>
        <?php endif; ?>

        <div class="form-container">
            <form method="POST" action="index.php?action=save" id="sampleForm" enctype="multipart/form-data">
                <input type="hidden" name="id" value="<?= htmlspecialchars($sample['id'] ?? '') ?>">
                
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-info-circle"></i>
                        基本信息
                    </h3>
                    
                    <div class="form-row">
                        <div class="form-field select-input required">
                            <label>食材</label>
                            <select name="ingredient_id" class="form-control" required>
                                <option value="">请选择食材</option>
                                <?php if (!empty($ingredients)): ?>
                                <?php foreach ($ingredients as $ingredient): ?>
                                <option value="<?= $ingredient['id'] ?>" <?= (isset($sample) && $sample['ingredient_id'] == $ingredient['id']) ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($ingredient['name']) ?> (<?= htmlspecialchars($ingredient['code']) ?>)
                                </option>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        
                        <div class="form-field text-input required">
                            <label>批次号</label>
                            <input type="text" name="batch_number" class="form-control" 
                                   value="<?= htmlspecialchars($sample['batch_number'] ?? '') ?>" 
                                   placeholder="请输入批次号" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-field text-input required">
                            <label>留样编号</label>
                            <div class="input-group">
                                <input type="text" name="sample_code" class="form-control" 
                                       value="<?= htmlspecialchars($sample['sample_code'] ?? '') ?>" 
                                       placeholder="请输入留样编号" required>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="generateSampleCode()">
                                    <i class="fas fa-magic"></i>
                                    自动生成
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-field text-input required">
                            <label>留样数量</label>
                            <input type="number" name="sample_quantity" class="form-control" 
                                   value="<?= htmlspecialchars($sample['sample_quantity'] ?? '') ?>" 
                                   placeholder="请输入留样数量" step="0.01" min="0" required>
                        </div>
                        
                        <div class="form-field select-input required">
                            <label>留样单位</label>
                            <select name="sample_unit" class="form-control" required>
                                <option value="">请选择单位</option>
                                <option value="克" <?= (isset($sample) && $sample['sample_unit'] == '克') ? 'selected' : '' ?>>克</option>
                                <option value="千克" <?= (isset($sample) && $sample['sample_unit'] == '千克') ? 'selected' : '' ?>>千克</option>
                                <option value="毫升" <?= (isset($sample) && $sample['sample_unit'] == '毫升') ? 'selected' : '' ?>>毫升</option>
                                <option value="升" <?= (isset($sample) && $sample['sample_unit'] == '升') ? 'selected' : '' ?>>升</option>
                                <option value="份" <?= (isset($sample) && $sample['sample_unit'] == '份') ? 'selected' : '' ?>>份</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-clock"></i>
                        时间信息
                    </h3>
                    
                    <div class="form-row">
                        <div class="form-field select-input required">
                            <label>餐次</label>
                            <select name="meal_type" class="form-control" required>
                                <option value="">请选择餐次</option>
                                <option value="breakfast" <?= (isset($sample) && $sample['meal_type'] == 'breakfast') ? 'selected' : '' ?>>早餐</option>
                                <option value="lunch" <?= (isset($sample) && $sample['meal_type'] == 'lunch') ? 'selected' : '' ?>>午餐</option>
                                <option value="dinner" <?= (isset($sample) && $sample['meal_type'] == 'dinner') ? 'selected' : '' ?>>晚餐</option>
                                <option value="other" <?= (isset($sample) && $sample['meal_type'] == 'other') ? 'selected' : '' ?>>其他</option>
                            </select>
                        </div>
                        
                        <div class="form-field date-input required">
                            <label>用餐日期</label>
                            <input type="date" name="meal_date" class="form-control" 
                                   value="<?= htmlspecialchars($sample['meal_date'] ?? date('Y-m-d')) ?>" 
                                   required>
                        </div>
                        
                        <div class="form-field datetime-input">
                            <label>留样时间</label>
                            <input type="datetime-local" name="sample_time" class="form-control" 
                                   value="<?= htmlspecialchars(isset($sample) ? date('Y-m-d\TH:i', strtotime($sample['sample_time'])) : date('Y-m-d\TH:i')) ?>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-field date-input required">
                            <label>过期日期</label>
                            <input type="date" name="expiration_date" class="form-control" 
                                   value="<?= htmlspecialchars($sample['expiration_date'] ?? date('Y-m-d', strtotime('+48 hours'))) ?>" 
                                   required>
                            <small class="form-text text-muted">建议设置为留样后48小时</small>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-warehouse"></i>
                        存储信息
                    </h3>
                    
                    <div class="form-row">
                        <div class="form-field select-input">
                            <label>存储位置</label>
                            <select name="storage_location" class="form-control">
                                <option value="">请选择存储位置</option>
                                <option value="冷藏室A" <?= (isset($sample) && $sample['storage_location'] == '冷藏室A') ? 'selected' : '' ?>>冷藏室A</option>
                                <option value="冷藏室B" <?= (isset($sample) && $sample['storage_location'] == '冷藏室B') ? 'selected' : '' ?>>冷藏室B</option>
                                <option value="冷冻室" <?= (isset($sample) && $sample['storage_location'] == '冷冻室') ? 'selected' : '' ?>>冷冻室</option>
                                <option value="常温储存区" <?= (isset($sample) && $sample['storage_location'] == '常温储存区') ? 'selected' : '' ?>>常温储存区</option>
                                <option value="其他" <?= (isset($sample) && $sample['storage_location'] == '其他') ? 'selected' : '' ?>>其他</option>
                            </select>
                        </div>
                        
                        <div class="form-field select-input">
                            <label>存储温度</label>
                            <select name="storage_temperature" class="form-control">
                                <option value="">请选择存储温度</option>
                                <option value="0-4℃" <?= (isset($sample) && $sample['storage_temperature'] == '0-4℃') ? 'selected' : '' ?>>0-4℃（冷藏）</option>
                                <option value="-18℃以下" <?= (isset($sample) && $sample['storage_temperature'] == '-18℃以下') ? 'selected' : '' ?>>-18℃以下（冷冻）</option>
                                <option value="常温" <?= (isset($sample) && $sample['storage_temperature'] == '常温') ? 'selected' : '' ?>>常温（15-25℃）</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-camera"></i>
                        留样图片
                    </h3>
                    
                    <div class="form-row">
                        <div class="form-field file-input">
                            <label>上传留样图片</label>
                            <div class="file-upload-area" onclick="document.getElementById('sample_image').click()">
                                <div class="upload-placeholder" id="uploadPlaceholder">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>点击上传图片或拖拽图片到此处</p>
                                    <small>支持 JPG、PNG 格式，最大 5MB</small>
                                </div>
                                <div class="image-preview" id="imagePreview" style="display: none;">
                                    <img id="previewImg" src="" alt="预览图片">
                                    <div class="image-actions">
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeImage()">
                                            <i class="fas fa-trash"></i>
                                            删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <input type="file" id="sample_image" name="sample_image" accept="image/jpeg,image/jpg,image/png" style="display: none;" onchange="previewImage(this)">
                            <?php if (isset($sample['image_path']) && !empty($sample['image_path'])): ?>
                            <div class="current-image">
                                <small class="text-muted">当前图片：</small>
                                <img src="<?= htmlspecialchars($sample['image_path']) ?>" alt="当前留样图片" style="max-width: 100px; max-height: 100px; border-radius: 4px;">
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-user-shield"></i>
                        管理信息
                    </h3>
                    
                    <div class="form-row">
                        <div class="form-field text-input required">
                            <label>责任人</label>
                            <input type="text" name="responsible_person" class="form-control" 
                                   value="<?= htmlspecialchars($sample['responsible_person'] ?? '') ?>" 
                                   placeholder="请输入责任人姓名" required>
                        </div>
                        
                        <div class="form-field select-input required">
                            <label>状态</label>
                            <select name="sample_status" class="form-control" required>
                                <option value="1" <?= (isset($sample) && $sample['sample_status'] == 1) ? 'selected' : '' ?>>有效</option>
                                <option value="0" <?= (isset($sample) && $sample['sample_status'] == 0) ? 'selected' : '' ?>>已处理</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-field textarea-input">
                        <label>备注</label>
                        <textarea name="notes" class="form-control" rows="3" placeholder="请输入备注信息"><?= htmlspecialchars($sample['notes'] ?? '') ?></textarea>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        <?= (isset($action) && $action === 'create') ? '新增留样' : '保存修改' ?>
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-redo"></i>
                        重置
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        取消
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* 表单容器样式 - 与其他页面保持一致 */
.form-container {
    max-width: 2600px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 其他表单样式保持不变 */
.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.form-section:last-child {
    border-bottom: none;
}

.section-title {
    margin-bottom: 20px;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
    font-weight: 600;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-field {
    display: flex;
    flex-direction: column;
}

.form-field label {
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    justify-content: flex-end;
}

.btn {
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
}

.alert {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.alert-danger {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.alert i {
    margin-right: 10px;
}

/* 文件上传区域样式 */
.file-upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
}

.file-upload-area:hover, .file-upload-area.dragover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.upload-placeholder i {
    font-size: 2rem;
    color: #6b7280;
    margin-bottom: 10px;
}

.image-preview img {
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    margin-bottom: 10px;
}

.input-group {
    display: flex;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-container {
        padding: 20px;
    }
}
</style>

<script>
// 表单验证和提交处理
document.getElementById('sampleForm').addEventListener('submit', function(e) {
    const mealDate = new Date(document.querySelector('input[name="meal_date"]').value);
    const expirationDate = new Date(document.querySelector('input[name="expiration_date"]').value);

    if (expirationDate <= mealDate) {
        e.preventDefault();
        showAlert('过期日期必须晚于用餐日期', 'error');
        return false;
    }

    // 检查图片文件大小
    const imageFile = document.getElementById('sample_image').files[0];
    if (imageFile && imageFile.size > 5 * 1024 * 1024) {
        e.preventDefault();
        showAlert('图片文件大小不能超过5MB', 'error');
        return false;
    }

    // 显示提交状态
    const submitBtn = this.querySelector('button[type="submit"]');
    const formContainer = document.querySelector('.form-container');

    submitBtn.classList.add('loading');
    submitBtn.disabled = true;
    formContainer.classList.add('submitting');

    // 如果是AJAX提交，可以在这里处理
    // 否则让表单正常提交
});

// 显示提示信息
function showAlert(message, type = 'info') {
    // 移除现有的提示
    const existingAlert = document.querySelector('.alert-message');
    if (existingAlert) {
        existingAlert.remove();
    }

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-message`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        ${message}
    `;

    // 插入到表单容器前面
    const formContainer = document.querySelector('.form-container');
    formContainer.parentNode.insertBefore(alertDiv, formContainer);

    // 3秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// 生成留样编号
function generateSampleCode() {
    const now = new Date();
    const dateStr = now.getFullYear().toString() + 
                   (now.getMonth() + 1).toString().padStart(2, '0') + 
                   now.getDate().toString().padStart(2, '0');
    const timeStr = now.getHours().toString().padStart(2, '0') + 
                   now.getMinutes().toString().padStart(2, '0');
    const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();
    
    const sampleCode = `LY${dateStr}${timeStr}${randomStr}`;
    document.querySelector('input[name="sample_code"]').value = sampleCode;
}

// 图片预览功能
function previewImage(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];
        
        // 检查文件类型
        if (!file.type.match('image.*')) {
            alert('请选择图片文件');
            input.value = '';
            return;
        }
        
        // 检查文件大小
        if (file.size > 5 * 1024 * 1024) {
            alert('图片文件大小不能超过5MB');
            input.value = '';
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('uploadPlaceholder').style.display = 'none';
            document.getElementById('imagePreview').style.display = 'block';
            document.getElementById('previewImg').src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
}

// 删除图片
function removeImage() {
    document.getElementById('sample_image').value = '';
    document.getElementById('uploadPlaceholder').style.display = 'block';
    document.getElementById('imagePreview').style.display = 'none';
    document.getElementById('previewImg').src = '';
}

// 拖拽上传功能
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.querySelector('.file-upload-area');
    
    if (uploadArea) {
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const fileInput = document.getElementById('sample_image');
                fileInput.files = files;
                previewImage(fileInput);
            }
        });
    }
    
    // 根据用餐日期自动设置过期日期
    const mealDateInput = document.querySelector('input[name="meal_date"]');
    const expirationDateInput = document.querySelector('input[name="expiration_date"]');
    
    if (mealDateInput && expirationDateInput) {
        mealDateInput.addEventListener('change', function() {
            if (this.value) {
                const mealDate = new Date(this.value);
                mealDate.setDate(mealDate.getDate() + 2); // 默认48小时后过期
                expirationDateInput.value = mealDate.toISOString().split('T')[0];
            }
        });
    }
    
    // 智能填充责任人
    const responsiblePersonInput = document.querySelector('input[name="responsible_person"]');
    if (responsiblePersonInput && !responsiblePersonInput.value) {
        // 可以从用户session或其他地方获取当前用户名
        // responsiblePersonInput.value = getCurrentUserName();
    }

    // 自动保存草稿功能
    const formFields = document.querySelectorAll('#sampleForm input, #sampleForm select, #sampleForm textarea');
    const draftKey = 'sample_form_draft_' + (new Date().toDateString());

    // 加载草稿
    loadDraft();

    // 监听字段变化，自动保存草稿
    formFields.forEach(field => {
        field.addEventListener('input', debounce(saveDraft, 1000));
        field.addEventListener('change', saveDraft);
    });

    function saveDraft() {
        const formData = new FormData(document.getElementById('sampleForm'));
        const draftData = {};

        for (let [key, value] of formData.entries()) {
            if (key !== 'sample_image') { // 不保存文件
                draftData[key] = value;
            }
        }

        localStorage.setItem(draftKey, JSON.stringify(draftData));
        showDraftStatus('草稿已保存');
    }

    function loadDraft() {
        const savedDraft = localStorage.getItem(draftKey);
        if (savedDraft) {
            try {
                const draftData = JSON.parse(savedDraft);

                // 询问是否恢复草稿
                if (confirm('检测到未完成的表单草稿，是否恢复？')) {
                    Object.keys(draftData).forEach(key => {
                        const field = document.querySelector(`[name="${key}"]`);
                        if (field && !field.value) {
                            field.value = draftData[key];
                        }
                    });
                    showDraftStatus('草稿已恢复');
                }
            } catch (e) {
                console.error('草稿恢复失败:', e);
            }
        }
    }

    function showDraftStatus(message) {
        // 移除现有状态
        const existingStatus = document.querySelector('.draft-status');
        if (existingStatus) {
            existingStatus.remove();
        }

        const statusDiv = document.createElement('div');
        statusDiv.className = 'draft-status';
        statusDiv.textContent = message;
        statusDiv.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #374151;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
            opacity: 0.9;
        `;

        document.body.appendChild(statusDiv);

        setTimeout(() => {
            if (statusDiv.parentNode) {
                statusDiv.remove();
            }
        }, 2000);
    }

    // 表单提交成功后清除草稿
    document.getElementById('sampleForm').addEventListener('submit', function() {
        setTimeout(() => {
            localStorage.removeItem(draftKey);
        }, 1000);
    });

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
});

// 表单重置时清除图片预览
document.querySelector('button[type="reset"]').addEventListener('click', function() {
    setTimeout(function() {
        removeImage();
    }, 100);
});

// 拖拽上传功能
const uploadArea = document.querySelector('.file-upload-area');
if (uploadArea) {
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        uploadArea.classList.add('dragover');
    }

    function unhighlight(e) {
        uploadArea.classList.remove('dragover');
    }

    uploadArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            const fileInput = document.getElementById('sample_image');
            fileInput.files = files;
            previewImage(fileInput);
        }
    }
}

// 表单字段实时验证
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('sampleForm');
    const requiredFields = form.querySelectorAll('[required]');

    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateField(this);
        });

        field.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                validateField(this);
            }
        });
    });

    function validateField(field) {
        const isValid = field.checkValidity();

        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
            removeFieldError(field);
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
            showFieldError(field, field.validationMessage);
        }
    }

    function showFieldError(field, message) {
        removeFieldError(field);

        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;

        field.parentNode.appendChild(errorDiv);
    }

    function removeFieldError(field) {
        const existingError = field.parentNode.querySelector('.invalid-feedback');
        if (existingError) {
            existingError.remove();
        }
    }
});
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>