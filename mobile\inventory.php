<?php
/**
 * 移动端库存查询页面
 */
require_once '../includes/Database.php';

// 获取搜索参数
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$status = $_GET['status'] ?? '';

try {
    $db = Database::getInstance();
    
    // 获取分类列表
    $categories = $db->fetchAll("
        SELECT id, name 
        FROM ingredient_categories 
        WHERE status = 1 
        ORDER BY name
    ");
    
    // 构建查询条件
    $whereConditions = ['i.status = 1'];
    $params = [];
    
    if ($search) {
        $whereConditions[] = 'i.name LIKE ?';
        $params[] = '%' . $search . '%';
    }
    
    if ($category) {
        $whereConditions[] = 'i.category_id = ?';
        $params[] = $category;
    }
    
    if ($status) {
        switch ($status) {
            case 'low_stock':
                $whereConditions[] = 'i.current_stock <= i.min_stock';
                break;
            case 'out_of_stock':
                $whereConditions[] = 'i.current_stock = 0';
                break;
            case 'normal':
                $whereConditions[] = 'i.current_stock > i.min_stock';
                break;
        }
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // 获取库存数据
    $inventory = $db->fetchAll("
        SELECT 
            i.id,
            i.name,
            i.unit,
            i.current_stock,
            i.min_stock,
            i.unit_price,
            c.name as category_name,
            CASE 
                WHEN i.current_stock = 0 THEN 'out_of_stock'
                WHEN i.current_stock <= i.min_stock THEN 'low_stock'
                ELSE 'normal'
            END as stock_status
        FROM ingredients i
        LEFT JOIN ingredient_categories c ON i.category_id = c.id
        WHERE $whereClause
        ORDER BY i.current_stock ASC, i.name ASC
        LIMIT 50
    ", $params);
    
    // 获取统计数据
    $stats = $db->fetchOne("
        SELECT 
            COUNT(*) as total_items,
            SUM(CASE WHEN current_stock > min_stock THEN 1 ELSE 0 END) as normal_stock,
            SUM(CASE WHEN current_stock <= min_stock AND current_stock > 0 THEN 1 ELSE 0 END) as low_stock,
            SUM(CASE WHEN current_stock = 0 THEN 1 ELSE 0 END) as out_of_stock,
            SUM(current_stock * unit_price) as total_value
        FROM ingredients 
        WHERE status = 1
    ");
    
} catch (Exception $e) {
    $inventory = [];
    $categories = [];
    $stats = [
        'total_items' => 0,
        'normal_stock' => 0,
        'low_stock' => 0,
        'out_of_stock' => 0,
        'total_value' => 0
    ];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>库存查询 - 学校食堂食材出入库管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
    <style>
        /* 库存查询特有样式 */
        .search-section {
            background: white;
            margin: 60px 0 15px 0;
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .search-input-group {
            display: flex;
            gap: 8px;
        }
        
        .search-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .search-btn {
            padding: 12px 20px;
            background: #4299e1;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
        }
        
        .filter-row {
            display: flex;
            gap: 8px;
        }
        
        .filter-select {
            flex: 1;
            padding: 10px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 18px;
            color: white;
        }
        
        .stat-icon.blue { background: #4299e1; }
        .stat-icon.green { background: #48bb78; }
        .stat-icon.orange { background: #ed8936; }
        .stat-icon.red { background: #f56565; }
        
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #718096;
        }
        
        .inventory-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .inventory-item {
            padding: 16px;
            border-bottom: 1px solid #f7fafc;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .inventory-item:last-child {
            border-bottom: none;
        }
        
        .item-info {
            flex: 1;
        }
        
        .item-name {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }
        
        .item-details {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #718096;
            margin-bottom: 4px;
        }
        
        .category-tag {
            background: #e2e8f0;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
        }
        
        .stock-info {
            text-align: right;
        }
        
        .current-stock {
            font-size: 18px;
            font-weight: bold;
            color: #2d3748;
        }
        
        .min-stock {
            font-size: 12px;
            color: #718096;
            margin-top: 2px;
        }
        
        .stock-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            margin-top: 4px;
            display: inline-block;
        }
        
        .status-normal { background: #c6f6d5; color: #22543d; }
        .status-low { background: #fed7aa; color: #c05621; }
        .status-out { background: #fed7d7; color: #c53030; }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #718096;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .value-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            margin-top: 15px;
        }
        
        .value-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 4px;
        }
        
        .value-amount {
            font-size: 24px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <a href="index.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div class="header-title">
                <span>库存查询</span>
            </div>
            <div class="header-actions">
                <button class="refresh-btn" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="mobile-main">
        <!-- 搜索区域 -->
        <div class="search-section">
            <form method="GET" class="search-form">
                <div class="search-input-group">
                    <input type="text" name="search" class="search-input" 
                           placeholder="搜索食材名称..." value="<?= htmlspecialchars($search) ?>">
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="filter-row">
                    <select name="category" class="filter-select">
                        <option value="">全部分类</option>
                        <?php foreach ($categories as $cat): ?>
                        <option value="<?= $cat['id'] ?>" <?= $category == $cat['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($cat['name']) ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                    <select name="status" class="filter-select">
                        <option value="">全部状态</option>
                        <option value="normal" <?= $status === 'normal' ? 'selected' : '' ?>>库存正常</option>
                        <option value="low_stock" <?= $status === 'low_stock' ? 'selected' : '' ?>>库存不足</option>
                        <option value="out_of_stock" <?= $status === 'out_of_stock' ? 'selected' : '' ?>>已缺货</option>
                    </select>
                </div>
            </form>
        </div>

        <!-- 统计卡片 -->
        <section class="stats-section">
            <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon blue">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stat-number"><?= number_format($stats['total_items']) ?></div>
                <div class="stat-label">食材品种</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon green">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number"><?= number_format($stats['normal_stock']) ?></div>
                <div class="stat-label">库存正常</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon orange">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number"><?= number_format($stats['low_stock']) ?></div>
                <div class="stat-label">库存不足</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon red">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-number"><?= number_format($stats['out_of_stock']) ?></div>
                <div class="stat-label">已缺货</div>
            </div>
        </div>
        </section>

        <!-- 库存列表 -->
        <div class="inventory-list">
            <?php if (!empty($inventory)): ?>
                <?php foreach ($inventory as $item): ?>
                <div class="inventory-item">
                    <div class="item-info">
                        <div class="item-name"><?= htmlspecialchars($item['name']) ?></div>
                        <div class="item-details">
                            <span class="category-tag"><?= htmlspecialchars($item['category_name'] ?? '未分类') ?></span>
                            <span>单位: <?= htmlspecialchars($item['unit']) ?></span>
                            <span>¥<?= number_format($item['unit_price'], 2) ?></span>
                        </div>
                        <span class="stock-status status-<?= $item['stock_status'] ?>">
                            <?php
                            $statusText = [
                                'normal' => '库存正常',
                                'low_stock' => '库存不足', 
                                'out_of_stock' => '已缺货'
                            ];
                            echo $statusText[$item['stock_status']] ?? '未知';
                            ?>
                        </span>
                    </div>
                    <div class="stock-info">
                        <div class="current-stock"><?= number_format($item['current_stock'], 1) ?></div>
                        <div class="min-stock">最低: <?= number_format($item['min_stock'], 1) ?></div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <p>暂无库存记录</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- 库存价值汇总 -->
        <?php if ($stats['total_value'] > 0): ?>
        <div class="value-summary">
            <div class="value-label">库存总价值</div>
            <div class="value-amount">¥<?= number_format($stats['total_value'], 2) ?></div>
        </div>
        <?php endif; ?>

        <!-- 底部间距 -->
        <div class="bottom-spacer"></div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="index.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="inbound.php" class="nav-item">
            <i class="fas fa-box"></i>
            <span>入库</span>
        </a>
        <a href="inventory.php" class="nav-item active">
            <i class="fas fa-warehouse"></i>
            <span>库存</span>
        </a>
        <a href="purchase.php" class="nav-item">
            <i class="fas fa-shopping-cart"></i>
            <span>采购</span>
        </a>
        <a href="reports.php" class="nav-item">
            <i class="fas fa-chart-bar"></i>
            <span>报表</span>
        </a>
        <a href="profile.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </nav>

    <script src="main.js"></script>
</body>
</html>