<!-- 侧边栏 -->
<div class="sidebar">
    <div class="sidebar-header">
        <h2>🏫 学校食堂管理系统</h2>
        <p>Canteen Management System</p>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <div class="nav-section-title">主要功能</div>
            <a href="../dashboard/index.php" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                仪表板
            </a>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">基础数据</div>
            <a href="../ingredients/index.php" class="nav-item">
                <i class="fas fa-carrot"></i>
                食材管理
            </a>
            <a href="../categories/index.php" class="nav-item">
                <i class="fas fa-tags"></i>
                食材分类
            </a>
            <a href="index.php" class="nav-item active">
                <i class="fas fa-truck"></i>
                供应商管理
            </a>
            <a href="../purchase/index.php" class="nav-item">
                <i class="fas fa-shopping-cart"></i>
                采购管理
            </a>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">库存管理</div>
            <a href="../inbound/index.php" class="nav-item">
                <i class="fas fa-arrow-down"></i>
                食材入库
            </a>
            <a href="../outbound/index.php" class="nav-item">
                <i class="fas fa-arrow-up"></i>
                食材出库
            </a>
            <a href="../stocktaking/index.php" class="nav-item">
                <i class="fas fa-clipboard-check"></i>
                食材盘点
            </a>
            <a href="../inventory/index.php" class="nav-item">
                <i class="fas fa-search"></i>
                库存查询
            </a>
            <a href="../damage/index.php" class="nav-item">
                <i class="fas fa-exclamation-triangle"></i>
                食材报损
            </a>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">报表分析</div>
            <a href="#" class="nav-item" onclick="alert('功能开发中，敬请期待！'); return false;">
                <i class="fas fa-chart-bar"></i>
                数据报表
                <span style="font-size: 10px; color: #a0aec0; margin-left: auto;">(开发中)</span>
            </a>
            <a href="#" class="nav-item" onclick="alert('功能开发中，敬请期待！'); return false;">
                <i class="fas fa-chart-line"></i>
                趋势分析
                <span style="font-size: 10px; color: #a0aec0; margin-left: auto;">(开发中)</span>
            </a>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">系统管理</div>
            <a href="#" class="nav-item" onclick="alert('功能开发中，敬请期待！'); return false;">
                <i class="fas fa-users"></i>
                用户管理
                <span style="font-size: 10px; color: #a0aec0; margin-left: auto;">(开发中)</span>
            </a>
            <a href="#" class="nav-item" onclick="alert('功能开发中，敬请期待！'); return false;">
                <i class="fas fa-cog"></i>
                系统设置
                <span style="font-size: 10px; color: #a0aec0; margin-left: auto;">(开发中)</span>
            </a>
        </div>
    </nav>
</div>

<style>
/* 侧边栏样式 */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 260px;
    height: 100vh;
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    color: #e2e8f0;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #4a5568;
    text-align: center;
}

.sidebar-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #e2e8f0;
    margin: 0;
}

.sidebar-header p {
    font-size: 12px;
    color: #a0aec0;
    margin: 5px 0 0 0;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-section {
    margin-bottom: 30px;
}

.nav-section-title {
    padding: 0 20px 10px;
    font-size: 16px;
    font-weight: 700;
    color: #a0aec0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-item {
    display: block;
    padding: 12px 20px;
    color: #e2e8f0;
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: #4a5568;
    border-left-color: #4299e1;
    color: #e2e8f0;
    text-decoration: none;
}

.nav-item.active {
    background: #4a5568;
    border-left-color: #4299e1;
    color: #4299e1;
}

.nav-item i {
    width: 20px;
    margin-right: 12px;
    text-align: center;
}

.main-content {
    margin-left: 260px;
    min-height: 100vh;
    background: #f7fafc;
}
</style>
