/* 人员管理模块样式 */

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #718096;
    font-weight: 500;
}

/* 搜索区域 */
.search-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form {
    margin: 0;
}

.search-group {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
}

.search-field {
    flex: 1;
    min-width: 200px;
}

.search-field .form-control {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.search-field .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 人员表格 */
.users-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.users-table th {
    background: #f7fafc;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: #4a5568;
    border-bottom: 2px solid #e2e8f0;
    font-size: 14px;
}

.users-table td {
    padding: 16px;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
}

.users-table tr:hover {
    background: #f7fafc;
}

/* 用户头像 */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

/* 用户信息 */
.user-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.user-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
}

.user-email {
    font-size: 12px;
    color: #718096;
}

/* 角色徽章 */
.role-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.role-admin {
    background: #fed7d7;
    color: #c53030;
}

.role-chef {
    background: #c6f6d5;
    color: #2f855a;
}

.role-buyer {
    background: #bee3f8;
    color: #2b6cb0;
}

.role-staff {
    background: #e9d8fd;
    color: #6b46c1;
}

/* 状态徽章 */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.status-active {
    background: #c6f6d5;
    color: #2f855a;
}

.status-inactive {
    background: #fed7d7;
    color: #c53030;
}

/* 联系信息 */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 13px;
    color: #4a5568;
}

.contact-info i {
    width: 14px;
    margin-right: 6px;
    color: #718096;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 8px;
}

.action-buttons .btn {
    padding: 8px 14px;
    border-radius: 6px;
    font-size: 13px;
    min-width: 36px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.action-buttons .btn i {
    margin-right: 4px;
    font-size: 12px;
}

.action-buttons .btn span {
    font-size: 12px;
    font-weight: 500;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 48px;
    color: #cbd5e0;
    margin-bottom: 16px;
}

.empty-state h5 {
    font-size: 18px;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 8px;
}

.empty-state p {
    margin-bottom: 24px;
    color: #718096;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .search-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-field {
        min-width: auto;
    }
    
    .users-table-container {
        overflow-x: auto;
    }
    
    .users-table {
        min-width: 800px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

/* 页面头部美化 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e2e8f0;
}

.page-title h1 {
    font-size: 28px;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-title h1 i {
    color: #667eea;
}

.page-title p {
    color: #718096;
    margin: 0;
    font-size: 16px;
}

.page-actions .btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.page-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}
