<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-eye"></i>
                食材详情
            </h1>
            <div class="header-actions">
                <a href="?action=edit&id=<?= $ingredient['id'] ?>" class="btn btn-warning">
                    <i class="fas fa-edit"></i>
                    编辑食材
                </a>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <!-- 食材基本信息 -->
        <div class="info-card">
            <div class="card-header">
                <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
            </div>
            <div class="card-body">
                <div class="info-grid">
                    <div class="info-item">
                        <label>食材名称</label>
                        <div class="info-value"><?= htmlspecialchars($ingredient['name']) ?></div>
                    </div>
                    <div class="info-item">
                        <label>食材编码</label>
                        <div class="info-value"><?= htmlspecialchars($ingredient['code'] ?? '-') ?></div>
                    </div>
                    <div class="info-item">
                        <label>分类</label>
                        <div class="info-value">
                            <?php if ($ingredient['parent_category_name']): ?>
                                <?= htmlspecialchars($ingredient['parent_category_name']) ?> > 
                            <?php endif; ?>
                            <?= htmlspecialchars($ingredient['category_name'] ?? '未分类') ?>
                        </div>
                    </div>
                    <div class="info-item">
                        <label>计量单位</label>
                        <div class="info-value"><?= htmlspecialchars($ingredient['unit']) ?></div>
                    </div>
                    <div class="info-item">
                        <label>单价</label>
                        <div class="info-value price">¥<?= number_format($ingredient['unit_price'] ?? 0, 2) ?></div>
                    </div>
                    <div class="info-item">
                        <label>保质期</label>
                        <div class="info-value"><?= $ingredient['shelf_life_days'] ?? 30 ?> 天</div>
                    </div>
                    <div class="info-item">
                        <label>当前库存</label>
                        <div class="info-value stock <?= ($ingredient['current_stock'] ?? 0) <= ($ingredient['min_stock'] ?? 0) ? 'stock-low' : 'stock-normal' ?>">
                            <?= number_format($ingredient['current_stock'] ?? 0, 1) ?> <?= htmlspecialchars($ingredient['unit']) ?>
                        </div>
                    </div>
                    <div class="info-item">
                        <label>最低库存</label>
                        <div class="info-value"><?= number_format($ingredient['min_stock'] ?? 0, 1) ?> <?= htmlspecialchars($ingredient['unit']) ?></div>
                    </div>
                    <div class="info-item">
                        <label>状态</label>
                        <div class="info-value">
                            <span class="status-badge <?= $ingredient['status'] == 1 ? 'status-active' : 'status-inactive' ?>">
                                <?= $ingredient['status'] == 1 ? '启用' : '禁用' ?>
                            </span>
                        </div>
                    </div>
                    <div class="info-item">
                        <label>创建时间</label>
                        <div class="info-value"><?= date('Y-m-d H:i:s', strtotime($ingredient['created_at'])) ?></div>
                    </div>
                    <?php if (!empty($ingredient['description'])): ?>
                    <div class="info-item full-width">
                        <label>描述</label>
                        <div class="info-value"><?= nl2br(htmlspecialchars($ingredient['description'])) ?></div>
                    </div>
                    <?php endif; ?>
                    <?php if (!empty($ingredient['specification'])): ?>
                    <div class="info-item full-width">
                        <label>规格</label>
                        <div class="info-value"><?= htmlspecialchars($ingredient['specification']) ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- 库存记录 -->
        <?php if (!empty($stock_records)): ?>
        <div class="info-card">
            <div class="card-header">
                <h3><i class="fas fa-history"></i> 最近库存记录</h3>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>类型</th>
                                <th>数量</th>
                                <th>单价</th>
                                <th>批次号</th>
                                <th>过期日期</th>
                                <th>时间</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($stock_records as $record): ?>
                            <tr>
                                <td>
                                    <span class="type-badge <?= $record['type'] ?>">
                                        <?= $record['type'] == 'inbound' ? '入库' : '出库' ?>
                                    </span>
                                </td>
                                <td class="<?= $record['quantity'] > 0 ? 'text-success' : 'text-danger' ?>">
                                    <?= $record['quantity'] > 0 ? '+' : '' ?><?= number_format($record['quantity'], 1) ?>
                                </td>
                                <td><?= $record['unit_price'] > 0 ? '¥' . number_format($record['unit_price'], 2) : '-' ?></td>
                                <td><?= htmlspecialchars($record['batch_number'] ?? '-') ?></td>
                                <td><?= $record['expiry_date'] ? date('Y-m-d', strtotime($record['expiry_date'])) : '-' ?></td>
                                <td><?= date('m-d H:i', strtotime($record['created_at'])) ?></td>
                                <td><?= htmlspecialchars($record['notes'] ?? '-') ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- 快捷操作 -->
        <div class="info-card">
            <div class="card-header">
                <h3><i class="fas fa-tools"></i> 快捷操作</h3>
            </div>
            <div class="card-body">
                <div class="action-grid">
                    <a href="../inbound/create.php?ingredient_id=<?= $ingredient['id'] ?>" class="action-btn inbound">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>食材入库</span>
                    </a>
                    <a href="../outbound/index.php?action=create&ingredient_id=<?= $ingredient['id'] ?>" class="action-btn outbound">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>食材出库</span>
                    </a>
                    <a href="?action=edit&id=<?= $ingredient['id'] ?>" class="action-btn edit">
                        <i class="fas fa-edit"></i>
                        <span>编辑信息</span>
                    </a>
                    <a href="index.php" class="action-btn back">
                        <i class="fas fa-list"></i>
                        <span>返回列表</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 详情页面样式 */
.info-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    border-bottom: 1px solid #e2e8f0;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-body {
    padding: 20px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-item label {
    font-size: 12px;
    font-weight: 600;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 14px;
    color: #2d3748;
    font-weight: 500;
}

.info-value.price {
    color: #38a169;
    font-weight: 600;
    font-size: 16px;
}

.info-value.stock {
    font-weight: 600;
    font-size: 16px;
}

.stock-normal {
    color: #38a169;
}

.stock-low {
    color: #e53e3e;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background: #c6f6d5;
    color: #22543d;
}

.status-inactive {
    background: #fed7d7;
    color: #c53030;
}

.type-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.type-badge.inbound {
    background: #c6f6d5;
    color: #22543d;
}

.type-badge.outbound {
    background: #fed7d7;
    color: #c53030;
}

.text-success {
    color: #38a169 !important;
    font-weight: 600;
}

.text-danger {
    color: #e53e3e !important;
    font-weight: 600;
}

/* 快捷操作 */
.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px;
    border-radius: 12px;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    text-decoration: none;
}

.action-btn.inbound {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.action-btn.outbound {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
}

.action-btn.edit {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
}

.action-btn.back {
    background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
    color: white;
}

.action-btn i {
    font-size: 24px;
}

.action-btn span {
    font-size: 14px;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .action-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .card-body {
        padding: 15px;
    }
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
