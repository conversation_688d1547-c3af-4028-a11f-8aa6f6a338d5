<?php
/**
 * 测试导入页面与供应商管理页面布局一致性
 */

echo "=== 布局一致性测试 ===\n\n";

echo "1. 检查导入页面布局结构:\n";
if (file_exists('modules/purchase/import-template.php')) {
    $import_content = file_get_contents('modules/purchase/import-template.php');
    
    // 检查是否使用了标准的content类
    if (strpos($import_content, 'class="content"') !== false) {
        echo "   ✅ 使用标准的 .content 容器\n";
    } else {
        echo "   ❌ 未使用标准的 .content 容器\n";
    }
    
    // 检查是否移除了自定义容器
    if (strpos($import_content, 'import-container') === false) {
        echo "   ✅ 已移除自定义 import-container\n";
    } else {
        echo "   ❌ 仍然使用自定义 import-container\n";
    }
    
    // 检查布局结构
    if (strpos($import_content, 'upload-section') !== false && 
        strpos($import_content, 'info-section') !== false) {
        echo "   ✅ 包含上传区域和信息区域\n";
    } else {
        echo "   ❌ 缺少必要的区域\n";
    }
}

echo "\n2. 检查供应商管理页面布局:\n";
if (file_exists('modules/suppliers/template.php')) {
    $supplier_content = file_get_contents('modules/suppliers/template.php');
    
    // 检查供应商页面的布局结构
    if (strpos($supplier_content, 'class="content"') !== false) {
        echo "   ✅ 供应商页面使用 .content 容器\n";
    } else {
        echo "   ❌ 供应商页面未使用 .content 容器\n";
    }
    
    if (strpos($supplier_content, 'class="main-content"') !== false) {
        echo "   ✅ 供应商页面使用 .main-content 容器\n";
    } else {
        echo "   ❌ 供应商页面未使用 .main-content 容器\n";
    }
}

echo "\n3. 检查CSS样式一致性:\n";
if (file_exists('modules/purchase/style.css')) {
    $purchase_css = file_get_contents('modules/purchase/style.css');
    
    // 检查是否移除了自定义容器样式
    if (strpos($purchase_css, '.import-container') === false) {
        echo "   ✅ 已移除自定义容器样式\n";
    } else {
        echo "   ❌ 仍然包含自定义容器样式\n";
    }
    
    // 检查信息区域的横向布局
    if (strpos($purchase_css, 'flex-direction: row') !== false) {
        echo "   ✅ 信息区域设置为横向布局\n";
    } else {
        echo "   ❌ 信息区域未设置为横向布局\n";
    }
    
    // 检查卡片的flex设置
    if (strpos($purchase_css, 'flex: 1') !== false && 
        strpos($purchase_css, 'min-width: 280px') !== false) {
        echo "   ✅ 卡片设置为等宽分布，最小宽度280px\n";
    } else {
        echo "   ❌ 卡片布局设置不正确\n";
    }
}

echo "\n4. 检查通用样式文件:\n";
if (file_exists('includes/styles.css')) {
    $common_css = file_get_contents('includes/styles.css');
    
    // 检查content类的边距设置
    if (preg_match('/\.content\s*\{[^}]*padding:\s*30px[^}]*\}/', $common_css)) {
        echo "   ✅ .content 类设置了30px内边距\n";
    } else {
        echo "   ❌ .content 类内边距设置不正确\n";
    }
    
    // 检查main-content的边距
    if (strpos($common_css, 'margin-left: 260px') !== false) {
        echo "   ✅ .main-content 设置了260px左边距\n";
    } else {
        echo "   ❌ .main-content 左边距设置不正确\n";
    }
}

echo "\n5. 检查响应式设计:\n";
if (file_exists('modules/purchase/style.css')) {
    $purchase_css = file_get_contents('modules/purchase/style.css');
    
    // 检查移动端布局
    if (strpos($purchase_css, 'flex-direction: column') !== false) {
        echo "   ✅ 移动端设置为纵向布局\n";
    } else {
        echo "   ❌ 移动端布局设置不正确\n";
    }
    
    // 检查移动端最小宽度重置
    if (strpos($purchase_css, 'min-width: auto') !== false) {
        echo "   ✅ 移动端重置了最小宽度\n";
    } else {
        echo "   ❌ 移动端最小宽度未重置\n";
    }
}

echo "\n6. 布局对比分析:\n";
echo "   供应商管理页面结构:\n";
echo "     .main-content > .content > [页面内容]\n";
echo "   \n";
echo "   导入页面结构:\n";
echo "     .main-content > .content > .upload-section + .info-section\n";
echo "   \n";
echo "   边距继承:\n";
echo "     - 左边距: 260px (侧边栏宽度)\n";
echo "     - 内容边距: 30px (桌面端) / 20px (移动端)\n";
echo "     - 卡片间距: 1rem\n";

echo "\n7. 访问链接对比:\n";
echo "   供应商管理: http://localhost:8000/modules/suppliers/index.php\n";
echo "   导入页面: http://localhost:8000/modules/purchase/index.php?action=import\n";

echo "\n=== 布局一致性测试完成 ===\n";
echo "✨ 导入页面已与供应商管理页面保持一致的布局！\n";
echo "🎯 使用相同的 .content 容器和边距设置\n";
echo "📱 保持响应式设计和移动端适配\n";
echo "🔄 信息卡片横向排列，移动端自动换行\n";
?>
