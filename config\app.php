<?php
/**
 * 应用程序配置文件
 */

return [
    'name' => '学校食堂管理系统',
    'version' => '1.0.0',
    'timezone' => 'Asia/Shanghai',
    'locale' => 'zh-CN',
    'debug' => true,
    
    // 路径配置
    'paths' => [
        'root' => dirname(__DIR__),
        'modules' => dirname(__DIR__) . '/modules',
        'includes' => dirname(__DIR__) . '/includes',
        'assets' => dirname(__DIR__) . '/assets',
        'uploads' => dirname(__DIR__) . '/uploads',
    ],
    
    // URL配置
    'url' => [
        'base' => 'http://localhost:8080',
        'assets' => 'http://localhost:8080/assets',
    ],
    
    // 分页配置
    'pagination' => [
        'per_page' => 20,
        'max_per_page' => 100,
    ],
    
    // 文件上传配置
    'upload' => [
        'max_size' => 10 * 1024 * 1024, // 10MB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'xlsx', 'xls', 'csv'],
        'path' => 'uploads/',
    ],
    
    // 模块配置
    'modules' => [
        'dashboard' => [
            'name' => '仪表板',
            'icon' => 'fas fa-tachometer-alt',
            'enabled' => true,
        ],
        'ingredients' => [
            'name' => '食材管理',
            'icon' => 'fas fa-carrot',
            'enabled' => true,
        ],
        'categories' => [
            'name' => '食材分类',
            'icon' => 'fas fa-tags',
            'enabled' => true,
        ],
        'suppliers' => [
            'name' => '供应商管理',
            'icon' => 'fas fa-truck',
            'enabled' => true,
        ],
        'purchase' => [
            'name' => '采购管理',
            'icon' => 'fas fa-shopping-cart',
            'enabled' => true,
        ],
        'inbound' => [
            'name' => '食材入库',
            'icon' => 'fas fa-arrow-down',
            'enabled' => true,
        ],
        'outbound' => [
            'name' => '食材出库',
            'icon' => 'fas fa-arrow-up',
            'enabled' => true,
        ],
        'inventory' => [
            'name' => '库存查询',
            'icon' => 'fas fa-search',
            'enabled' => true,
        ],
        'stocktaking' => [
            'name' => '食材盘点',
            'icon' => 'fas fa-clipboard-check',
            'enabled' => true,
        ],
        'damage' => [
            'name' => '食材报损',
            'icon' => 'fas fa-exclamation-triangle',
            'enabled' => true,
        ],
        'users' => [
            'name' => '人员管理',
            'icon' => 'fas fa-users',
            'enabled' => true,
        ],
        'reports' => [
            'name' => '报表分析',
            'icon' => 'fas fa-chart-bar',
            'enabled' => true,
        ],
        'settings' => [
            'name' => '系统设置',
            'icon' => 'fas fa-cog',
            'enabled' => true,
        ],
    ],
];
?>
