<?php
/**
 * 初始化测试数据脚本
 */

// 数据库配置
$host = '************';
$port = 3306;
$dbname = 'sc';
$username = 'sc';
$password = 'pw5K4SsM7kZsjdxy';

try {
    // 连接数据库
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "数据库连接成功！\n";
    
    // 创建基础表（如果不存在）
    createTables($pdo);
    
    // 插入测试数据
    insertTestData($pdo);
    
    echo "测试数据初始化完成！\n";
    
} catch (PDOException $e) {
    echo "数据库错误: " . $e->getMessage() . "\n";
}

/**
 * 创建基础表
 */
function createTables($pdo) {
    $tables = [
        // 食材分类表
        "CREATE TABLE IF NOT EXISTS ingredient_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            status TINYINT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // 食材表
        "CREATE TABLE IF NOT EXISTS ingredients (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            category_id INT,
            unit VARCHAR(20) NOT NULL,
            current_stock DECIMAL(10,2) DEFAULT 0,
            min_stock DECIMAL(10,2) DEFAULT 0,
            unit_price DECIMAL(10,2) DEFAULT 0,
            status TINYINT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES ingredient_categories(id)
        )",
        
        // 供应商表
        "CREATE TABLE IF NOT EXISTS suppliers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            contact_person VARCHAR(50),
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            status TINYINT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // 采购订单表
        "CREATE TABLE IF NOT EXISTS purchase_orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_number VARCHAR(50) UNIQUE NOT NULL,
            supplier_id INT NOT NULL,
            order_date DATE NOT NULL,
            expected_delivery_date DATE,
            total_amount DECIMAL(12,2) DEFAULT 0,
            status TINYINT DEFAULT 1 COMMENT '1:待确认 2:已确认 3:部分到货 4:已完成 5:已取消',
            notes TEXT,
            created_by INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
        )",
        
        // 采购订单明细表
        "CREATE TABLE IF NOT EXISTS purchase_order_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id INT NOT NULL,
            ingredient_id INT NOT NULL,
            quantity DECIMAL(10,2) NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(12,2) NOT NULL,
            received_quantity DECIMAL(10,2) DEFAULT 0,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE,
            FOREIGN KEY (ingredient_id) REFERENCES ingredients(id)
        )",
        
        // 入库记录表
        "CREATE TABLE IF NOT EXISTS inbound_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ingredient_id INT NOT NULL,
            quantity DECIMAL(10,2) NOT NULL,
            unit_price DECIMAL(10,2),
            total_amount DECIMAL(12,2),
            supplier_id INT,
            batch_number VARCHAR(50),
            production_date DATE,
            expiry_date DATE,
            notes TEXT,
            created_by INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (ingredient_id) REFERENCES ingredients(id),
            FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
        )"
    ];
    
    foreach ($tables as $sql) {
        $pdo->exec($sql);
        echo "表创建成功\n";
    }
}

/**
 * 插入测试数据
 */
function insertTestData($pdo) {
    // 清空现有数据
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    $pdo->exec("TRUNCATE TABLE purchase_order_items");
    $pdo->exec("TRUNCATE TABLE purchase_orders");
    $pdo->exec("TRUNCATE TABLE inbound_records");
    $pdo->exec("TRUNCATE TABLE ingredients");
    $pdo->exec("TRUNCATE TABLE ingredient_categories");
    $pdo->exec("TRUNCATE TABLE suppliers");
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    // 插入食材分类
    $categories = [
        ['蔬菜类', '新鲜蔬菜及蔬菜制品'],
        ['肉类', '猪肉、牛肉、鸡肉等'],
        ['水产类', '鱼类、虾类、蟹类等'],
        ['粮油类', '大米、面粉、食用油等'],
        ['调料类', '盐、糖、酱油、醋等'],
        ['豆制品', '豆腐、豆干、豆皮等']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO ingredient_categories (name, description) VALUES (?, ?)");
    foreach ($categories as $category) {
        $stmt->execute($category);
    }
    
    // 插入食材
    $ingredients = [
        ['白菜', 1, '斤', 100, 20, 2.5],
        ['土豆', 1, '斤', 80, 15, 3.0],
        ['猪肉', 2, '斤', 50, 10, 25.0],
        ['鸡蛋', 2, '斤', 30, 5, 8.0],
        ['带鱼', 3, '斤', 20, 5, 18.0],
        ['大米', 4, '斤', 200, 50, 4.5],
        ['食用油', 4, '桶', 10, 2, 45.0],
        ['豆腐', 6, '块', 40, 10, 3.5]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO ingredients (name, category_id, unit, current_stock, min_stock, unit_price) VALUES (?, ?, ?, ?, ?, ?)");
    foreach ($ingredients as $ingredient) {
        $stmt->execute($ingredient);
    }
    
    // 插入供应商
    $suppliers = [
        ['绿色蔬菜供应商', '张三', '13800138001', '<EMAIL>', '北京市朝阳区'],
        ['优质肉类供应商', '李四', '13800138002', '<EMAIL>', '北京市海淀区'],
        ['新鲜水产供应商', '王五', '13800138003', '<EMAIL>', '北京市西城区'],
        ['粮油批发商', '赵六', '13800138004', '<EMAIL>', '北京市东城区']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO suppliers (name, contact_person, phone, email, address) VALUES (?, ?, ?, ?, ?)");
    foreach ($suppliers as $supplier) {
        $stmt->execute($supplier);
    }
    
    // 插入采购订单
    $orders = [
        ['PO20241201001', 1, '2024-12-01', '2024-12-03', 500.00, 2],
        ['PO20241202001', 2, '2024-12-02', '2024-12-04', 800.00, 1],
        ['PO20241203001', 3, '2024-12-03', '2024-12-05', 360.00, 3],
        ['PO20241204001', 4, '2024-12-04', '2024-12-06', 450.00, 1]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO purchase_orders (order_number, supplier_id, order_date, expected_delivery_date, total_amount, status) VALUES (?, ?, ?, ?, ?, ?)");
    foreach ($orders as $order) {
        $stmt->execute($order);
    }
    
    // 插入采购订单明细
    $orderItems = [
        [1, 1, 100, 2.5, 250.00, 0],
        [1, 2, 50, 3.0, 150.00, 0],
        [1, 8, 25, 3.5, 87.50, 0],
        [2, 3, 20, 25.0, 500.00, 0],
        [2, 4, 30, 8.0, 240.00, 0],
        [3, 5, 20, 18.0, 360.00, 0],
        [4, 6, 100, 4.5, 450.00, 0]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO purchase_order_items (order_id, ingredient_id, quantity, unit_price, total_price, received_quantity) VALUES (?, ?, ?, ?, ?, ?)");
    foreach ($orderItems as $item) {
        $stmt->execute($item);
    }
    
    // 插入入库记录
    $inboundRecords = [
        [1, 50, 2.5, 125.00, 1, 'BATCH001', '2024-12-01', '2024-12-15'],
        [2, 30, 3.0, 90.00, 1, 'BATCH002', '2024-12-01', '2024-12-20'],
        [3, 15, 25.0, 375.00, 2, 'BATCH003', '2024-12-02', '2024-12-10'],
        [4, 20, 8.0, 160.00, 2, 'BATCH004', '2024-12-02', '2024-12-12']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO inbound_records (ingredient_id, quantity, unit_price, total_amount, supplier_id, batch_number, production_date, expiry_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    foreach ($inboundRecords as $record) {
        $stmt->execute($record);
    }
    
    echo "测试数据插入完成！\n";
}
?>
