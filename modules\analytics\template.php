<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<!-- 确保样式被加载 -->
<link rel="stylesheet" href="style.css">

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <div class="header-left">
                <h1>
                    <i class="fas fa-chart-line"></i>
                    趋势分析
                </h1>
                <p class="header-subtitle">深度数据分析与趋势预测</p>
            </div>
            <div class="header-actions">
                <div class="time-range-selector">
                    <select class="form-control" onchange="changeTimeRange(this.value)">
                        <option value="7d" <?= $data['time_range'] === '7d' ? 'selected' : '' ?>>最近7天</option>
                        <option value="30d" <?= $data['time_range'] === '30d' ? 'selected' : '' ?>>最近30天</option>
                        <option value="90d" <?= $data['time_range'] === '90d' ? 'selected' : '' ?>>最近90天</option>
                        <option value="1y" <?= $data['time_range'] === '1y' ? 'selected' : '' ?>>最近1年</option>
                    </select>
                </div>
                <button type="button" class="btn btn-success" onclick="exportAnalytics()">
                    <i class="fas fa-download"></i>
                    导出分析
                </button>
            </div>
        </div>

        <!-- 指标选择 -->
        <div class="metric-tabs">
            <a href="?metric=purchase&range=<?= $data['time_range'] ?>" class="metric-tab <?= $data['current_metric'] === 'purchase' ? 'active' : '' ?>">
                <i class="fas fa-shopping-cart"></i>
                <span>采购趋势</span>
            </a>
            <a href="?metric=inventory&range=<?= $data['time_range'] ?>" class="metric-tab <?= $data['current_metric'] === 'inventory' ? 'active' : '' ?>">
                <i class="fas fa-warehouse"></i>
                <span>库存趋势</span>
            </a>
            <a href="?metric=cost&range=<?= $data['time_range'] ?>" class="metric-tab <?= $data['current_metric'] === 'cost' ? 'active' : '' ?>">
                <i class="fas fa-dollar-sign"></i>
                <span>成本趋势</span>
            </a>
            <a href="?metric=supplier&range=<?= $data['time_range'] ?>" class="metric-tab <?= $data['current_metric'] === 'supplier' ? 'active' : '' ?>">
                <i class="fas fa-truck"></i>
                <span>供应商趋势</span>
            </a>
        </div>

        <!-- 趋势分析内容 -->
        <div class="analytics-content">
            <?php if ($data['current_metric'] === 'purchase'): ?>
                <?php include 'views/purchase-trends.php'; ?>
            <?php elseif ($data['current_metric'] === 'inventory'): ?>
                <?php include 'views/inventory-trends.php'; ?>
            <?php elseif ($data['current_metric'] === 'cost'): ?>
                <?php include 'views/cost-trends.php'; ?>
            <?php elseif ($data['current_metric'] === 'supplier'): ?>
                <?php include 'views/supplier-trends.php'; ?>
            <?php endif; ?>
        </div>

        <!-- 图表概览 -->
        <div class="charts-overview">
            <div class="chart-grid">
                <!-- 主要趋势图 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-chart-line"></i>
                            主要趋势分析
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="mainTrendChart"></canvas>
                    </div>
                </div>

                <!-- 分布饼图 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-chart-pie"></i>
                            数据分布
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="distributionChart"></canvas>
                    </div>
                </div>

                <!-- 对比柱状图 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-chart-bar"></i>
                            对比分析
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="comparisonChart"></canvas>
                    </div>
                </div>

                <!-- 预测图表 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-crystal-ball"></i>
                            趋势预测
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="predictionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预测与洞察 -->
        <div class="insights-section">
            <div class="row">
                <div class="col-md-6">
                    <div class="insight-card">
                        <div class="insight-header">
                            <h3>
                                <i class="fas fa-crystal-ball"></i>
                                趋势预测
                            </h3>
                        </div>
                        <div class="insight-content">
                            <div class="prediction-item">
                                <div class="prediction-label">下月预测采购额</div>
                                <div class="prediction-value">¥<?= number_format($data['predictions']['next_month_purchase']) ?></div>
                                <div class="prediction-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    +<?= $data['predictions']['predicted_growth'] ?>%
                                </div>
                            </div>
                            <div class="confidence-meter">
                                <div class="confidence-label">预测置信度</div>
                                <div class="confidence-bar">
                                    <div class="confidence-fill" style="width: <?= $data['predictions']['confidence_level'] ?>%"></div>
                                </div>
                                <div class="confidence-value"><?= $data['predictions']['confidence_level'] ?>%</div>
                            </div>
                            <div class="risk-factors">
                                <h5>风险因素</h5>
                                <ul>
                                    <?php foreach ($data['predictions']['risk_factors'] as $risk): ?>
                                    <li><?= htmlspecialchars($risk) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="insight-card">
                        <div class="insight-header">
                            <h3>
                                <i class="fas fa-lightbulb"></i>
                                智能洞察
                            </h3>
                        </div>
                        <div class="insight-content">
                            <div class="findings-section">
                                <h5>关键发现</h5>
                                <ul class="findings-list">
                                    <?php foreach ($data['insights']['key_findings'] as $finding): ?>
                                    <li>
                                        <i class="fas fa-check-circle"></i>
                                        <?= htmlspecialchars($finding) ?>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            
                            <div class="recommendations-section">
                                <h5>优化建议</h5>
                                <ul class="recommendations-list">
                                    <?php foreach ($data['insights']['recommendations'] as $recommendation): ?>
                                    <li>
                                        <i class="fas fa-lightbulb"></i>
                                        <?= htmlspecialchars($recommendation) ?>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            
                            <?php if (!empty($data['insights']['alerts'])): ?>
                            <div class="alerts-section">
                                <h5>注意事项</h5>
                                <ul class="alerts-list">
                                    <?php foreach ($data['insights']['alerts'] as $alert): ?>
                                    <li>
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <?= htmlspecialchars($alert) ?>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 对比分析 -->
        <div class="comparison-section">
            <div class="comparison-card">
                <div class="comparison-header">
                    <h3>
                        <i class="fas fa-balance-scale"></i>
                        对比分析
                    </h3>
                </div>
                <div class="comparison-content">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>同比增长 (年度对比)</h5>
                            <div class="comparison-metrics">
                                <div class="metric-item">
                                    <span class="metric-label">采购增长</span>
                                    <span class="metric-value positive">+<?= $data['comparisons']['year_over_year']['purchase_growth'] ?>%</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">成本降低</span>
                                    <span class="metric-value positive">-<?= $data['comparisons']['year_over_year']['cost_reduction'] ?>%</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">供应商改善</span>
                                    <span class="metric-value positive">+<?= $data['comparisons']['year_over_year']['supplier_improvement'] ?>%</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>环比变化 (月度对比)</h5>
                            <div class="comparison-metrics">
                                <div class="metric-item">
                                    <span class="metric-label">采购变化</span>
                                    <span class="metric-value positive">+<?= $data['comparisons']['month_over_month']['purchase_change'] ?>%</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">库存变化</span>
                                    <span class="metric-value negative"><?= $data['comparisons']['month_over_month']['inventory_change'] ?>%</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">效率提升</span>
                                    <span class="metric-value positive">+<?= $data['comparisons']['month_over_month']['efficiency_change'] ?>%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js 库 -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="script.js"></script>

<script>
function changeTimeRange(range) {
    const currentMetric = '<?= $data['current_metric'] ?>';
    window.location.href = `?metric=${currentMetric}&range=${range}`;
}

function exportAnalytics() {
    alert('导出功能开发中...');
}

// 传递数据到JavaScript
const analyticsData = <?= json_encode($data['trends']) ?>;
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
