<?php
/**
 * 分类导入功能调试测试
 */

echo "=== 分类导入功能调试测试 ===\n\n";

echo "1. 检查控制器路由:\n";
if (file_exists('modules/categories/CategoriesController.php')) {
    $controller_content = file_get_contents('modules/categories/CategoriesController.php');
    
    // 检查路由处理
    echo "   路由处理检查:\n";
    if (strpos($controller_content, "case 'import':") !== false) {
        echo "     ✅ import路由已添加\n";
    } else {
        echo "     ❌ import路由缺失\n";
    }
    
    if (strpos($controller_content, 'return $this->import()') !== false) {
        echo "     ✅ import方法调用正确\n";
    } else {
        echo "     ❌ import方法调用缺失\n";
    }
    
    // 检查import方法
    echo "   import方法检查:\n";
    if (strpos($controller_content, 'private function import()') !== false) {
        echo "     ✅ import方法已定义\n";
    } else {
        echo "     ❌ import方法未定义\n";
    }
    
    if (strpos($controller_content, "render('import-template.php')") !== false) {
        echo "     ✅ 渲染import-template.php\n";
    } else {
        echo "     ❌ 模板渲染不正确\n";
    }
    
} else {
    echo "   ❌ 控制器文件不存在\n";
}

echo "\n2. 检查导入模板:\n";
if (file_exists('modules/categories/import-template.php')) {
    $template_content = file_get_contents('modules/categories/import-template.php');
    
    // 检查模板结构
    echo "   模板结构检查:\n";
    if (strpos($template_content, 'require_once \'../../includes/header.php\'') !== false) {
        echo "     ✅ 包含头部文件\n";
    } else {
        echo "     ❌ 缺少头部文件\n";
    }
    
    if (strpos($template_content, 'class="main-content"') !== false) {
        echo "     ✅ 使用标准页面布局\n";
    } else {
        echo "     ❌ 页面布局不正确\n";
    }
    
    if (strpos($template_content, 'sidebar.php') !== false) {
        echo "     ✅ 包含侧边栏\n";
    } else {
        echo "     ❌ 缺少侧边栏\n";
    }
    
    // 检查文件选择功能
    echo "   文件选择功能检查:\n";
    if (strpos($template_content, 'type="file"') !== false) {
        echo "     ✅ 包含文件输入\n";
    } else {
        echo "     ❌ 缺少文件输入\n";
    }
    
    if (strpos($template_content, 'triggerFileSelect') !== false) {
        echo "     ✅ 包含文件选择函数\n";
    } else {
        echo "     ❌ 缺少文件选择函数\n";
    }
    
    // 检查JavaScript
    echo "   JavaScript检查:\n";
    if (strpos($template_content, '<script>') !== false) {
        echo "     ✅ 包含JavaScript代码\n";
    } else {
        echo "     ❌ 缺少JavaScript代码\n";
    }
    
} else {
    echo "   ❌ 导入模板文件不存在\n";
}

echo "\n3. 检查主模板按钮:\n";
if (file_exists('modules/categories/template.php')) {
    $main_template = file_get_contents('modules/categories/template.php');
    
    // 检查导入按钮
    echo "   导入按钮检查:\n";
    if (strpos($main_template, 'href="index.php?action=import"') !== false) {
        echo "     ✅ 导入按钮链接正确\n";
    } else {
        echo "     ❌ 导入按钮链接不正确\n";
    }
    
    if (strpos($main_template, '导入分类') !== false) {
        echo "     ✅ 导入按钮文本正确\n";
    } else {
        echo "     ❌ 导入按钮文本不正确\n";
    }
    
} else {
    echo "   ❌ 主模板文件不存在\n";
}

echo "\n4. 检查必要文件:\n";
$required_files = [
    'modules/categories/index.php' => '入口文件',
    'modules/categories/CategoriesController.php' => '控制器',
    'modules/categories/template.php' => '主模板',
    'modules/categories/import-template.php' => '导入模板',
    'modules/categories/sidebar.php' => '侧边栏',
    'modules/categories/style.css' => '样式文件'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ {$description}: {$file}\n";
    } else {
        echo "   ❌ {$description}: {$file} - 文件不存在\n";
    }
}

echo "\n5. 模拟请求测试:\n";
echo "   测试URL: http://localhost:8000/modules/categories/index.php?action=import\n";
echo "   预期行为:\n";
echo "     1. 控制器接收action=import参数\n";
echo "     2. 调用import()方法\n";
echo "     3. 渲染import-template.php\n";
echo "     4. 显示导入页面\n";

echo "\n6. 可能的问题:\n";
echo "   常见问题:\n";
echo "     • 控制器路由未正确配置\n";
echo "     • 模板文件路径错误\n";
echo "     • JavaScript错误导致页面异常\n";
echo "     • CSS样式冲突\n";
echo "     • 文件权限问题\n";

echo "\n7. 调试步骤:\n";
echo "   1. 检查浏览器开发者工具控制台\n";
echo "   2. 查看网络请求是否正常\n";
echo "   3. 确认URL参数是否正确传递\n";
echo "   4. 检查服务器错误日志\n";
echo "   5. 验证文件路径和权限\n";

echo "\n8. 快速修复建议:\n";
echo "   如果导入页面无法正常显示:\n";
echo "     • 清除浏览器缓存\n";
echo "     • 检查控制器中的import方法\n";
echo "     • 确认模板文件存在且可读\n";
echo "     • 验证JavaScript语法\n";

echo "\n=== 分类导入功能调试测试完成 ===\n";

// 尝试直接访问模板文件
echo "\n9. 直接访问测试:\n";
echo "   可以尝试直接访问模板文件进行测试:\n";
echo "   http://localhost:8000/modules/categories/import-template.php\n";
echo "   (注意：直接访问可能缺少控制器设置的变量)\n";

// 检查当前工作目录和文件路径
echo "\n10. 文件路径检查:\n";
$current_dir = getcwd();
echo "   当前工作目录: {$current_dir}\n";

$categories_dir = 'modules/categories';
if (is_dir($categories_dir)) {
    echo "   分类模块目录存在\n";
    $files = scandir($categories_dir);
    echo "   目录内容:\n";
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            echo "     • {$file}\n";
        }
    }
} else {
    echo "   ❌ 分类模块目录不存在\n";
}
?>
