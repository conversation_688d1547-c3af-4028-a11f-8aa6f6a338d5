<?php
/**
 * 测试简化后的采购单批量导入功能
 */

echo "=== 简化版采购单批量导入测试 ===\n\n";

// 检查简化后的文件
$files_to_check = [
    'modules/purchase/import-template.php',
    'modules/purchase/style.css',
    'modules/purchase/download_template.php'
];

echo "1. 检查简化后的文件:\n";
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "   ✅ {$file} ({$size} 字节)\n";
    } else {
        echo "   ❌ {$file} - 文件不存在\n";
    }
}

echo "\n2. 检查模板文件结构:\n";
if (file_exists('modules/purchase/import-template.php')) {
    $template_content = file_get_contents('modules/purchase/import-template.php');
    $template_lines = count(explode("\n", $template_content));
    echo "   模板文件行数: {$template_lines}\n";
    
    // 检查简化后的关键元素
    $key_elements = [
        'import-container' => '主容器布局',
        'upload-section' => '上传区域',
        'info-section' => '信息区域',
        'upload-area' => '拖拽上传',
        'file-info' => '文件信息',
        'info-card' => '信息卡片'
    ];
    
    echo "   关键布局元素:\n";
    foreach ($key_elements as $element => $description) {
        if (strpos($template_content, $element) !== false) {
            echo "     ✅ {$description}\n";
        } else {
            echo "     ❌ {$description} - 未找到\n";
        }
    }
}

echo "\n3. 检查CSS样式简化:\n";
if (file_exists('modules/purchase/style.css')) {
    $css_content = file_get_contents('modules/purchase/style.css');
    $css_lines = count(explode("\n", $css_content));
    echo "   CSS文件行数: {$css_lines}\n";
    
    // 检查简化后的样式
    $key_styles = [
        '.import-container' => '主容器样式',
        '.upload-section' => '上传区域样式',
        '.info-section' => '信息区域样式',
        '.upload-area' => '拖拽区域样式',
        '.info-card' => '信息卡片样式',
        '@media (max-width: 768px)' => '响应式设计'
    ];
    
    echo "   关键样式类:\n";
    foreach ($key_styles as $style => $description) {
        if (strpos($css_content, $style) !== false) {
            echo "     ✅ {$description}\n";
        } else {
            echo "     ❌ {$description} - 未找到\n";
        }
    }
}

echo "\n4. 检查JavaScript简化:\n";
if (file_exists('modules/purchase/import-template.php')) {
    $template_content = file_get_contents('modules/purchase/import-template.php');
    
    // 检查是否移除了复杂的功能
    $removed_features = [
        'nextStep' => '步骤导航',
        'showTab' => '标签页切换',
        'wizard-steps' => '向导步骤',
        'step-content' => '步骤内容'
    ];
    
    echo "   已移除的复杂功能:\n";
    foreach ($removed_features as $feature => $description) {
        if (strpos($template_content, $feature) === false) {
            echo "     ✅ {$description} - 已移除\n";
        } else {
            echo "     ❌ {$description} - 仍然存在\n";
        }
    }
    
    // 检查保留的核心功能
    $core_features = [
        'handleFileSelect' => '文件选择处理',
        'validateFile' => '文件验证',
        'dragover' => '拖拽功能'
    ];
    
    echo "   保留的核心功能:\n";
    foreach ($core_features as $feature => $description) {
        if (strpos($template_content, $feature) !== false) {
            echo "     ✅ {$description}\n";
        } else {
            echo "     ❌ {$description} - 未找到\n";
        }
    }
}

echo "\n5. 布局特性检查:\n";
if (file_exists('modules/purchase/style.css')) {
    $css_content = file_get_contents('modules/purchase/style.css');
    
    // 检查左右分栏布局
    if (strpos($css_content, 'grid-template-columns: 1fr 400px') !== false) {
        echo "   ✅ 左右分栏布局 (1fr 400px)\n";
    } else {
        echo "   ❌ 左右分栏布局 - 未找到\n";
    }
    
    // 检查边距设置
    if (strpos($css_content, 'padding: 0 1rem') !== false) {
        echo "   ✅ 左右边距设置\n";
    } else {
        echo "   ❌ 左右边距设置 - 未找到\n";
    }
    
    // 检查最大宽度
    if (strpos($css_content, 'max-width: 1200px') !== false) {
        echo "   ✅ 最大宽度限制\n";
    } else {
        echo "   ❌ 最大宽度限制 - 未找到\n";
    }
}

echo "\n6. 文件大小对比:\n";
$template_size = file_exists('modules/purchase/import-template.php') ? filesize('modules/purchase/import-template.php') : 0;
$css_size = file_exists('modules/purchase/style.css') ? filesize('modules/purchase/style.css') : 0;

echo "   模板文件: " . number_format($template_size) . " 字节\n";
echo "   CSS文件: " . number_format($css_size) . " 字节\n";
echo "   总大小: " . number_format($template_size + $css_size) . " 字节\n";

echo "\n7. 访问链接:\n";
echo "   简化后的导入页面: http://localhost:8000/modules/purchase/index.php?action=import\n";
echo "   模板下载: http://localhost:8000/modules/purchase/download_template.php\n";

echo "\n=== 简化测试完成 ===\n";
echo "✨ 采购管理批量导入页面已成功简化！\n";
echo "🎯 采用左右分栏布局，操作更加直观\n";
echo "📱 保持响应式设计，适配各种设备\n";
echo "🚀 移除复杂功能，提升用户体验\n";
?>
