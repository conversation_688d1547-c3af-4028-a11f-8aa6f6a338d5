<?php
/**
 * 移动端食材入库页面
 */
require_once '../includes/Database.php';
require_once '../modules/inbound/InboundController.php';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 设置 action 参数为 create
    $_GET['action'] = 'create';
    $controller = new InboundController();
    $result = $controller->handleRequest();
    
    // 如果是 AJAX 请求，返回 JSON 响应
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
        strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => '入库成功']);
    }
    exit;
}

// 获取基础数据
try {
    $db = Database::getInstance();

    // 获取供应商
    $suppliers = $db->fetchAll("
        SELECT id, name, contact_person
        FROM suppliers
        WHERE status = 1
        ORDER BY name ASC
    ");

    // 获取食材（包含分类信息）
    $ingredients = $db->fetchAll("
        SELECT i.id, i.name, i.unit, i.category_id,
               ic.name as category_name, ic.parent_id,
               CASE WHEN ic.parent_id IS NOT NULL THEN ic.id ELSE NULL END as subcategory_id,
               CASE WHEN ic.parent_id IS NOT NULL THEN ic.name ELSE NULL END as subcategory_name,
               CASE WHEN ic.parent_id IS NOT NULL THEN pic.id ELSE ic.id END as primary_category_id,
               CASE WHEN ic.parent_id IS NOT NULL THEN pic.name ELSE ic.name END as primary_category_name
        FROM ingredients i
        LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
        LEFT JOIN ingredient_categories pic ON ic.parent_id = pic.id
        WHERE i.status = 1
        ORDER BY primary_category_name, subcategory_name, i.name ASC
    ");

    // 获取一级分类列表
    $primary_categories = $db->fetchAll("
        SELECT id, name
        FROM ingredient_categories
        WHERE (parent_id IS NULL OR parent_id = 0) AND status = 1
        ORDER BY name ASC
    ");

    // 获取二级分类列表
    $secondary_categories = $db->fetchAll("
        SELECT id, name, parent_id
        FROM ingredient_categories
        WHERE parent_id IS NOT NULL AND parent_id > 0 AND status = 1
        ORDER BY parent_id, name ASC
    ");

    // 获取待入库的采购单
    $purchase_orders = $db->fetchAll("
        SELECT
            po.id,
            po.order_number,
            po.supplier_id,
            po.order_date,
            po.order_amount,
            po.notes,
            s.name as supplier_name,
            poi.id as item_id,
            poi.ingredient_id,
            poi.quantity,
            poi.unit_price,
            poi.total_price,
            poi.notes as item_notes,
            i.name as ingredient_name,
            i.unit as ingredient_unit
        FROM purchase_orders po
        LEFT JOIN suppliers s ON po.supplier_id = s.id
        LEFT JOIN purchase_order_items poi ON po.id = poi.order_id
        LEFT JOIN ingredients i ON poi.ingredient_id = i.id
        WHERE po.status IN (1, 2)
        ORDER BY po.created_at DESC, poi.id ASC
    ");

} catch (Exception $e) {
    $suppliers = [];
    $ingredients = [];
    $purchase_orders = [];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>食材入库 - 学校食堂食材出入库管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
    <link href="inbound.css" rel="stylesheet">

    <style>
        /* 修复select控件文字显示问题 */
        select.form-control {
            height: 48px !important;
            line-height: 1.4 !important;
            padding: 12px 40px 12px 16px !important;
            font-size: 16px !important;
            display: flex !important;
            align-items: center !important;
            vertical-align: middle !important;
            box-sizing: border-box !important;
        }

        select.form-control option {
            padding: 12px 16px !important;
            line-height: 1.4 !important;
            height: auto !important;
            min-height: 40px !important;
            display: block !important;
            font-size: 16px !important;
        }



        /* 确保在所有设备上的一致性 */
        @media screen and (max-width: 768px) {
            select.form-control {
                font-size: 16px !important; /* 防止iOS缩放 */
                -webkit-appearance: none !important;
                -moz-appearance: none !important;
                appearance: none !important;
            }
        }

        /* 修复iOS Safari的select样式 */
        @supports (-webkit-appearance: none) {
            select.form-control {
                -webkit-appearance: none !important;
                background-color: white !important;
            }
        }

        /* 强制卡片头部文字为纯白色 */
        .card-header h2,
        .card-header p {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
        }

        .card-header h2 i {
            color: #ffffff !important;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header" id="inboundHeader">
        <div class="header-content">
            <a href="index.php" class="back-btn touch-feedback">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div class="header-title">
                <i class="fas fa-box"></i>
                <span>食材入库</span>
            </div>
            <div class="header-actions">
                <button class="help-btn touch-feedback" onclick="showHelp()">
                    <i class="fas fa-question-circle"></i>
                </button>
                <button class="scan-btn touch-feedback" onclick="startScan()" style="display: none;">
                    <i class="fas fa-qrcode"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 进度指示器 -->
    <div class="progress-indicator">
        <div class="progress-step active" data-step="1">
            <div class="step-number">1</div>
            <div class="step-label">基本信息</div>
        </div>
        <div class="progress-line">
            <div class="progress-fill"></div>
        </div>
        <div class="progress-step" data-step="2">
            <div class="step-number">2</div>
            <div class="step-label">添加食材</div>
        </div>
        <div class="progress-line">
            <div class="progress-fill"></div>
        </div>
        <div class="progress-step" data-step="3">
            <div class="step-number">3</div>
            <div class="step-label">拍照确认</div>
        </div>
    </div>

    <!-- 主要内容 -->
    <main class="mobile-main">
        <form id="inboundForm" method="POST" enctype="multipart/form-data">
            <!-- 步骤1：基本信息 -->
            <section class="step-section active" id="step1">
                <div class="step-card">
                    <div class="card-header">
                        <h2><i class="fas fa-info-circle"></i> 基本信息</h2>
                        <p>请选择采购单或手动填写入库信息</p>
                    </div>

                    <!-- 采购单选择 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-file-invoice"></i>
                            选择采购单
                        </label>
                        <select id="purchase_order_select" class="form-control">
                            <option value="">请选择采购单</option>
                            <?php if (!empty($purchase_orders)): ?>
                                <?php
                                $grouped_orders = [];
                                foreach ($purchase_orders as $order) {
                                    $key = $order['id'] . '_' . $order['order_number'];
                                    if (!isset($grouped_orders[$key])) {
                                        // 清理数据，移除可能导致JSON问题的字段
                                        $clean_order = [
                                            'id' => $order['id'],
                                            'order_number' => $order['order_number'],
                                            'supplier_id' => $order['supplier_id'],
                                            'supplier_name' => $order['supplier_name'],
                                            'order_date' => $order['order_date'],
                                            'order_amount' => $order['order_amount'],
                                            'notes' => $order['notes'] ?? ''
                                        ];

                                        $grouped_orders[$key] = [
                                            'order_info' => $clean_order,
                                            'items' => []
                                        ];
                                    }
                                    if ($order['item_id']) {
                                        // 清理商品数据
                                        $clean_item = [
                                            'id' => $order['item_id'],
                                            'ingredient_id' => $order['ingredient_id'],
                                            'ingredient_name' => $order['ingredient_name'],
                                            'ingredient_unit' => $order['ingredient_unit'],
                                            'quantity' => $order['quantity'],
                                            'unit_price' => $order['unit_price'],
                                            'total_price' => $order['total_price'],
                                            'notes' => $order['item_notes'] ?? ''
                                        ];
                                        $grouped_orders[$key]['items'][] = $clean_item;
                                    }
                                }
                                ?>
                                <?php foreach ($grouped_orders as $key => $group): ?>
                                    <option value="<?= htmlspecialchars(json_encode($group, JSON_HEX_QUOT | JSON_HEX_APOS)) ?>">
                                        <?= htmlspecialchars($group['order_info']['order_number']) ?> -
                                        <?= htmlspecialchars($group['order_info']['supplier_name']) ?>
                                        (<?= $group['order_info']['order_date'] ?>) -
                                        共<?= count($group['items']) ?>种商品
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <option value="create_new" style="background-color: #e6f3ff; font-weight: bold;">
                                + 创建新采购单并入库
                            </option>
                        </select>
                    </div>

                    <!-- 采购单信息显示 -->
                    <div id="order_info_display" class="order-info-card" style="display: none;">
                        <div class="order-header">
                            <h4 id="order_title"></h4>
                            <div class="order-details">
                                <span id="order_supplier"></span>
                                <span id="order_date"></span>
                                <span id="order_amount"></span>
                            </div>
                        </div>
                    </div>

                    <!-- 手动填写区域 -->
                    <div id="manual_input_area">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-barcode"></i>
                                批次号
                            </label>
                            <input type="text" name="batch_number" class="form-control"
                                   value="<?= 'BATCH' . date('YmdHis') ?>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-calendar"></i>
                                入库日期
                            </label>
                            <input type="date" name="inbound_date" class="form-control"
                                   value="<?= date('Y-m-d') ?>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-user"></i>
                                操作员
                            </label>
                            <input type="text" name="operator_name" class="form-control"
                                   value="管理员" required>
                        </div>

                        <div class="form-group" id="supplier_select_group">
                            <label class="form-label">
                                <i class="fas fa-truck"></i>
                                供应商
                            </label>
                            <select name="supplier_id" class="form-control" required>
                                <option value="">请选择供应商</option>
                                <?php foreach ($suppliers as $supplier): ?>
                                    <option value="<?= $supplier['id'] ?>">
                                        <?= htmlspecialchars($supplier['name']) ?>
                                        <?php if ($supplier['contact_person']): ?>
                                            - <?= htmlspecialchars($supplier['contact_person']) ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="step-actions">
                    <button type="button" class="btn btn-primary btn-block" onclick="nextStep()">
                        下一步 <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </section>

            <!-- 步骤2：确认食材 -->
            <section class="step-section" id="step2">
                <div class="step-card">
                    <div class="card-header">
                        <h2><i class="fas fa-weight-hanging"></i> 确认食材重量</h2>
                        <p id="step2Description">请输入实际重量并拍摄称重照片</p>
                    </div>

                    <!-- 手动添加食材表单（仅在手动模式显示） -->
                    <div class="add-item-form" id="manualAddForm" style="display: none;">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-search"></i>
                                选择食材
                            </label>
                            <select id="ingredientSelect" class="form-control">
                                <option value="">请选择食材</option>
                                <?php foreach ($ingredients as $ingredient): ?>
                                    <option value="<?= $ingredient['id'] ?>" data-unit="<?= htmlspecialchars($ingredient['unit']) ?>">
                                        <?= htmlspecialchars($ingredient['name']) ?> (<?= htmlspecialchars($ingredient['unit']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-weight"></i>
                                    数量
                                </label>
                                <input type="number" id="quantityInput" class="form-control"
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-dollar-sign"></i>
                                    单价
                                </label>
                                <input type="number" id="priceInput" class="form-control"
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>

                        <button type="button" class="btn btn-success btn-block" onclick="addItem()">
                            <i class="fas fa-plus"></i> 添加食材
                        </button>
                    </div>



                    <!-- 食材列表 -->
                    <div class="items-container" id="itemsContainer">
                        <div class="items-header" id="itemsHeader" style="display: none;">
                            <div class="items-header-top">
                                <h3><i class="fas fa-list"></i> <span id="itemsTitle">食材清单</span></h3>
                                <span class="items-count" id="itemsCount">0</span>
                            </div>
                            <div class="items-stats" id="itemsStats">
                                <span class="stat-item">
                                    <span class="stat-label">商品数量:</span>
                                    <span class="stat-value" id="totalCount">0</span>
                                </span>
                                <span class="stat-item">
                                    <span class="stat-label">验收数量:</span>
                                    <span class="stat-value accepted" id="acceptedCount">0</span>
                                </span>
                                <span class="stat-item">
                                    <span class="stat-label">未验收数量:</span>
                                    <span class="stat-value pending" id="pendingCount">0</span>
                                </span>
                            </div>
                        </div>
                        <div class="items-list" id="itemsList">
                            <!-- 动态添加的食材项目 -->
                        </div>
                        <div class="items-summary" id="itemsSummary" style="display: none;">
                            <div class="summary-row">
                                <span>预计总金额：</span>
                                <span class="total-amount" id="totalAmount">¥0.00</span>
                            </div>
                        </div>
                    </div>

                    <!-- 空状态提示 -->
                    <div class="empty-items" id="emptyItems">
                        <i class="fas fa-box-open"></i>
                        <p>请先在上一步选择采购单或添加食材</p>
                    </div>
                </div>

                <div class="step-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevStep()">
                        <i class="fas fa-arrow-left"></i> 上一步
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextStep()" id="step2NextBtn" disabled>
                        下一步 <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </section>

            <!-- 步骤3：拍照确认 -->
            <section class="step-section" id="step3">
                <div class="step-card">
                    <div class="card-header">
                        <h2><i class="fas fa-camera"></i> 拍照确认</h2>
                        <p>拍摄送货单照片作为入库凭证</p>
                    </div>
                    
                    <div class="photo-section">
                        <div class="photo-upload">
                            <input type="file" id="deliveryPhoto" name="delivery_photo" 
                                   accept="image/*" capture="environment" style="display: none;">
                            
                            <div class="photo-area" id="photoArea" onclick="document.getElementById('deliveryPhoto').click()">
                                <div class="photo-placeholder">
                                    <i class="fas fa-camera"></i>
                                    <h3>拍摄送货单</h3>
                                    <p>点击此处拍摄送货单照片</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-sticky-note"></i>
                            备注信息
                        </label>
                        <textarea name="notes" class="form-control" rows="3" 
                                  placeholder="请输入备注信息（可选）"></textarea>
                    </div>
                </div>

                <div class="step-actions">
                    <button type="button" class="btn btn-secondary" onclick="prevStep()">
                        <i class="fas fa-arrow-left"></i> 上一步
                    </button>
                    <button type="submit" class="btn btn-success" id="submitBtn">
                        <i class="fas fa-check"></i> 完成入库
                    </button>
                </div>
            </section>

            <!-- 隐藏字段 -->
            <input type="hidden" name="batch_type" value="batch">
            <input type="hidden" name="order_id" value="">
            <input type="hidden" name="is_new_order" value="0">
            <input type="hidden" name="new_order_data" value="">
        </form>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="index.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="inbound.php" class="nav-item active">
            <i class="fas fa-box"></i>
            <span>入库</span>
        </a>
        <a href="inventory.php" class="nav-item">
            <i class="fas fa-warehouse"></i>
            <span>库存</span>
        </a>
        <a href="purchase.php" class="nav-item">
            <i class="fas fa-shopping-cart"></i>
            <span>采购</span>
        </a>
        <a href="reports.php" class="nav-item">
            <i class="fas fa-chart-bar"></i>
            <span>报表</span>
        </a>
        <a href="profile.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </nav>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在处理入库...</div>
        </div>
    </div>

    <script>
        // 分类数据
        const primaryCategories = <?= json_encode($primary_categories) ?>;
        const secondaryCategories = <?= json_encode($secondary_categories) ?>;
        const ingredientsData = <?= json_encode($ingredients) ?>;



        // 更新统计信息
        function updateStats() {
            const totalCount = addedItems.length;
            const acceptedCount = addedItems.filter(item => item.photo_url).length;
            const pendingCount = totalCount - acceptedCount;

            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('acceptedCount').textContent = acceptedCount;
            document.getElementById('pendingCount').textContent = pendingCount;
        }

        // 渲染商品列表
        function renderItems(items) {
            const itemsList = document.getElementById('itemsList');

            // 更新统计信息
            updateStats();

            if (items.length === 0) {
                itemsList.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <p>没有找到符合条件的商品</p>
                    </div>
                `;
                return;
            }

            // 渲染商品列表
            itemsList.innerHTML = `
                <!-- 表头 -->
                <div class="items-table-header">
                    <div class="header-name">商品名称</div>
                    <div class="header-quantity">采购数量</div>
                    <div class="header-weight">实际重量</div>
                    <div class="header-photo">拍照</div>
                    <div class="header-status">状态</div>
                </div>

                <!-- 商品列表 -->
                ${items.map(item => `
                    <div class="order-item-table-row" data-index="${item.index}">
                        <div class="item-name-cell">
                            ${item.ingredient_name}
                        </div>

                        <div class="item-quantity-cell">
                            ${item.quantity}${item.unit}
                        </div>

                        <div class="item-weight-cell">
                            <div class="weight-input-compact">
                                <input type="number"
                                       name="items[${item.index}][actual_quantity]"
                                       class="weight-input-small"
                                       value="${item.actual_quantity}"
                                       step="0.01"
                                       min="0"
                                       placeholder="0.00"
                                       onchange="updateItemWeight(${item.index}, this.value)"
                                       required>
                                <span class="weight-unit-small">${item.unit}</span>
                            </div>
                        </div>

                        <div class="item-photo-cell" id="photoSection_${item.index}">
                            ${item.photo_url ? `
                                <div class="photo-actions-compact">
                                    <button type="button" class="photo-btn-small has-photo" onclick="takeItemPhoto(${item.index})" title="重新拍摄">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="view-photo-btn-small" onclick="viewItemPhoto(${item.index})" title="查看照片">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            ` : `
                                <button type="button"
                                        class="photo-btn-small"
                                        onclick="takeItemPhoto(${item.index})"
                                        title="拍摄称重照片">
                                    <i class="fas fa-camera"></i>
                                </button>
                            `}
                            <input type="file"
                                   id="itemPhoto_${item.index}"
                                   name="items[${item.index}][weight_photo]"
                                   accept="image/*"
                                   capture="environment"
                                   style="display: none;"
                                   onchange="handleItemPhoto(${item.index})">
                        </div>

                        <div class="item-status-cell" id="itemStatus_${item.index}">
                            <i class="fas fa-clock text-warning"></i>
                        </div>

                        <!-- 隐藏字段 -->
                        <input type="hidden" name="items[${item.index}][ingredient_id]" value="${item.ingredient_id}">
                        <input type="hidden" name="items[${item.index}][unit_price]" value="${item.price}">
                        <input type="hidden" name="items[${item.index}][order_quantity]" value="${item.quantity}">
                    </div>
                `).join('')}
            `;
        }
    </script>
    <script src="main.js"></script>
    <script src="inbound.js"></script>
    <script>
        // 移动端入库页面优化脚本
        document.addEventListener('DOMContentLoaded', function() {
            initializeInboundPage();
        });

        function initializeInboundPage() {
            // 初始化进度指示器
            updateProgressIndicator(1);

            // 初始化拖拽上传
            initializeDragAndDrop();

            // 初始化触摸优化
            initializeTouchOptimization();

            // 初始化表单验证
            initializeFormValidation();

            // 初始化相机功能
            initializeCameraFeatures();
        }

        // 更新进度指示器
        function updateProgressIndicator(currentStep) {
            const steps = document.querySelectorAll('.progress-step');
            const progressFills = document.querySelectorAll('.progress-fill');

            steps.forEach((step, index) => {
                const stepNumber = index + 1;
                step.classList.remove('active', 'completed');

                if (stepNumber < currentStep) {
                    step.classList.add('completed');
                } else if (stepNumber === currentStep) {
                    step.classList.add('active');
                }
            });

            // 更新进度条
            progressFills.forEach((fill, index) => {
                if (index < currentStep - 1) {
                    fill.style.width = '100%';
                } else {
                    fill.style.width = '0%';
                }
            });
        }

        // 步骤导航
        function goToStep(stepNumber) {
            // 隐藏所有步骤
            document.querySelectorAll('.step-section').forEach(section => {
                section.classList.remove('active');
            });

            // 显示目标步骤
            const targetStep = document.getElementById(`step${stepNumber}`);
            if (targetStep) {
                targetStep.classList.add('active');
                updateProgressIndicator(stepNumber);

                // 滚动到顶部
                window.scrollTo({ top: 0, behavior: 'smooth' });

                // 更新扫码按钮显示
                const scanBtn = document.querySelector('.scan-btn');
                if (scanBtn) {
                    scanBtn.style.display = stepNumber === 2 ? 'flex' : 'none';
                }
            }
        }

        // 初始化拖拽上传
        function initializeDragAndDrop() {
            const uploadAreas = document.querySelectorAll('.photo-upload-area');

            uploadAreas.forEach(area => {
                area.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });

                area.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });

                area.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        handleFileUpload(files[0], this);
                    }
                });
            });
        }

        // 处理文件上传
        function handleFileUpload(file, uploadArea) {
            if (!file.type.startsWith('image/')) {
                showToast('请选择图片文件', 'error');
                return;
            }

            if (file.size > 5 * 1024 * 1024) {
                showToast('图片大小不能超过5MB', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                addPhotoPreview(e.target.result, uploadArea);
            };
            reader.readAsDataURL(file);
        }

        // 添加照片预览
        function addPhotoPreview(imageSrc, uploadArea) {
            const previewGrid = uploadArea.nextElementSibling;
            if (!previewGrid || !previewGrid.classList.contains('photo-preview-grid')) {
                return;
            }

            const preview = document.createElement('div');
            preview.className = 'photo-preview';
            preview.innerHTML = `
                <img src="${imageSrc}" alt="预览图片">
                <button type="button" class="photo-remove" onclick="removePhotoPreview(this)">
                    <i class="fas fa-times"></i>
                </button>
            `;

            previewGrid.appendChild(preview);

            // 添加动画效果
            preview.style.opacity = '0';
            preview.style.transform = 'scale(0.8)';
            setTimeout(() => {
                preview.style.transition = 'all 0.3s ease-out';
                preview.style.opacity = '1';
                preview.style.transform = 'scale(1)';
            }, 10);
        }

        // 移除照片预览
        function removePhotoPreview(button) {
            const preview = button.closest('.photo-preview');
            preview.style.transition = 'all 0.3s ease-out';
            preview.style.opacity = '0';
            preview.style.transform = 'scale(0.8)';
            setTimeout(() => {
                preview.remove();
            }, 300);
        }

        // 初始化触摸优化
        function initializeTouchOptimization() {
            // 优化表单输入框
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
                });

                input.addEventListener('blur', function() {
                    this.style.transform = '';
                    this.style.boxShadow = '';
                });
            });

            // 优化按钮点击
            const buttons = document.querySelectorAll('button, .btn');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                }, { passive: true });

                button.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                }, { passive: true });
            });
        }

        // 初始化表单验证
        function initializeFormValidation() {
            const form = document.getElementById('inboundForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!validateCurrentStep()) {
                        e.preventDefault();
                        showToast('请完善必填信息', 'error');
                    }
                });
            }
        }

        // 验证当前步骤
        function validateCurrentStep() {
            const activeStep = document.querySelector('.step-section.active');
            if (!activeStep) return false;

            const requiredFields = activeStep.querySelectorAll('[required]');
            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    field.focus();
                    field.style.borderColor = 'var(--danger-color)';
                    setTimeout(() => {
                        field.style.borderColor = '';
                    }, 3000);
                    return false;
                }
            }

            return true;
        }

        // 初始化相机功能
        function initializeCameraFeatures() {
            // 检查设备是否支持相机
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                console.log('设备支持相机功能');
            } else {
                console.log('设备不支持相机功能');
            }
        }

        // 开始扫码
        function startScan() {
            showToast('扫码功能开发中...', 'info');
        }

        // 显示帮助
        function showHelp() {
            const helpContent = `
                <div style="text-align: left; line-height: 1.6;">
                    <h3 style="margin-bottom: 16px; color: var(--primary-color);">
                        <i class="fas fa-info-circle"></i> 入库操作指南
                    </h3>
                    <div style="margin-bottom: 12px;">
                        <strong>步骤1：基本信息</strong><br>
                        选择采购单或手动填写供应商信息
                    </div>
                    <div style="margin-bottom: 12px;">
                        <strong>步骤2：添加食材</strong><br>
                        添加入库食材，填写实际重量
                    </div>
                    <div style="margin-bottom: 12px;">
                        <strong>步骤3：拍照确认</strong><br>
                        为每个食材拍摄称重照片作为凭证
                    </div>
                    <div style="color: var(--text-muted); font-size: 14px; margin-top: 16px;">
                        <i class="fas fa-lightbulb"></i>
                        提示：可以点击统计卡片快速导航到相关页面
                    </div>
                </div>
            `;

            showModal('操作指南', helpContent);
        }

        // 显示模态框
        function showModal(title, content) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button type="button" class="modal-close" onclick="closeModal(this)">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            `;

            // 添加模态框样式
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                backdrop-filter: blur(5px);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                animation: fadeIn 0.3s ease-out;
            `;

            const modalContent = modal.querySelector('.modal-content');
            modalContent.style.cssText = `
                background: white;
                border-radius: 16px;
                max-width: 400px;
                width: 100%;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: var(--shadow-xl);
                animation: scaleIn 0.3s ease-out;
            `;

            const modalHeader = modal.querySelector('.modal-header');
            modalHeader.style.cssText = `
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid var(--border-color);
            `;

            const modalBody = modal.querySelector('.modal-body');
            modalBody.style.cssText = `
                padding: 20px;
            `;

            const closeBtn = modal.querySelector('.modal-close');
            closeBtn.style.cssText = `
                background: none;
                border: none;
                font-size: 18px;
                color: var(--text-muted);
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                transition: all 0.2s ease;
            `;

            document.body.appendChild(modal);

            // 点击背景关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal(modal.querySelector('.modal-close'));
                }
            });
        }

        // 关闭模态框
        function closeModal(button) {
            const modal = button.closest('.modal-overlay');
            modal.style.animation = 'fadeOut 0.3s ease-out forwards';
            setTimeout(() => {
                document.body.removeChild(modal);
            }, 300);
        }

        // 显示提示消息（复用主页的函数）
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <div class="toast-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            toast.style.cssText = `
                position: fixed;
                top: 100px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? 'var(--success-gradient)' : type === 'error' ? 'var(--danger-gradient)' : 'var(--info-gradient)'};
                color: white;
                padding: 12px 20px;
                border-radius: 25px;
                box-shadow: var(--shadow-lg);
                z-index: 10000;
                animation: slideDown 0.3s ease-out;
                font-weight: 500;
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideUp 0.3s ease-out forwards';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }
    </script>
</body>
</html>
